{
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.guides.bracketPairs": true,
  "diffEditor.ignoreTrimWhitespace": false, // 关闭自动检测缩进
  "jest.runMode": {
    "type": "on-demand",
    "showInlineError": false
  },
  "testExplorer.onStart": "show",
  "testing.openTesting": "neverOpen", // 跟jest.outputConfig配合使用
  "jest.outputConfig": {
    "revealOn": "run",
    "revealWithFocus": "test-results",
    "clearOnRun": "test-results"
  },
  "testing.coverageToolbarEnabled": true,
  "testing.automaticallyOpenTestResults": "neverOpen",
  "workbench.tree.indent": 12,
  "workbench.view.alwaysShowHeaderActions": true,
  "workbench.activityBar.orientation": "vertical",
  "testing.automaticallyOpenPeekView": "never",
  "testing.alwaysRevealTestOnStateChange": true,
  "testing.showAllMessages": true,
  "testing.followRunningTest": true,
  "workbench.layoutControl.enabled": true,
  "workbench.sideBar.location": "left",
  "workbench.panel.showLabels": true,
  "workbench.colorCustomizations": {
    "[*Light*]": {
      "tab.activeBackground": "#cef1ed"
    },
    "[Default Dark+]": {
      "tab.activeBackground": "#0d3a5d"
    }
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnType": false, // required
  "editor.formatOnPaste": false, // optional
  "editor.formatOnSave": true, // optional
  "editor.formatOnSaveMode": "file", // required to format on save
  "files.autoSave": "onFocusChange" // optional but recommended
}
