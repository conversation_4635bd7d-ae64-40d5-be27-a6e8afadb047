{"index_patterns": ["kys_snapshot_*"], "settings": {"number_of_shards": 3, "number_of_replicas": 2}, "mappings": {"_source": {"enabled": true}, "properties": {"id": {"type": "keyword"}, "dimensionId": {"type": "keyword"}, "dimensionKey": {"type": "keyword"}, "dimensionContent": {"type": "text", "index": false}, "createDate": {"type": "date"}, "diligence": {"properties": {"id": {"type": "keyword"}, "orgId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "diligenceId": {"type": "keyword"}, "batchId": {"type": "keyword"}, "snapshotId": {"type": "keyword"}, "diligenceAt": {"type": "date"}, "createDate": {"type": "date"}, "updateHistory": {"type": "nested", "properties": {"operator": {"type": "keyword"}, "status": {"type": "keyword"}, "content": {"type": "text"}, "createDate": {"type": "date"}}}}}, "dimensionStrategy": {"properties": {"id": {"type": "keyword"}, "orgId": {"type": "keyword"}, "companyId": {"type": "keyword"}, "diligenceId": {"type": "keyword"}, "batchId": {"type": "keyword"}, "snapshotId": {"type": "keyword"}, "strategyId": {"type": "keyword"}}}, "relation": {"type": "join", "eager_global_ordinals": true, "relations": {"dimension": ["diligence", "dimensionStrategy"]}}}}}