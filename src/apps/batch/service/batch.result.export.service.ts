/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { tmpdir } from 'os';
import * as Excel from 'exceljs';
import { createReadStream } from 'fs';
import * as Bluebird from 'bluebird';
import { OssService } from '@kezhaozhao/qcc-utils';
import { ConfigService } from 'libs/config/config.service';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { Repository } from 'typeorm';
import { BatchJobResultTypeEnums, GetBatchJobResultTypeEnumsName } from 'libs/enums/batch/BatchJobResultTypeEnums';
import { ExcelParserSettingPO } from 'libs/model/batch/po/parse/ExcelParserSettingPO';
import { v1 as uuidv1 } from 'uuid';
import { FieldParseErrorTypeEnums } from '../../../libs/enums/batch/FieldParseErrorTypeEnums';
import Worksheet from 'exceljs/index';

@Injectable()
export class BatchResultExportService {
  private readonly logger: Logger = QccLogger.getLogger(BatchResultExportService.name);

  constructor(
    private configService: ConfigService,
    private readonly ossService: OssService,
    @InjectRepository(BatchResultEntity) private readonly batchResultRepo: Repository<BatchResultEntity>,
  ) {}

  // 获取excel对象
  public async getExcel(filepath: string, sheetName: string, excelColumns: object) {
    const options = {
      filename: filepath,
      useStyles: true,
      useSharedStrings: true,
    };
    const workbook = new Excel.stream.xlsx.WorkbookWriter(options);
    const worksheet = workbook.addWorksheet(sheetName, {
      views: [
        {
          state: 'frozen',
          xSplit: 1,
          ySplit: 1,
        },
      ],
    });
    const displayColumns: any = [];
    Object.keys(excelColumns).forEach((field) => {
      const column = excelColumns[field];
      if (column) {
        displayColumns.push({
          key: field,
          header: column.header,
          width: column.width,
        });
      }
    });

    worksheet.columns = displayColumns;
    for (let i = 1; i <= displayColumns.length; i++) {
      worksheet.getColumn(i).alignment = {
        wrapText: true,
        vertical: 'middle',
        horizontal: 'center',
      };
    }
    const header = worksheet.getRow(1);
    header.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      // size: 14,
    };
    header.height = 22;
    header.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF228B22' },
      bgColor: { argb: 'FF228B22' },
    };
    return {
      workbook,
      worksheet,
    };
  }

  //获取多sheet excel对象
  public async getExcelByTemplate(filepath: string, excelSettings: ExcelParserSettingPO[]) {
    const workbook = new Excel.Workbook();
    const worksheets: Worksheet[] = [];
    excelSettings?.forEach((excelSetting) => {
      const sheetName = excelSetting.sheetName;
      const excelColumns = excelSetting.exportExcelColumns;
      const excelColumnsStartLine = excelSetting.excelColumnsStartLine || 1;
      const worksheet = workbook.addWorksheet(sheetName, {
        views: [
          {
            state: 'frozen',
            xSplit: 1,
            ySplit: 1,
          },
        ],
      });
      //worksheet.addRow(excelSetting.templateTitle);
      worksheet.insertRow(excelColumnsStartLine, excelSetting.templateTitle);
      Object.keys(excelColumns).forEach((field, index) => {
        const column = excelColumns[field];
        const columnIndexStr = this.getColumnIndexStr(index);
        const columnTemp = worksheet.getColumn(columnIndexStr);
        if (column) {
          columnTemp.width = column.width;
          columnTemp.key = field;
          // columnTemp.header = column.header;
          columnTemp.alignment = {
            wrapText: true,
            vertical: 'middle',
            horizontal: 'left',
          };
        }
      });
      if (excelSetting?.headerDescription) {
        //按开始行，开始列，结束行，结束列合并
        // worksheet.mergeCells(`${String.fromCharCode(65)}1:${String.fromCharCode(64 + excelSetting.templateTitle?.length)}1`);
        worksheet.mergeCells(`${String.fromCharCode(65)}1:${this.getColumnIndexStr(excelSetting.templateTitle?.length - 1)}1`);
        const titleRow = worksheet.getRow(1);
        titleRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFC0C0C0' },
          bgColor: { argb: 'FFC0C0C0' },
        };
        // 设置声明行的数据和样式
        titleRow.getCell(1).value = excelSetting?.headerDescription;
        // 合并单元格并居中展示声明行，默认声明行为第一行
        titleRow.getCell(1).alignment = { horizontal: 'left', vertical: 'middle' };
      }
      if (excelSetting?.tableTitle) {
        worksheet.mergeCells(
          `${String.fromCharCode(65)}${excelSetting.tableTitleLine}:${this.getColumnIndexStr(excelSetting.templateTitle?.length - 1)}${
            excelSetting.tableTitleLine
          }`,
        );
        const tableTitle = worksheet.getRow(excelSetting.tableTitleLine);
        // 设置表格标题行的数据和样式
        tableTitle.getCell(1).value = excelSetting?.tableTitle;
        // 合并单元格并居中展示标题行，默认表格标题行为第二行
        tableTitle.getCell(1).alignment = { horizontal: 'center', vertical: 'middle' };
        tableTitle.getCell(1).font = { bold: true, size: 14 };
      }
      const header = worksheet.getRow(excelColumnsStartLine);
      header.font = {
        color: { argb: 'FFFFFFFF' },
        bold: true,
        // size: 14,
      };
      header.height = 22;
      header.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF228B22' },
        bgColor: { argb: 'FF228B22' },
      };
      worksheets.push(worksheet);
    });
    return {
      workbook,
      worksheets,
    };
  }

  /**
   * 并上传到oss 返回oss下载地址
   * @param filepath
   * @param fileName
   * @param displayName
   * @returns
   */
  public async putFileToOss(filepath: string, fileName: string, displayName?: string) {
    try {
      const ossObject = this.configService.getOssObject(`batch_import_result`, fileName);
      const options: any = {};
      if (displayName) {
        const downloadName = encodeURIComponent(displayName);
        options.headers = {
          'Content-Disposition': `attachment;filename=${downloadName};filename*=UTF-8''${downloadName}`,
        };
      }
      const result = await this.ossService.putStream(ossObject, createReadStream(filepath), options);
      await this.ossService.putACL(result['name'], 'private');
      return result['name'];
      // return result['url'].replace(/-internal/gi, '');
      // return this.ossService.signatureUrl(ossObject, { expires: this.configService.server.oss.urlExpires });
    } catch (e) {
      this.logger.error('oss error', e);
    }
  }

  // 获取导出数据并生成excel
  public async generateResultFile(batchId: number, businessType: BatchBusinessTypeEnums) {
    const results: BatchResultEntity[] = await this.batchResultRepo.find({ batchId });
    if (!results?.length) {
      this.logger.info('generateResultFile no result info for batchId: ', batchId);
      return '';
    }
    const fileName = `batch_import_result_info_${batchId}_${uuidv1()}.xlsx`;
    const filepath = `${tmpdir()}/${fileName}`;

    this.logger.info(`generateResultFile batchId: ${batchId} fileName ${fileName}`);
    try {
      let excelParserSetting: ExcelParserSettingPO;
      switch (businessType) {
        case BatchBusinessTypeEnums.Diligence_ID: {
          excelParserSetting = {
            explainRows: 3,
            limit: 5000,
            sheetName: '批量排查-输入框手动填写',
            templateTitle: ['企业名称/统一社会信用代码/注册号'],
            exportExcelColumns: {
              resultDes: { header: '导入结果', width: 30 },
              companyName: { header: '企业名称/统一社会信用代码/注册号', width: 50 },
            },
          };
          break;
        }
        case BatchBusinessTypeEnums.Diligence_Continuous: {
          //TODO 持续排查时候批次失败
        }
        default: {
          const msg = `generateResultFile unknown batch business type: ${businessType}`;
          this.logger.warn(msg);
          return '';
        }
      }

      const { workbook, worksheet } = await this.getExcel(filepath, excelParserSetting.sheetName, excelParserSetting.exportExcelColumns);
      await Bluebird.map(results, (result) => {
        let resultDes = '';
        if (result.resultType == BatchJobResultTypeEnums.FAILED_VERIFICATION) {
          resultDes = result.comment;
        } else if (result.comment === FieldParseErrorTypeEnums.Required) {
          resultDes = GetBatchJobResultTypeEnumsName(BatchJobResultTypeEnums.COLUMN_REQUIRED);
        } else if (result.comment === FieldParseErrorTypeEnums.Unmatched_Format) {
          resultDes = GetBatchJobResultTypeEnumsName(BatchJobResultTypeEnums.FAILED_VERIFICATION);
        } else {
          resultDes = GetBatchJobResultTypeEnumsName(result.resultType);
        }

        if (result?.resultInfo) {
          worksheet.addRow({ ...result.resultInfo, resultDes }).commit();
        }
      });
      worksheet.commit();
      await workbook.commit();
      await Bluebird.delay(200);

      const displayName = `${excelParserSetting.sheetName}_结果导出.xlsx`;
      // await unlinkSync(filepath);
      return await this.putFileToOss(filepath, fileName, displayName);
    } catch (error) {
      this.logger.error(`generateResultFile error batchId: ${batchId} error ${error}`);
      this.logger.error(error);
      throw error;
    }
  }

  private getColumnIndexStr(index: number) {
    const columnLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (index < 0) {
      return null;
    }
    let result = '';
    while (index >= 0) {
      result = columnLetters[index % 26] + result;
      index = Math.floor(index / 26) - 1;
    }
    return result;
  }
}
