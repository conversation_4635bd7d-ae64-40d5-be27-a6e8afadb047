import { BatchDiligenceProcessor } from './batch.diligence.processor';
import { Injectable } from '@nestjs/common';
import { BatchBusinessTypeEnums } from '../../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchJobMessagePO } from '../../../../../libs/model/batch/po/message/BatchJobMessagePO';
import { BatchRunDDResultPO } from '../../../../../libs/model/batch/po/BatchRunDDResultPO';
import { AnalyzeMonitorDynamicMessagePO } from '../../../../monitor/po/AnalyzeMonitorDynamicMessagePO';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { BatchDiligenceExcelRecord } from '../../../../../libs/model/batch/po/parse/ParsedRecordBase';
import { DDCostItemPO, DDCostPO } from '../../../../../libs/model/batch/po/DDCostItemPO';
import { BatchConstants } from '../../../common/batch.constants';
import * as Bluebird from 'bluebird';
import { DiligenceResponse } from '../../../../../libs/model/diligence/req&res/DiligenceResponseV2';
import { BatchResultPO } from '../../../model/BatchResultPO';
import { BatchJobResultTypeEnums } from '../../../../../libs/enums/batch/BatchJobResultTypeEnums';
import { BatchDiligenceEntity } from '../../../../../libs/entities/BatchDiligenceEntity';
import { processBatchJobFailed } from '../../../common/batch.utils';
import { Cacheable } from '@type-cacheable/core';
import { BatchEntity } from 'libs/entities/BatchEntity';

@Injectable()
export class ContinuousDiligenceProcessor extends BatchDiligenceProcessor {
  constructor() {
    super();
  }

  getBusinessType(): BatchBusinessTypeEnums[] {
    return [BatchBusinessTypeEnums.Diligence_Continuous];
  }
  @Cacheable({ ttlSeconds: 60 })
  async fetchBatch(batchId: number) {
    const batch = await this.batchRepo.findOne({
      where: {
        batchId,
      },
    });
    return batch;
  }

  async processJobMessage(message: BatchJobMessagePO): Promise<BatchRunDDResultPO> {
    this.logger.info(`processJobMessage for continuous diligence message: ${JSON.stringify(message)}`);

    const results: BatchRunDDResultPO = await this.batchRunContinuousDiligence(message);

    // 如果持续尽调忽略生成动态，则不发送消息
    const monitorGroupId = message.items
      .map((i) => i.recordParams?.monitorGroupId)
      .filter((i) => i)
      .pop();
    // 如果 monitorGroupId 有值，则开始对这个monitorGroup进行分析
    // 如果 monitorGroupId 没有值，则不进行分析(说明只是利用持续尽调这个batch生成了基于监控模型的尽调记录)
    if (monitorGroupId) {
      //TODO  根据batchID去获取一下batchInfo ，看一下，如果有refQaTaskId，则不发送消息
      const batchEntity: BatchEntity = await this.fetchBatch(message.batchId);
      if (batchEntity?.batchInfo?.refQaTaskId) {
        this.logger.info(`batchId=${message.batchId} has refQaTaskId, skip sendMessage to handleMetricsAnalyze`);
      } else {
        this.logger.info(`batchId=${message.batchId} has no refQaTaskId, sendMessage to handleMetricsAnalyze`);
        const msgPO: AnalyzeMonitorDynamicMessagePO = {
          orgId: message.orgId,
          product: message.product,
          monitorGroupId,
          diligenceIds: results.ddDetails?.details.map((i) => i.diligenceId).filter((i) => i),
          batchId: message.batchId,
          retryCount: 0,
        };
        //发送消息，触发对尽调结果进行指标分析并存储
        const ttl = 60 * 1000;
        this.logger.info(`sendMessage to handleMetricsAnalyze delay: ${ttl} `);
        await this.queueService.continuousDiligenceAnalyzeQueue.sendMessageV2(msgPO, { ttl });
      }
    }
    return results;
  }

  /**
   * 单个 batchJob 执行
   * @param message 任务信息
   */
  @TraceLog({ throwError: true, spanType: 3, spanName: 'batchRunContinuousDiligence' })
  public async batchRunContinuousDiligence(message: BatchJobMessagePO): Promise<BatchRunDDResultPO> {
    const { batchId, items, jobId } = message;
    const data: BatchDiligenceExcelRecord[] = items;
    const response: BatchRunDDResultPO = new BatchRunDDResultPO();
    response.passedCount = data.length;
    const jobStart = Date.now();
    this.logger.info(`processJobMessage batchRunDiligence: batchId=${batchId},jobId=${jobId}`);
    const result = await this.runContinuousDiligence(data, message);
    response.ddDetails = result;
    //设置一个平均时间，只有大于这个时间的，才记录 ddCostDetails
    const ddDetails = response.ddDetails;
    if (ddDetails?.details) {
      if (!response.ddDetails.details) {
        this.logger.warn('error here');
      }
    }
    response.totalCost = Date.now() - jobStart;
    return response;
  }

  @TraceLog({ throwError: true, spanType: 3, spanName: 'runContinuousDiligence' })
  public async runContinuousDiligence(items: BatchDiligenceExcelRecord[], jobParams: BatchJobMessagePO): Promise<DDCostPO> {
    const { batchId, jobId } = jobParams;
    const results: DDCostItemPO[] = [];
    const concurrency = BatchConstants.JobConcurrency.Continuous;
    const response: DDCostPO = { concurrency, recordCount: items.length, totalCost: 0, details: [] };
    const startTime = Date.now();
    await Bluebird.map(
      items,
      async (diligenceItem: BatchDiligenceExcelRecord) => {
        const { currentOrg } = diligenceItem.recordParams.currentUser;
        const dd: DDCostItemPO = {
          companyId: diligenceItem.companyId,
          cost: 0,
        };
        const orgModelId = diligenceItem.recordParams.orgModelIds[0];
        const start = Date.now();
        try {
          this.logger.info(`runMonitorRisk() start for batchId=${batchId}, jobId=${jobId}, companyId=${diligenceItem.companyId}`);
          const res: DiligenceResponse = await this.evaluationService.runMonitorRisk(
            currentOrg,
            {
              companyName: diligenceItem.companyName,
              companyId: diligenceItem.companyId,
              isRelated: diligenceItem.isRelated,
              orgModelIds: [orgModelId],
              batchId,
            },
            'SAAS_PRO',
          );
          const { id: diligenceId, paid } = res;
          dd.diligenceId = diligenceId;
          dd.paid = paid == 1;
          const batchDiligence = await this.batchDiligenceRepo.findOne({
            where: {
              batchId,
              diligenceId,
            },
          });
          const toSave: BatchResultPO = {
            batchId,
            jobId,
            resultInfo: { ...diligenceItem, diligenceId },
            resultHashkey: jobParams?.items?.find((t) => t.companyId === diligenceItem.companyId)?.recordHashkey,
            resultType: BatchJobResultTypeEnums.SUCCEED_UNPAID,
          };
          if (batchDiligence) {
            await this.batchHelperService.handleJobResult([Object.assign(toSave, { resultType: BatchJobResultTypeEnums.SUCCEED_DUPLICATED })]);
            this.logger.info(
              `runMonitorRisk() finished(with existed batchDiligence) for batchId=${batchId}, jobId=${jobId}, companyId=${diligenceItem.companyId}`,
            );
          } else {
            await Bluebird.all([
              this.batchHelperService.handleJobResult([
                Object.assign(toSave, { resultType: paid ? BatchJobResultTypeEnums.SUCCEED_PAID : BatchJobResultTypeEnums.SUCCEED_UNPAID }),
              ]),
              this.batchDiligenceRepo.save(
                Object.assign(new BatchDiligenceEntity(), {
                  batchId,
                  jobId,
                  diligenceId,
                }),
              ),
            ]);
            this.logger.info(`runMonitorRisk() finished for batchId=${batchId}, jobId=${jobId}, companyId=${diligenceItem.companyId}`);
          }
        } catch (e) {
          //出错后保存到数据库中
          this.logger.error(
            `runMonitorRisk error: batchId=${batchId},jobId=${jobId},param=${JSON.stringify(diligenceItem)},error=${e.response?.error || e.message}`,
          );
          this.logger.error(e);
          const batchResult: BatchResultPO = {
            batchId,
            jobId,
            resultInfo: diligenceItem,
            resultHashkey: jobParams?.items?.find((t) => t.companyId === diligenceItem.companyId)?.recordHashkey,
            resultType: BatchJobResultTypeEnums.FAILED_CODE,
          };
          processBatchJobFailed(e, batchResult);
          await this.batchHelperService.handleJobResult([batchResult]);
        }
        dd.cost = Date.now() - start;
        results.push(dd);
      },
      { concurrency },
    );
    response.details = results;
    response.totalCost = Date.now() - startTime;
    return response;
  }
}
