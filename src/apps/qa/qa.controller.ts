import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiGuardInternal } from '../../libs/guards/api.guard.internal';
import { PlatformUser } from '../../libs/model/common';
import { DatasetService } from './dataset.service';
import { AddItemsToDatasetRequest } from './model/request/AddItemsToDatasetRequest';
import { AddQaItemsRequest } from './model/request/AddQaItemsRequest';
import { CreateDatesetRequest } from './model/request/CreateDatesetRequest';
import { CreateQaLabelRequest } from './model/request/CreateQaLabelRequest';
import { CreateQaTaskRequest } from './model/request/CreateQaTaskRequest';
import { EditQaCompanyTagsRequest } from './model/request/EditQaCompanyTagsRequest';
import { SearchDatasetItemsRequest } from './model/request/SearchDatasetItemsRequest';
import { SearchDatasetRequest } from './model/request/SearchDatasetRequest';
import { SearchQaCompanyRequest } from './model/request/SearchQaCompanyRequest';
import { SearchQaLabelRequest } from './model/request/SearchQaLabelRequest';
import { SearchQaTaskRequest } from './model/request/SearchQaTaskRequest';
import { QaCompanyService } from './qa.company.service';
import { QaLabelService } from './qa.label.service';
import { QaTaskService } from './qa.task.service';
import { SearchQaTaskResultRequest } from './model/request/SearchQaTaskResultRequest';
import { SearchQaTaskResultStatisticsRequest } from './model/request/SearchQaTaskResultStatisticsRequest';
import { MarkQaTaskResultRequest } from './model/request/MarkQaTaskResultRequest';
import { QaTaskResultService } from './qa.task.result.service';
import { SearchAnnotatedDataRequest } from './model/request/SearchAnnotatedDataRequest';
import { AddQaItemsByNameRequest } from './model/request/AddQaItemsByNameRequest';
import { BatchStatisticsBasePO } from 'libs/model/batch/po/BatchStatisticsBasePO';
import { QaExportAnalysisService } from './qa.export.analysis.service';

@Controller('qa')
@ApiTags('QA')
@UseGuards(ApiGuardInternal)
export class QaController {
  constructor(
    private readonly dataSetService: DatasetService,
    private readonly qaCompanyService: QaCompanyService,
    private readonly qaTaskService: QaTaskService,
    private readonly qaLabelService: QaLabelService,
    private readonly qaTaskResultService: QaTaskResultService,
    private readonly qaExportAnalysisService: QaExportAnalysisService,
  ) {}

  /**
   *  公司相关
   */
  @Post('company/add')
  async addQaCompanyInfo(@Body() data: AddQaItemsRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaCompanyService.createCompanyInfoBatch(data.companyKeyNos, user);
  }

  @Post('company/addByNames')
  async addQaCompanyInfoByNames(@Body() data: AddQaItemsByNameRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaCompanyService.createCompanyInfoByNameBatch(data.companyNames, user);
  }

  @Post('company/:companyIntId/labels/add')
  async addQaCompanyLabels(@Param('companyIntId') companyIntId: number, @Body() data: EditQaCompanyTagsRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaCompanyService.addCompanyLabels(companyIntId, data.labelIds, user);
  }

  @Post('company/:companyIntId/labels/remove')
  async removeQaCompanyLabels(@Param('companyIntId') companyIntId: number, @Body() data: EditQaCompanyTagsRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaCompanyService.removeCompanyLabels(companyIntId, data.labelIds, user);
  }

  @Post('company/search')
  async searchComapny(@Body() data: SearchQaCompanyRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaCompanyService.searchCompany(data, user);
  }

  /**
   *  测试集相关
   */

  @Post('dataset/create')
  async createSet(@Body() data: CreateDatesetRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.dataSetService.createSet(data, user);
  }

  @Post('dataset/items/add')
  async addItemsToSet(@Body() data: AddItemsToDatasetRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.dataSetService.addItemsToSet(data, user);
  }

  @Post('dataset/items/remove')
  async removeItemsFromSet() {}

  @Post('dataset/items/search')
  async searchItems(@Body() data: SearchDatasetItemsRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.dataSetService.searchItems(data, user);
  }

  @Post('dataset/search')
  async search(@Body() postData: SearchDatasetRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.dataSetService.search(postData, user);
  }

  @Post('dataset/remove/:datasetId')
  async removeSet(@Param('datasetId') datasetId: number, @Req() req: any) {
    return this.dataSetService.removeSet(datasetId);
  }

  @Get('dataset/related/monitor/groups')
  async getQaRelatedMonitorGroups(@Req() req: any) {
    return this.dataSetService.getQaRelatedMonitorGroups(req.user);
  }

  /**
   *  QA 任务管理
   */
  @Post('task/create')
  async createTask(@Body() data: CreateQaTaskRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskService.createTask(data, user);
  }

  @Post('annotated/search')
  async searchAnnotatedData(@Body() data: SearchAnnotatedDataRequest, @Req() req: any) {
    return this.qaTaskResultService.searchAnnotatedData(data);
  }

  @Post('task/delete/:taskId')
  async deleteTask(@Param('taskId') taskId: number, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskService.deleteTask(taskId, user);
  }

  @Post('task/search')
  async searchTask(@Body() data: SearchQaTaskRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskService.search(data, user);
  }

  @Post('task/result/mark')
  @ApiOperation({ summary: '标记结果' })
  async markResultAsFalse(@Body() data: MarkQaTaskResultRequest, @Req() req: any) {
    return this.qaTaskResultService.markTaskResult(data, req.user);
  }

  @Post('task/result/search')
  @ApiOperation({ summary: '查看任务的测试结果' })
  async taskResultAnalyze(@Body() data: SearchQaTaskResultRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskResultService.searchTaskResult(data, user);
  }

  @Post('/task/statistics/search')
  @ApiOperation({ summary: '分析任务的测试结果' })
  async calculateTaskStatistics(@Body() data: SearchQaTaskResultStatisticsRequest, @Req() req: any) {
    return this.qaTaskResultService.statisticsSearch(data, req.user);
  }

  @Post('task/:taskId/start')
  async startTask(@Param('taskId') taskId: number, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskService.startTask(taskId, user);
  }

  @Post('task/:taskId/analyze')
  async analyzeTask(@Param('taskId') taskId: number, @Req() req: any) {
    return this.qaTaskService.analyzeTask(taskId);
  }

  @Post('task/:taskId/retry')
  async retryTask(@Param('taskId') taskId: number, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaTaskService.retryTask(taskId, user);
  }

  @Post('task/:taskId/progress')
  @ApiOkResponse({ type: BatchStatisticsBasePO })
  async getTaskProgress(@Param('taskId') taskId: number) {
    return this.qaTaskService.getTaskProgress(taskId);
  }

  @Post('task/:taskId/export')
  @ApiTags('统计一批次task分值数据的结果')
  @ApiOperation({ description: '统计一批次尽调/监控分值数据的结果，返回CSV文件的OSS下载地址' })
  async generateBatchDilligenceStatistic(@Param('taskId') taskId: number) {
    const result = await this.qaExportAnalysisService.generateBatchDilligenceStatistic(taskId);
    if (!result.success) {
      return result;
    }
    return {
      success: true,
      downloadUrl: result.downloadUrl,
      fileName: result.fileName,
    };
  }

  /**
   *  标签管理
   */

  @Post('label/create')
  async createLabel(@Body() data: CreateQaLabelRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaLabelService.createLabel(data, user);
  }

  @Post('label/remove/:labelId')
  async removeLabel(@Param('labelId') labelId: number, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaLabelService.removeLabel(labelId, user);
  }

  @Post('label/search')
  async searchLabel(@Body() data: SearchQaLabelRequest, @Req() req: any) {
    const user: PlatformUser = req.user;
    return this.qaLabelService.searchLabel(data, user);
  }
}
