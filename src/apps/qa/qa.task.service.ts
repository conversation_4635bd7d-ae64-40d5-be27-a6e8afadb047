import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BadRequestException, Injectable, NotImplementedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BatchInfoPO } from 'apps/batch/model/BatchInfoPO';
import { BatchBaseHelper } from 'apps/batch/service/helper/batch.base.helper';
import { BatchCreatorHelperBase } from 'apps/batch/service/helper/batch.creator.helper.base';
import * as Bluebird from 'bluebird';
import { getFullRiskModel } from 'libs/db_helpers/resource.publish.helper';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { QaDatasetEntity } from 'libs/entities/QaDatasetEntity';
import { QaDatasetItemEntity } from 'libs/entities/QaDatasetItemEntity';
import { QaTaskEntity } from 'libs/entities/QaTaskEntity';
import { BatchStatusEnums } from 'libs/enums/batch/BatchStatusEnums';
import { ParsedRecordBase } from 'libs/model/batch/po/parse/ParsedRecordBase';
import { Logger } from 'log4js';
import * as moment from 'moment';
import { In, Repository } from 'typeorm';
import { QaTaskTypeEnums } from './model/enums/QaTaskTypeEnums';
import { QaTypeEnums } from './model/enums/QaTypeEnums';
import { CreateQaTaskRequest } from './model/request/CreateQaTaskRequest';
import { SearchQaTaskRequest } from './model/request/SearchQaTaskRequest';
import { SearchQaTaskResponse } from './model/response/SearchQaTaskResponse';
import { QaTaskAnalyzerService } from './qa.task.analyzer.service';
import { QaTaskResultService } from './qa.task.result.service';
import { QaTaskResultEntity } from 'libs/entities/QaTaskResultEntity';
import { QaTaskResultAnalyzedEntity } from 'libs/entities/QaTaskResultAnalyzedEntity';
import { PlatformUser } from 'libs/model/common';
import { BatchBusinessTypeEnums } from 'libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from 'libs/enums/batch/BatchTypeEnums';
import { RiskModelEntity } from 'libs/entities/RiskModelEntity';
import { MonitorCompanyEntity } from 'libs/entities/MonitorCompanyEntity';
import { MonitorGroupEntity } from 'libs/entities/MonitorGroupEntity';
import { MonitorJobService } from 'apps/monitor/monitor.job.service';
import { MonitorIntervalUnitType } from 'apps/monitor/po/MonitorCompanyMessagePO';
import { BatchStatisticsBasePO } from 'libs/model/batch/po/BatchStatisticsBasePO';

@Injectable()
export class QaTaskService {
  private readonly logger: Logger = QccLogger.getLogger(QaTaskService.name);

  constructor(
    @InjectRepository(QaTaskEntity) private readonly qaTaskRepo: Repository<QaTaskEntity>,
    @InjectRepository(QaDatasetEntity) private readonly qaDatasetRepo: Repository<QaDatasetEntity>,
    @InjectRepository(QaDatasetItemEntity) private readonly qaDatasetItemRepo: Repository<QaDatasetItemEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(QaTaskResultEntity) private readonly qaTaskResultRepo: Repository<QaTaskResultEntity>,
    @InjectRepository(QaTaskResultAnalyzedEntity) private readonly qaTaskResultAnalyzedRepo: Repository<QaTaskResultAnalyzedEntity>,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
    private readonly qaTaskAnalyzerService: QaTaskAnalyzerService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly qaTaskResultService: QaTaskResultService,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    private readonly monitorJobService: MonitorJobService,
  ) {}

  /**
   * 创建QA测试任务， 同时需要检测一下 modelId
   * 暂时这里只会给BO开发这个功能，所以不需要检测modelId的归属权限问题
   * @param data
   * @param user
   */
  async createTask(data: CreateQaTaskRequest, user: PlatformUser) {
    const { baseModelId, datasetId, taskName, taskType } = data;

    if (taskType === QaTaskTypeEnums.Evaluation) {
      throw new NotImplementedException('模型评估功能暂未支持');
    }

    // 检查模型是否存在
    const promise: Promise<any>[] = [
      this.qaDatasetRepo.findOne({
        where: {
          id: datasetId,
        },
      }),
      this.qaDatasetItemRepo.count({
        where: {
          datasetId,
        },
      }),
    ];
    if (baseModelId) {
      promise.push(getFullRiskModel(baseModelId, this.qaTaskRepo.manager));
    }
    const [datasetEntity, datasetSize1, fullRiskModelBase] = await Promise.all(promise);
    if (!datasetEntity) {
      throw new BadRequestException('数据集不存在');
    }
    let datasetSize = datasetSize1;

    if (datasetEntity.qaType === QaTypeEnums.Diligence) {
      //throw new NotImplementedException('尽调模型测试功能暂未支持');
    } else if (datasetEntity.qaType === QaTypeEnums.Monitor && datasetEntity.refMonitorGroupId) {
      const monitorGroup = await this.monitorGroupRepo.findOne({
        where: { monitorGroupId: datasetEntity.refMonitorGroupId },
      });
      if (!monitorGroup) {
        throw new BadRequestException('监控分组不存在');
      }
      datasetSize = await this.monitorCompanyRepo.count({
        where: { monitorGroupId: datasetEntity.refMonitorGroupId },
      });
    }

    return await this.qaTaskRepo.save({
      taskName,
      orgId: user.currentOrg,
      product: user.currentProduct,
      baseModelId: baseModelId,
      modelBranchCode: fullRiskModelBase.branchCode,
      datasetId,
      qaType: datasetEntity.qaType,
      datasetSize: datasetSize,
      taskType,
      createUserId: user.userId,
    });
  }

  /**
   *  TODO 这里考虑把 task 按照 modelId 或者 modelBranchCode 来分组再展示，默认用 modelBranchCode 分组
   *  按照 branchCode 分组的时候， 需要用 modelBranchCode 来找到主分组 branchTier=1 的name ， 用作分组的名字， qaTask对应的用 modelId 关联展示即可
   * @param data
   * @param user
   * @returns
   */
  async search(data: SearchQaTaskRequest, user: PlatformUser) {
    const { pageSize, pageIndex, taskName, qaType, baseModelId, modelBranchCode } = data;

    const queryBuilder = this.qaTaskRepo.createQueryBuilder('qa_task').leftJoinAndSelect('qa_task.qaDatasetEntity', 'qaDatasetEntity');
    // .leftJoinAndSelect('qa_task.testRiskModelEntity', 'testRiskModelEntity')
    // .leftJoinAndSelect('qa_task.baseRiskModelEntity', 'baseRiskModelEntity');
    queryBuilder.where('qa_task.orgId = :orgId', { orgId: user.currentOrg });
    queryBuilder.andWhere('qa_task.product = :product', { product: user.currentProduct });
    if (taskName) {
      queryBuilder.andWhere('qa_task.taskName like :taskName', { taskName: `%${taskName}%` });
    }
    if (qaType) {
      queryBuilder.andWhere('qa_task.qaType = :qaType', { qaType });
    }
    if (baseModelId) {
      queryBuilder.andWhere('qa_task.baseModelId = :baseModelId', { baseModelId });
    }
    if (modelBranchCode) {
      queryBuilder.andWhere('qa_task.modelBranchCode = :modelBranchCode', { modelBranchCode });
    }
    queryBuilder.skip(pageSize * (pageIndex - 1));
    queryBuilder.take(pageSize).orderBy('qa_task.id', 'DESC');
    const [tasks, total] = await queryBuilder.getManyAndCount();
    const riskModels = await this.riskModelRepo.find({
      where: {
        modelId: In(Array.from(new Set([...tasks.map((t) => t.baseModelId)]))),
      },
    });
    const pagination = new SearchQaTaskResponse();
    pagination.data = tasks.map((t) => {
      const baseModel = riskModels.find((m) => m.modelId === t.baseModelId);
      t.baseRiskModelEntity = baseModel;
      return t;
    });
    pagination.total = total;
    pagination.pageIndex = pageIndex;
    pagination.pageSize = pageSize;
    return pagination;
  }

  async startTask(taskId: number, user: PlatformUser, retryMsg?: string) {
    this.logger.info(`startTask: taskId=${taskId}, user=${JSON.stringify(user)}, retryMsg=${retryMsg}`);
    const task = await this.qaTaskRepo.findOne({
      where: {
        id: taskId,
        orgId: user.currentOrg,
        product: user.currentProduct,
      },
      relations: ['qaDatasetEntity'],
    });
    if (!task) {
      throw new BadRequestException('任务不存在');
    }
    const toUpdate: any = {
      status: BatchStatusEnums.Processing,
    };
    if (retryMsg) {
      toUpdate.comment = task.comment + '\n' + retryMsg;
      toUpdate.error = '';
    }
    const monitorIntervalString = task.monitorIntervalString;
    let intervalUnit: MonitorIntervalUnitType = 'year';
    let intervalValue = 3;
    if (monitorIntervalString) {
      // 使用正则表达式匹配时间单位和数值
      const match = monitorIntervalString.match(/(year|month|week|day):(\d+)/);
      if (match) {
        intervalUnit = match[1] as MonitorIntervalUnitType;
        intervalValue = parseInt(match[2], 10);
      }
      if (intervalUnit || !intervalValue) {
        intervalUnit = 'year';
        intervalValue = 3;
      }
    }

    if (task.qaType === QaTypeEnums.Monitor && task.qaDatasetEntity?.refMonitorGroupId) {
      const batchEntity = await this.monitorJobService.startBatchForGroup(
        {
          orgId: user.currentOrg,
          product: user.currentProduct,
          monitorGroupId: task.qaDatasetEntity.refMonitorGroupId,
          currentUser: user,
          interval: intervalValue,
          intervalUnit,
        },
        [task.baseModelId],
        task.id,
      );
      toUpdate.refDiligenceBatchIdBase = batchEntity.batchId;
    } else {
      //  创建一个batch 来执行批量尽调/风险监控（本质也是尽调）
      const batchEntities = await this.createBatchForQaTask(task, user, intervalUnit, intervalValue);
      toUpdate.refDiligenceBatchIdBase = batchEntities[0]?.batchId;
      // toUpdate.refDiligenceBatchId = batchEntities[1]?.batchId;
    }

    await this.qaTaskRepo.update(task.id, toUpdate);
  }

  async getTaskProgress(taskId: number): Promise<BatchStatisticsBasePO> {
    const task = await this.qaTaskRepo.findOne({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      throw new BadRequestException('任务不存在');
    }
    const batchEntity = await this.batchRepo.findOne({
      where: {
        batchId: task.refDiligenceBatchIdBase,
      },
    });
    if (!batchEntity) {
      throw new BadRequestException('任务对应的batch不存在');
    }
    return batchEntity.statisticsInfo;
  }

  async retryTask(taskId: number, user: PlatformUser) {
    this.logger.info(`retryTask: taskId=${taskId}, user=${JSON.stringify(user)}`);
    await this.clearTaskData(taskId);
    await this.startTask(taskId, user, `retry task at ${new Date()}`);
  }

  async analyzeTask(taskId: number, taskEntity?: QaTaskEntity) {
    let task: QaTaskEntity = taskEntity;
    if (!task) {
      task = await this.qaTaskRepo.findOne({
        where: {
          id: taskId,
        },
        relations: ['qaDatasetEntity'],
      });
      if (!task) {
        this.logger.warn(`taskId: ${taskId} not found`);
        return;
      }
    }
    await Bluebird.all([this.qaTaskResultRepo.delete({ taskId }), this.qaTaskResultAnalyzedRepo.delete({ taskId })]);
    await this.qaTaskAnalyzerService.analyze(task);
    await this.qaTaskResultService.analyzeMetricAndStrategy(taskId, task.baseModelId);
    await this.qaTaskResultService.analyzeCompany(taskId, task.baseModelId);
  }

  async createBatchForQaTask(task: QaTaskEntity, user: PlatformUser, internalUnit: MonitorIntervalUnitType, internalValue: number) {
    const { datasetId, baseModelId } = task;
    // batchInfo 中 去掉 monitorGroupID， 这样持续尽调结束之后就不会尝试去分析动态，这里QA 任务，不需要关心动态级别，只需要测试 指标是否能命中
    const batchInfo: BatchInfoPO = {
      asyncBatch: 1, // 标记为异步启动批次，需要后续手动启动
      orgModelIds: [baseModelId],
      cacheHours: 0,
      currentUser: user,
      batchStartTime: moment().subtract(internalValue, internalUnit).unix(), // 设置动态时间区间范围
      batchEndTime: moment().unix(),
      refQaTaskId: task.id,
      batchComment: `QA测试任务: ${task.taskName} - baseModelId=${baseModelId}`,
    };
    this.logger.info(`createBatchForQaTask : taskId=${task.id}, batchInfo=${JSON.stringify(batchInfo)}`);
    const qaType = task.qaType;
    const batchEntities: BatchEntity[] = [];
    const batchEntityBase: BatchEntity = await this.batchCreatorHelperService.createBatchEntity(
      user,
      {
        succeedItems: [],
        failedItems: [],
      },
      qaType == QaTypeEnums.Diligence ? BatchBusinessTypeEnums.Diligence_ID : BatchBusinessTypeEnums.Diligence_Continuous, //风险监控(持续尽调)
      '',
      '',
      qaType == QaTypeEnums.Diligence ? BatchTypeEnums.Diligence : BatchTypeEnums.ContinuousDiligence, //持续尽调
      batchInfo,
      true,
    );
    this.logger.info(`batch(batchId=${batchEntityBase.batchId}) created for monitor...`);
    batchEntities.push(batchEntityBase);

    try {
      const pageSize = 500;
      let pageIndex = 1;
      let fetched = 0;
      do {
        const datasetItems = await this.qaDatasetItemRepo.find({
          where: {
            datasetId,
          },
          relations: ['qaCompanyEntity'],
          skip: (pageIndex - 1) * pageSize,
          take: pageSize,
        });
        fetched += datasetItems.length;
        if (datasetItems.length === 0) {
          break;
        }
        const toAddItems: ParsedRecordBase[] = datasetItems.map((i) => {
          return {
            companyName: i.qaCompanyEntity.name,
            companyId: i.qaCompanyEntity.companyId,
          };
        });
        for (const batchEntity of batchEntities) {
          await this.batchCreatorHelperService.upsertJobsToBatch(toAddItems, batchEntity);
        }
        if (datasetItems.length < pageSize) {
          break;
        }
        pageIndex++;
      } while (true);

      for (const batchEntity of batchEntities) {
        this.logger.info(`scan ${fetched} jobs and add to batch(batchId=${batchEntity.batchId})`);
        const messageResponse = await this.batchBaseHelperService.sendBatchMonitorMessage(batchEntity, user);
        this.logger.info(`batch(batchId=${batchEntity.batchId}) monitor message sent..., response=${messageResponse}`);
      }
    } catch (e) {
      this.logger.error(e);
      for (const batchEntity of batchEntities) {
        await this.batchCreatorHelperService.changeBatchStatusBeforeStart(
          batchEntity.batchId,
          BatchStatusEnums.Error,
          `createBatchForQaTask() 报错: ${e.message?.substr(0, 200)}`,
        );
      }
    }
    return batchEntities;
  }

  /**
   * 当任务关联的batch执行完成时，会调用该方法来更新task的下一步动作
   * @param taskId
   * @returns
   */
  async onTaskFinished(taskId: number, batchEntity?: BatchEntity) {
    try {
      const task: QaTaskEntity = await this.qaTaskRepo.findOne({
        where: {
          id: taskId,
        },
        relations: ['qaDatasetEntity'],
      });
      if (!task) {
        this.logger.warn(`taskId: ${taskId} not found`);
        return;
      }

      await this.analyzeTask(taskId, task);
      await this.qaTaskRepo
        .createQueryBuilder()
        .update(QaTaskEntity)
        .set({
          finishedBatchCount: () => `finished_batch_count + 1`,
          status: BatchStatusEnums.Done,
        })
        .where('id = :id', { id: taskId })
        .execute();
    } catch (error) {
      this.logger.error(`onTaskFinished error: ${error}`);
      throw error;
    }
  }

  async onTaskError(taskId: number, error: string) {
    try {
      const task = await this.qaTaskRepo.findOne({
        where: {
          id: taskId,
        },
      });
      if (!task) {
        this.logger.warn(`taskId: ${taskId} not found`);
        return;
      }
      task.status = BatchStatusEnums.Error;
      task.error = `任务执行失败: ${error}`;
      await this.qaTaskRepo.save(task);
    } catch (error) {
      this.logger.error(`onTaskError error: ${error}`);
    }
  }

  private async clearTaskData(taskId: number) {
    this.logger.info(`clearTaskData: taskId=${taskId}`);
    const task = await this.qaTaskRepo.findOne({
      where: {
        id: taskId,
      },
    });
    if (!task) {
      return;
    }
    const batchIds = [];
    if (task.refDiligenceBatchId) {
      batchIds.push(task.refDiligenceBatchId);
    }
    if (task.refDiligenceBatchIdBase) {
      batchIds.push(task.refDiligenceBatchIdBase);
    }
    if (batchIds.length > 0) {
      await Bluebird.map(batchIds, async (batchId) => {
        await this.batchRepo.delete(batchId);
        await this.batchDiligenceRepo.delete(batchId);
      });
    }
    await Bluebird.all([this.qaTaskResultRepo.delete({ taskId }), this.qaTaskResultAnalyzedRepo.delete({ taskId })]);
  }

  async deleteTask(taskId: number, user: PlatformUser) {
    await this.clearTaskData(taskId);
    await this.qaTaskRepo.delete(taskId);
  }
}
