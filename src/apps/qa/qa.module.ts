import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BatchModule } from 'apps/batch/batch.module';
import { CompanySearchModule } from 'apps/company/company-search.module';
import { MonitorModule } from 'apps/monitor/monitor.module';
import { DimensionHitStrategyEntity } from 'libs/entities/DimensionHitStrategyEntity';
import { MonitorGroupEntity } from 'libs/entities/MonitorGroupEntity';
import { QaCompanyAnnotatedTestEntity } from 'libs/entities/QaComapnyAnnotatedTestEntity';
import { QaCompanyLabelEntity } from 'libs/entities/QaComapnyLabelEntity';
import { QaCompanyEntity } from 'libs/entities/QaCompanyEntity';
import { QaDatasetEntity } from 'libs/entities/QaDatasetEntity';
import { QaDatasetItemEntity } from 'libs/entities/QaDatasetItemEntity';
import { QaLabelEntity } from 'libs/entities/QaLabelEntity';
import { QaTaskEntity } from 'libs/entities/QaTaskEntity';
import { QaTaskResultEntity } from 'libs/entities/QaTaskResultEntity';
import { DatasetService } from './dataset.service';
import { QaCompanyService } from './qa.company.service';
import { QaController } from './qa.controller';
import { QaLabelService } from './qa.label.service';
import { QaTaskAnalyzerService } from './qa.task.analyzer.service';
import { QaTaskService } from './qa.task.service';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { QaTaskResultService } from './qa.task.result.service';
import { QaTaskResultAnalyzedEntity } from 'libs/entities/QaTaskResultAnalyzedEntity';
import { RiskModelEntity } from 'libs/entities/RiskModelEntity';
import { MonitorCompanyEntity } from 'libs/entities/MonitorCompanyEntity';
import MyOssService from '../basic/my-oss.service';
import { RiskModelModule } from '../risk_model/risk_model.module';
import { QaExportAnalysisService } from './qa.export.analysis.service';

@Module({
  controllers: [QaController],
  providers: [
    MyOssService,
    DatasetService,
    QaTaskService,
    QaCompanyService,
    QaLabelService,
    QaTaskAnalyzerService,
    QaTaskResultService,
    QaExportAnalysisService,
  ],
  exports: [QaTaskService, QaExportAnalysisService],
  imports: [
    RiskModelModule,
    TypeOrmModule.forFeature([
      QaDatasetEntity,
      QaDatasetItemEntity,
      QaCompanyEntity,
      QaTaskEntity,
      QaTaskResultEntity,
      QaCompanyLabelEntity,
      QaLabelEntity,
      MonitorGroupEntity,
      QaCompanyAnnotatedTestEntity,
      DimensionHitStrategyEntity,
      BatchEntity,
      BatchDiligenceEntity,
      QaTaskResultAnalyzedEntity,
      RiskModelEntity,
      MonitorCompanyEntity,
    ]),
    CompanySearchModule,
    forwardRef(() => MonitorModule),
    forwardRef(() => BatchModule),
  ],
})
export class QaModule {}
