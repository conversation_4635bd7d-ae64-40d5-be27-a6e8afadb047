import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { EntityManager } from 'typeorm';
import { PlatformUser } from '../../libs/model/common';
import { MonitorCompanyEntity } from '../../libs/entities/MonitorCompanyEntity';
import * as Bluebird from 'bluebird';
import { MonitorMetricsDynamicEntity } from '../../libs/entities/MonitorMetricsDynamicEntity';
import { PushRuleEntity } from '../../libs/entities/PushRuleEntity';
import { MonitorDynamicsRemarkEntity } from '../../libs/entities/MonitorDynamicsRemarkEntity';
import { ProductCodeEnums } from '../../libs/enums/ProductCodeEnums';
import { DataStatusEnums } from '../../libs/enums/DataStatusEnums';
import { MonitorStatusEnums } from '../../libs/enums/monitor/MonitorStatusEnums';
import { v4 as uuidv4 } from 'uuid';
import { DistributedSystemResourceEntity } from '../../libs/entities/DistributedSystemResourceEntity';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorCompanyRelatedDailyEntity } from '../../libs/entities/MonitorCompanyRelatedDailyEntity';

export const generateMonitorGroupTestData = async (entityManager: EntityManager, testUser: PlatformUser, riskModuleId: number): Promise<MonitorGroupEntity> => {
  // 创建监控分组
  const testMonitorGroup = new MonitorGroupEntity();
  testMonitorGroup.orgId = testUser.currentOrg;
  testMonitorGroup.product = ProductCodeEnums.Pro;
  testMonitorGroup.ownerId = testUser.userId;
  testMonitorGroup.name = `测试监控分组_${testUser.currentOrg}`;
  testMonitorGroup.status = DataStatusEnums.Enabled;
  (testMonitorGroup.monitorStatus = MonitorStatusEnums.Enabled), (testMonitorGroup.monitorModelId = riskModuleId);
  await entityManager.save(testMonitorGroup);

  // 创建监控企业
  const companyInfos: any[] = [
    {
      companyId: 'a5a0ba522ce994fb2a8de3a7625534e1',
      companyName: '华谊兄弟传媒股份有限公司',
    },
    {
      companyId: '498d440b7591af14aa609be377ecccb6',
      companyName: '宜昌聚辉产业投资有限公司',
    },
    {
      companyId: '913025f9a9b228bdb1c69c423a9e66a2',
      companyName: '长春润德投资控股集团有限公司',
    },
    {
      companyId: 'cbb8949fd9adacfb0583186c85f28aa6',
      companyName: '喀什农村商业银行股份有限公司',
    },
    {
      companyId: '5ddd9ed23e087a7089884efc1cbf1e22',
      companyName: '江西冠岳商业管理有限公司',
    },
    {
      companyId: '1ff5ae07910e2eb0fcba855a5e62b576',
      companyName: '湖南五凌电力新能源有限公司',
    },
  ];
  const dbInserts = companyInfos.map((info) => {
    return Object.assign(new MonitorCompanyEntity(), {
      orgId: testUser.currentOrg,
      product: ProductCodeEnums.Pro,
      createBy: testUser.userId,
      monitorGroupId: testMonitorGroup.monitorGroupId,
      companyId: info.companyId,
      companyName: info.companyName,
      relatedDynamicHashKey: `TEST_${uuidv4()}`,
      riskLevel: null,
    });
  });
  await entityManager.save(dbInserts);
  return testMonitorGroup;
};

export const clearMonitorGroupTestData = async (entityManager: EntityManager, testUser: PlatformUser): Promise<void> => {
  // 清除分组数据
  await entityManager.delete(MonitorGroupEntity, { orgId: testUser.currentOrg });
  // 清除企业数据
  await entityManager.delete(MonitorCompanyEntity, { orgId: testUser.currentOrg });
};

export const clearMonitorTestData = async (entityManager: EntityManager, testUser: PlatformUser): Promise<void> => {
  // const monitorGroups = await entityManager.find(MonitorGroupEntity, { orgId: testUser.currentOrg });
  await Bluebird.all([
    entityManager.delete(MonitorCompanyEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(MonitorMetricsDynamicEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(MonitorGroupEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(PushRuleEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(MonitorDynamicsRemarkEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(DistributedSystemResourceEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(RiskModelEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(MonitorCompanyRelatedPartyEntity, { orgId: testUser.currentOrg }),
    entityManager.delete(MonitorCompanyRelatedDailyEntity, { orgId: testUser.currentOrg }),
  ]);
};

export const removeRandomElements = (array, count) => {
  const result = [...array]; // 创建数组的副本，以避免修改原数组
  for (let i = 0; i < count; i++) {
    if (result.length === 0) break; // 如果数组为空，停止操作
    const randomIndex = Math.floor(Math.random() * result.length); // 生成随机索引
    result.splice(randomIndex, 1); // 移除随机索引处的元素
  }
  return result;
};
