import { cloneDeep, flatMap, pick } from 'lodash';
import { BaseDimensionDefinitionForGenerating } from '../../libs/constants/dimension.constants';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from '../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionDefinitionEntity } from '../../libs/entities/DimensionDefinitionEntity';
import { DimensionHitStrategyFieldsEntity } from '../../libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import { EntityManager } from 'typeorm';
import { DimensionFieldsEntity } from '../../libs/entities/DimensionFieldsEntity';
import * as Bluebird from 'bluebird';
import { DimensionHitStrategyEntity } from '../../libs/entities/DimensionHitStrategyEntity';
import { DimensionFieldSearchTypeEnums } from '../../libs/enums/dimension/DimensionFieldSearchTypeEnums';
import { DataStatusEnums } from '../../libs/enums/DataStatusEnums';
import { MetricsEntity } from '../../libs/entities/MetricsEntity';
import { MetricTypeEnums } from '../../libs/enums/metric/MetricTypeEnums';
import { ProductCodeEnums } from '../../libs/enums/ProductCodeEnums';
import { DimensionRiskLevelEnum } from '../../libs/enums/diligence/DimensionRiskLevelEnum';
import { GroupMetricRelationEntity } from '../../libs/entities/GroupMetricRelationEntity';
import { GroupEntity } from '../../libs/entities/GroupEntity';
import { QueryHitStrategyPO } from '../../libs/model/QueryHitStrategyPO';
import { PlatformUser } from '../../client';
import { MetricDimensionRelationEntity } from '../../libs/entities/MetricDimensionRelationEntity';
import { StrategyRoleEnums } from '../../libs/enums/StrategyRoleEnums';
import { DynamicSettingPO } from '../../libs/model/DynamicSettingPO';
import { MetricDynamicStrategy } from '../../libs/model/metric/MetricDynamicStrategy';
import { ScoreStrategyEnums } from '../../libs/enums/ScoreStrategyEnums';

export class DimFieldOption {
  fieldKey: DimensionFieldKeyEnums;
  fieldValue: any;
  compareType?: DimensionFieldCompareTypeEnums;
  /** 用户端对filed访问权限：0-完整权限; 1-用户不可见不可修改; 2-用户可见不可修改; 3-仅用于展示策略名称描述; 4-后端该属性不参与结果返回(companyDetail数据源) */
  accessScope? = 0;
  options?: Array<any>;
}

/**
 * 根据维度key，构造一个用于排查的维度PO，并设置一些默认值
 * @param dimKey
 * @param values
 * @returns
 */
export const getDimensionHitStrategyPO = (dimKey: DimensionTypeEnums, values: DimFieldOption[] = []) => {
  const baseDim = cloneDeep(BaseDimensionDefinitionForGenerating[dimKey]);

  const dimHitStrage = new DimensionHitStrategyPO(
    Object.assign(
      new DimensionDefinitionEntity(),
      pick(baseDim, ['name', 'key', 'source', 'template', 'typeCode', 'sourcePath', 'detailSource', 'detailSourcePath']),
    ),
  );

  let fieldId = 1000;
  const mustfieldHitStrategyIds = [];

  if (values?.length) {
    dimHitStrage.strategyFields = values.map((field) => {
      fieldId++;
      const dimBase = baseDim?.dimensionFields?.find((f) => f.fieldKey == field.fieldKey);
      mustfieldHitStrategyIds.push(fieldId);
      return Object.assign(new DimensionHitStrategyFieldsEntity(), {
        id: fieldId,
        fieldValue: field.fieldValue,
        compareType: field?.compareType || dimBase.defaultCompareType,
        dimensionFieldKey: field.fieldKey,
        dimensionFieldName: dimBase?.fieldName,
        options: field?.options || dimBase?.options,
        accessScope: field?.accessScope,
        // dimensionField: pick(field, ['fieldName', 'fieldKey', 'inputType', 'dataType', 'isArray', 'status']),
      });
    });
  } else {
    dimHitStrage.strategyFields = baseDim?.dimensionFields?.map((field) => {
      fieldId++;
      mustfieldHitStrategyIds.push(fieldId);
      return Object.assign(new DimensionHitStrategyFieldsEntity(), {
        id: fieldId,
        fieldValue: field.defaultValue,
        compareType: field.defaultCompareType,
        dimensionFieldKey: field.fieldKey,
        dimensionFieldName: field.fieldName,
        options: field?.options,
        accessScope: undefined,
        // dimensionField: pick(field, ['fieldName', 'fieldKey', 'inputType', 'dataType', 'isArray', 'status']),
      });
    });
  }
  dimHitStrage.strategyId = 1;
  dimHitStrage.fieldHitStrategy = Object.assign(new QueryHitStrategyPO(), { must: mustfieldHitStrategyIds });

  return dimHitStrage;
};

export class StrategyDimsFieldsOption {
  strategyRole?: StrategyRoleEnums;
  dimStrategyName?: string;
  comment: string;
  dimKey: DimensionTypeEnums;
  fields: DimFieldOption[];
  dynamicSetting?: DynamicSettingPO;
}

export class MetricHitStrategy {
  dimsFields: StrategyDimsFieldsOption[];
  maxScore: number;
  riskLevel: DimensionRiskLevelEnum;
  operation? = 'must';
  order? = 0;
}

/**
 *  初始化维度，维度属性
 * @param entityManger
 * @param testUser
 */
export const createDimensionFields = async (entityManger: EntityManager, testUser: PlatformUser) => {
  const { userId: createBy } = testUser;
  const allDim = Object.keys(BaseDimensionDefinitionForGenerating);
  await Bluebird.map(allDim, async (dimKey: any) => {
    const baseDim = BaseDimensionDefinitionForGenerating[dimKey];
    // 1. 查找维度
    let dimension = await entityManger.findOne(DimensionDefinitionEntity, {
      where: {
        key: dimKey,
      },
    });
    if (!dimension) {
      // 查询不到直接创建维度
      dimension = await entityManger.save(DimensionDefinitionEntity, {
        ...pick(baseDim, ['name', 'key', 'source', 'template', 'typeCode', 'sourcePath', 'detailSource', 'detailSourcePath']),
        createBy,
      });
    }
    // 2 创建维度字段
    if (baseDim?.dimensionFields?.length) {
      await Bluebird.map(baseDim.dimensionFields, async (baseField: any) => {
        // 3，查询数据库中存在的维度字段
        const dimensionField = await entityManger.findOne(DimensionFieldsEntity, {
          dimensionId: dimension.dimensionId,
          fieldKey: baseField.fieldKey,
        });
        if (!dimensionField) {
          // 未查到直接创建维度字段
          await entityManger.insert(DimensionFieldsEntity, {
            ...pick(baseField, ['fieldName', 'fieldKey', 'inputType', 'dataType', 'isArray', 'fieldOrder', 'comment', 'options']),
            dimensionId: dimension.dimensionId,
          });
        }
      });
    }
  });
};

/*
 *
 * @param entityManger
 * @param testUser
 * @param groupId 指标分组
 * @param metricName 指标名称
 * @param metricComment 指标说明
 * @param order 指标排序
 * @param metricHitStrategies  指标的命中策略组
 * @param metricType  指标类别
 * @param dynamicStrategy  用作监控指标的时候，生成指标动态的策略
 * @param scoreStrategy  指标取分数最高，还是风险等级最高
 * @returns
 */
export const createMetric = async (
  entityManger: EntityManager,
  testUser: PlatformUser,
  groupId: number,
  metricName: string,
  metricComment: string,
  order: number,
  metricHitStrategies: MetricHitStrategy[],
  metricType?: MetricTypeEnums,
  dynamicStrategy?: MetricDynamicStrategy,
  scoreStrategy?: ScoreStrategyEnums,
) => {
  const { userId: createBy, currentOrg: orgId } = testUser;

  const hitStrategy = [];
  await Bluebird.map(
    metricHitStrategies,
    async (metricHitStrategy: MetricHitStrategy) => {
      const { dimsFields, maxScore, riskLevel, order } = metricHitStrategy;
      const operation = metricHitStrategy?.operation || 'must';
      const dimensionStrategyIds = [];
      if (!dimsFields?.length) {
        return console.error('dimsFields 为空！');
      }
      await Bluebird.map(
        dimsFields,
        async (dimAndField: StrategyDimsFieldsOption) => {
          const { dimKey, fields, dimStrategyName, comment, strategyRole } = dimAndField;
          const baseDim = BaseDimensionDefinitionForGenerating[dimKey];
          if (!baseDim) {
            return console.log('baseDim 为空！');
          }
          // 1. 查找维度
          let dimension = await entityManger.findOne(DimensionDefinitionEntity, { key: dimKey });
          if (!dimension) {
            // 查询不到直接创建
            dimension = await entityManger.save(DimensionDefinitionEntity, {
              ...pick(baseDim, ['name', 'key', 'source', 'template', 'typeCode', 'sourcePath', 'detailSource', 'detailSourcePath']),
              createBy,
            });
          }
          // 2 创建维度命中策略
          const dimensionStrategy = await entityManger.save(DimensionHitStrategyEntity, {
            dimensionId: dimension.dimensionId,
            strategyName: dimStrategyName || dimension.name,
            comment: comment,
            status: DataStatusEnums.Developing,
            orgId,
            createBy,
            template: baseDim.template,
            strategyRole: strategyRole || StrategyRoleEnums.Normal,
            // hitStrategy: {},
            fieldHitStrategy: { must: [] },
          });
          const hitStrategyFieldIds = [];

          if (baseDim?.dimensionFields?.length) {
            await Bluebird.map(
              baseDim.dimensionFields,
              async (baseField: any) => {
                const field = fields?.find((f) => f.fieldKey == baseField.fieldKey);

                if (field) {
                  // 3，查询数据库中存在的维度字段
                  let dimensionField = await entityManger.findOne(DimensionFieldsEntity, {
                    dimensionId: dimension.dimensionId,
                    fieldKey: field.fieldKey,
                  });
                  if (!dimensionField) {
                    // 未查到直接创建维度字段
                    dimensionField = await entityManger.save(DimensionFieldsEntity, {
                      ...pick(baseField, ['fieldName', 'fieldKey', 'inputType', 'dataType', 'isArray', 'fieldOrder', 'comment', 'options']),
                      dimensionId: dimension.dimensionId,
                    });
                  }
                  // 4 命中策略绑定字段
                  const hitStrategyField = await entityManger.save(DimensionHitStrategyFieldsEntity, {
                    comment: baseField.comment,
                    strategyId: dimensionStrategy.strategyId,
                    dimensionId: dimension.dimensionId,
                    dimensionFieldId: dimensionField.fieldId,
                    dimensionFieldKey: dimensionField.fieldKey,
                    dimensionFieldName: dimensionField.fieldName,
                    options: field?.options ? field?.options : dimensionField.options,
                    accessScope: field.accessScope,
                    createBy,
                    fieldValue: field.fieldValue,
                    compareType: field.compareType,
                    searchType: DimensionFieldSearchTypeEnums.General,
                    status: DataStatusEnums.Developing,
                    orgId,
                  });
                  if (dimensionField.fieldKey !== DimensionFieldKeyEnums.sortField) {
                    hitStrategyFieldIds.push(hitStrategyField.id);
                  }

                  return hitStrategyField.id;
                }
              },
              { concurrency: 1 },
            );
          }
          // 5 维度命中策略的 字段命中逻辑
          await entityManger.update(DimensionHitStrategyEntity, dimensionStrategy.strategyId, {
            fieldHitStrategy: { must: hitStrategyFieldIds, minimum_should_match: 1 },
          });

          dimensionStrategyIds.push(dimensionStrategy.strategyId);
        },
        { concurrency: 1 },
      );
      hitStrategy.push({
        [operation]: dimensionStrategyIds,
        minimum_should_match: 1,
        status: DataStatusEnums.Enabled,
        order,
        scoreSettings: {
          maxScore,
          riskLevel,
        },
      });
    },
    { concurrency: 1 },
  );

  // 6 创建指标 给指标绑定 维度命中策略
  let detailsJson = {};
  if (dynamicStrategy) {
    detailsJson = Object.assign(detailsJson, { dynamicStrategy });
  }
  if (scoreStrategy) {
    detailsJson = Object.assign(detailsJson, { scoreStrategy });
  }
  const metric = await entityManger.save(MetricsEntity, {
    name: metricName,
    orgId,
    isVeto: 0,
    comment: metricComment,
    metricType: metricType || MetricTypeEnums.Simple,
    productCode: ProductCodeEnums.Pro,
    createBy,
    hitStrategy,
    detailsJson,
    status: DataStatusEnums.Developing,
  });

  const metricDimensionRelationEntities = flatMap(
    hitStrategy.map((hs) => {
      const res = [];
      if (hs?.must) {
        res.push(...hs.must);
      }
      if (hs?.should) {
        res.push(...hs.should);
      }
      if (hs?.must_not) {
        res.push(...hs.must_not);
      }
      return res;
    }),
  ).map((strategyId) => {
    return Object.assign(new MetricDimensionRelationEntity(), {
      metricsId: metric.metricsId,
      dimensionStrategyId: strategyId,
      priority: 1,
      isPreCondition: 1,
      order: 0,
      createBy,
    });
  });
  // 给指标绑定 维度命中策略
  await entityManger.save(MetricDimensionRelationEntity, metricDimensionRelationEntities);

  // 指标添加到分组
  await entityManger.save(GroupMetricRelationEntity, { groupId, metricsId: metric.metricsId, order });
  return metric;
};

/**
 *
 * @param entityManger
 * @param modelId
 * @param groupName
 * @param order
 * @returns
 */
export const createGroup = async (entityManger: EntityManager, testUser: PlatformUser, modelId: number, groupName: string, order: number) => {
  const { userId: createBy, currentOrg: orgId, currentProduct: productCode } = testUser;
  const group = await entityManger.save(GroupEntity, {
    groupName: groupName,
    productCode: productCode,
    comment: groupName,
    createBy,
    status: DataStatusEnums.Enabled,
    riskLevel: DimensionRiskLevelEnum.High,
    orgId,
    modelId,
    order,
  });

  return group;
};
