import { Test } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { AppTestModule } from '../../app/app.test.module';
import { DataModule } from '../data.module';
import { PersonHelper } from './person.helper';

jest.setTimeout(300000);
describe('debug person helper', () => {
  let personHelper: PersonHelper;
  let entityManager: EntityManager;
  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    personHelper = module.get<PersonHelper>(PersonHelper);
    entityManager = getManager();
  });
  afterAll(async () => {
    await entityManager.connection.close();
  });

  it('debug getFinalActualController 实控人', async () => {
    //深聪半导体（江苏）有限公司Names数组中有多个Names
    const response = await personHelper.getFinalActualController('ab2565763606171706df11d993cbec40', false);
    expect(response.length).toEqual(2);
    //宁夏中科生物科技股份有限公司实控人无keyNo
    const response2 = await personHelper.getFinalActualController('35710dfd5ed7d2046270a643246e97f1', false);
    expect(response2.length).toEqual(0);

    //江西小黑小蜜食品有限责任公司Name.Names.PersonList有多个
    const response3 = await personHelper.getFinalActualController('00d4cfde27955a817bd8e85421a7cc0d', false);
    expect(response3.length).toEqual(7);

    //小米科技有限责任公司
    const response4 = await personHelper.getFinalActualController('9cce0780ab7644008b73bc2120479d31', false);
    expect(response4.length).toEqual(1);
  });

  it('debug getPartnerList 股东列表', async () => {
    // {
    //   history: false,
    //   name: "杭州宝群实业集团有限公司",
    //   job: "股东",
    //   keyNo: "f494f6aaea0bac21132bcffe824ab9f5",
    //   tags: [
    //     "有股权质押",
    //     "大股东",
    //   ],
    //   stockPercent: "33.89%",
    //   shouldCapi: "151,589,199",
    //   shoudDate: null,
    // }

    // {
    //   history: false,
    //   name: "鲍正梁",
    //   job: "股东",
    //   keyNo: "pr1950bc35eb148bd2f902e664987d8a",
    //   tags: [
    //     "有股权质押",
    //   ],
    //   stockPercent: "3.13%",
    //   shouldCapi: "13,981,207",
    //   shoudDate: null,
    // }

    //通策医疗股份有限公司
    const response = await personHelper.getPartnerList('d8a0f15ef7e585ea2429cf92ba8ee290', 'all');
    expect(response).toBeDefined();
    //锐奇控股股份有限公司
    const response2 = await personHelper.getPartnerList('e938cdc76291644fa11b83759156e5c8', 'all');
    expect(response2).toBeDefined();
  });
});
