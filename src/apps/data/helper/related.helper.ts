import { Injectable } from '@nestjs/common';
import { PersonHelper } from './person.helper';
import { CompanyDetailService } from '../../company/company-detail.service';
import { CompanySearchService } from '../../company/company-search.service';
import { RelatedPartyGroupPO } from '../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { relatedTypeAnnotations, RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';

@Injectable()
/**
 * 企业关联方
 */
export class RelatedHelper {
  constructor(
    private readonly personHelper: PersonHelper,
    private readonly companyDetailService: CompanyDetailService,
    private readonly companySearchService: CompanySearchService,
  ) {}

  /**
   * 获取对外投资企业中持股比例大于50%的企业
   * @param companyId
   * @param maxCount
   * @param relatedType
   * @returns
   */
  private async getInvestCompany(companyId: string, maxCount = 50, relatedType: RelatedTypeEnums): Promise<RelatedPartyGroupPO[]> {
    const holdingList: RelatedPartyGroupPO[] = [];
    const pageSize = 100;
    let pageIndex = 1;
    let hasMore = false;
    let holdingCompanyCount = 0;
    const relatedTypeDescList = relatedTypeAnnotations[relatedType];
    do {
      const holdingCompanyList = await this.companyDetailService.getInvestCompany(companyId, 0, [], pageSize, pageIndex, '50,100.00001');
      holdingCompanyCount = holdingCompanyList?.Paging?.TotalRecords || 0;
      holdingCompanyList?.Result?.forEach((n) => {
        if (!this.ignoreCheck(n.Name)) {
          holdingList.push(
            Object.assign(new RelatedPartyGroupPO(), {
              companyKeynoRelated: n.KeyNo,
              companyNameRelated: n.Name,
              shortStatus: n.Status,
              relatedTypeDescList: [relatedTypeDescList],
              relatedTypes: [relatedType],
              paidCapi: n.RegistCapi,
              // riskTypeDescList: [],
              // riskTypes: [],
              // vids: [],
              // contactList: [],
            }),
          );
        }
      });
      hasMore = holdingCompanyCount > pageIndex * pageSize;
      if (holdingList.length >= maxCount) {
        hasMore = false;
      }
      pageIndex++;
    } while (hasMore);
    return holdingList;
  }

  private async findMajorShareholders(companyId: string, depth = 0, maxDepth = 10): Promise<any[]> {
    // 防止无限递归
    if (depth >= maxDepth) {
      return [];
    }

    // 获取公司的股东列表
    const partnerList = await this.personHelper.getPartnerList(companyId, 'other', false, 200);
    // 找出大股东
    const majorShareholders = partnerList.filter((partner) => partner.tags?.includes('大股东'));
    if (!majorShareholders.length) {
      return [];
    }

    // 递归获取每个大股东的上层大股东
    const results = await Promise.all(
      majorShareholders.map(async (shareholder) => {
        const upperShareholders = await this.findMajorShareholders(shareholder.keyNo, depth + 1, maxDepth);
        return {
          companyId: shareholder.keyNo,
          companyName: shareholder.name,
          stockPercent: shareholder.stockPercent,
          shouldCapi: shareholder.shouldCapi,
          tags: shareholder.tags,
          upperShareholders: upperShareholders,
        };
      }),
    );

    return results;
  }

  // 打平股东树状结构为一维数组
  private flattenShareholders(shareholders: any[]): RelatedPartyGroupPO[] {
    const flattened: RelatedPartyGroupPO[] = [];
    const seen = new Set(); // 用于去重的 Set

    function flatten(shareholder) {
      if (!shareholder) return;

      // 如果已经处理过这个 companyId，则跳过
      if (seen.has(shareholder.companyId)) {
        return;
      }

      // 打平后，如果股东是国资委，则跳过
      if (shareholder.companyName.replace('）', '').endsWith('国有资产监督管理委员会')) {
        return [];
      }

      seen.add(shareholder.companyId);

      flattened.push(
        Object.assign(new RelatedPartyGroupPO(), {
          companyKeynoRelated: shareholder.companyId,
          companyNameRelated: shareholder.companyName,
          shortStatus: shareholder.ShortStatus,
          relatedTypeDescList: [relatedTypeAnnotations[RelatedTypeEnums.ShareholderChain]],
          relatedTypes: [RelatedTypeEnums.ShareholderChain],
          // paidCapi: shareholder.shouldCapi,
          // riskTypeDescList: [],
          // riskTypes: [],
          // vids: [],
          // contactList: [],
        }),
      );

      // flattened.push(pick(shareholder, ['companyId', 'companyName', 'stockPercent', 'shouldCapi', 'tags']));
      if (shareholder.upperShareholders?.length) {
        shareholder.upperShareholders.forEach(flatten);
      }
    }

    shareholders.forEach(flatten);
    return flattened;
  }

  private ignoreCheck(companyName: string): boolean {
    return companyName.includes('有限合伙') || companyName.includes('国有资产监督管理委员会') || companyName.includes('国有资产监督管理局');
  }

  /**
   * 添加公司列表并根据 companyId 去重, 并排除国有资产监督管理委员会和国有资产监督管理局 有效合伙企业
   * @param targetArray 目标数组
   * @param newCompanies 要添加的新公司列表
   */
  private pushUniqueCompanies(targetArray: RelatedPartyGroupPO[], newCompanies: RelatedPartyGroupPO[], ignoreCompanyId: string) {
    newCompanies.forEach((company) => {
      const exists = targetArray.some((existing) => existing.companyKeynoRelated === company.companyKeynoRelated);
      if (!exists && company.companyKeynoRelated !== ignoreCompanyId && !this.ignoreCheck(company.companyNameRelated)) {
        // 同时排除当前公司主体
        targetArray.push(company);
      }
    });

    return targetArray;
  }

  /**
   * sf关联方定义
     1 主体控制的企业（大于50%，穿透一层） 主体对外投资企业中持股比例超过50%; 子公司 MajorityInvestment
     3 实控人控制的企业（大于50%，穿透一层） 主体实控人对外投资企业中持股比例超过50%;  实控人子公司
     3.1 实控人穿透到全国各级国资委的（实控人穿透到最后关键词包含「国资委」的，不再列示他的实控人对外投资企业）
     4 实控路线（实控路线上的大股东企业，也就是母公司及祖母公司，直到实控人)

     国资实控关联方 = 实控路线 + 主体控制企业
     非国资实控关联方 =  实控路线 + 主体控制企业 + 实控人控制企业

     关联方取值规则，
     1、剔除 有限合伙 和 国有资产监督管理委员会、 国有资产监督管理局
     2、注册资本排序， 大中型只取前50， 小微前20
     3、取值优先级： 实际控制人 =》 控股股东链路 =》 主体控制企业 =》 实控人控制企业
   * @param companyId
   * @returns
   */
  async getIcbcSFRelateds(companyId: string): Promise<RelatedPartyGroupPO[]> {
    // 获取实控人
    const actualController = await this.personHelper.getFinalActualController(companyId);
    const hasGZW = actualController.some(
      (a) => a.name.replace('）', '').endsWith('国有资产监督管理委员会') || a.name.replace('）', '').endsWith('国有资产监督管理局'),
    );

    // 获取企业规模
    const companyDetail = await this.companySearchService.companyDetailsQcc(companyId);
    let relatedCompanyCount = 20;
    if (['大型企业', '中型企业'].includes(companyDetail?.Scale)) {
      relatedCompanyCount = 50;
    }
    /** 关联方企业 */
    const uniqueCompanies: RelatedPartyGroupPO[] = [];

    // 2 实控人(已废弃，关联方不包含实控人)， 实控人的指标都是通过主体实控人单独判断，不是通过关联方
    /* if (!hasGZW && actualController[0]?.keyNo) {
      // 实际控制人 加到关联方 
      this.pushUniqueCompanies(
        uniqueCompanies,
        [
          Object.assign(new RelatedPartyGroupPO(), {
            companyKeynoRelated: actualController[0].keyNo,
            companyNameRelated: actualController[0].name,
            shortStatus: '',
            relatedTypeDescList: [relatedTypeAnnotations[RelatedTypeEnums.ActualController]],
            relatedTypes: [RelatedTypeEnums.ActualController],
            // riskTypeDescList: [],
            // riskTypes: [],
            // vids: [],
            // contactList: [],
          }),

          // { companyKeynoRelated: actualController[0].keyNo, companyNameRelated: actualController[0].name }
        ],
        companyId,
      );
    }*/

    // 获取控股股东链路
    const shareholderChain = await this.findMajorShareholders(companyId);
    /** 实控路线企业 = 控股股东链路打平 + 剔除国资委 */
    const flattenedShareholders = this.flattenShareholders(shareholderChain);

    // 添加 控股股东链路打平
    this.pushUniqueCompanies(uniqueCompanies, flattenedShareholders, companyId);
    if (uniqueCompanies.length >= relatedCompanyCount) {
      return uniqueCompanies.slice(0, relatedCompanyCount);
    }

    // 主体控制企业
    const subjectHoldingList = await this.getInvestCompany(companyId, relatedCompanyCount, RelatedTypeEnums.ControlCompany);
    this.pushUniqueCompanies(uniqueCompanies, subjectHoldingList, companyId);
    if (uniqueCompanies.length >= relatedCompanyCount) {
      return uniqueCompanies.slice(0, relatedCompanyCount);
    }

    if (!hasGZW && actualController[0]?.keyNo) {
      // 企业为非国资实控
      // 关联方 = 实控路线 + 主体控制企业 + 实控人控制企业
      const acHoldingList = await this.getInvestCompany(actualController[0].keyNo, relatedCompanyCount, RelatedTypeEnums.ActualControllerControl);
      // 获取实控人控制企业
      this.pushUniqueCompanies(uniqueCompanies, acHoldingList, companyId);
      if (uniqueCompanies.length >= relatedCompanyCount) {
        return uniqueCompanies.slice(0, relatedCompanyCount);
      }
    }

    return uniqueCompanies;
  }
}
