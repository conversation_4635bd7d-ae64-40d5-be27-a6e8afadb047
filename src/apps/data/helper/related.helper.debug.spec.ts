import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../../app/app.test.module';
import { getDimensionHitStrategyPO } from '../../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DataModule } from '../data.module';
import * as Bluebird from 'bluebird';
import { RelatedHelper } from './related.helper';
import * as fs from 'fs';
import * as path from 'path';
import { pick } from 'lodash';
import { CompanySearchService } from '../../company/company-search.service';

jest.setTimeout(300000);
describe('单元测试-获取企业关联方', () => {
  let relatedHelper: RelatedHelper;
  let companySearchService: CompanySearchService;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    relatedHelper = module.get(RelatedHelper);
    companySearchService = module.get(CompanySearchService);
  });

  async function getCompanyList() {
    const companyList = [
      { type: '中型', Name: '深圳本贸科技股份有限公司' },
      { type: '中型', Name: '深圳碧汇源环保科技有限公司' },
      { type: '中型', Name: '深圳壁虎新能源汽车科技有限公司' },
      { type: '中型', Name: '深圳波顿香料有限公司' },
      { type: '中型', Name: '深圳昌毅电线电缆有限公司' },
      { type: '中型', Name: '深圳超纯水科技股份有限公司' },
      { type: '中型', Name: '深圳创维数字技术有限公司' },
      { type: '中型', Name: '深圳达实智能股份有限公司' },
      { type: '中型', Name: '深圳德森精密设备有限公司' },
      { type: '中型', Name: '深圳东创技术股份有限公司' },
      { type: '中型', Name: '深圳东吴投资集团有限公司' },
      { type: '中型', Name: '深圳飞扬骏研新材料股份有限公司' },
      { type: '中型', Name: '深圳感臻智能股份有限公司' },
      { type: '中型', Name: '深圳高速公路集团股份有限公司' },
      { type: '中型', Name: '深圳光峰科技股份有限公司' },
      { type: '中型', Name: '深圳光华伟业股份有限公司' },
      { type: '中型', Name: '深圳光韵达光电科技股份有限公司' },
      { type: '中型', Name: '深圳广药联康医药有限公司' },
      { type: '中型', Name: '深圳国人科技股份有限公司' },
      { type: '中型', Name: '深圳国人通信股份有限公司' },
      { type: '中型', Name: '深圳国人通信有限公司' },
      { type: '中型', Name: '深圳海宁加油站' },
      { type: '中型', Name: '深圳海源恒业商贸有限公司' },
      { type: '中型', Name: '深圳海源恒业投资有限公司' },
      { type: '中型', Name: '深圳汉莎技术有限公司' },
      { type: '中型', Name: '深圳和美精艺半导体科技股份有限公司' },
      { type: '中型', Name: '深圳恒邦置地有限公司' },
      { type: '中型', Name: '深圳恒通源环保节能科技有限公司' },
      { type: '中型', Name: '深圳宏芯宇电子股份有限公司' },
      { type: '中型', Name: '深圳华宝利电子有限公司' },
      { type: '中型', Name: '深圳华科育成科技开发有限公司' },
      { type: '中型', Name: '深圳华强半导体集团有限公司' },
      { type: '中型', Name: '深圳华强实业股份有限公司' },
      { type: '中型', Name: '深圳华锐分布式技术股份有限公司' },
      { type: '中型', Name: '深圳华云信息系统科技股份有限公司' },
      { type: '大型', Name: '深圳市水务(集团)有限公司' },
      { type: '大型', Name: '深圳市天威视讯股份有限公司' },
      { type: '大型', Name: '深圳市沃尔核材股份有限公司' },
      { type: '大型', Name: '深圳市物资集团有限公司' },
      { type: '大型', Name: '深圳市新天下集团有限公司' },
      { type: '大型', Name: '深圳市信利康供应链管理有限公司' },
      { type: '大型', Name: '深圳市星广源房地产开发有限公司' },
      { type: '大型', Name: '深圳市兴森快捷电路科技股份有限公司' },
      { type: '大型', Name: '深圳市迅捷兴科技股份有限公司' },
      { type: '大型', Name: '深圳市怡亚通供应链股份有限公司' },
      { type: '大型', Name: '深圳市银宝山新科技股份有限公司' },
      { type: '大型', Name: '深圳市英捷迅实业发展有限公司' },
      { type: '大型', Name: '深圳市英龙置业有限公司' },
      { type: '大型', Name: '深圳市盈峰智慧科技有限公司' },
      { type: '大型', Name: '深圳市宇宏投资集团有限公司' },
      { type: '大型', Name: '深圳市粤豪珠宝有限公司' },
      { type: '大型', Name: '深圳市长盈精密技术股份有限公司' },
      { type: '大型', Name: '深圳市中林实业发展有限公司' },
      { type: '大型', Name: '深圳市左右家私有限公司' },
      { type: '大型', Name: '深圳顺络电子股份有限公司' },
      { type: '小型', Name: '深圳沸石信息技术有限公司' },
      { type: '小型', Name: '深圳粉之宝餐饮管理有限公司' },
      { type: '小型', Name: '深圳丰盛年汽车有限公司' },
      { type: '小型', Name: '深圳丰亿纸品有限公司' },
      { type: '小型', Name: '深圳丰源电器有限公司' },
      { type: '小型', Name: '深圳丰远贸易有限公司' },
      { type: '小型', Name: '深圳风车乐园儿童游乐有限公司' },
      { type: '小型', Name: '深圳风语科技有限公司' },
      { type: '小型', Name: '深圳枫帆体育产业管理有限公司' },
      { type: '小型', Name: '深圳枫帆物业管理有限公司' },
      { type: '小型', Name: '深圳枫维机电设备有限公司' },
      { type: '小型', Name: '深圳疯狂创造文化传媒有限公司' },
      { type: '小型', Name: '深圳峰创智诚科技有限公司' },
      { type: '小型', Name: '深圳蜂鸟创新科技服务有限公司' },
      { type: '小型', Name: '深圳蜂速科技有限公司' },
      { type: '小型', Name: '深圳凤地电脑绣花设计有限公司' },
      { type: '小型', Name: '深圳符氏文化有限公司' },
      { type: '小型', Name: '深圳福艾林科技有限公司' },
      { type: '小型', Name: '深圳福德源数码科技有限公司' },
      { type: '小型', Name: '深圳福都贵金属有限公司' },
      { type: '小型', Name: '深圳福高科技有限公司' },
      { type: '小型', Name: '深圳福健微电子科技有限公司' },
      { type: '小型', Name: '深圳福聚海无量科技有限公司' },
      { type: '小型', Name: '深圳福客商贸有限公司' },
      { type: '小型', Name: '深圳福山生物科技有限公司' },
      { type: '小型', Name: '深圳福燊实业有限公司' },
      { type: '小型', Name: '深圳福特斯线缆科技有限公司' },
      { type: '小型', Name: '深圳富达金技术有限公司' },
      { type: '小型', Name: '深圳富德为智能科技有限公司' },
      { type: '小型', Name: '深圳富弘投资咨询有限公司' },
      { type: '小型', Name: '深圳富利登贸易有限公司' },
      { type: '小型', Name: '深圳富龙泰实业有限公司' },
      { type: '小型', Name: '深圳富士泰科电子有限公司' },
      { type: '小型', Name: '深圳富视安智能科技有限公司' },
      { type: '小型', Name: '深圳富沃远东国际贸易有限公司' },
      { type: '小型', Name: '深圳盖特普服饰有限公司' },
      { type: '小型', Name: '深圳盖亚兄弟信息技术有限公司' },
      { type: '小型', Name: '深圳感通科技有限公司' },
      { type: '小型', Name: '深圳港龙医院' },
      { type: '小型', Name: '深圳高度创新技术有限公司' },
      { type: '小型', Name: '深圳高飞翔塑胶五金制品有限公司' },
      { type: '小型', Name: '深圳高捷通电子有限公司' },
      { type: '小型', Name: '深圳高农科技有限公司' },
      { type: '小型', Name: '深圳高鹏企业服务股份有限公司' },
      { type: '小型', Name: '深圳高特高感科技有限公司' },
    ];
    const companys = await companySearchService.getCompaniesWithFreeText({
      text: companyList.map((item) => item.Name).join('\n'),
    });

    return companyList.map((item) => {
      const KeyNo = companys.Result.find((i) => i.Word === item.Name).LinkCompany[0].KeyNo;
      return { ...item, KeyNo };
    });
  }

  it('debug- 获取自定义关联方(工行)', async () => {
    // 关联方定义
    // 2.1 主体控制的企业（对外投资企业控股大于50%，穿透一层）
    // 2.2 实控人
    // 2.3 实控人控制的企业（对外投资企业控股大于50%，穿透一层）
    // 2.3.1 实控人穿透到全国各级国资委的（实控人穿透到最后关键词包含「国资委」的，不再列示他的实控人对外投资企业）
    // 2.4 实控路线（实控路线上的企业，也就是母公司及祖母公司，直到实控人

    // 1、剔除有限合伙 和 国有资产监督管理委员会、 国有资产监督管理局
    // 2、控股股东链路 =》 主体控制企业 =》 实控人控制企业
    // 3、注册资本排序， 大中型只取前50， 小微前20
    const companyList = await getCompanyList();

    // 获取最终实际控制人并分类
    const resultList = [];

    // const gzwGzwdimHitStrategies = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
    //   {
    //     fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    //     fieldValue: [
    //       // 子公司（对外投资(>50%的企业)）
    //       RelatedTypeEnums.MajorityInvestment,
    //     ],
    //     // options: RelatedTypeMap,
    //     compareType: DimensionFieldCompareTypeEnums.ContainsAny,
    //   },
    // ]);

    // 并行处理所有公司
    await Bluebird.map(
      companyList,
      async (company) => {
        const relatedInfo = await relatedHelper.getIcbcSFRelateds(company.KeyNo);
        resultList.push({ type: company.type, companyName: company.Name, relatedInfo });
      },
      { concurrency: 10 },
    );

    const totalCount = resultList.reduce((sum, company) => sum + company.relatedInfo.length, 0);
    const typeCompanyCounts = resultList.reduce((acc, company) => {
      acc[company.type] = (acc[company.type] || 0) + 1;
      return acc;
    }, {});
    // const typeCompanyIsCut = resultList.reduce((acc, company) => {
    //   if (company.isCut) {
    //     acc[company.type] = (acc[company.type] || 0) + 1;
    //   }
    //   return acc;
    // }, {});

    const typeRelatedCompanyCounts = resultList.reduce((acc, company) => {
      acc[company.type] = (acc[company.type] || 0) + company.relatedInfo.length;
      return acc;
    }, {});

    console.log(`企业数量: ${resultList.length} 关联方数量: ${totalCount}`);
    console.log('各类型公司数量:', typeCompanyCounts);
    console.log('各类型关联方数量:', typeRelatedCompanyCounts);
    // console.log('各类型关联方被截取的数量:', typeCompanyIsCut);

    // const outputPath = path.join(__dirname, 'companies-data9.json');
    // fs.writeFileSync(outputPath, JSON.stringify(resultList, null, 2));
    // console.log(`数据已导出到: ${outputPath}`);
  });

  it('debug getCompanyRelatedInfo', async () => {
    const companyId = 'c406abaf3e36d173186aa5e99d506fbd';
    // const type = '中型';
    // const dimHitStrategies = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
    //   {
    //     fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    //     fieldValue: [
    //       // 子公司（对外投资(>50%的企业)）
    //       RelatedTypeEnums.MajorityInvestment,
    //     ],
    //     // options: RelatedTypeMap,
    //     compareType: DimensionFieldCompareTypeEnums.ContainsAny,
    //   },
    // ]);

    const relatedInfo = await relatedHelper.getIcbcSFRelateds(companyId);
    expect(relatedInfo);
  });

  it.skip('debug getInvestCompany', async () => {
    const companyId = 'c406abaf3e36d173186aa5e99d506fbd';
    // const type = '中型';
    // const dimHitStrategies = getDimensionHitStrategyPO(DimensionTypeEnums.RelatedCompanies, [
    //   {
    //     fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    //     fieldValue: [
    //       // 子公司（对外投资(>50%的企业)）
    //       RelatedTypeEnums.MajorityInvestment,
    //     ],
    //     // options: RelatedTypeMap,
    //     compareType: DimensionFieldCompareTypeEnums.ContainsAny,
    //   },
    // ]);

    // const relatedInfo = await relatedHelper.getInvestCompany(companyId, 50, RelatedTypeEnums.InvestCompany);
    // expect(relatedInfo);
  });

  // it('debug 分析 data.json', async () => {
  //   const resultList = data;
  //   const result = resultList.filter((item) => item.count > 50 && item.type == '大型');
  //   console.log(result.map((item) => pick(item, ['companyName', 'count'])));
  // });

  it('debug 分析 data9.json', async () => {
    const resultList = JSON.parse(fs.readFileSync(path.join(__dirname, 'companies-data9.json'), 'utf8'));
    const result = resultList.filter((item) => item.isCut);
    console.log(result.map((item) => pick(item, ['type', 'companyName', 'count', 'totalCount'])));
  });
});
