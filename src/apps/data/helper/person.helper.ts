import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { PersonData } from 'libs/model/data/source/PersonData';
import { compact, flatten, flattenDeep, map, uniq, uniqBy } from 'lodash';
import { CompanySearchService } from '../../company/company-search.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { Cacheable } from '@type-cacheable/core';
import { OperType } from '../../../libs/constants/credit.analyze.constants';
import { isOrganism } from '../../company/utils';
import * as Bluebird from 'bluebird';

/**
 * rover自身数据源接口
 */
@Injectable()
export class PersonHelper {
  private readonly logger: Logger = QccLogger.getLogger(PersonHelper.name);

  constructor(private readonly companySearchService: CompanySearchService, private readonly companyDetailsService: CompanyDetailService) {}

  /**
   * 获取公司高管keyNo:Job list
   * @param keyNo
   * @param stockType all-全部类型， person-自然人股东, other-非自然人股东
   * @param ignoreInvest — 过滤投资机构 ture=忽略投资机构
   * @param ignoreCompany 过滤公司主体
   * @param searchKey
   */
  // @Cacheable({ ttlSeconds: 300 })
  async getCompanyExecutivesKeyNosV2(keyNo: string, stockType = 'person', ignoreInvest = true, ignoreCompany = false, searchKey?: string) {
    let personList: PersonData[][];
    if (isOrganism(keyNo)) {
      personList = await Bluebird.all([
        this.getLegalPerson(keyNo), // 法定代表人
        this.getOrganismPerson(keyNo), //社会组织主要人员
      ]);
    } else {
      personList = await Bluebird.all([
        this.getLegalPerson(keyNo), // 法定代表人
        this.getEmployeeList(keyNo), // 获取(当前)主要人员（最新公示+工商登记） 董监高
        this.getPartnerList(keyNo, stockType, ignoreInvest, 200, searchKey), // 获取股东信息  （最新公示+工商登记）
        this.getBenefitList(keyNo), // 受益所有人
      ]);
    }
    //const personJobList: PersonData[] = unionBy(flattenDeep(compact(personList)), (p) => p?.keyNo && p?.name);
    const personJobList: PersonData[] = [];
    const personNameSet = new Set<string>();
    flattenDeep(compact(personList)).forEach((f) => {
      const nameKeyNo = `${f?.name}${f?.keyNo}`;
      if (personNameSet.has(nameKeyNo)) {
        personJobList.forEach((pf) => {
          if (nameKeyNo === `${pf?.name}${pf?.keyNo}`) {
            pf.tags = uniq(flatten([pf?.tags, f?.tags]));
            pf.job = uniq(compact(pf?.job?.split(',')?.concat(f?.job?.split(','))))?.join(',');
          }
        });
      } else {
        personNameSet.add(nameKeyNo);
        personJobList.push(f);
      }
    });
    const personJobSet: Record<string, string> = {};
    if (!ignoreCompany) {
      personJobSet[keyNo] = '企业主体';
    }
    personJobList.forEach((e) => {
      if (e?.keyNo) {
        e.name = e.name.replace(/<em>/g, '').replace(/<\/em>/g, '');
        if (personJobSet[e.keyNo]) {
          personJobSet[e.keyNo] = uniq(compact(personJobSet[e.keyNo].split(',').concat(e.job.split(',')))).join(',');
        } else {
          personJobSet[e.keyNo] = e.job;
        }
      }
    });
    return { personNos: Object.keys(personJobSet), personJobSet, personJobList };
  }

  /**
   * 获取社会组织主要人员，带分页
   * @param keyNo
   * @param pageSize
   * @param pageIndex
   */
  public async getOrganismPersonPaging(keyNo: string, pageSize = 10, pageIndex = 1) {
    const params = {
      keyNo,
      pageSize,
      pageIndex,
    };
    return this.companyDetailsService.getOrganismPersonList(params);
  }

  /**
   * 社会组织获取主要人员
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  private async getOrganismPerson(keyNo: string): Promise<PersonData[]> {
    try {
      const param = {
        keyNo,
        pageSize: 200,
        pageIndex: 1,
      };
      const limited = 3000;
      let totalFetched = 0;
      const resultItems = [];
      do {
        const res1 = await this.companyDetailsService.getOrganismPersonList(param);
        if (res1.Result?.length) {
          totalFetched += res1.Result.length;
          resultItems.push(...res1.Result);
        }
        if (!res1.Result?.length || res1.Result.length < param.pageSize || totalFetched >= limited) {
          break;
        }
        param.pageIndex++;
      } while (true);
      return resultItems.map((item) => {
        return Object.assign(new PersonData(), {
          name: item?.PersonName,
          job: item?.Position,
          keyNo: item?.PersonKey,
        });
      });
    } catch (e) {
      this.logger.error(`http Get QccSearch/List/OrganismPerson err:`, e);
      return null;
    }
  }

  /**
   * 获取(当前)主要人员（最新公示+工商登记） 董监高
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployeeList(keyNo: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    if (isOrganism(keyNo)) {
      return this.getOrganismPerson(keyNo);
    }

    try {
      const [list1, list2] = await Bluebird.all([this.getEmployeeData(keyNo, 'IpoEmployees'), this.getEmployeeData(keyNo, 'Employees')]);
      result.push(...list1, ...list2);
    } catch (e) {
      this.logger.error('getEmployeeList() error:', e);
    }

    // try {
    //   // type = IpoEmployees 最新公示的主要人员
    //   await this.getEmployeeData(keyNo, 'IpoEmployees', result);
    // } catch (error) {
    //   this.logger.error('getEmployeeList IpoEmployees error:', error);
    // }
    //
    // try {
    //   // type = Employees 工商登记的主要人员
    //   await this.getEmployeeData(keyNo, 'Employees', result);
    // } catch (error) {
    //   this.logger.error('getEmployeeList Employees error:', error);
    // }

    return result;
  }

  /**
   * 获取 实际控制人（疑似）
   * @param keyNo
   * @returns
   */
  public async getActualController(keyNo: string) {
    const res = await this.companyDetailsService.getSuspectedActualControllerV5(keyNo);
    return res?.Result;
  }

  /**
   * 获取最终实际控制人
   * 先从ActualControl取，ActualControl没值再从 FinalActualControl 里取，再没值从Names里取
   * 20250512-取所有的实际控制人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getFinalActualController(keyNo: string, onlyPerson = true): Promise<PersonData[]> {
    const actualControllerResponse = await this.getActualController(keyNo);
    const sourceCompanyId = actualControllerResponse?.KeyNo;
    const sourceCompanyName = actualControllerResponse?.CompanyName;
    let finalActPersonList: PersonData[] = [];
    if (!actualControllerResponse) {
      return finalActPersonList;
    }
    const getFilteredPersonList = (personList: any[], onlyPerson = true) => {
      if (!personList.length) {
        return finalActPersonList;
      }
      const filteredPersonList = personList
        .filter((p) => p?.KeyNo?.length > 0)
        .map((e) => ({
          keyNo: e.KeyNo,
          name: e.Name,
          sourceCompanyId,
          sourceCompanyName,
          stockPercent: e.PercentTotal,
        }));
      const result = uniqBy(filteredPersonList, (x) => x.keyNo);

      if (onlyPerson) {
        return result.filter((p) => p.keyNo.startsWith('p')) || [];
      }
      return result;
    };
    finalActPersonList = getFilteredPersonList(actualControllerResponse?.ActualControl?.PersonList || [], onlyPerson);
    // 如果没有取到实际控制人或者 FinalActualControl有值，再从 FinalActualControl 里取
    if (actualControllerResponse.FinalActualControl) {
      finalActPersonList = getFilteredPersonList(actualControllerResponse.FinalActualControl?.PersonList || [], onlyPerson);
    }
    if (actualControllerResponse?.Names?.length) {
      if (finalActPersonList?.length) {
        actualControllerResponse.Names?.forEach((e) => {
          finalActPersonList.forEach((f) => {
            e.Names?.PersonList.forEach((m) => {
              if (f.keyNo === m.KeyNo) {
                f.tags = m?.Tags?.length ? m.Tags : ['实际控制人'];
                f.job = e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人';
              }
            });
          });
        });
      }
      actualControllerResponse.Names?.forEach((e) => {
        e.Names?.PersonList.forEach((m) => {
          finalActPersonList.push(
            Object.assign(new PersonData(), {
              name: m.Name,
              keyNo: m.KeyNo,
              tags: m?.Tags?.length ? m.Tags : ['实际控制人'],
              job: e.Job ? [...e.Job.split(','), '实际控制人'].join(',') : '实际控制人',
              stockPercent: m.PercentTotal,
              sourceCompanyId,
              sourceCompanyName,
            }),
          );
        });
      });
    }
    return finalActPersonList;
  }

  /**
   * 获取法定代表人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getLegalPerson(keyNo: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    const companyDetail = await this.companySearchService.companyDetailsQcc(keyNo);

    if (companyDetail?.Oper?.Name) {
      result.push(
        Object.assign(new PersonData(), {
          name: companyDetail?.Oper?.Name,
          keyNo: companyDetail?.Oper?.KeyNo,
          job: OperType[companyDetail?.Oper?.OperType?.toString()] || OperType['1'],
        }),
      );
    }
    return result;
  }

  /**
   * 获取历史法定代表人
   * @param keyNo
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHisLegalPerson(keyNo: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    const res = await this.companyDetailsService.getCoyHistoryInfo(keyNo);

    if (res?.Result?.OperList.length) {
      res?.Result?.OperList.forEach((oper) => {
        result.push(
          Object.assign(new PersonData(), {
            name: oper?.OperName,
            keyNo: oper?.KeyNo,
            job: OperType[oper.OperType?.toString()] || OperType['1'],
            history: true,
          }),
        );
      });
    }
    return result;
  }

  /**
   * 获取股东信息 （最新公示+工商登记）
   * @param keyNo
   * @param stockType all-全部类型， person-自然人股东, other-非自然人股东
   * @param ignoreInvest — 过滤投资机构 ture=忽略投资机构  投资机构过滤不精准，目前先不支持
   * @param pageSize
   * @param searchKey
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getPartnerList(keyNo: string, stockType = 'person', ignoreInvest = false, pageSize = 200, searchKey?: string): Promise<PersonData[]> {
    const result: PersonData[] = [];
    ignoreInvest = false; // 投资机构过滤不精准，目前先不支持
    try {
      // type = IpoPartners 最新公示的股东
      await this.getPartnerData(keyNo, stockType, 'IpoPartners', result, ignoreInvest, pageSize, searchKey);
      if (result.length > 0) {
        // 如果有最新公示股东，直接返回，不再查询工商登记股东，因为工商登记股东已经不会更新了
        return result;
      }
    } catch (error) {
      this.logger.error('getPartnerList IpoPartners error:', error);
    }

    try {
      // type = Partners 工商登记的股东
      await this.getPartnerData(keyNo, stockType, 'Partners', result, ignoreInvest, pageSize, searchKey);
    } catch (error) {
      this.logger.error('getPartnerList Partners error:', error);
    }

    return result;
  }

  /**
   * 获取最终受益人，受益自然人
   * @param keyNo
   * @param isBenefit 是否为最终受益人 true=最终受益人（受益所有人） false=受益自然人，默认为 true
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBenefitList(keyNo: string, isBenefit = true): Promise<PersonData[]> {
    const result: PersonData[] = [];
    const benefit = '受益所有人';
    try {
      const params = {
        keyNo,
        isBenefit,
      };
      const res = await this.companyDetailsService.getBenefitDetail(params);
      res?.Result?.Names?.forEach((e) => {
        result.push(
          Object.assign(new PersonData(), {
            name: e.Name,
            keyNo: e.KeyNo,
            tag: [benefit],
            job: [...compact(map(e?.BenefitTypeInfo, 'Job')), benefit]?.join(','),
          }),
        );
      });
    } catch (error) {
      this.logger.error('getBenefitList error:', error);
    }
    return result;
  }

  /**
   * 查询主要人员
   * @param keyNo 公司keyno
   * @param type  type = IpoEmployees 最新公示的主要人员  type = Employees 工商登记的主要人员
   * @param personList
   */
  public async getEmployeeData(keyNo: string, type: string): Promise<PersonData[]> {
    const res = await this.getEmployee(keyNo, type, 300);
    const result = [];
    res?.Result?.forEach((item) => {
      result.push(
        Object.assign(new PersonData(), {
          name: item.Name,
          job: item?.Job || '高管',
          keyNo: item.KeyNo,
          tags: item.Tags,
        }),
      );
    });
    return result;
  }

  /**
   * 查询历史主要人员
   * @param keyNo 公司keyno
   * @param personList
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getHisEmployeeData(keyNo: string): Promise<PersonData[]> {
    const res = await this.getEmployeeHistory(keyNo);
    const result = [];
    res?.Result?.forEach((item) => {
      result.push(
        Object.assign(new PersonData(), {
          history: true,
          name: item.EmployeeName,
          job: item?.Job || '高管',
          keyNo: item.KeyNo,
          tags: item.Tags,
        }),
      );
    });
    return result;
  }

  /**
   * 主要人员
   * @param keyNo
   * @param type
   * @param pageSize
   * @returns
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployee(keyNo: string, type: string, pageSize = 10) {
    const params = {
      keyNo,
      nodeName: type,
      pageSize,
    };
    return await this.companyDetailsService.getEmployeeList(params);
  }

  /**
   * 历史主要人员
   * @param keyNo
   * @param type
   * @param pageSize
   * @returns
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getEmployeeHistory(keyNo: string, pageSize = 100) {
    const params = {
      keyNo,
      pageSize,
    };
    return await this.companyDetailsService.getEmployeeListHistory(params);
  }

  /**
   * 查询公司股东信息
   * @param keyNo
   * @param stockType  all-全部类型， person-自然人股东, other-非自然人股东
   * @param type  type = IpoPartners 最新公示的股东  type = Partners 工商登记的股东
   * @param personList
   * @param ignoreInvest 过滤投资机构 ture=忽略投资机构
   * @param pageSize
   * @param searchKey
   */
  private async getPartnerData(
    keyNo: string,
    stockType: string,
    type: string,
    personList: PersonData[],
    ignoreInvest = false,
    pageSize = 200,
    searchKey?: string,
  ) {
    const partners = await this.getPartner(keyNo, type, pageSize, searchKey);

    partners?.Result?.forEach((item) => {
      const person = Object.assign(new PersonData(), {
        name: item.StockName,
        job: compact(['股东', item?.Job]).join(','),
        keyNo: item.KeyNo,
        tags: item.Tags,
        stockPercent: item.StockPercent,
        shouldCapi: item.ShouldCapi,
        shoudDate: item.ShoudDate,
      });
      switch (stockType) {
        case 'person':
          if (item.Org === 2) {
            personList.push(person);
          }
          break;
        case 'other':
          if (item.Org !== 2) {
            if (!ignoreInvest || !item?.Invest) {
              personList.push(person);
            }
          }
          break;
        default:
          if (!ignoreInvest || !item?.Invest) {
            personList.push(person);
          }
          break;
      }
    });
  }

  /**
   * TODO 当人员是股东列表的时候，可能会存在列表数量过多，进而导致上游尽调服务构造es查询条件时候超限(目前存在较多抽象方便的定义涉及到es query，修改代价较大)
   * 所以，暂定只取3000条
   *
   * 公司股东数量统计：
   * 股东最多的是12476，
   * 3000以上的有30 个，
   * 2000以上的92个，
   * 1000 有451个，
   * 500 有1720 ，
   * 200 以上的有8500个
   *
   * @param keyNo
   * @param type
   * @param pageSize
   * @param searchKey
   */
  public async getPartner(keyNo: string, type: string, pageSize = 500, searchKey?: string): Promise<{ Result: any[] }> {
    const param = {
      keyNo,
      type,
      pageSize,
      pageIndex: 1,
    };
    if (searchKey) {
      Object.assign(param, { searchKey });
    }
    const limited = 3000;
    let totalFetched = 0;
    const res: {
      Result: any[];
    } = {
      Result: [],
    };
    do {
      const res1 = await this.companyDetailsService.getPartnerWithGroup(param);
      if (res1.Result) {
        totalFetched += res1.Result.length;
        Array.prototype.push.apply(res.Result, res1.Result);
      } else {
        break;
      }
      if (totalFetched >= limited || totalFetched >= res1.Paging?.TotalRecords || res1.Result.length < param.pageSize) {
        break;
      }
      param.pageIndex++;
    } while (true);

    return res;
  }
}
