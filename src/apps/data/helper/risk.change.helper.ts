import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Injectable } from '@nestjs/common';
import { includes } from 'lodash';
import { Logger } from 'log4js';
import * as moment from 'moment';
import { IntellectualType, SimpleCancelTypeConstant } from '../../../libs/constants/risk.change.constants';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { getCompareResult, getCompareResultForArray } from '../../../libs/utils/diligence/diligence.utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { CompanySearchService } from '../../company/company-search.service';
import dynamicConstant from '../risk.copy.from.c/dynamic-constant';
import { PersonHelper } from './person.helper';
import { BankLitigationHelper } from '../source/risk-change/helper/bank-litigation.helper';
import { CaseReasonHelper } from '../source/risk-change/helper/case-reason.helper';
import { CompanyChangeHelper } from '../source/risk-change/helper/company-change.helper';
import { CompanyFinaceHelper } from '../source/risk-change/helper/company-finace.helper';
import { CompanyShareHelper } from '../source/risk-change/helper/company-share.helper';
import { CompanyStockHelper } from '../source/risk-change/helper/company-stock.helper';
import { JudgementHelper } from '../source/risk-change/helper/judgement.helper';
import { MainEmployeeHelper } from '../source/risk-change/helper/main-employee.helper';
import { PenaltyHelper } from '../source/risk-change/helper/penalty.helper';
import { RelatedAnalyzeHelper } from '../source/risk-change/helper/related-analyze.helper';
import { BaseHelper } from '../source/risk-change/helper/base.helper';

@Injectable()
export class RiskChangeHelper {
  public readonly logger: Logger = QccLogger.getLogger(RiskChangeHelper.name);

  constructor(
    private readonly personHelper: PersonHelper,
    private readonly companySearchService: CompanySearchService,
    private readonly companyDetailService: CompanyDetailService,
    private readonly companyShareHelper: CompanyShareHelper,
    private readonly relatedAnalyzeHelper: RelatedAnalyzeHelper,
    private readonly bankLitigationHelper: BankLitigationHelper,
    private readonly caseReasonHelper: CaseReasonHelper,
    private readonly companyChangeHelper: CompanyChangeHelper,
    private readonly judgementHelper: JudgementHelper,
    private readonly mainEmployeeHelper: MainEmployeeHelper,
    private readonly penaltyHelper: PenaltyHelper,
    private readonly companyStockHelper: CompanyStockHelper,
    private readonly companyFinanceHelper: CompanyFinaceHelper,
    private readonly baseHelper: BaseHelper,
  ) {}
  public hitLayTypesField(layTypesField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitLayTypesField(layTypesField, item);
  }
  public hitLayTypesField72(layTypesField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitLayTypesField72(layTypesField, item);
  }
  /**动产抵押*/
  public category15Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category15Field(dimension, item);
  }
  //判断被申请人是否为目标企业 被申请人取changeExtend.C
  // 破产重整
  public category58Field(item: any) {
    return this.companyChangeHelper.category58Field(item);
  }
  /**土地抵押*/
  public category30Field(companyId: string, dimension: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category30Field(companyId, dimension, item);
  }
  /**担保信息*/
  public category101Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category101Field(dimension, item);
  }
  /**税务催缴*/
  public category131Field(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category131Field(dimension, item);
  }
  public hitIsBPField(isBPField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitIsBPField(isBPField, item);
  }
  public hitBeforeContentField(beforeContentField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitBeforeContentField(beforeContentField, item);
  }
  public hitShareChangeStatusField(changeStatusField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitShareChangeStatusField(changeStatusField, item);
  }
  /**持股比例变更幅度*/
  public hitShareChangeRateField(changeRateField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitShareChangeRateField(changeRateField, item);
  }
  public hitTimePeriodThresholdCountField(thresholdCountField: DimensionHitStrategyFieldsEntity, allPeriodList: any[]) {
    return this.companyChangeHelper.hitTimePeriodThresholdCountField(thresholdCountField, allPeriodList);
  }
  /**行业变更*/
  public async hitIndustryThresholdField(typeField: DimensionHitStrategyFieldsEntity, subHitData: any[], companyId: string) {
    return this.companyChangeHelper.hitIndustryThresholdField(this.companySearchService, typeField, subHitData, companyId);
  }
  public hitChangeStatusField(changeStatusField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitChangeStatusField(changeStatusField, item);
  }
  public hitAfterContentField(afterContentField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitAfterContentField(afterContentField, item);
  }
  public hitCurrencyChangeField(currencyChangeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitCurrencyChangeField(currencyChangeField, item);
  }

  public hitCategory123CurrencyChangeField(currencyChangeField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, item);
  }
  public hitRegisCapitalTrendField(regisCapitalTrendField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitRegisCapitalTrendField(regisCapitalTrendField, item);
  }
  public hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField: DimensionHitStrategyFieldsEntity, item: any): boolean {
    return this.companyChangeHelper.hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField, item);
  }
  public capitalReduceSelectCompareResult(dimension: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.capitalReduceSelectCompareResult(dimension, item);
  }
  public category38(businessStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category38(businessStatusField, item);
  }

  /**
   *  限制高消费，限制对象，1-企业本身，2-法人代表
   * @param restricterTypeField
   * @param newItem
   */
  public restricterTypeField(restricterTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.restricterTypeField(restricterTypeField, item);
  }
  public category4(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category4(judicialRoleTypeField, item);
  }
  public caseReasonTypeFieldByKeyCauseAction(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.caseReasonHelper.caseReasonTypeFieldByKeyCauseAction(judicialRoleTypeField, item);
  }
  public caseReasonTypeField(caseReasonField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.caseReasonHelper.caseReasonTypeField(caseReasonField, item);
  }
  public checkContractDisputeField(contractDisputeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.caseReasonHelper.checkContractDisputeField(contractDisputeField, item);
  }
  public checkFinancialReasonField(financialReasonField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.caseReasonHelper.checkFinancialReasonField(financialReasonField, item);
  }
  public checkBankOrFinancialLeasingField49(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField49(bankOrFlField, item);
  }
  public checkBankOrFinancialLeasingField4(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField4(bankOrFlField, item);
  }
  public checkBankOrFinancialLeasingField18(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField18(bankOrFlField, item);
  }
  public checkBankOrFinancialLeasingField7(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField7(bankOrFlField, item);
  }
  public checkBankOrFinancialLeasingField27(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField27(bankOrFlField, item);
  }
  public checkBankOrFinancialLeasingField90(bankOrFlField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.bankLitigationHelper.checkBankOrFinancialLeasingField90(bankOrFlField, item);
  }
  public checkCaseTypeField(caseTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.caseReasonHelper.checkCaseTypeField(caseTypeField, item);
  }
  public category49(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category49(judicialRoleTypeField, item);
  }
  public category18(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category18(judicialRoleTypeField, item);
  }
  public category90(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category90(judicialRoleTypeField, item);
  }
  public category7(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category7(judicialRoleTypeField, item);
  }
  public category27(judicialRoleTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.judgementHelper.category27(judicialRoleTypeField, item);
  }

  /**
   * 股权冻结：
   * equityFreezeScope 股权冻结范围：changeInfo.T2 !== 1  不等1 （失效，无效，解除） changeInfo?.T === 1 企业股权被冻结  , changeInfo?.T === 2 持有股权被冻结
   * @param equityFreezeScopeField
   * @param item
   */
  public equityFreezeScopeFieldCategory26(equityFreezeScopeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityFreezeScopeFieldCategory26(equityFreezeScopeField, item);
  }

  /**
   * 拍卖类型  新增${changeInfo.Q === 1 ? '破产' : '司法'}拍卖
   * @param auctionTypeField
   * @param newItem
   */
  public auctionTypeField(auctionTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.auctionTypeField(auctionTypeField, item);
  }
  public hitCompChangeRoleField(compChangeRoleField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.mainEmployeeHelper.hitCompChangeRoleField(compChangeRoleField, item);
  }
  public changeThresholdField(
    compChangeRoleField: DimensionHitStrategyFieldsEntity,
    changeThresholdField: DimensionHitStrategyFieldsEntity,
    newItem: any,
    periodResults: any[],
    personDatas: PersonData[],
  ) {
    return this.mainEmployeeHelper.changeThresholdField(compChangeRoleField, changeThresholdField, newItem, periodResults, personDatas);
  }
  public async checkListedField(listedField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyChangeHelper.checkListedField(this.companySearchService, listedField, item, keyNo);
  }
  public penaltyRedCardFieldCategory107(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyRedCardFieldCategory107(penaltyRedCardField, item);
  }
  public penaltyRedCardFieldCategory22(penaltyRedCardField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyRedCardFieldCategory22(penaltyRedCardField, item);
  }
  public penaltyUnitField(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyUnitField(penaltyUnitField, item);
  }
  public penaltyIssuingUnitField(penaltyIssuingUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyIssuingUnitField(penaltyIssuingUnitField, item);
  }
  public penaltyUnitField31(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyUnitField31(penaltyUnitField, item);
  }
  public penaltyUnitField117(penaltyUnitField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.penaltyUnitField117(penaltyUnitField, item);
  }
  public punishTypeField(punishTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.punishTypeField(punishTypeField, item);
  }
  public businessAbnormalTypeField(businessAbnormalTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.businessAbnormalTypeField(businessAbnormalTypeField, item);
  }
  public punishEnvTypeField(punishEnvTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.punishEnvTypeField(punishEnvTypeField, item);
  }
  public financialPenaltyCauseTypeField(financialPenaltyCauseTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.financialPenaltyCauseTypeField(financialPenaltyCauseTypeField, item);
  }

  /**
   * 知识产权： 1-出质人，2-质权人
   * @param intellectualRoleField
   * @param newItem
   */
  public category86IntellectualRole(intellectualRoleField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.category86IntellectualRole(intellectualRoleField, item);
  }

  /**
   * 知识产权分类：1-专利，2-商标 （ChangeExtend.F = 1 专利）
   */
  public category86IntellectualType(intellectualTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.category86IntellectualType(intellectualTypeField, item);
  }

  /**
   * 抽样检查结果 不合格（ChangeExtend.E）
   * @param inspectionResultTypeField
   * @param item
   */
  public inspectionResultTypeField(inspectionResultTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.penaltyHelper.inspectionResultTypeField(inspectionResultTypeField, item);
  }

  /**
   * 金额属性的判断，单位是元，设置的阀值单位是万元
   * @param amountField 属性设置的阈值
   * @param rawValue 当前的动态的金额值
   * @param conversionRate 设置的阀值与动态金额值的转换关系，默认是10000，即设置的阈值单位是万元，动态金额值单位是元
   * @returns
   */
  public amountField(amountField: DimensionHitStrategyFieldsEntity, rawValue: any, conversionRate = 10000) {
    return this.baseHelper.amountField(amountField, rawValue, conversionRate);
  }

  public checkAmountField(amountField: DimensionHitStrategyFieldsEntity, rawValue: any, conversionRate = 10000) {
    return this.baseHelper.checkAmountField(amountField, rawValue, conversionRate);
  }

  /**
   * 注册资本变更趋势
   * 公司1个自然年度内拟减少注册资本超过其原注册资本5%
   * @param periodRegisCapitalField
   * @param newItem
   * @param baseLineItem
   */
  public hitPeriodRegisCapitalField(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, newItem: any, baseLineItems: any[]) {
    return this.companyChangeHelper.hitPeriodRegisCapitalField(periodRegisCapitalField, newItem, baseLineItems);
  }

  //减资公告
  public hitPeriodRegisCapitalField123(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, baseLineItem: any[], newItem: any) {
    return this.companyChangeHelper.hitPeriodRegisCapitalField123(periodRegisCapitalField, baseLineItem, newItem);
  }

  public hitMainInfoUpdateCapitalChange(periodRegisCapitalField: DimensionHitStrategyFieldsEntity, baseLineItem: any[]) {
    return this.companyChangeHelper.hitMainInfoUpdateCapitalChange(periodRegisCapitalField, baseLineItem);
  }

  /**
   * 食品安全抽检产品源
   * 判断typeField.fieldValue,默认为[1,2] 1-销售的产品,2-生产的产品
   * 判断ChangeExtend的 F(销售源),G(生产源)中的 KeyNo是否能和 targetKeyNo匹配
   * @param typeField
   * @param item
   */
  public category79Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.category79Field(typeField, item);
  }

  /**
   * 融资动态筛选
   * TODO:退市动态暂未找到获取方式
   * @param typeField
   * @param item
   */
  public category28Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.baseHelper.category28Field(typeField, item);
  }

  /**
   * 简易注销
   * @param typeField
   * @param item
   */
  public category23Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyChangeHelper.category23Field(typeField, item);
  }
  public category12Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.category12Field(typeField, item);
  }
  public category50Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.category50Field(typeField, item);
  }
  public limitPriceTypeField(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.limitPriceTypeField(typeField, item);
  }
  public categoryAnnouncementReportField(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.categoryAnnouncementReportField(typeField, item);
  }
  public async hitHolderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, keyNos: string[], targetKeyNo: string) {
    return this.companyStockHelper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);
  }

  /**
   * 股权冻结
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory26(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyStockHelper.holderRoleFieldCategory26(holderRoleField, item, keyNo);
  }

  /**
   * 股权质押
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory50(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyStockHelper.holderRoleFieldCategory50(holderRoleField, item, keyNo);
  }

  /**
   *  股权冻结金额
   * @param equityFrozenAmountField
   * @param item
   */
  public equityFrozenAmountFieldCategory26(equityFrozenAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, item);
  }

  /**
   *  股权质押-质押占总股本比例
   * @param stockPledgeRatioField
   * @param item
   */
  public stockPledgeRatioFieldCategory50(stockPledgeRatioField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);
  }

  /**
   *  股权质押-质押股份数量(股)
   * @param stockPledgeQuantityField
   * @param item
   */
  public stockPledgeQuantityFieldCategory50(stockPledgeQuantityField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, item);
  }

  /**
   * 股权质押的状态
   * @param sharePledgeStatusField
   * @param newItem
   */
  public sharePledgeStatusFieldCategory50(sharePledgeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);
  }

  /**
   * 股权出质
   * @param holderRoleField
   * @param employeeList
   */
  public async holderRoleFieldCategory12(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyStockHelper.holderRoleFieldCategory12(holderRoleField, item, keyNo);
  }

  /**
   * 股权出质的状态
   * @param equityPledgeStatusField
   * @param newItem
   */
  public equityPledgeStatusFieldCategory12(equityPledgeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);
  }

  /**
   *  股权出质比例
   * @param equityPledgeRatioField
   * @param item
   */
  public equityPledgeRatioFieldCategory12(equityPledgeRatioField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);
  }

  /**
   * 出质股份数量
   * @param equityPledgeQuantityField
   * @param item
   */
  public equityPledgeQuantityFieldCategory12(equityPledgeAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityPledgeQuantityFieldCategory12(equityPledgeAmountField, item);
  }

  /**
   * 出质股权数额
   * @param equityPledgeAmountField
   * @param item
   */
  public equityPledgeAmountFieldCategory12(equityPledgeAmountField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.equityPledgeAmountFieldCategory12(equityPledgeAmountField, item);
  }

  /**
   * 资产拍卖
   * @param typeField
   * @param item
   */
  public category75Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.category75Field(typeField, item);
  }

  /**
   * 询价评估
   * @param typeField
   * @param item
   */
  public category59Field(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyStockHelper.category59Field(typeField, item);
  }
  public categoryAnnualReportField(annualReportTypeField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyFinanceHelper.categoryAnnualReportField(annualReportTypeField, item);
  }
  public async categoryRetainedProfitField(retainedProfitField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryRetainedProfitField(retainedProfitField, item, keyNo);
  }
  async categoryNetProfitRatioField(netProfitRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryNetProfitRatioField(netProfitRatioField, item, keyNo);
  }
  async categoryRevenueRatioField(revenueRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryRevenueRatioField(revenueRatioField, item, keyNo);
  }
  async categoryAccountsReceivableRatioField(accountsReceivableRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryAccountsReceivableRatioField(accountsReceivableRatioField, item, keyNo);
  }
  async categoryInventoryRatioField(inventoryRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryInventoryRatioField(inventoryRatioField, item, keyNo);
  }
  async categoryInterestBearingLiabilitiesRatioField(interestBearingLiabilitiesRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryInterestBearingLiabilitiesRatioField(interestBearingLiabilitiesRatioField, item, keyNo);
  }
  async categoryIbdAnnualRevRatioField(ibdAnnualRevRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryIbdAnnualRevRatioField(ibdAnnualRevRatioField, item, keyNo);
  }
  async categoryCmAndStbRatioField(cmAndStbRatioField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyFinanceHelper.categoryCmAndStbRatioField(cmAndStbRatioField, item, keyNo);
  }
  async categoryTotalLiabToAssetsRatioField(totalLiabToAssetsRatioField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    return this.companyFinanceHelper.categoryTotalLiabToAssetsRatioField(totalLiabToAssetsRatioField, newItem, keyNo);
  }
  async categoryCashFlowFromActivitiesAmountField(cashFlowFromActivitiesAmountField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    return this.companyFinanceHelper.categoryCashFlowFromActivitiesAmountField(cashFlowFromActivitiesAmountField, newItem, keyNo);
  }

  @TraceLog({ throwError: true, spanType: 3, spanName: 'detailAnalyzeForRelated' })
  async detailAnalyzeForRelated(esHitDetails: any[], dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    return this.relatedAnalyzeHelper.detailAnalyzeForRelated(esHitDetails, dimension, params);
  }

  /**
   * 判断近n个月，实际控制人合计增持或减持是否超过x%
   * @param holderRoleField 角色字段
   * @param shareChangeStatusField 变更趋势字段 (1增加, 0减少)
   * @param changeThresholdField 变更比例阈值字段
   * @param allPeriodList 近n个月的数据列表
   * @param keyNo 企业keyNo
   */
  public async calculatePeriodHolderRoleChangeThreshold(
    holderRoleField: DimensionHitStrategyFieldsEntity,
    shareChangeStatusField: DimensionHitStrategyFieldsEntity,
    changeThresholdField: DimensionHitStrategyFieldsEntity,
    allPeriodList: any[],
    keyNo: string,
  ): Promise<boolean> {
    return this.companyShareHelper.calculatePeriodHolderRoleChangeThreshold(
      holderRoleField,
      shareChangeStatusField,
      changeThresholdField,
      allPeriodList,
      keyNo,
    );
  }

  /**
   * 72 成员变更
   * 判断是否是PEVC 机构进来融资
   * @param isPEVCField
   * @param newItem
   * @param keyNo
   */
  async category72isPEVCField(isPEVCField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    return this.companyShareHelper.category72isPEVCField(isPEVCField, newItem, keyNo);
  }

  async category72periodShareRatioChangeField(
    periodShareRatioChangeField: DimensionHitStrategyFieldsEntity,
    item: any,
    keyNo: string,
    allPeriodList: any[],
    actualControllerKeyNo: string,
  ) {
    return this.companyShareHelper.category72periodShareRatioChangeField(periodShareRatioChangeField, item, keyNo, allPeriodList, actualControllerKeyNo);
  }

  /**
   * 72 成员变更，变更趋势
   * @param shareChangeStatusField
   * @param newItem
   * @param keyNoHits
   */
  category72ShareChangeStatusField(shareChangeStatusField: DimensionHitStrategyFieldsEntity, newItem: any, keyNoHits: any[]) {
    return this.companyShareHelper.category72ShareChangeStatusField(shareChangeStatusField, newItem, keyNoHits);
  }

  /**
   * 72成员变更
   * 判断是否是实际控制人的变更
   * @param holderRoleField
   * @param newItem
   * @param keyNo
   *   // PartInfo  D 股份下降， H 股份上升，F 股份新增
   */
  public async category72holderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, newItem: any, keyNo: string) {
    return this.companyShareHelper.category72holderRoleField(holderRoleField, newItem, keyNo);
  }

  /**
   * 判断是否是实际控制人
   * @param holderRoleField
   * @param item
   */
  async category114holderRoleField(holderRoleField: DimensionHitStrategyFieldsEntity, item: any, keyNo: string) {
    return this.companyShareHelper.category114holderRoleField(holderRoleField, item, keyNo);
  }

  /**
   * 变更趋势
   * @param shareChangeStatusField
   * @param item
   */
  async category114shareChangeStatusField(shareChangeStatusField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyShareHelper.category114shareChangeStatusField(shareChangeStatusField, item);
  }

  /**
   * 变更前持股比例
   * @param beforeContentField
   * @param newItem
   */
  category114beforeContentField(beforeContentField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyShareHelper.category114beforeContentField(beforeContentField, item);
  }

  category114afterContentField(afterContentField: DimensionHitStrategyFieldsEntity, item: any) {
    return this.companyShareHelper.category114afterContentField(afterContentField, item);
  }

  /**
   * 差值比例
   * @param differenceRatioField
   * @param holders
   * @param keyNoHits
   */
  hitDifferenceRatioField(differenceRatioField: DimensionHitStrategyFieldsEntity, holders: any[], keyNoHits: string[]) {
    return this.companyShareHelper.hitDifferenceRatioField(differenceRatioField, holders, keyNoHits);
  }

  /**
   * 绝对值占比
   ;   ;* @param absRatioField
   * @param holders
   * @param keyNoHits
   */
  hitAbsRatioField(absRatioField: DimensionHitStrategyFieldsEntity, holders: any[], keyNoHits: string[]) {
    return this.companyShareHelper.hitAbsRatioField(absRatioField, holders, keyNoHits);
  }
}
