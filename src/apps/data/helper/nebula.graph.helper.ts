import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { HttpUtilsService } from '../../../libs/config/httputils.service';
import { ConfigService } from '../../../libs/config/config.service';
import { uniq } from 'lodash';
import { RelatedPartyBasePO } from '../../../libs/model/diligence/graph/RelatedPartyBasePO';
import { BaseRelatedPartyRequest } from '../../../libs/model/company/BaseRelatedPartyRequest';
import { ALLRelatedType, RelatedRiskTypesMap } from '../../../libs/constants/related.constants';
import { RelatedPartyGroupPO, RiskType } from '../../../libs/model/diligence/graph/RelatedPartyGroupPO';
import { CompanySearchService } from '../../company/company-search.service';
import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { ESResponse } from '@kezhaozhao/search-utils';
import { Cacheable } from 'type-cacheable';

@Injectable()
export class NebulaGraphHelper {
  private readonly logger: Logger = QccLogger.getLogger(NebulaGraphHelper.name);

  constructor(
    private readonly httpUtilsService: HttpUtilsService,
    private readonly configService: ConfigService, // private readonly roverService: RoverService,
    private readonly companySearchService: CompanySearchService,
  ) {}

  public async getCompanyInfoMap(companyIds: string[]) {
    if (companyIds?.length) {
      try {
        const pageSize = 500; // 每页最大公司 ID 数量
        const resultMap = new Map<string, KysCompanyResponseDetails>();
        // 分页处理
        for (let i = 0; i < companyIds.length; i += pageSize) {
          const currentIds = companyIds.slice(i, i + pageSize); // 当前页的公司 ID
          const requestPayload = Object.assign(new KysCompanySearchRequest(), {
            includeFields: ['id', 'address'],
            filter: { ids: currentIds },
            pageSize: currentIds.length,
            pageIndex: 1, // 固定为第一页，因为当前切片已实现分页
          });
          const response: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(requestPayload);
          if (response?.Result?.length) {
            response.Result.forEach((c) => {
              resultMap.set(c.id, c);
            });
          }
        }
        return Object.fromEntries(resultMap);
      } catch (e) {
        this.logger.error(`批量获取企业信息异常:`);
        this.logger.error(JSON.stringify(e));
      }
    }
    return {};
  }

  /**
   * 获取持牌机构列表
   * @param fncList
   * @param shortStatusList
   */
  public async searchFNCCompanyIds(fncList: string[], shortStatusList?: string[]): Promise<string[]> {
    //默认获取持牌机构且被注销的公司
    const kysCompanyResponseDetails = await this.companySearchService.searchFNCCompany(
      fncList?.length ? fncList : ['FNC_JR', 'FNC_BX', 'FNC_ZJ'],
      shortStatusList?.length ? shortStatusList : ['99'],
    );
    if (kysCompanyResponseDetails?.length) {
      return kysCompanyResponseDetails.map((c) => c.id);
    }
    return [];
  }

  /**
   * 获取公司关联方风险信息列表
   * @param requestParam
   * @param url
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyRelatedParties(requestParam: BaseRelatedPartyRequest, url: string): Promise<RelatedPartyGroupPO[]> {
    this.logger.info(`getCompanyRelatedParties url:${url} begin, requestParam:${JSON.stringify(requestParam)}`);
    try {
      const partnerResponse: RelatedPartyBasePO[] = await this.httpUtilsService.postRequest(this.configService.kzzServer.roverGraphService + url, requestParam);
      if (!partnerResponse?.length) {
        return [];
      }
      // relatedType映射关系生成
      const relatedTypeToEdgeTypeMap = Object.fromEntries(ALLRelatedType.map(({ label, edgeType }) => [edgeType, label]));
      const risTypeMap = Object.fromEntries(RelatedRiskTypesMap.map(({ value, label }) => [value.toString(), label]));
      // 更新 partnerResponse 数据
      partnerResponse?.forEach((p) => {
        p.relatedTypeDesc = relatedTypeToEdgeTypeMap[p.relatedType];
        p.riskTypeDesc = risTypeMap[p.riskType] || p.shortStatus;
      });
      // 处理 partnerResponse，按 companyKeynoRelated 分组
      const relationResponse = this.groupRelatedParties(partnerResponse);
      //this.logger.info(`getCompanyRelatedParties, result:${JSON.stringify(relationResponse)}`);
      return relationResponse;
    } catch (e) {
      this.logger.error(`getCompanyRelatedParties url:${url}`);
      this.logger.error(`getCompanyRelatedParties throw exception: ${e.message || e}`);
      return [];
    }
  }

  public async getLoopInvestment(depth: number, keyNo: string, url) {
    try {
      const loopResp: any[] = await this.httpUtilsService.postRequest(this.configService.kzzServer.roverGraphService + url, { depth, startCompanyId: keyNo });
      if (!loopResp.length) {
        return [];
      }
      return loopResp;
    } catch (e) {
      this.logger.error(`getLoopInvestment url:${url}`);
      this.logger.error(`getLoopInvestment throw exception: ${e.message || e}`);
      return [];
    }
  }

  /**
   * 同一个公司存在多种关系或风险按公司聚合
   * @param partnerResponse
   * @returns
   */
  private groupRelatedParties(partnerResponse: RelatedPartyBasePO[]): RelatedPartyGroupPO[] {
    const relationMap = new Map<string, RelatedPartyGroupPO>();

    uniq(partnerResponse).forEach((p) => {
      const group = relationMap.get(p.companyKeynoRelated) || new RelatedPartyGroupPO();

      if (!relationMap.has(p.companyKeynoRelated)) {
        Object.assign(group, {
          startCompanyKeyno: p.startCompanyKeyno,
          startCompanyName: p.startCompanyName,
          companyKeynoRelated: p.companyKeynoRelated,
          companyNameRelated: p.companyNameRelated,
          shortStatus: p.shortStatus,
          relatedTypeDescList: [],
          riskTypeDescList: [],
          riskTypes: [],
          relatedTypes: [],
          vids: [],
          contactList: [],
          riskTypeInfos: [],
          businessStartTime: p?.businessStartTime,
          paidCapi: p?.paidCapi,
        });
        relationMap.set(p.companyKeynoRelated, group);
      }

      group.relatedTypeDescList.push(p.relatedTypeDesc);
      group.riskTypeDescList.push(p.riskTypeDesc);
      group.riskTypes.push(p.riskType);
      group.relatedTypes.push(p.relatedType === 'AC' ? 'ActualController' : p.relatedType);
      group.vids.push(p.vid);
      group.contactList.push(p.contact);
      const riskTypeInfo = group.riskTypeInfos.find((r) => r.riskType === p.riskType);
      if (group.riskTypeInfos.length && riskTypeInfo) {
        group.riskTypeInfos.forEach((f) => {
          if (f.riskType === p.riskType && !f.vids.includes(p.vid)) {
            f.vids.push(p.vid);
          }
        });
      } else {
        if (p.riskType && p.vid) {
          group.riskTypeInfos.push(new RiskType(p.riskType, p.riskTypeDesc, p.vid));
        }
      }

      // 去重
      group.relatedTypeDescList = [...new Set(group?.relatedTypeDescList)];
      group.riskTypeDescList = [...new Set(group?.riskTypeDescList)];
      group.riskTypes = [...new Set(group?.riskTypes)];
      group.relatedTypes = [...new Set(group?.relatedTypes)];
      group.vids = [...new Set(group?.vids)];
      group.contactList = [...new Set(group?.contactList)];
    });

    return Array.from(relationMap.values());
  }
}
