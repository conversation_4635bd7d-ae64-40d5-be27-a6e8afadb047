import { Test, TestingModule } from '@nestjs/testing';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';
import { RiskChangeHelper } from './helper/risk.change.helper';
import { DataModule } from './data.module';
import { AppTestModule } from '../app/app.test.module';
import { getDimensionHitStrategyPO } from '../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import {
  AnnouncementReportType,
  BaseLineDateSelect,
  ChangeStatusMap,
  CurrencyChangeMap,
  IsBPMap,
  keyCauseActionMap,
  LayTypeMap,
  RegisCapitalTrendMap,
  ShareChangeStatusMap,
} from '../../libs/constants/risk.change.constants';
import { HitDetailsBaseQueryParams } from '../../libs/model/diligence/details/request';
import * as moment from 'moment';

jest.setTimeout(60 * 1000);
describe('test risk change service', () => {
  let riskChangeService: RiskChangeEsSource;
  let riskChangeHelper: RiskChangeHelper;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    riskChangeService = module.get<RiskChangeEsSource>(RiskChangeEsSource);
    riskChangeHelper = module.get<RiskChangeHelper>(RiskChangeHelper);
  });
  /**
   * 招商-监控动态模型
   */
  it('【主要人员变更】-企业法人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    //时间改为固定起止时间
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '984fe65f9001905cfdce0fff590d3956';
    const companyName = '驻马店市稳盈设备租赁有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [39],
        options: [{ value: 39, label: '企业法人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1, 2, 3, 4, 5, 6, 7, 9],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【主要人员变更分析】-发生总经理变动', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    //时间改为固定起止时间
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '04f6826ce7ba8e0bd452140639a4c5a7';
    const companyName = '深圳市鹏宸宇电子有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [46],
        options: [{ value: 46, label: '主要成员' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.compChangeRole,
        fieldValue: [2],
        options: [{ value: 2, label: '总经理' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【主要人员变更分析】-发行人在1个自然年度内发生三分之一以上董事变动', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(0, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1767196799;
    const companyId = 'b471a883933c4e533fe14aa1beddbcc6';
    const companyName = '湖南世晨物资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [46],
        options: [{ value: 46, label: '主要成员' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.compChangeRole,
        fieldValue: [3],
        options: [{ value: 3, label: '董事' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.baselineDate,
        fieldValue: [1],
        options: BaseLineDateSelect,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 变更占比 > 30%
        fieldKey: DimensionFieldKeyEnums.changeThreshold,
        fieldValue: [33],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【主要人员变更分析】-发行人在1个自然年度内发生三分之二以上监事变动', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(0, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1767196799;
    const companyId = 'b471a883933c4e533fe14aa1beddbcc6';
    const companyName = '湖南世晨物资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [46],
        options: [{ value: 46, label: '主要成员' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.compChangeRole,
        fieldValue: [4],
        options: [{ value: 4, label: '监事' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.baselineDate,
        fieldValue: [1],
        options: BaseLineDateSelect,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        // 变更占比 > 66%
        fieldKey: DimensionFieldKeyEnums.changeThreshold,
        fieldValue: [66],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【主要人员变更分析】-发生董事长变动', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'b2297f768399c9c6057426d3b4042b71';
    const companyName = '诏安县诏发贸易有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [46],
        options: [{ value: 46, label: '主要成员' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.compChangeRole,
        fieldValue: [1],
        options: [{ value: 1, label: '董事长' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股东变更】-企业股东股份变更', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '135f7604a0e0b34e58bf6af3fc4b30d8';
    const companyName = '陕西金御智通物联科技发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [44],
        options: [{ value: 44, label: '股东股份变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【股东变更】-企业大股东变更', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '9726d92752c802149c057b223ff9429c';
    const companyName = '德清恒海新能源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [24],
        options: [{ value: 24, label: '大股东变动' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【控股股东变更分析】-股权结构发生重大变化', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'edaea768cee4248a822a5d07393b2745';
    const companyName = '南昌贝欧特医疗科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [68],
        options: [{ value: 68, label: '持股比例变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [2],
        options: IsBPMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【控股股东变更分析】-控股股东及前后持股比例', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '8cc032fb66ef26e8837f54ed7ea1861d';
    const companyName = '潍坊德佳节能科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [68],
        options: [{ value: 68, label: '持股比例变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [1],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      /*{
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },*/
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实际控制人变更】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '1c481abe582c4bbb50e1eb94fe3d4081';
    const companyName = '安徽全鑫胜特种设备销售有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [25],
        options: [{ value: 25, label: '实际控制人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【受益所有人】', async () => {
    // const startTime = moment().subtract(2, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '892ce53ca389cdd15e7c481c5649083c';
    const companyName = '上海涵麒科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [114],
        options: [{ value: 114, label: '受益所有人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【对外投资变更】-撤出对外投资', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '1163cecd830ac957bc2186ed1dd3d3bc';
    const companyName = '无锡市蠡湖低碳研究有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17],
        options: [{ value: 17, label: '对外投资' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [2],
        options: ChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【对外投资变更】-成为大股东', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'e7f52ca590352357548a64588b5a71ab';
    const companyName = '云南泽仁企业管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17],
        options: [{ value: 17, label: '对外投资' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.afterContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [1],
        options: IsBPMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【对外投资变更】-不再是大股东', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'e5ed432ceeeec735233abd3f728b3aaf';
    const companyName = '成都奇智铭泰企业管理合伙企业';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17],
        options: [{ value: 17, label: '对外投资' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [2],
        options: ChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isBP,
        fieldValue: [2],
        options: IsBPMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【对外投资变更】-所持股份减少', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1767196799;
    const companyId = 'e87987ec3b4c9c2b2ce5ea2ee437329b';
    const companyName = '深圳市三联盛科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [68],
        options: [{ value: 68, label: '股比变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [0],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【对外投资变更】-所持股份上升', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '5e3e90d3f6bb30b7d5fbfd43b9c2e741';
    const companyName = '河南星善新能源科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [68],
        options: [{ value: 68, label: '股比变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
        fieldValue: [1],
        options: ShareChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【对外投资变更】-新增对外投资', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '8716574fa79d6ba0204c8c32a1cbb339';
    const companyName = '北京明德盛元储能科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17],
        options: [{ value: 17, label: '对外投资' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【注册资本变更】- 1个自然年度内拟减少注册资本超过其原注册资本5%', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(0, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1767196799;
    const companyId = 'ecaae0715c5af5f9151d563389236c7e';
    const companyName = '河池市康达食品配送有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        fieldValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: 5,
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: {
              label: '占比',
              value: { unit: '%', min: 0, max: 100 },
            },
            valuePeriodThreSholdCompareType: {
              label: '占比比较(大于/小于)',
              value: DimensionFieldCompareTypeEnums.GreaterThan,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【注册资本变更】-注册资本减少且减幅超50%', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '80c83c9a22924db4d6ecb341037ede69';
    const companyName = '广西柳州市螺小苑餐饮管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.currencyChange,
        fieldValue: [0],
        options: CurrencyChangeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
        fieldValue: [1],
        options: RegisCapitalTrendMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.regisCapitalChangeRatio,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【注册资本变更】- 注册资本减少且减幅在50%内', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '8e15fa1c039ec6ff01babb2e17b4682e';
    const companyName = '贵州微尔古德文化传媒有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.currencyChange,
        fieldValue: [0],
        options: CurrencyChangeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
        fieldValue: [1],
        options: RegisCapitalTrendMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.regisCapitalChangeRatio,
        fieldValue: [50],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // TODO
  /*it('【注册资本变更】- 注册资本币种变更', async () => {
    const startTime = moment().subtract(1, 'year').startOf('year').unix();
    const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const companyId = '';
    const companyName = '';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [37],
        options: [{ value: 37, label: '注册资本' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.currencyChange,
        fieldValue: [1],
        options: CurrencyChangeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });*/

  it('【经营范围变更】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '0de9726e8b91f356ec1f063efa6ec9b4';
    const companyName = '山西金烨金属制品有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [41],
        options: [{ value: 41, label: '经营范围' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【经营状态变更】-清算,吊销,责令关闭', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '9dbb834ca2202bf4a21d68c231a0b619';
    const companyName = '沈阳铧氨图文设计有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [38],
        options: [{ value: 38, label: '经营状态' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessStatus,
        fieldValue: [40, 90, 85],
        options: [
          { label: '清算', value: 40 },
          { label: '吊销', value: 90 },
          { label: '责令关闭', value: 85 },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【经营状态变更】-停业；注销；撤销；歇业', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '25af8750138008617e46d8e7fe03e17a';
    const companyName = '邯山区我爱我家商贸店';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [38],
        options: [{ value: 38, label: '经营状态' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessStatus,
        fieldValue: [70, 99, 80, 75],
        options: [
          { label: '停业', value: 70 },
          { label: '注销', value: 99 },
          { label: '撤销', value: 80 },
          { label: '歇业', value: 75 },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【经营状态变更】-迁入,迁出', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'bf706fd249f1b85196ed6fc9e7674672';
    const companyName = '崇川区酱出食品经营部';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [38],
        options: [{ value: 38, label: '经营状态' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessStatus,
        fieldValue: [50, 60],
        options: [
          { label: '迁入', value: 50 },
          { label: '迁出', value: 60 },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【企业名称变更】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'fd55294ecdee42b0be4eb61a021115a9';
    const companyName = '山西晋宝望农业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [60],
        options: [{ value: 60, label: '企业名称变更' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【企业注册地址变更】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '880495e902ef46b8bb5117524d95815d';
    const companyName = '山西晋通银能源有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [40],
        options: [{ value: 40, label: '企业地址' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【企业注册经营地址变更】', async () => {
    const id = 'a6878a160df93deaf1612e0199570065';
    const companyId = '16617c9e9e66f1792aac0f9349d45dcd';
    const companyName = '厦门久唐禾帆电子商务有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [139],
        options: [{ value: 139, label: '经营地址' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【企业类型变更】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '7e0a69f360f44906b4cfae1d881fd9c5';
    const companyName = '贵州瑞力斯通装备制造有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [42],
        options: [{ value: 42, label: '企业类型' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【新增控股子公司】', async () => {
    const endTime = 1742268258; // 3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'f72ff7ad9d477c129454026c2298ab15';
    const companyName = '上海有问企业管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.timePeriod,
        fieldValue: [3],
        options: [{ unit: '月', min: 1, max: 12 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.thresholdCount,
        fieldValue: [3], // 超过3个
        options: [{ unit: '个', min: 1, max: 50 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [17],
        options: [{ value: 17, label: '对外投资' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.changeStatus,
        fieldValue: [1],
        options: ChangeStatusMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.beforeContent,
        fieldValue: [0],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(5);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(5);
  });

  it('【失信被执行人】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'c58bef0097f039f0d958a7ede5d95aa7';
    const companyName = '鹿象集团（天津）运营管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [2],
        options: [{ value: 2, label: '失信被执行人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(17);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(17);
  });

  it('【被执行人】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'e07d6f062cf1673467eb4c9fa69321b0';
    const companyName = '贵州装呗装饰工程有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [2],
        options: [{ value: 2, label: '失信被执行人' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【限制高消费】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'd95e0a422028ad97b24d5d4f88bc54fa';
    const companyName = '偃师市恒祥化工设备有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [55],
        options: [{ value: 55, label: '限制高消费' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.restricterType,
        fieldValue: [1],
        options: [
          { value: 1, label: '企业本身' },
          //{ value: 2, label: '法人代表' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【终本案件】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '857f51447584932cf33e1a9833f0680c';
    const companyName = '泛美（大连）置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [56],
        options: [{ value: 56, label: '终本案件' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(3);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(3);
  });

  it('【裁判文书】- 被告,被上诉人,被申请人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '4b15e0d4e0a7534ff20ab775b1df9c76';
    const companyName = '安徽金鹏地产有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '裁判文书' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【裁判文书】- 关键案由', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = '490b7fb3a0f962a92a0065ab07cda63c';
    const companyName = '上海农村商业银行股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '裁判文书' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [],
        options: keyCauseActionMap,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【裁判文书】- 原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'cb9d2636aafbb3c351637ff1b181ca3a';
    const companyName = '琼中黎族苗族自治县农村信用合作联社股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [4],
        options: [{ value: 4, label: '裁判文书' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(288);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(288);
  });

  it('【立案信息】- 被告,被上诉人,被申请人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '30e19c67f604aaa61693d0473a4c9dbb';
    const companyName = '江苏博大新材料科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '49' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【立案信息】-公诉人,原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '886c713c8b75ed1e01166a170f7d2ba1';
    const companyName = '辽宁农村商业银行股份有限公司昌图支行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '49' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(208);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(208);
  });

  it('【立案信息】- 关键案由', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = 'd79a96bcb1bce1eeef19d32289d3ad29';
    const companyName = '中国建设银行股份有限公司连云港分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [49],
        options: [{ value: 49, label: '立案信息' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [],
        options: keyCauseActionMap,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告】- 被告,被上诉人,被申请人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '5c58320cb156325fb8def6814b3f834e';
    const companyName = '万宁协盛置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(7);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(7);
  });

  it('【开庭公告】- 原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '402fdc157d9f2f1e8c5b390a6419eef3';
    const companyName = '广州心泽服饰有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【开庭公告】- 关键案由', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = '2a7f5702eb5384b8783f72c189b58efc';
    const companyName = '平安银行股份有限公司厦门分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [18],
        options: [{ value: 18, label: '开庭公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [],
        options: keyCauseActionMap,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【法院公告】- 被告,被上诉人,被申请人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'f3825d9f05a1f4b965f5b4e5ac9d6ea8';
    const companyName = '重庆华宇盛瑞房地产开发有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【法院公告】- 原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '7be02211ecded3df42aff2415e41e4ad';
    const companyName = '深圳市志健实业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【法院公告】- 关键案由', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = '8ed41ab23860840a793a6b4683af0896';
    const companyName = '中国建设银行股份有限公司烟台分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [7],
        options: [{ value: 7, label: '法院公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [],
        options: keyCauseActionMap,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【送达公告】- 被告,被上诉人,被申请人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'e6b0f68e313e895ad4cfeaf5f00db37a';
    const companyName = '深圳市京兰物业发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27],
        options: [{ value: 27, label: '送达公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【送达公告】-原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'aec48459a6be548f0ad50e609d8be9fe';
    const companyName = '博野县富鑫商贸有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [27],
        options: [{ value: 27, label: '送达公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【诉前调解】- 被告,被上诉人,被申请人', async () => {
    const id = '85213e6c2e492e0206031439d0bcc132';
    const companyId = '5bff37cf1bc16f615699a281d84941e2';
    const companyName = '中国人民财产保险股份有限公司兴城支公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90],
        options: [{ value: 90, label: '诉前调解' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['21', '23', '24'],
        options: [
          { label: '被告', value: '21' },
          { label: '被上诉人', value: '23' },
          { label: '被申请人', value: '24' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【诉前调解】- 公诉人,原告,上诉人,申请人,第三人,其他当事人', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'be48e7902b728c907893cab68a432d90';
    const companyName = '中国太平洋财产保险股份有限公司四川分公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90],
        options: [{ value: 90, label: '诉前调解' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.judicialRole,
        fieldValue: ['11', '13', '14', '91', '99'],
        options: [
          { label: '原告', value: '11' },
          { label: '上诉人', value: '13' },
          { label: '申请人', value: '14' },
          { label: '第三人', value: '91' },
          { label: '其他', value: '99' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(9);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(9);
  });

  it('【诉前调解】- 关键案由', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1600704000;
    const endTime = 1739345075;
    const companyId = 'b684a6e356a1582d743e7a75f762a4a3';
    const companyName = '广发银行股份有限公司辽阳分行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [90],
        options: [{ value: 90, label: '诉前调解' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.CaseReasonType,
        fieldValue: [],
        options: keyCauseActionMap,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【破产重整】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = '0e2a62c5215065380bc14a4613c1e572';
    const companyName = '江苏诚建商砼有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [58],
        options: [{ value: 58, label: '破产重整' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权冻结】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = 'a8b4a490338bfa92eaa7825b805842f9';
    const companyName = '奥园集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityFreezeScope,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '企业股权被冻结' },
          { value: 2, label: '持有股权被冻结' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【司法拍卖】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'g90ec101420b5af8a73133fa4ce83c45';
    const companyName = '河南省郑州市新密市超化镇圣帝庙村村民委员会';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [57],
        options: [{ value: 57, label: '司法拍卖' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.auctionType,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '破产拍卖' },
          { value: 2, label: '司法拍卖' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.listingPrice,
        fieldValue: [],
        options: [{ unit: '元', min: 0, max: 99999999, label: '起拍价（元）' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(105);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(105);
  });

  it('【询价评估】- 资产被询价评估', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = 'f940333674a10cdeffb2367d2a6d2d13';
    const companyName = '驻马店市凤凰置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [59],
        options: [{ value: 59, label: '询价评估' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.evaluationPrice,
        fieldValue: [],
        options: [{ unit: '元', min: 0, max: 99999999, label: '询价结果（元）' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【询价评估】- 资产选定询价评估机构', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '4c5de88e86df13fab87c68e600625498';
    const companyName = '烟台市百舸苑置业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [76],
        options: [{ value: 76, label: '询价评估-机构' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(6);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(6);
  });

  it('【限制出境】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '2076877a2bf196eeb748fed91ac25e99';
    const companyName = '上海柚苗体育发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [91],
        options: [{ value: 91, label: '限制出境' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【公安通告】', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '8f8a0a77481e21c5e33be5fca5aaae9b';
    const companyName = '瓮安县聚福小额贷款有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [109],
        options: [{ value: 109, label: '公安通告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【行政处罚】-责令关闭', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1733554701;
    const endTime = 1741330701;
    const companyId = '633438dfb53da7d49258107280d13fda';
    const companyName = '惠东县阿婆角佳遇音乐餐吧（个体工商户）';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 1,
        fieldValue: [1],
        options: [{ label: 1, value: '税务局' }],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0908', '0911', '0915'],
        options: [
          { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
          { label: '责令关闭', value: '0911', esCode: 'A011' },
          { label: '移送司法机关', value: '0915', esCode: 'A015' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【行政处罚】-责令停产停业', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'cefed2e165b2f0115ed75e2186d1817d';
    const companyName = '上海禹工供应链管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 1,
        fieldValue: [1],
        options: [{ label: 1, value: '税务局' }],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0910', '0914', '0913'],
        options: [
          { label: '责令停产停业', value: '0910', esCode: 'A010' },
          { label: '行政拘留', value: '0914', esCode: 'A014' },
          { label: '限制从业', value: '0913', esCode: 'A013' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【行政处罚】-罚款', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '0e2a62c5215065380bc14a4613c1e572';
    const companyName = '江苏诚建商砼有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 1,
        fieldValue: [1],
        options: [{ label: 1, value: '税务局' }],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
        options: [
          { label: '罚款', value: '0903', esCode: 'A003' },
          { label: '没收违法所得', value: '0904', esCode: 'A004' },
          { label: '警告', value: '0901', esCode: 'A001' },
          { label: '通报批评', value: '0902', esCode: 'A002' },
          { label: '没收非法财物', value: '0905', esCode: 'A005' },
          { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
          { label: '降低资质等级', value: '0907', esCode: 'A007' },
          { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【行政处罚】-不予处罚', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1735660800;
    const endTime = 1741334397;
    const companyId = '35d4bf9c63193629ae2268c849d31b9f';
    const companyName = '赣州经济技术开发区尚美艺美容美发店';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.penaltyUnit,
        accessScope: 1,
        fieldValue: [1],
        options: [{ label: 1, value: '税务局' }],
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [107],
        options: [{ value: 107, label: '行政处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0916', '0999'],
        options: [
          { label: '其他行政处罚', value: '0999', esCode: 'A099' },
          { label: '不予处罚', value: '0916', esCode: 'A016' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【经营异常】-公示信息隐瞒真实情况/弄虚作假', async () => {
    // const startTime = moment().startOf('year').unix();
    // const endTime = moment().unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'c1a3ece4dc425c19555de098986dc21e';
    const companyName = '河南东泉机械设备有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [11],
        options: [{ value: 11, label: '经营异常' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        fieldValue: ['0803'],
        options: [{ value: '0803', esCode: '3', label: '公示信息隐瞒真实情况/弄虚作假' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【经营异常】-登记的住所/经营场所无法联系企业； 未在规定期限公示年度报告；未按规定公示企业信息； 未在登记所从事经营活动；商事主体名称不适宜；其他原因', async () => {
    // const startTime = moment().startOf('year').unix();
    // const endTime = moment().unix();
    const startTime = 1735660800;
    const endTime = 1741330827;
    const companyId = '51d568757a28e2be097feb153e8518e8';
    const companyName = '深圳市锦宏丰园艺景观绿化工程有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [11],
        options: [{ value: 11, label: '经营异常' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
        fieldValue: ['0801', '0805', '0802', '0804', '0806', '0807'],
        options: [
          { value: '0801', esCode: '1', label: '登记的住所/经营场所无法联系企业' },
          { value: '0805', esCode: '5', label: '未在规定期限公示年度报告' },
          { value: '0802', esCode: '2', label: '未按规定公示企业信息' },
          { value: '0804', esCode: '4', label: '未在登记所从事经营活动' },
          { value: '0806', esCode: '6', label: '商事主体名称不适宜' },
          { value: '0807', esCode: '7', label: '其他原因' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【严重违法】-企业被列入严重违法', async () => {
    // const startTime = moment().startOf('year').unix();
    // const endTime = moment().unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '0d02a0d18ff0c6da36ceff238d3d2ca7';
    const companyName = '江陵县毅康药店';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [20],
        options: [{ value: 20, label: '严重违法' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【环保处罚】-吊销许可证/执照，责令关闭，移送司法机关', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '483c7b197096404cef0cc18d64ec949d';
    const companyName = '宁波盛拓物流有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [22],
        options: [{ value: 22, label: '环保处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0908', '0911', '0915'],
        options: [
          { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
          { label: '责令关闭', value: '0911', esCode: 'A011' },
          { label: '移送司法机关', value: '0915', esCode: 'A015' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【环保处罚】-责令停产停业，行政拘留，限制从业', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '080eba653d209787efdf646390041f71';
    const companyName = '蒙自英茂悦达汽车销售服务有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [22],
        options: [{ value: 22, label: '环保处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0910', '0914', '0913'],
        options: [
          { label: '责令停产停业', value: '0910', esCode: 'A010' },
          { label: '行政拘留', value: '0914', esCode: 'A014' },
          { label: '限制从业', value: '0913', esCode: 'A013' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【环保处罚】-罚款，没收违法所得，警告，通报批评，没收非法财物，暂扣许可证件，降低资质等级，限制开展生产经营活动', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1733554701;
    const endTime = 1741330701;
    const companyId = '41baa068352b26a71e30396c176ab1a0';
    const companyName = '山东中景大观实业集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [22],
        options: [{ value: 22, label: '环保处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
        options: [
          { label: '罚款', value: '0903', esCode: 'A003' },
          { label: '没收违法所得', value: '0904', esCode: 'A004' },
          { label: '警告', value: '0901', esCode: 'A001' },
          { label: '通报批评', value: '0902', esCode: 'A002' },
          { label: '没收非法财物', value: '0905', esCode: 'A005' },
          { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
          { label: '降低资质等级', value: '0907', esCode: 'A007' },
          { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【环保处罚】-其他行政处罚，不予处罚', async () => {
    // const startTime = moment().subtract(3, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1733554701;
    const endTime = 1741330701;
    const companyId = '2268bc8da3432993d4fe97c843576455';
    const companyName = '河北车迪石油化工有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [22],
        options: [{ value: 22, label: '环保处罚' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.punishType,
        fieldValue: ['0916', '0999'],
        options: [
          { label: '不予处罚', value: '0916', esCode: 'A016' },
          { label: '其他行政处罚', value: '0999', esCode: 'A099' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【欠税公告】-欠税金额在500万元以上', async () => {
    const startTime = 1733554701;
    const endTime = 1741330701;
    const companyId = '917720a16d08bdde171681983391aed0';
    const companyName = '恒大地产集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [31],
        options: [{ value: 31, label: '欠税公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
        fieldValue: [[500]],
        options: [{ label: '500万元以上', value: [500] }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【欠税公告】-欠税金额在200万元至500万元之间', async () => {
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = '7536eb7e0bfb37f70cca2e6332e79934';
    const companyName = '攀枝花市运昌工贸有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [31],
        options: [{ value: 31, label: '欠税公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
        fieldValue: [[200, 500]],
        options: [{ label: '200万元-500万元', value: [200, 500] }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【欠税公告】-欠税金额在200万元以下', async () => {
    const startTime = 1733554701;
    const endTime = 1741330701;
    const companyId = '25ae0ef8f535a5254e69ab0840c8ea14';
    const companyName = '上海笃悠悠餐饮管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [31],
        options: [{ value: 31, label: '欠税公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
        fieldValue: [[0, 200]],
        options: [{ label: '200万元以下', value: [0, 200] }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【金融监管】-暂停相关业务，暂停或者限制交易权限，加入黑名单', async () => {
    const startTime = 1704038400;
    const endTime = 1741334718;
    const companyId = '86349fa7402c3bee403133200b004a06';
    const companyName = '上海佳律私募基金管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [121],
        options: [{ value: 121, label: '金融监管' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
        fieldValue: ['112', '111', '118'],
        options: [
          { label: '暂停相关业务', value: '112', esCode: 'A112' },
          { label: '暂停或者限制交易权限', value: '111', esCode: 'A111' },
          { label: '加入黑名单', value: '118', esCode: 'A118' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【金融监管】-严重警告，注销基金管理人登记', async () => {
    const startTime = 1704038400;
    const endTime = 1741334718;
    const companyId = '5fcd4a659f74a6b96f7f1e1d03e581aa';
    const companyName = '陕西旅游集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [121],
        options: [{ value: 121, label: '金融监管' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
        fieldValue: ['110', '119'],
        options: [
          { label: '严重警告', value: '110', esCode: 'A110' },
          { label: '注销基金管理人登记', value: '119', esCode: 'A119' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【金融监管】-公开谴责，监管关注，监管警示，监管函，诫勉谈话，警告，警示函，内部批评，书面警示，通报批评，认定不适当人选，责令致歉，自律管理，其他处理措施', async () => {
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = 'c78bcab8a1614a719cd671dd456a8cba';
    const companyName = '北京凯因科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [121],
        options: [{ value: 121, label: '金融监管' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
        fieldValue: ['101', '102', '104', '103', '105', '117', '106', '107', '109', '116', '108', '114', '115', '199'],
        options: [
          { label: '公开谴责', value: '101', esCode: 'A101' },
          { label: '监管关注', value: '102', esCode: 'A102' },
          { label: '监管警示', value: '104', esCode: 'A104' },
          { label: '监管函', value: '103', esCode: 'A103' },
          { label: '诫勉谈话', value: '105', esCode: 'A105' },
          { label: '警告', value: '117', esCode: 'A117' },
          { label: '警示函', value: '106', esCode: 'A106' },
          { label: '内部批评', value: '107', esCode: 'A107' },
          { label: '书面警示', value: '109', esCode: 'A109' },
          { label: '通报批评', value: '116', esCode: 'A116' },
          { label: '认定不适当人选', value: '108', esCode: 'A108' },
          { label: '责令致歉', value: '114', esCode: 'A114' },
          { label: '自律管理', value: '115', esCode: 'A115' },
          { label: '其他处理措施', value: '199', esCode: 'A199' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【税收违法】-税务处罚，税收违法', async () => {
    const id = 'e6bd1bf8ec177e3dd3a3b042a3b7bf3b';
    const companyId = 'e44305fcc66156048ba2f366a9dcd652';
    const companyName = '西藏岷薇实业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [29],
        options: [{ value: 29, label: '税收违法' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【抽查检查】-企业被抽查检查结果为不合格', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '66259106ca8626bb40b64e49a1c0ce8f';
    const companyName = '泰安昇达农业发展有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [14],
        options: [{ value: 14, label: '抽查检查' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.inspectionResultType,
        fieldValue: [0],
        options: [{ value: 0, label: '不合格' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【双随机抽查】-企业被双随机抽查', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '7a2bd64a7b6df2a5e5e7b6917d307456';
    const companyName = '广州建设工程质量安全检测中心有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [63],
        options: [{ value: 63, label: '双随机抽查' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【未准入境】-企业相关产品被禁止入境', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '086c900220be1a822b39a9b38ff0a910';
    const companyName = '健安喜(上海)贸易有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [98],
        options: [{ value: 98, label: '未准入境' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【惩戒名单】-企业被列入惩戒名单', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '65d965d26f435026bca7f1cac470a2b0';
    const companyName = '河南鸿辉酒店有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [77],
        options: [{ value: 77, label: '惩戒名单' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【产品召回】-企业产品被召回', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = 'c7237faf4a4954616b93a2c748bd3a28';
    const companyName = '宿州盈德服装有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [78],
        options: [{ value: 78, label: '产品召回' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【食品安全】-销售/生产的产品被抽检不合格', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '0d077b578a08de1ee3eae6eab87bd5f5';
    const companyName = '江西青龙集团商厦有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [79],
        options: [{ value: 79, label: '食品安全' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.productSource,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '销售的产品' },
          { value: 2, label: '生产的产品' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【注销备案】-企业被注销备案', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '84897bc8a120809d1989a89b8ec45ab8';
    const companyName = '深圳市杰讯淼科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [61],
        options: [{ value: 61, label: '注销备案' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【简易注销】-正在进行，准予注销，准许注销', async () => {
    const startTime = 1741248318;
    const endTime = 1741334718;
    const companyId = 'b56444d9610231b86d4ca832e87eeda5';
    const companyName = '北京荣耀福瑞科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [23],
        options: [{ value: 23, label: '简易注销' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.simpleCancelType,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '正在进行' },
          { value: 2, label: '准予注销' },
          { value: 3, label: '准许注销' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【简易注销】-不予受理，已撤销', async () => {
    const startTime = 1741248318;
    const endTime = 1741334718;
    const companyId = '4599ca72bbf8cb64165ac8df0e7717d3';
    const companyName = '石家庄芸宇日化有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [23],
        options: [{ value: 23, label: '简易注销' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.simpleCancelType,
        fieldValue: [4, 5],
        options: [
          { value: 4, label: '不予受理' },
          { value: 5, label: '已撤销' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【票据违约】-企业存在票据违约', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '0941792af2d1d18a87a4b2ecb8d96e58';
    const companyName = '高碑店市隆博工程设计咨询有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [108],
        options: [{ value: 108, label: '票据违约' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【融资动态】-企业获得融资', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '3239b6bc45edcaa1738e7f7472062481';
    const companyName = '北京智束科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [28],
        options: [{ value: 28, label: '融资动态' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【企业公告】-产生企业公告', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = 'e44fa97b737ba2f6f3646b00d23fc113';
    const companyName = '广西北部湾银行股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [65, 113],
        options: [
          { value: 65, label: '企业公告' },
          { value: 113, label: '企业公告' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.announcementReportType,
        fieldValue: [],
        options: AnnouncementReportType,
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【股权出质】-总股本被出质比例在95%以上', async () => {
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgedRatioOrHolding,
        fieldValue: [95],
        options: [
          {
            unit: '%',
            min: 0,
            max: 100,
          },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);
    const item = {
      Es_Version: 999999,
      IsImportant: null,
      Category: 12,
      p1time: 1736928023,
      CreateDate: 1584892800,
      Name: '江苏赣榆农村商业银行股份有限公司',
      BeforeContent: '',
      UpdateDate: 1736927883,
      Extend1: {
        A: 80046.92,
        B: 54395.2076,
        C: 0,
      },
      GroupMd5: '4f4eb7c5d9903bd834234fec2abee6f1',
      essynctime: 1736927883,
      DataType: 1,
      p2time: 1736928023,
      essynctimedesc: '2025-01-15 15:58:03.0',
      Extend3: '1',
      Extend2: '1',
      Extend5: '',
      IsValid: 1,
      Extend4: '',
      ImportanceFlag: 0,
      ChangeStatus: 3,
      IsRK: 1,
      ObjectId: 'be495606a051cc01f34b63f9b8627c69',
      ChangeDate: 1584892800,
      AfterContent: '',
      ChangeExtend: {
        Status: '无效',
        PledgorInfo: {
          Name: '张波',
          Org: -2,
        },
        CompanyId: '067bd3802ef06c42fe216be3d16cea8f',
        Org: -2,
        Percent: '',
        RegDate: 1541088000,
        PledgeeInfo: {
          Name: '李盛志',
          Org: -2,
        },
        Name: '张波',
        CompanyName: '江苏赣榆农村商业银行股份有限公司',
        T: 1,
        T2: 1,
        PledgedAmount: '8.004692万股',
      },
      es_mysql_table: 'risk_change_list_0',
      KeyNo: '067bd3802ef06c42fe216be3d16cea8f',
      IsRisk: 0,
      Id: '974e81a6410693ce081cc910b57fae05',
      RiskLevel: 3,
    };
    const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgedRatioOrHolding);
    const isHit = riskChangeHelper.category12Field(typeField, item);
    expect(isHit).toEqual(false);
  });

  it('【股权质押】-总股本被质押比例在95以上', async () => {
    // const startTime = moment().subtract(1, 'year').startOf('year').unix();
    // const endTime = moment().subtract(1, 'year').endOf('year').unix();
    const startTime = 1704038400;
    const endTime = 1735660799;
    const companyId = '4ba8d3610f81fe61f24ead328c600270';
    const companyName = '江西中燃天然气投资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.pledgedRatioOrHolding,
        fieldValue: [95],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【动产抵押】-被担保主债权数额在0万元以上', async () => {
    const startTime = 1672502400;
    const endTime = 1735660799;
    const companyId = '0cc7b9b17ffcc37da34ca6a328d7747c';
    const companyName = '宁夏华峰化工有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [15],
        options: [{ value: 15, label: '动产抵押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.guaranteedPrincipal,
        fieldValue: [0],
        options: [{ unit: '万元', min: 0, max: 99999999 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【担保信息】-担保金额在50000万以下', async () => {
    const companyId = '83f8da049c74f6c0de382957aaaae6aa';
    const companyName = '南京寒锐钴业股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [53, 101],
        options: [
          { value: 53, label: '担保信息' },
          { value: 101, label: '担保信息' },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.guaranteeAmount,
        fieldValue: [50000],
        options: [{ unit: '万元', min: 0, max: 99999999 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【公示催告】-企业公示催告', async () => {
    const companyId = '0b79af9eae0efebf14bbb46a9329f304';
    const companyName = '十堰奥标工贸有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [51],
        options: [{ value: 51, label: '公示催告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  // ---------------------------------------------------------

  it('【资产拍卖】-企业涉及资产拍卖', async () => {
    const startTime = 1735660799;
    const endTime = 1741334718;
    const companyId = 'a86e38883b1908f3afc4cb043b34821e';
    const companyName = '江苏福慧农业科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [75],
        options: [{ value: 75, label: '资产拍卖' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.quoteResultPrice,
        fieldValue: [],
        options: [{ unit: '元', min: 0, max: 99999999, label: '起拍价（元）' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【知识产权】-质权人专利', async () => {
    // const startTime = moment().subtract(1, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1672502400;
    const endTime = 1741330443;
    const companyId = 'e09a0b2f9937a46cebad02c8c7ea9da4';
    const companyName = '上海农村商业银行股份有限公司普陀支行';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [86],
        options: [{ value: 86, label: '知识产权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.intellectualRole,
        fieldValue: [2],
        options: [{ value: 2, label: '质权人' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.intellectualType,
        fieldValue: [1],
        options: [{ value: 1, label: '专利' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(15);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(15);
  });

  it('【知识产权】-出质人商标', async () => {
    // const startTime = moment().subtract(2, 'month').unix();
    // const endTime = moment().unix();
    const startTime = 1672502400;
    const endTime = 1741330626;
    const companyId = 'baa215a808cc71e03d7a4403dd4617f7';
    const companyName = '温州喜百年品牌管理有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [86],
        options: [{ value: 86, label: '知识产权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.intellectualRole,
        fieldValue: [1],
        options: [{ value: 1, label: '出质人' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.intellectualType,
        fieldValue: [2],
        options: [{ value: 2, label: '商标' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(2);
  });

  it('【债券违约】-企业涉及债券违约', async () => {
    const companyId = '8f016356baa3b3f0f4fda050872602a6';
    const companyName = '广汇汽车服务有限责任公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [110],
        options: [{ value: 110, label: '债券违约' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【税务催报】-企业被税务催报', async () => {
    const companyId = 'db730a6c4681b3fb8124a6c59af8f5a2';
    const companyName = '红星美凯龙控股集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [130],
        options: [{ value: 130, label: '税务催报' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【税务催缴】-欠缴金额在500万元以下', async () => {
    const companyId = '5560d05bdb4210546d6bae417c820520';
    const companyName = '上海晨入物资有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [131],
        options: [{ value: 131, label: '税务催缴' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.AmountOwed,
        fieldValue: [500],
        options: [{ unit: '万元', min: 0, max: 99999999 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【减资公告】-一个自然年企业注册资本变更趋势大于50%', async () => {
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [123],
        options: [{ value: 37, label: '减资公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
        fieldValue: [
          {
            valuePeriodTrend: 1,
            valuePeriodThreShold: [[50]],
            valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
            valuePeriodBaseLine: 1,
          },
        ],
        options: [
          {
            valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
            valuePeriodThreShold: {
              label: '占比',
              value: { unit: '%', min: 0, max: 100 },
            },
            valuePeriodThreSholdCompareType: {
              label: '占比比较',
              value: DimensionFieldCompareTypeEnums.ContainsAny,
            },
            valuePeriodBaseLine: {
              label: '时间基准',
              value: BaseLineDateSelect,
            },
          },
        ],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const item = {
      ChangeExtend: {
        A: 1719417600,
        B: 1719504000,
        C: '2024-06-28 至 2024-08-12',
        D: '本公司已2024年06月27日作出减少注册资本决议, 由300.000000万元人民币元减至100.000000万元人民币元。请本公司债权人自本公告发布之日起四十五日内,与本公司联系,要求清偿债务或者提供相应的担保。',
        E: '{"Bc":"人民币","Ac":"人民币","Bm":3000000.00000000,"Am":1000000.00000000}',
        F: '关于厦门泰宏兴进出口有限公司减少注册资本的公告',
        G: 2,
      },
    };
    const item1 = {
      BeforeContent: '50万元人民币',
      AfterContent: '100万元人民币',
    };
    const item2 = {
      BeforeContent: '100万元人民币',
      AfterContent: '10万元人民币',
    };
    const baseLineItem = [item1, item2];
    const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
    const isHit = riskChangeHelper.hitPeriodRegisCapitalField123(typeField, baseLineItem, item);
    expect(isHit).toEqual(true);
  });

  it('【减资公告】-企业减资幅度小于20%', async () => {
    /*const companyIds = await riskChangeService.TestGetCompanyId([RiskChangeCategoryEnum.category123]);
    await Bluebird.map(companyIds, async (companyId: string) => {

    });*/

    const companyId = '702db97510efb3cbc4e04f5164ef9d07';
    const companyName = '无锡锡简科技有限公司';
    const id = '604f9ff513984c80e1c303ad19080ea6';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [123],
        options: [{ value: 123, label: '减资公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
        accessScope: 2,
        options: [{ label: '20%以下', value: [0, 20] }],
        fieldValue: [[0, 20]],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【减资公告】-涉及币种变更', async () => {
    const companyId = '9514dd6ea93854a8387721c7da80f87a';
    const companyName = '庆云县盛越建筑材料有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [123],
        options: [{ value: 123, label: '减资公告' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.currencyChange,
        fieldValue: [1],
        options: CurrencyChangeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThanOrEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(1);
  });

  it('【大股东及实控人新增股权出质】出质比例>5%', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'c0836da17b1dff6fd0f2a47f1c9f6c8a';
    const companyName = '吉林省稻香农业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
        fieldValue: [5],
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权出质】出质股权金额大于5000万元', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'c0836da17b1dff6fd0f2a47f1c9f6c8a';
    const companyName = '吉林省稻香农业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeAmount,
        fieldValue: [50000000],
        options: [{ unit: '元', min: 0, max: 99999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权出质】出质股份数量大于5000万股', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(5, 'year')
      .unix();
    const companyId = 'e33cb6adb7c5efc22eacd01688435537';
    const companyName = '浩宇集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeQuantity,
        fieldValue: [50000000],
        options: [{ unit: '股', min: 0, max: 99999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权出质】出质比例1%-5%', async () => {
    /*const companyIds = await riskChangeService.TestGetCompanyId([RiskChangeCategoryEnum.category12]);
    await Bluebird.map(companyIds, async (companyId: string) => {

    });*/

    const id = '74e2a9e7d1c273c09b6a801bb62850f4';
    const companyId = '1aa0990db399f9c693cc6baa9211cc39';
    const companyName = '太原市聚鑫港石化有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
        fieldValue: [1, 60], // 没数据改成60%
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Between,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  // 未找到数据
  it.skip('【大股东及实控人新增股权出质】出质比例＜1%', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = '8b468401e3168e57c16fed7748a5f9b3';
    const companyName = '广州熵能创新材料股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
        fieldValue: [1],
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权出质】新增对外出质', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'c0836da17b1dff6fd0f2a47f1c9f6c8a';
    const companyName = '吉林省稻香农业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [12],
        options: [{ value: 12, label: '股权出质' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
        fieldValue: [1],
        options: [
          { value: 1, label: '有效' },
          { value: 2, label: '无效' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权质押】占总股本比例＞10%', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = '431fc76a41913e7365d2c54c6c4ffa22';
    const companyName = '浙矿重工股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '未达预警线' },
          { value: 2, label: '已解除质押' },
          { value: 3, label: '已达预警线未达平仓线' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
        fieldValue: [10],
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权质押】质押股份数量大于5万(股)', async () => {
    const id = '77439db90fd7c41b0a161ef8d9f313af';
    const companyId = 'accd0982a25feb6b2afd432fa279e8d7';
    const companyName = '厦门光莆电子股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '未达预警线' },
          { value: 2, label: '已解除质押' },
          { value: 3, label: '已达预警线未达平仓线' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.stockPledgeQuantity,
        fieldValue: [50000],
        options: [{ unit: '股', min: 0, max: 99999999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权质押】占总股本比例 5%-10%', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'd145ec2e52fbbecc02374f93612916a4';
    const companyName = '浙江开创电气股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '未达预警线' },
          { value: 2, label: '已解除质押' },
          { value: 3, label: '已达预警线未达平仓线' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
        fieldValue: [5, 10],
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Between,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【大股东及实控人新增股权质押】占总股本比例＜5%', async () => {
    const companyId = 'accd0982a25feb6b2afd432fa279e8d7';
    const companyName = '厦门光莆电子股份有限公司';
    const id = '77439db90fd7c41b0a161ef8d9f313af';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '未达预警线' },
          { value: 2, label: '已解除质押' },
          { value: 3, label: '已达预警线未达平仓线' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
        fieldValue: [5],
        options: [{ unit: '%', min: 0, max: 100 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权质押】新增对外质押', async () => {
    const companyId = 'accd0982a25feb6b2afd432fa279e8d7';
    const companyName = '厦门光莆电子股份有限公司';
    const id = '77439db90fd7c41b0a161ef8d9f313af';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [50],
        options: [{ value: 50, label: '股权质押' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
        fieldValue: [1, 2, 3],
        options: [
          { value: 1, label: '未达预警线' },
          { value: 2, label: '已解除质押' },
          { value: 3, label: '已达预警线未达平仓线' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      id,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权冻结】冻结股权数额在5000万元以上', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = '9d997e2d5f2b108b967081c617623c76';
    const companyName = '湖南味菇坊生物科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
        fieldValue: [50000000],
        options: [{ unit: '元', min: 0, max: 99999999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权冻结】冻结股权数额在500到5000万元之间', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'fd8a804ad408aabb98cdb473c8f7cc48';
    const companyName = '山东泽成油品销售有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
        fieldValue: [5000000, 50000000],
        options: [{ unit: '元', min: 0, max: 99999999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Between,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【大股东及实控人新增股权冻结】冻结股权数额在500万元以下', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = '05cc78669c6ee085d39adb7a3ea80787';
    const companyName = '深圳市南唐盛世文化产业有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
        fieldValue: [5000000],
        options: [{ unit: '元', min: 0, max: 99999999999 }],
        //accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.LessThan,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(2);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(2);
  });

  it('【大股东及实控人新增股权冻结】新增对外冻结', async () => {
    const endTime = 1742268258; // 2025.3.18 日
    const startTime = moment(endTime * 1000)
      .subtract(1, 'year')
      .unix();
    const companyId = 'c36f92db6f09bccd7bb7d39b5a3a0058';
    const companyName = '承德金泰琪新材料科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [26],
        options: [{ value: 26, label: '股权冻结' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.holderRole,
        fieldValue: [1, 2],
        options: [
          { value: 1, label: '大股东' },
          { value: 2, label: '实际控制人' },
        ],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    dimension.dimensionFilter = {
      startTime,
      endTime,
    };
    const detail = await riskChangeService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await riskChangeService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });
});
