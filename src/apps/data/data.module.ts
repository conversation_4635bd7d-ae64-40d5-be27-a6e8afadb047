import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DiligenceExcludesEntity } from '../../libs/entities/DiligenceExcludesEntity';
import { DistributedSystemResourceEntity } from '../../libs/entities/DistributedSystemResourceEntity';
import { MonitorCompanyRelatedDailyEntity } from '../../libs/entities/MonitorCompanyRelatedDailyEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { WGCLEntity } from '../../libs/entities/WGCLEntity';
import { CompanySearchModule } from '../company/company-search.module';
import { DataExternalController } from './data.external.controller';
import { EnterpriseLibHelper } from './helper/enterprise.lib.helper';
import { MongodbSearchHelper } from './helper/mongodb.search.helper';
import { NebulaGraphHelper } from './helper/nebula.graph.helper';
import { PersonHelper } from './helper/person.helper';
import { ProHelper } from './helper/pro.helper';
import { RelatedHelper } from './helper/related.helper';
import { BankLitigationHelper } from './source/risk-change/helper/bank-litigation.helper';
import { BaseHelper } from './source/risk-change/helper/base.helper';
import { CaseReasonHelper } from './source/risk-change/helper/case-reason.helper';
import { CompanyChangeHelper } from './source/risk-change/helper/company-change.helper';
import { CompanyFinaceHelper } from './source/risk-change/helper/company-finace.helper';
import { CompanyShareHelper } from './source/risk-change/helper/company-share.helper';
import { CompanyStockHelper } from './source/risk-change/helper/company-stock.helper';
import { JudgementHelper } from './source/risk-change/helper/judgement.helper';
import { MainEmployeeHelper } from './source/risk-change/helper/main-employee.helper';
import { PenaltyHelper } from './source/risk-change/helper/penalty.helper';
import { RelatedAnalyzeHelper } from './source/risk-change/helper/related-analyze.helper';
import { RelatedDimensionHitDetailProcessor } from './source/risk-change/related-dimension-hit-detail.processor';
import { DimensionHitDetailProcessor } from './source/risk-change/dimension-hit-detail.processor';
import { RiskChangeHelper } from './helper/risk.change.helper';
import { AssertESSource } from './source/asset-es.source';
import { BidCollusiveSource } from './source/bid-collusive.source';
import { CaseSource } from './source/case.source';
import { CompanyApiSource } from './source/company-api.source';
import { CreditApiSource } from './source/credit-api.source';
import { CreditEsSource } from './source/credit-es.source';
import { EnterpriseLibApiSource } from './source/enterprise-lib-api.source';
import { JudgementSource } from './source/judgement.source';
import { NegativeNewsSource } from './source/negative-news.source';
import { OuterBlacklistSource } from './source/outer-blacklist.source';
import { OvsSanctionsBlacklistSource } from './source/ovs-sanctions-blacklist.source';
import { PledgeMergerEsSource } from './source/pledge-merger-es.source';
import { PledgeSource } from './source/pledge.source';
import { QccProApiSource } from './source/qcc-pro-api.source';
import { RelatedCompanySource } from './source/related-company.source';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';
import { SupervisePunishEsSource } from './source/supervise-punish-es.source';
import { TaxEsSource } from './source/tax-es.source';
import { TenderApiSource } from './source/tender-api.source';
import { ViolationSource } from './source/violation.source';

@Module({
  controllers: [DataExternalController],
  providers: [
    PledgeMergerEsSource,
    AssertESSource,
    CreditEsSource,
    CaseSource,
    CompanyApiSource,
    CreditApiSource,
    EnterpriseLibApiSource,
    RelatedCompanySource,
    TenderApiSource,
    NegativeNewsSource,
    JudgementSource,
    TaxEsSource,
    PledgeSource,
    RiskChangeEsSource,
    OuterBlacklistSource,
    OvsSanctionsBlacklistSource,
    SupervisePunishEsSource,
    PersonHelper,
    MongodbSearchHelper,
    NebulaGraphHelper,
    BidCollusiveSource,
    EnterpriseLibHelper,
    ViolationSource,
    QccProApiSource,
    RiskChangeHelper,
    ProHelper,
    RelatedHelper,
    // risk-change helper
    BankLitigationHelper,
    CaseReasonHelper,
    CompanyChangeHelper,
    CompanyFinaceHelper,
    CompanyShareHelper,
    CompanyStockHelper,
    JudgementHelper,
    MainEmployeeHelper,
    PenaltyHelper,
    RelatedAnalyzeHelper,
    BaseHelper,
    DimensionHitDetailProcessor,
    RelatedDimensionHitDetailProcessor,
  ],
  exports: [
    PledgeMergerEsSource,
    AssertESSource,
    CreditEsSource,
    CaseSource,
    CompanyApiSource,
    CreditApiSource,
    EnterpriseLibApiSource,
    RelatedCompanySource,
    TenderApiSource,
    NegativeNewsSource,
    JudgementSource,
    TaxEsSource,
    PledgeSource,
    RiskChangeEsSource,
    OuterBlacklistSource,
    OvsSanctionsBlacklistSource,
    SupervisePunishEsSource,
    BidCollusiveSource,
    ViolationSource,
    QccProApiSource,
    PersonHelper,
    ProHelper,
  ],
  imports: [
    CompanySearchModule,
    TypeOrmModule.forFeature([
      DiligenceExcludesEntity,
      WGCLEntity,
      MonitorGroupEntity,
      MonitorCompanyRelatedPartyEntity,
      DistributedSystemResourceEntity,
      MonitorCompanyRelatedDailyEntity,
    ]),
  ],
})
@Global()
export class DataModule {}
