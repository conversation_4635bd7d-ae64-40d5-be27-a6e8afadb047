import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Injectable } from '@nestjs/common';
import { CompanyDetailService } from 'apps/company/company-detail.service';
import { CompanySearchService } from 'apps/company/company-search.service';
import * as Bluebird from 'bluebird';
import { NegativePositiveTopicTypes } from 'libs/constants/news.constants';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { processAmountString } from 'libs/utils/utils';
import * as _ from 'lodash';
import { Logger } from 'log4js';
import { PersonHelper } from '../../../helper/person.helper';
import { BankLitigationHelper } from './bank-litigation.helper';
import { CaseReasonHelper } from './case-reason.helper';
import { CompanyChangeHelper } from './company-change.helper';
import { CompanyFinaceHelper } from './company-finace.helper';
import { CompanyShareHelper } from './company-share.helper';
import { CompanyStockHelper } from './company-stock.helper';
import { JudgementHelper } from './judgement.helper';
import { MainEmployeeHelper } from './main-employee.helper';
import { PenaltyHelper } from './penalty.helper';
import { BaseHelper } from './base.helper';

@Injectable()
export class RelatedAnalyzeHelper {
  private readonly logger: Logger = QccLogger.getLogger(RelatedAnalyzeHelper.name);

  constructor(
    private readonly personHelper: PersonHelper,
    private readonly companySearchService: CompanySearchService,
    private readonly bankLitigationHelper: BankLitigationHelper,
    private readonly caseReasonHelper: CaseReasonHelper,
    private readonly companyChangeHelper: CompanyChangeHelper,
    private readonly judgementHelper: JudgementHelper,
    private readonly mainEmployeeHelper: MainEmployeeHelper,
    private readonly penaltyHelper: PenaltyHelper,
    private readonly companyStockHelper: CompanyStockHelper,
    private readonly companyFinanceHelper: CompanyFinaceHelper,
    private readonly baseHelper: BaseHelper,
  ) {}

  @TraceLog({ throwError: true, spanType: 3, spanName: 'detailAnalyzeForRelated' })
  async detailAnalyzeForRelated(esHitDetails: any[], dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<any[]> {
    const hitData = [];
    await Bluebird.map(esHitDetails, async (itemRaw) => {
      try {
        const newItem = _.cloneDeep(itemRaw);
        // 先对item数据中的json字段做预处理
        Object.keys(newItem).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            const value = newItem[key];
            try {
              newItem[key] = value ? JSON.parse(value) : {};
            } catch (error) {
              newItem[key] = value;
            }
          }
        });
        let isHit = true;
        switch (newItem.Category) {
          // 负面/正面新闻
          case RiskChangeCategoryEnum.category62:
          case RiskChangeCategoryEnum.category66:
          case RiskChangeCategoryEnum.category67: {
            const topics = this.getDimesionTopics(dimension);
            if (topics && isHit) {
              isHit = this.negativePositiveNewsField(topics, newItem);
            }
            break;
          }
          // 法定代表人变更
          case RiskChangeCategoryEnum.category39: {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.companyChangeHelper.hitLayTypesField(layTypesField, newItem);
            }
            break;
          }
          // 主要人员变更
          case RiskChangeCategoryEnum.category46: {
            const compChangeRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.compChangeRole);
            if (compChangeRoleField && isHit) {
              isHit = this.mainEmployeeHelper.hitCompChangeRoleField(compChangeRoleField, newItem);
            }
            break;
          }
          //动产抵押
          case RiskChangeCategoryEnum.category15: {
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteedPrincipal);
            if (riskCategories && isHit) {
              isHit = this.companyChangeHelper.category15Field(riskCategories, newItem);
            }
            break;
          }
          //土地抵押
          case RiskChangeCategoryEnum.category30: {
            //校验向企业抵押 以及 抵押权人
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.landMortgageAmount);
            if (riskCategories && isHit) {
              isHit = this.companyChangeHelper.category30Field(params.keyNo, riskCategories, newItem);
            }
            break;
          }
          //担保信息
          case RiskChangeCategoryEnum.category53:
          case RiskChangeCategoryEnum.category101: {
            //校验changeInfo.T === 1 提供担保
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.guaranteeAmount);
            if (riskCategories && isHit) {
              isHit = this.companyChangeHelper.category101Field(riskCategories, newItem);
            }
            break;
          }
          //税务催缴
          case RiskChangeCategoryEnum.category131: {
            const riskCategories = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.AmountOwed);
            if (riskCategories && isHit) {
              isHit = this.companyChangeHelper.category131Field(riskCategories, newItem);
            }
            break;
          }
          //减资公告
          case RiskChangeCategoryEnum.category123: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              isHit = this.companyChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, newItem);
            }
            const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
            if (regisCapitalChangeRatioField && isHit) {
              isHit = this.companyChangeHelper.capitalReduceSelectCompareResult(regisCapitalChangeRatioField, newItem);
            }
            break;
          }
          //持股比例变更
          case RiskChangeCategoryEnum.category68: {
            const shareChangeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeStatus);
            if (shareChangeStatusField && isHit) {
              isHit = this.companyChangeHelper.hitShareChangeStatusField(shareChangeStatusField, newItem);
            }

            const shareChangeRateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareChangeRate);
            if (shareChangeRateField && isHit) {
              isHit = this.companyChangeHelper.hitShareChangeRateField(shareChangeRateField, newItem);
            }

            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = this.companyChangeHelper.hitBeforeContentField(beforeContentField, newItem);
            }

            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = this.companyChangeHelper.hitAfterContentField(afterContentField, newItem);
            }

            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              isHit = this.companyChangeHelper.hitIsBPField(isBPField, newItem);
            }

            const keyNoHits: string[] = [];
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              const { hit, hitKeyNos } = await this.companyStockHelper.hitHolderRoleField(holderRoleField, newItem.ChangeExtend.K, params.keyNo);
              isHit = hit;
              if (hitKeyNos?.length) {
                keyNoHits.push(...hitKeyNos);
              }
            }
            break;
          }
          //对外投资变更
          case RiskChangeCategoryEnum.category203:
          case RiskChangeCategoryEnum.category17: {
            const changeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeStatus);
            if (changeStatusField && isHit) {
              isHit = this.companyChangeHelper.hitChangeStatusField(changeStatusField, newItem);
            }
            const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
            if (afterContentField && isHit) {
              isHit = this.companyChangeHelper.hitAfterContentField(afterContentField, newItem);
            }
            // 企业是否在90天内注册
            /*const companyStartDateField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CompanyStartDate);
            if (companyStartDateField && isHit) {
              isHit = await this.hitCompanyStartDateField(companyStartDateField, newItem);
            }
            // 实缴异常
            const realRegistrationErrorField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.realRegistrationError);
            if (realRegistrationErrorField && isHit) {
              isHit = await this.hitRealRegistrationErrorField(realRegistrationErrorField, newItem);
            }*/
            const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
            if (isBPField && isHit) {
              isHit = this.companyChangeHelper.hitIsBPField(isBPField, newItem);
            }
            const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
            if (beforeContentField && isHit) {
              isHit = this.companyChangeHelper.hitBeforeContentField(beforeContentField, newItem);
            }
            const companySocpeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companySocpe);
            if (companySocpeField && isHit) {
              isHit = await this.hitCompanyDetail(companySocpeField, newItem);
            }
            const companyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyName);
            if (companyNameField && isHit) {
              isHit = await this.hitCompanyDetail(companyNameField, newItem);
            }
            const companyIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.companyIndustry);
            if (companyIndustryField && isHit) {
              isHit = await this.hitCompanyDetail(companyIndustryField, newItem);
            }
            const qccIndustryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.qccIndustry);
            if (qccIndustryField && isHit) {
              isHit = await this.hitCompanyDetail(qccIndustryField, newItem);
            }
            const excludeCompanyNameField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.excludeCompanyName);
            if (excludeCompanyNameField && isHit) {
              isHit = await this.hitCompanyDetail(excludeCompanyNameField, newItem);
            }

            break;
          }
          //受益人变更 ,没有数据
          /*case RiskChangeCategoryEnum.category21: {
              const beneficiaryTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beneficiaryType);
              if (beneficiaryTypeField && isHit) isHit = this.hitBeneficiaryTypeField(beneficiaryTypeField, newItem);
              break;
            }*/
          //注册资本变更
          case RiskChangeCategoryEnum.category37: {
            const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
            if (currencyChangeField && isHit) {
              isHit = this.companyChangeHelper.hitCurrencyChangeField(currencyChangeField, newItem);
            }
            const regisCapitalTrendField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalTrend);
            if (regisCapitalTrendField && isHit) {
              isHit = this.companyChangeHelper.hitRegisCapitalTrendField(regisCapitalTrendField, newItem);
            }
            const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalChangeRatio);
            if (regisCapitalChangeRatioField && isHit) {
              isHit = this.companyChangeHelper.hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField, newItem);
            }
            break;
          }
          //经营状态变更
          case RiskChangeCategoryEnum.category38: {
            const businessStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessStatus);
            if (businessStatusField && isHit) {
              isHit = this.companyChangeHelper.category38(businessStatusField, newItem);
            }
            break;
          }
          //被限制高消费
          case RiskChangeCategoryEnum.category208:
          case RiskChangeCategoryEnum.category55: {
            const restrictTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.restricterType);
            if (restrictTypeField && isHit) {
              isHit = this.baseHelper.restricterTypeField(restrictTypeField, newItem);
            }
            break;
          }
          //破产重整
          case RiskChangeCategoryEnum.category58: {
            isHit = this.companyChangeHelper.category58Field(newItem);
            break;
          }
          //裁判文书
          case RiskChangeCategoryEnum.category221:
          case RiskChangeCategoryEnum.category4: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category4(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const lawsuitAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.lawsuitAmount);
            if (lawsuitAmountField && isHit) {
              isHit = this.baseHelper.checkAmountField(lawsuitAmountField, processAmountString(newItem?.ChangeExtend?.I), 1);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField4(isBankOrFlField, newItem);
            }
            break;
          }
          //立案信息
          case RiskChangeCategoryEnum.category220:
          case RiskChangeCategoryEnum.category49: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category49(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField49(isBankOrFlField, newItem);
            }
            break;
          }
          //开庭公告
          case RiskChangeCategoryEnum.category219:
          case RiskChangeCategoryEnum.category18: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category18(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField18(isBankOrFlField, newItem);
            }
            break;
          }
          //法院公告
          case RiskChangeCategoryEnum.category218:
          case RiskChangeCategoryEnum.category7: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category7(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField7(isBankOrFlField, newItem);
            }
            break;
          }
          //送达公告
          case RiskChangeCategoryEnum.category217:
          case RiskChangeCategoryEnum.category27: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category27(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField27(isBankOrFlField, newItem);
            }
            break;
          }
          //诉前调解
          case RiskChangeCategoryEnum.category232:
          case RiskChangeCategoryEnum.category90: {
            const judicialRoleTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judicialRole);
            if (judicialRoleTypeField && isHit) {
              isHit = this.judgementHelper.category90(judicialRoleTypeField, newItem);
            }
            const caseReasonTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
            if (caseReasonTypeField && isHit) {
              isHit = this.caseReasonHelper.caseReasonTypeField(caseReasonTypeField, newItem);
            }
            const caseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
            if (caseTypeField && isHit) {
              isHit = this.caseReasonHelper.checkCaseTypeField(caseTypeField, newItem);
            }
            const isContractDisputeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isContractDispute);
            if (isContractDisputeField && isHit) {
              isHit = this.caseReasonHelper.checkContractDisputeField(isContractDisputeField, newItem);
            }
            const isFinancialReasonField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isFinancialReason);
            if (isFinancialReasonField && isHit) {
              isHit = this.caseReasonHelper.checkFinancialReasonField(isFinancialReasonField, newItem);
            }
            const isBankOrFlField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBankOrFinancialLeasing);
            if (isBankOrFlField && isHit) {
              isHit = this.bankLitigationHelper.checkBankOrFinancialLeasingField90(isBankOrFlField, newItem);
            }
            break;
          }
          // 司法拍卖
          case RiskChangeCategoryEnum.category57: {
            const auctionTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.auctionType);
            if (auctionTypeField && isHit) {
              isHit = this.baseHelper.auctionTypeField(auctionTypeField, newItem);
            }
            // 起拍价
            const limitPriceField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.listingPrice);
            if (limitPriceField && isHit) {
              isHit = this.companyStockHelper.limitPriceTypeField(limitPriceField, newItem);
            }
            break;
          }
          // 行政处罚
          case RiskChangeCategoryEnum.category238:
          case RiskChangeCategoryEnum.category107: {
            const penaltyUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyUnit);
            if (penaltyUnitField && isHit) {
              isHit = this.penaltyHelper.penaltyUnitField(penaltyUnitField, newItem);
            }
            const punishTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
            if (punishTypeField && isHit) {
              isHit = this.penaltyHelper.punishTypeField(punishTypeField, newItem);
            }
            const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishAmount);
            if (punishAmountField && isHit) {
              isHit = this.baseHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.F, 1);
            }
            const punishRedCardField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishRedCard);
            if (punishRedCardField && isHit) {
              isHit = this.penaltyHelper.penaltyRedCardFieldCategory107(punishRedCardField, newItem);
            }
            const penaltyIssuingUnitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltyIssuingUnit);
            if (penaltyIssuingUnitField && isHit) {
              isHit = this.penaltyHelper.penaltyIssuingUnitField(penaltyIssuingUnitField, newItem);
            }
            const isListedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isListed);
            if (isListedField && isHit) {
              isHit = await this.companyChangeHelper.checkListedField(this.companySearchService, isListedField, newItem, params.keyNo);
            }
            break;
          }
          // 经营异常
          case RiskChangeCategoryEnum.category11: {
            const businessAbnormalTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.businessAbnormalType);
            if (businessAbnormalTypeField && isHit) {
              isHit = this.penaltyHelper.businessAbnormalTypeField(businessAbnormalTypeField, newItem);
            }
            break;
          }
          // 环保处罚
          case RiskChangeCategoryEnum.category22: {
            const punishEnvTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.punishType);
            if (punishEnvTypeField && isHit) {
              isHit = this.penaltyHelper.punishEnvTypeField(punishEnvTypeField, newItem);
            }
            const punishAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.penaltiesAmount);
            if (punishAmountField && isHit) {
              isHit = this.baseHelper.checkAmountField(punishAmountField, newItem?.ChangeExtend?.E, 1);
            }
            break;
          }
          //欠税公告
          case RiskChangeCategoryEnum.category31: {
            const taxOwedAmountTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.taxOwedAmount);
            if (taxOwedAmountTypeField && isHit) {
              // isHit = this.taxOwedAmountTypeField(taxOwedAmountTypeField, newItem);
              isHit = this.baseHelper.checkAmountField(taxOwedAmountTypeField, newItem?.ChangeExtend?.B, 10000);
            }
            break;
          }
          //金融监管
          case RiskChangeCategoryEnum.category121: {
            const financialPenaltyCauseTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financialPenaltyCause);
            if (financialPenaltyCauseTypeField && isHit) {
              isHit = this.penaltyHelper.financialPenaltyCauseTypeField(financialPenaltyCauseTypeField, newItem);
            }
            break;
          }
          //抽查检查
          case RiskChangeCategoryEnum.category14: {
            const inspectionResultTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.inspectionResultType);
            if (inspectionResultTypeField && isHit) {
              isHit = this.penaltyHelper.inspectionResultTypeField(inspectionResultTypeField, newItem);
            }
            break;
          }
          // 知识产权
          case RiskChangeCategoryEnum.category86: {
            const intellectualRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualRole);
            if (intellectualRoleField && isHit) {
              isHit = this.baseHelper.category86IntellectualRole(intellectualRoleField, newItem);
            }
            const intellectualTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.intellectualType);
            if (intellectualTypeField && isHit) {
              isHit = this.baseHelper.category86IntellectualType(intellectualTypeField, newItem);
            }
            break;
          }
          // 未准入境
          case RiskChangeCategoryEnum.category98: {
            break;
          }
          // 产品召回
          case RiskChangeCategoryEnum.category78: {
            break;
          }
          // 食品安全
          case RiskChangeCategoryEnum.category79: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.productSource);
            if (typeField && isHit) {
              isHit = this.baseHelper.category79Field(typeField, newItem);
            }
            break;
          }
          // 注销备案
          case RiskChangeCategoryEnum.category61: {
            break;
          }
          // 简易注销
          case RiskChangeCategoryEnum.category23: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.simpleCancelType);
            if (typeField && isHit) {
              isHit = this.companyChangeHelper.category23Field(typeField, newItem);
            }
            break;
          }
          // 票据违约
          case RiskChangeCategoryEnum.category108: {
            break;
          }
          // 融资动态
          case RiskChangeCategoryEnum.category28: {
            //排除 获得融资的融资动态
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.financingDynamicType);
            if (typeField && isHit) {
              isHit = this.baseHelper.category28Field(typeField, newItem);
            }
            break;
          }
          // 企业公告65&113
          case RiskChangeCategoryEnum.category65: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementReportType);
            if (isHit && typeField) {
              isHit = this.companyStockHelper.categoryAnnouncementReportField(typeField, newItem);
            }
            break;
          }
          case RiskChangeCategoryEnum.category113: {
            // 不一致的情况，是香港企业
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.announcementReportType);
            if (isHit && typeField) {
              isHit = this.companyStockHelper.categoryAnnouncementReportField(typeField, newItem);
            }
            // 企业公告是年报的数据
            const annualReportTypeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.annualReportType);
            if (isHit && annualReportTypeField) {
              isHit = this.companyFinanceHelper.categoryAnnualReportField(annualReportTypeField, newItem);
            }
            // 企业是上市企业
            // 企业净利润总额
            const retainedProfitField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.netProfitAmount);
            if (isHit && retainedProfitField) {
              isHit = await this.companyFinanceHelper.categoryRetainedProfitField(retainedProfitField, newItem, newItem.KeyNo);
            }
            // 净利润同比
            const netProfitRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.netProfitRatio);
            if (isHit && netProfitRatioField) {
              isHit = await this.companyFinanceHelper.categoryNetProfitRatioField(netProfitRatioField, newItem, newItem.KeyNo);
            }
            // 营业收入同比
            const revenueRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.revenueRatio);
            if (isHit && revenueRatioField) {
              isHit = await this.companyFinanceHelper.categoryRevenueRatioField(revenueRatioField, newItem, newItem.KeyNo);
            }
            // 应收账款同比
            const accountsReceivableRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.AccountsReceivableRatio);
            if (isHit && accountsReceivableRatioField) {
              isHit = await this.companyFinanceHelper.categoryAccountsReceivableRatioField(accountsReceivableRatioField, newItem, newItem.KeyNo);
            }
            // 存货同比
            const inventoryRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.inventoryRatio);
            if (isHit && inventoryRatioField) {
              isHit = await this.companyFinanceHelper.categoryInventoryRatioField(inventoryRatioField, newItem, newItem.KeyNo);
            }
            // 有息负债同比
            const interestBearingLiabilitiesRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.interestBearingLiabilitiesRatio);
            if (isHit && interestBearingLiabilitiesRatioField) {
              isHit = await this.companyFinanceHelper.categoryInterestBearingLiabilitiesRatioField(
                interestBearingLiabilitiesRatioField,
                newItem,
                newItem.KeyNo,
              );
            }
            // 有息负债/年度总收入
            const ibdAnnualRevRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.ibdAnnualRevRatio);
            if (isHit && ibdAnnualRevRatioField) {
              isHit = await this.companyFinanceHelper.categoryIbdAnnualRevRatioField(ibdAnnualRevRatioField, newItem, newItem.KeyNo);
            }
            //（货币资金+交易性金融资产）/（短期借款+应付票据）
            const cmAndStbRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cmAndStbRatio);
            if (isHit && cmAndStbRatioField) {
              isHit = await this.companyFinanceHelper.categoryCmAndStbRatioField(cmAndStbRatioField, newItem, newItem.KeyNo);
            }
            // 负债合计/资产合计
            const totalLiabToAssetsRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.totalLiabToAssetsRatio);
            if (isHit && totalLiabToAssetsRatioField) {
              isHit = await this.companyFinanceHelper.categoryTotalLiabToAssetsRatioField(totalLiabToAssetsRatioField, newItem, newItem.KeyNo);
            }
            ///** 连续X年经营活动产生的现金流量净额 */
            const cashFlowFromActivitiesAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cashFlowFromActivitiesAmount);
            if (isHit && cashFlowFromActivitiesAmountField) {
              isHit = await this.companyFinanceHelper.categoryCashFlowFromActivitiesAmountField(cashFlowFromActivitiesAmountField, newItem, newItem.KeyNo);
            }
            break;
          }
          // 股权冻结
          case RiskChangeCategoryEnum.category26: {
            const equityFreezeScopeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFreezeScope);
            if (equityFreezeScopeField && isHit) {
              isHit = this.companyStockHelper.equityFreezeScopeFieldCategory26(equityFreezeScopeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.companyStockHelper.holderRoleFieldCategory26(holderRoleField, newItem, params.keyNo);
            }
            // 股权冻结金额
            const equityFrozenAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityFrozenAmount);
            if (equityFrozenAmountField && isHit) {
              isHit = this.companyStockHelper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, newItem);
            }
            break;
          }
          // 股权出质
          case RiskChangeCategoryEnum.category12:
          case RiskChangeCategoryEnum.category213: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgedRatioOrHolding);
            if (typeField && isHit) {
              isHit = this.companyStockHelper.category12Field(typeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              const employeeList = await this.personHelper.getEmployeeData(params.keyNo, 'Employees');
              isHit = await this.companyStockHelper.holderRoleFieldCategory12(holderRoleField, newItem, params.keyNo);
            }
            // 股权出质的状态
            const equityPledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeStatus);
            if (equityPledgeStatusField && isHit) {
              isHit = this.companyStockHelper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, newItem);
            }
            // 股权出质比例
            const equityPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeRatio);
            if (equityPledgeRatioField && isHit) {
              isHit = this.companyStockHelper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, newItem);
            }
            // 股权出质-出质股权数额
            const equityPledgeAmountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeAmount);
            if (equityPledgeAmountField && isHit) {
              isHit = this.companyStockHelper.equityPledgeAmountFieldCategory12(equityPledgeAmountField, newItem);
            }
            // 股权出质-质押股份数量(股)
            const equityPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityPledgeQuantity);
            if (equityPledgeQuantityField && isHit) {
              isHit = this.companyStockHelper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, newItem);
            }
            break;
          }
          // 股权质押(214-人的维度)
          case RiskChangeCategoryEnum.category214:
          case RiskChangeCategoryEnum.category50: {
            const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.pledgedRatioOrHolding);
            if (typeField && isHit) {
              isHit = this.companyStockHelper.category50Field(typeField, newItem);
            }
            // 主要人员
            const holderRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.holderRole);
            if (holderRoleField && isHit) {
              isHit = await this.companyStockHelper.holderRoleFieldCategory50(holderRoleField, newItem, params.keyNo);
            }
            // 股权质押的状态
            const sharePledgeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sharePledgeStatus);
            if (sharePledgeStatusField && isHit) {
              isHit = this.companyStockHelper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, newItem);
            }
            // 股权质押-质押占总股本比例
            const stockPledgeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeRatio);
            if (stockPledgeRatioField && isHit) {
              isHit = this.companyStockHelper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, newItem);
            }
            // 股权质押-质押股份数量(股)
            const stockPledgeQuantityField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.stockPledgeQuantity);
            if (stockPledgeQuantityField && isHit) {
              isHit = this.companyStockHelper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, newItem);
            }
            break;
          }
          // 询价评估
          case RiskChangeCategoryEnum.category59: {
            if (isHit && dimension.strategyFields) {
              const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.evaluationPrice);
              if (typeField && isHit) {
                isHit = this.companyStockHelper.category59Field(typeField, newItem);
              }
            }
            break;
          }
          // 询价评估-机构
          case RiskChangeCategoryEnum.category76: {
            break;
          }
          // 资产拍卖
          case RiskChangeCategoryEnum.category75: {
            if (isHit && dimension.strategyFields) {
              const typeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.quoteResultPrice);
              if (typeField && isHit) {
                isHit = this.companyStockHelper.category75Field(typeField, newItem);
              }
            }
            break;
          }
          default: {
            break;
          }
        }
        if (isHit) {
          hitData.push(itemRaw);
        }
      } catch (e) {
        this.logger.error(`RiskChange getDimensionDetail request: ${JSON.stringify(itemRaw)}`, e);
      }
    });
    return hitData;
  }

  private getDimesionTopics(dimension: DimensionHitStrategyPO) {
    const topicsFields = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.topics);
    if (topicsFields?.fieldValue.length) {
      if (topicsFields.fieldValue.includes('all')) {
        return NegativePositiveTopicTypes.filter((t) => t.value !== 'all').map((t) => t.value);
      } else {
        return topicsFields?.fieldValue;
      }
    }
    return [];
  }

  public negativePositiveNewsField(topics: string[], newItem: any) {
    let hit = false;
    // 判断主题是否匹配
    const topicSourceValue = newItem?.ChangeExtend?.newTags;
    //如果主题为空，则不命中
    if (topicSourceValue || !topics?.length) {
      // 判断新闻主题是否在策略定义的主题中
      hit = topicSourceValue?.every((item) => topics.includes(item));
    }
    return hit;
  }

  private async hitCompanyDetail(typeField: DimensionHitStrategyFieldsEntity, item: any) {
    let hit = false;
    const changeInfo = item.ChangeExtend;
    const companyName = changeInfo?.A;
    const companyKeyNo = changeInfo?.K;
    switch (typeField.dimensionFieldKey) {
      case DimensionFieldKeyEnums.excludeCompanyName: {
        if (companyName && typeField.fieldValue) {
          if (typeField.fieldValue.some((keyword) => companyName.includes(keyword))) {
            hit = false;
          } else {
            hit = true;
          }
        }
        break;
      }
      case DimensionFieldKeyEnums.companyName: {
        if (companyName && typeField.fieldValue) {
          hit = typeField.fieldValue.some((keyword) => companyName.includes(keyword));
        }
        break;
      }
      case DimensionFieldKeyEnums.companySocpe: {
        if (companyKeyNo && typeField.fieldValue) {
          const { Scope } = await this.companySearchService.companyDetailsQcc(companyKeyNo, ['Scope', 'IndustryV3']);
          hit = typeField.fieldValue.some((keyword) => Scope.includes(keyword));
        }
        break;
      }
      case DimensionFieldKeyEnums.qccIndustry: {
        if (companyKeyNo && typeField.fieldValue) {
          const { QccIndustry } = await this.companySearchService.companyDetailsQcc(companyKeyNo, ['Scope', 'QccIndustry']);
          if (QccIndustry) {
            /* {
               "Ac": "32",
               "An": "公用事业",
               "Bc": "3203",
               "Bn": "水务业",
               "Cc": "320302",
               "Cn": "污水处理及再生利用",
               "Dc": "32030201",
               "Dn": "污水处理及再生利用"
             }*/
            hit = typeField.fieldValue.some((qccValue) => {
              const qccValueArr = qccValue.split('-');
              if (qccValueArr.length === 3) {
                return QccIndustry['Ac'] == qccValueArr[0] && QccIndustry['Bc'] == qccValueArr[1] && QccIndustry['Cc'] == qccValueArr[2];
              } else if (qccValueArr.length === 2) {
                return QccIndustry['Ac'] == qccValueArr[0] && QccIndustry['Bc'] == qccValueArr[1];
              } else {
                return QccIndustry['Ac'] == qccValueArr[0];
              }
            });
          }
        }
        break;
      }
      case DimensionFieldKeyEnums.companyIndustry: {
        if (companyKeyNo && typeField.fieldValue) {
          const { IndustryV3 } = await this.companySearchService.companyDetailsQcc(companyKeyNo, ['Scope', 'IndustryV3']);
          // "IndustryV3": {
          //   "IndustryCode": "K",
          //   "Industry": "房地产业",
          //   "SubIndustryCode": "70",
          //   "SubIndustry": "房地产业",
          //   "MiddleCategoryCode": "701",
          //   "MiddleCategory": "房地产开发经营",
          //   "SmallCategoryCode": "7010",
          //   "SmallCategory": "房地产开发经营"
          // },

          if (IndustryV3['IndustryCode'] == 'K') {
            console.log(IndustryV3);
          }

          hit = typeField.fieldValue.some((industryValue) => {
            const industryValueArr = industryValue.split('-');
            if (industryValueArr.length === 3) {
              return (
                IndustryV3['IndustryCode'] == industryValueArr[0] &&
                IndustryV3['SubIndustryCode'] == industryValueArr[1] &&
                IndustryV3['MiddleCategoryCode'] == industryValueArr[2]
              );
            } else if (industryValueArr.length === 2) {
              return IndustryV3['IndustryCode'] == industryValueArr[0] && IndustryV3['SubIndustryCode'] == industryValueArr[1];
            } else {
              return IndustryV3['IndustryCode'] == industryValueArr[0];
            }
          });
        }
        break;
      }
      default:
        break;
    }
    return hit;
  }
}
