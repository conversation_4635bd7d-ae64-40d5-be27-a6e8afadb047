import { Client } from '@elastic/elasticsearch';
import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { ConfigService } from 'libs/config/config.service';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { EsOperator } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { RiskChangeAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { getCompareResult, getStartTimeByCycle } from 'libs/utils/diligence/diligence.utils';
import { getDimensionDescription, processDimHitResPO } from 'libs/utils/diligence/dimension.utils';
import { chunk, find, flatten, orderBy, pick, uniq } from 'lodash';
import { CompanySearchService } from '../../../company/company-search.service';
import { CaseReasonHelper } from './helper/case-reason.helper';
import { RelatedDimensionHitDetailProcessor } from './related-dimension-hit-detail.processor';
import { DimensionHitDetailProcessor } from './dimension-hit-detail.processor';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { getRiskListDesc } from '../../risk.copy.from.c/risk';
import { BaseEsAnalyzeService } from '../base-es-analyze.service';

export const RelateRiskChangeDimensions = [
  DimensionTypeEnums.RecentInvestCancellationsRiskChange,
  DimensionTypeEnums.ActualControllerRiskChange,
  DimensionTypeEnums.ListedEntityRiskChange,
];

/**
 * 风险动态数据源
 */
@Injectable()
export class RiskChangeEsSource extends BaseEsAnalyzeService {
  constructor(
    configService: ConfigService,
    private readonly riskChangeHelper: RiskChangeHelper,
    protected readonly companySearchService: CompanySearchService,
    private readonly dimensionHitDetailProcessor: DimensionHitDetailProcessor,
    private readonly relatedDimensionHitDetailsProcessor: RelatedDimensionHitDetailProcessor,
    private readonly caseReasonHelper: CaseReasonHelper,
  ) {
    super(
      'RiskChangeService',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
    this.dimensionHitDetailProcessor.bindRiskChangeEsSearchFn(this.searchEs.bind(this));
    this.relatedDimensionHitDetailsProcessor.bindRiskChangeEsSearchFn(this.searchEs.bind(this));
  }

  /**
   * 分析当前数据源所有维度命中清空
   * 先通过一个聚合查询es ，判断当前数据源多个维度的命中情况
   * 如果有命中 RiskChange 维度，有些属性需要二次再判断，
   * 最后再匹配 命中记录条数 属性
   * @param companyId
   * @param dimensionHitStrategyPOs
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    // 关联方风险动态维度单独处理处理
    const relatedRiskChangeDims = dimensionHitStrategyPOs.filter((d) => RelateRiskChangeDimensions.includes(d.key));
    const analyzeResult: DimensionHitResultPO[] = await this.relatedDimensionHitDetailsProcessor.processAnalyze(relatedRiskChangeDims, companyId);

    // 非关联方风险动态
    const dimHitStrategyPOs = dimensionHitStrategyPOs.filter((d) => !RelateRiskChangeDimensions.includes(d.key));
    if (!dimHitStrategyPOs?.length) {
      return analyzeResult;
    }
    // 先通过一个聚合查询es ，判断当前数据源多个维度的命中情况
    const dimHitRes = await super.analyze(companyId, dimHitStrategyPOs);
    if (dimHitRes?.length > 0) {
      await Bluebird.map(dimHitRes, async (dimHit: DimensionHitResultPO) => {
        const dimension = dimensionHitStrategyPOs.find((po) => po.strategyId === dimHit.strategyId);
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        let hitCount = 0;
        // 如果有命中 RiskChange 维度，有些属性需要通过 getDimensionDetail二次再判断，
        if (DimensionTypeEnums.RiskChange == dimension.key || DimensionTypeEnums.MainInfoUpdateCapitalChange === dimension.key) {
          const res = await this.getDimensionDetail(dimension, {
            keyNo: companyId,
            pageIndex: 1,
            pageSize: dimHit.totalHits,
          });
          if (res?.Paging.TotalRecords) {
            hitCount = res?.Paging.TotalRecords || 0;
          }

          // 再过滤命中记录条数
          const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
          if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
            hitCount = 0;
          }
        } else {
          hitCount = dimHit.totalHits;
        }
        if (hitCount > 0) {
          dimHit.totalHits = hitCount;
          if (dimension.template && desData) {
            // 返回描述处理
            desData['name'] = dimension.strategyName;
            dimHit.description = getDimensionDescription(dimension.template, Object.assign(desData, { count: hitCount }));
          }
          analyzeResult.push(dimHit);
          // return dimHit;
        }
        // return null;
      });
    } else {
      await Bluebird.map(dimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
        // 不命中是否提示
        const isShowTipField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.isShowTip);
        if (isShowTipField && isShowTipField.fieldValue[0] === 1) {
          const desData = {
            isHidden: '',
            isHiddenY: '',
          };
          const dimHitResPO = processDimHitResPO(d, 0, desData);
          analyzeResult.push(dimHitResPO);
        }
      });
    }

    return analyzeResult;
  }

  /**
   * 风险动态维度详情
   * @param dimension
   * @param params
   * @param analyzeParams
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (RelateRiskChangeDimensions.includes(dimension.key)) {
      return this.relatedDimensionHitDetailsProcessor.fetchHits(dimension, params);
    }

    if (DimensionTypeEnums.RiskChange == dimension.key) {
      const resp = await super.getDimensionDetail(
        dimension,
        Object.assign({}, params, { pageIndex: 1, pageSize: 1000, field: 'CreateDate', order: 'DESC' }),
        analyzeParams,
      );
      // 针对每条风险动态详情再做分析判断是否命中
      const hitData = await this.dimensionHitDetailProcessor.fetchHits(resp, dimension, params);

      // 内存分页
      const response = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      response.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      response.Result = sortedData.slice(start, end);
      if (response.Result.length > 0) {
        const changeExtendMap = {};
        response.Result.forEach((item) => {
          const { ChangeExtend, Id } = item;
          if (!changeExtendMap[Id]) {
            changeExtendMap[Id] = [];
          }
          changeExtendMap[Id].push(ChangeExtend);
        });
        // 返回数据结构处理
        response.Result = response.Result.map((d) => getRiskListDesc(d));
        // 暂不处理找不到caseId，C端查的是mongo我们没法查
        await this.caseReasonHelper.getCaseTitleDescData(response.Result, false, changeExtendMap);
      }
      return response;
    }

    if (DimensionTypeEnums.MainInfoUpdateCapitalChange == dimension.key) {
      const resp = await super.getDimensionDetail(
        dimension,
        Object.assign({}, params, { pageIndex: 1, pageSize: 1000, field: 'CreateDate', order: 'DESC' }),
        analyzeParams,
      );
      const hitData = [];
      if (resp?.Result?.length) {
        const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
        if (strategyFieldByKey) {
          const hit = this.riskChangeHelper.hitMainInfoUpdateCapitalChange(strategyFieldByKey, resp?.Result);
          if (hit) {
            hitData.push(resp?.Result[0]);
          }
        }
      }

      // 内存分页
      const response = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;
      response.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      response.Result = sortedData.slice(start, end);
      return response;
    }
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  protected async getDetailFromEs(companyId: string, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams) {
    const { pageIndex, pageSize } = params;

    const query = await this.getDimensionQuery(companyId, dimension);
    query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });

    // 查询企业本身风险
    query.bool.filter.push({ term: { KeyNo: companyId } });

    const sort = {};

    const dimSortFiled = dimension.getSortField();
    if (params?.field) {
      sort[params.field] = params?.order || 'DESC';
    } else if (dimSortFiled?.field) {
      sort[dimSortFiled.field] = dimSortFiled.order;
    }

    const dimensionFilter = dimension?.dimensionFilter;
    // 监控单位时间内的数据
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      const range = {
        CreateDate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      query.bool.filter.push({ range });
    }
    // 传动态Id
    if (dimensionFilter?.id) {
      query.bool.filter.push({ term: { Id: dimensionFilter.id } });
    }

    const response = await this.searchEs(
      {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort,
        query,
      },
      companyId,
    );
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    // 数据处理
    if (resp?.Result?.length) {
      resp.Result = resp.Result.map((it) => {
        const changeExt = it.ChangeExtend ? JSON.parse(it.ChangeExtend) : {};
        switch (dimension.key) {
          case DimensionTypeEnums.MainInfoUpdateCapitalChange:
          case DimensionTypeEnums.RiskChange:
            return it;
          case DimensionTypeEnums.SecurityNotice:
            return {
              name: changeExt.A,
              reason: changeExt.H,
              publishUnit: changeExt.D,
              publishDate: changeExt.C,
              isValid: changeExt.IsValid,
              keyNo: it.KeyNo,
              riskId: it.Id,
              id: changeExt.J,
            };
          case DimensionTypeEnums.CapitalReduction:
            return {
              Id: it.Id,
              DecideDate: changeExt.B,
              Content: changeExt.D,
              NoticeDate: changeExt.A,
              NoticePeriod: changeExt.C,
              NoticeTitle: changeExt.F,
              Name: it.Name,
              KeyNo: it.KeyNo,
            };
          // case DimensionTypeEnums.MainInfoUpdateLegalPerson:
          case DimensionTypeEnums.MainInfoUpdateHolder:
          case DimensionTypeEnums.MainInfoUpdatePerson:
          case DimensionTypeEnums.MainInfoUpdateBeneficiary: {
            return pick(it, [
              'GroupId',
              'Id',
              'KeyNo',
              'Name',
              'RiskLevel',
              'DataType',
              'Category',
              'BeforeContent',
              'AfterContent',
              'ChangeExtend',
              'ObjectId',
              'ChangeStatus',
              'ChangeDate',
              'CreateDate',
              'DetailCount',
              'DisplayList',
              'Extend1',
              'Extend4',
              'MaxLevel',
              'Extend3',
              'Extend2',
              'RKDetailCount',
              'IsImportant',
              'ImportantCount',
              'RelatedInfo',
            ]);
          }
          default:
            return changeExt;
        }
      });
    }
    return resp;
  }

  protected async getQuery(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<object> {
    const query = {
      bool: {
        filter: [],
        should: [],
      },
    };
    query.bool.filter.push({ term: { KeyNo: companyId } });
    query.bool.filter.push({ range: { Es_Version: { lt: 999999 } } });
    const dimensionFilter = DimensionHitStrategyPOs[0]?.dimensionFilter;
    // 监控单位时间内的数据
    if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
      const range = {
        CreateDate: {
          gte: Math.ceil(dimensionFilter?.startTime),
          lte: Math.ceil(dimensionFilter?.endTime),
        },
      };
      query.bool.filter.push({ range });
    }
    // 传动态Id
    if (dimensionFilter?.id) {
      query.bool.filter.push({ term: { Id: dimensionFilter.id } });
    }
    // 时间范围过滤
    const cycle = DimensionHitStrategyPOs[0].getCycle();
    if (cycle && cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      query.bool.filter.push({ range: { CreateDate: { gte: Math.ceil(timestamp / 1000) } } });
    }

    const dimRiskChangeCategories = [];
    await Bluebird.map(DimensionHitStrategyPOs, async (dimension) => {
      // RiskChange维度在监控中 由于会按着 category 分成很多个维度，每个维度独立的should的查询语句会把cpu拉的太高， 所以需要把 RiskChange 维度的 category 过滤出来拼成一个 query
      if (dimension.key === DimensionTypeEnums.RiskChange) {
        const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
        if (riskCategoriesParams) {
          dimRiskChangeCategories.push(...riskCategoriesParams.fieldValue);
        }
      } else {
        const dimQuery = await this.getDimensionQuery(companyId, dimension);
        if (dimQuery) {
          query.bool.should.push(dimQuery);
        }
      }
    });

    // 在填充 riskCategories 数组后，使用 Lodash 进行打平和去重
    const flattenedAndUniqueRiskCategories = uniq(flatten(dimRiskChangeCategories));

    // RiskChange 维度处理逻辑
    if (flattenedAndUniqueRiskCategories.length > 0) {
      const chunks = chunk(flattenedAndUniqueRiskCategories, 16);
      await Bluebird.map(chunks, async (chunk) => {
        const subBool = {
          filter: [],
        };
        subBool.filter.push({ term: { IsValid: 1 } });
        subBool.filter.push({ terms: { Category: chunk } });
        query.bool.should.push({ bool: subBool });
      });
    }

    if (query.bool.should.length > 0) {
      query.bool['minimum_should_match'] = 1;
    }

    return query;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO) {
    const subBool = {
      filter: [],
    };
    const categoryMappings = {
      MainInfoUpdateLegalPerson: 39,
      MainInfoUpdateHolder: 24,
      MainInfoUpdatePerson: 25,
      MainInfoUpdateBeneficiary: 114,
    };
    const cycle = dimension.getCycle();
    if (cycle && cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { CreateDate: { gte: Math.ceil(timestamp / 1000) } } });
    }
    const isValidParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
    if (isValidParams && Number(isValidParams.fieldValue[0]) >= 0) {
      subBool.filter.push({ term: { IsValid: Number(isValidParams.fieldValue[0]) } });
    }
    switch (dimension.key) {
      case DimensionTypeEnums.RiskChange: {
        const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
        if (riskCategoriesParams) {
          // const compareType = riskCategoriesParams.compareType;//当前默认为包含任一
          const riskCategories = riskCategoriesParams.fieldValue;
          subBool.filter.push({ terms: { Category: riskCategories } });
        }
        break;
      }
      case DimensionTypeEnums.SecurityNotice: {
        subBool.filter.push({ term: { Category: 109 } });

        // if (dimension.strategyModel?.cycle > 0) {
        //   const timestamp = getStartTimeByCycle(dimension.strategyModel.cycle);
        //   subBool.filter.push({ range: { CreateDate: { gte: Math.ceil(timestamp / 1000) } } });
        // }
        // const isValidParams = dimension.strategyModel?.detailsParams?.find((it) => it.field === DimensionFieldKeyEnums.isValid);
        // if (isValidParams && Number(isValidParams.fieldVal) >= 0) {
        //   subBool.filter.push({ term: { IsValid: Number(isValidParams.fieldVal) } });
        // }
        break;
      }
      case DimensionTypeEnums.CapitalReduction: {
        subBool.filter.push({ term: { Category: 123 } });
        subBool.filter.push({ term: { IsValid: 1 } });

        break;
      }
      // case DimensionTypeEnums.MainInfoUpdateLegalPerson:
      case DimensionTypeEnums.MainInfoUpdateHolder:
      case DimensionTypeEnums.MainInfoUpdatePerson:
      case DimensionTypeEnums.MainInfoUpdateBeneficiary: {
        subBool.filter.push({ term: { IsValid: 1 } });
        subBool.filter.push({ term: { IsRK: 1 } });
        subBool.filter.push({ term: { Category: categoryMappings[dimension.key] } });
        break;
      }
      //减资es查询
      case DimensionTypeEnums.MainInfoUpdateCapitalChange: {
        const naturalCycleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.naturalCycle);
        if (naturalCycleField) {
          const naturalCycle = naturalCycleField ? (naturalCycleField.fieldValue?.[0] as number) : 0;
          const naturalOperator = naturalCycleField ? EsOperator[naturalCycleField.compareType] : 'gte';
          const naturalTimestamp = getStartTimeByCycle(naturalCycle);
          subBool.filter.push({ range: { CreateDate: { [naturalOperator]: Math.ceil(naturalTimestamp / 1000) } } });
        }
        subBool.filter.push({ term: { Category: 37 } });
        break;
      }
      default: {
        this.logger.error('unreachable code');
      }
    }
    return { bool: subBool };
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]) {
    const aggs: any = {};
    const dimRiskChangeCategories = [];
    await Bluebird?.map(DimensionHitStrategyPOs, async (dimension) => {
      if (dimension.key === DimensionTypeEnums.RiskChange) {
        const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
        if (riskCategoriesParams) {
          dimRiskChangeCategories.push(...riskCategoriesParams.fieldValue);
        }
      } else {
        const dimQuery = await this.getDimensionQuery(companyId, dimension);
        if (dimQuery) {
          const aggsName = `${this.bucketNamePrefix}${dimension.strategyId}`;
          aggs[aggsName] = {
            filter: dimQuery,
            aggs: {},
          };
        }
      }
    });
    if (dimRiskChangeCategories.length > 0) {
      const aggsName = `${this.bucketNamePrefix}${DimensionTypeEnums.RiskChange}`;
      aggs[aggsName] = {
        terms: {
          field: 'Category',
          size: dimRiskChangeCategories.length,
        },
      };
    }
    return aggs;
  }

  protected processAggs(aggObj: any): RiskChangeAggBucketItemPO[] {
    const bucketData: RiskChangeAggBucketItemPO[] = [];

    Object.keys(aggObj).forEach((bucketName) => {
      const dimensionType = bucketName.replace(this.bucketNamePrefix, '');
      const bucket = aggObj[bucketName];
      if (dimensionType == DimensionTypeEnums.RiskChange) {
        if (bucket?.buckets?.length > 0) {
          bucket.buckets.forEach((bucket) => {
            const hitCount = bucket['doc_count'];
            if (hitCount > 0) {
              const res: RiskChangeAggBucketItemPO = {
                dimensionType: `${DimensionTypeEnums.RiskChange}_${bucket['key']}`,
                hitCount,
              };
              bucketData.push(res);
            }
          });
        }
      } else {
        const hitCount = bucket['doc_count'];
        if (hitCount > 0) {
          const res: RiskChangeAggBucketItemPO = {
            dimensionType,
            hitCount,
          };

          if (bucket?.top_docs?.hits?.length > 0) {
            res.hitsInfo = bucket.top_docs.hits.map((hit) => hit['_source']);
          }
          bucketData.push(res);
        }
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: RiskChangeAggBucketItemPO[], dimensionDefinitionPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    const categoryHits = {};

    const newBucketData = bucketData
      .map((item: RiskChangeAggBucketItemPO) => {
        if (item.dimensionType.startsWith(DimensionTypeEnums.RiskChange)) {
          const category = item.dimensionType.replace(DimensionTypeEnums.RiskChange + '_', '');
          categoryHits[category] = item.hitCount;
          return null;
        } else {
          return item;
        }
      })
      .filter((t) => t);

    if (Object.keys(categoryHits).length > 0) {
      dimensionDefinitionPOs.forEach((dimension) => {
        if (dimension.key === DimensionTypeEnums.RiskChange) {
          const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
          if (riskCategoriesParams?.fieldValue?.length > 0) {
            const categories = riskCategoriesParams.fieldValue;
            const hitCount = Object.keys(categoryHits).reduce((acc, cur) => {
              if (categories.includes(+cur)) {
                return acc + categoryHits[cur];
              }
              return acc;
            }, 0);
            if (hitCount > 0) {
              const dimensionType = dimension.strategyId + '';
              newBucketData.push({
                dimensionType,
                hitCount,
              });
            }
          }
        }
      });
    }

    const hitResult = newBucketData
      .map((item: RiskChangeAggBucketItemPO) => {
        const d: DimensionHitStrategyPO = find(dimensionDefinitionPOs, { strategyId: +item.dimensionType });
        if (!d.key) {
          return null;
        }
        const { hitCount } = item;

        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, {
            isHidden: '',
            isHiddenY: '',
          });
        }
        return null;
      })
      .filter((t) => t);

    return hitResult;
  }

  /**
   * 单元测试调试方法(用于获取具体的es数据，且具有随机性，仅供调试使用)
   * @param dimension
   * @param Id
   */
  public async getDimensionDetail4UnitTest(dimension: DimensionHitStrategyPO, Id?: string) {
    const riskCategoriesParams = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.riskCategories);
    const subBool = {
      filter: [],
    };
    subBool.filter.push({ term: { IsValid: 1 } });
    if (riskCategoriesParams) {
      const riskCategories = riskCategoriesParams.fieldValue;
      subBool.filter.push({ terms: { Category: riskCategories } });
    }
    if (Id) {
      subBool.filter.push({ term: { Id: Id } });
    }
    const body = {
      from: 0,
      size: 1,
      track_total_hits: true,
      sort: [
        {
          CreateDate: {
            order: 'ASC',
          },
        },
      ],
      query: {
        bool: subBool,
      },
    };
    try {
      const response = await this.searchEs(body, null);
      const resp = Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: 1,
          PageIndex: 1,
          TotalRecords: response?.body?.hits?.total?.value || 0,
        },
        Result:
          response?.body?.hits?.hits?.map((d) =>
            Object.assign(d._source, { ChangeExtend: d._source.ChangeExtend ? JSON.parse(d._source.ChangeExtend) : {} }),
          ) || [],
      });
      return resp;
    } catch (e) {
      this.logger.error(e);
      throw e;
    }
  }
}
