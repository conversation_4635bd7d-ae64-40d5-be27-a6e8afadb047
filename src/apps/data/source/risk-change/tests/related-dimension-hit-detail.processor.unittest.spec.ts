import { Test, TestingModule } from '@nestjs/testing';
import { RelatedDimensionHitDetailProcessor } from '../related-dimension-hit-detail.processor';
import { PersonHelper } from '../../helper/person.helper';
import { CompanyDetailService } from '../../../company/company-detail.service';
import { CompanySearchService } from '../../../company/company-search.service';
import { DimensionHitDetailProcessor } from '../dimension-hit-detail.processor';
import { RiskChangeHelper } from '../../helper/risk.change.helper';
import { CaseReasonHelper } from '../helper/case-reason.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

describe('RelatedDimensionHitDetailProcessor 单元测试', () => {
  let processor: RelatedDimensionHitDetailProcessor;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockCompanyDetailService: jest.Mocked<CompanyDetailService>;
  let mockCompanySearchService: jest.Mocked<CompanySearchService>;
  let mockRiskChangeHitDetailAnalyzer: jest.Mocked<DimensionHitDetailProcessor>;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockCaseReasonHelper: jest.Mocked<CaseReasonHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockPersonHelper = {
      getPersonData: jest.fn(),
      getActualControllerList: jest.fn(),
    } as any;

    mockCompanyDetailService = {
      getCompanyDetail: jest.fn(),
    } as any;

    mockCompanySearchService = {
      getInvestCompanyList: jest.fn(),
      getListedEntityList: jest.fn(),
    } as any;

    mockRiskChangeHitDetailAnalyzer = {
      fetchHits: jest.fn(),
    } as any;

    mockRiskChangeHelper = {
      hitMainInfoUpdateCapitalChange: jest.fn(),
    } as any;

    mockCaseReasonHelper = {
      checkCaseTypeField: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatedDimensionHitDetailProcessor,
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: CompanyDetailService, useValue: mockCompanyDetailService },
        { provide: CompanySearchService, useValue: mockCompanySearchService },
        { provide: DimensionHitDetailProcessor, useValue: mockRiskChangeHitDetailAnalyzer },
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: CaseReasonHelper, useValue: mockCaseReasonHelper },
      ],
    }).compile();

    processor = module.get<RelatedDimensionHitDetailProcessor>(RelatedDimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processAnalyze 方法测试', () => {
    it('应该正确处理空的关联方维度列表', async () => {
      // Arrange
      const relatedRiskChangeDims: DimensionHitStrategyPO[] = [];
      const companyId = 'test-company-id';

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toEqual([]);
    });

    it('应该正确处理实际控制人风险变更维度', async () => {
      // Arrange
      const dimension = createTestDimensionStrategy(DimensionTypeEnums.ActualControllerRiskChange);

      const relatedRiskChangeDims = [dimension];
      const companyId = 'test-company-id';

      // Mock fetchHits 返回结果
      const mockFetchResponse = new HitDetailsBaseResponse();
      mockFetchResponse.Paging = {
        PageSize: 1,
        PageIndex: 1,
        TotalRecords: 2,
      };
      mockFetchResponse.Result = [
        { id: 'result1', companyName: '关联公司1' },
        { id: 'result2', companyName: '关联公司2' },
      ];

      jest.spyOn(processor, 'fetchHits').mockResolvedValue(mockFetchResponse);

      // Act
      const result = await processor.processAnalyze(relatedRiskChangeDims, companyId);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].dimensionKey).toBe(DimensionTypeEnums.ActualControllerRiskChange);
      expect(result[0].totalHits).toBe(2);
      expect(result[0].description).toContain('实际控制人风险变更');
    });

    // 注意：更复杂的测试需要等待引入文件错误修复后再添加
  });
});
