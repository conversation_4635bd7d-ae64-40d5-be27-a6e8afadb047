import { Test, TestingModule } from '@nestjs/testing';
import { PenaltyHelper } from '../../helper/penalty.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';

// Mock external dependencies
jest.mock('libs/utils/diligence/diligence.utils', () => ({
  getCompareResult: jest.fn(),
  getCompareResultForArray: jest.fn(),
}));

jest.mock('libs/utils/utils', () => ({
  containsKeywords: jest.fn(),
}));

jest.mock('libs/constants/punish.constants', () => ({
  BusinessAbnormalType: [
    { value: '0801', esCode: '1', label: '登记的住所/经营场所无法联系企业' },
    { value: '0802', esCode: '2', label: '未按规定公示企业信息' },
    { value: '0803', esCode: '3', label: '公示信息隐瞒真实情况/弄虚作假' },
  ],
  FinancialSupervisionType: [
    { value: '1001', esCode: 'F001', label: '违规放贷' },
    { value: '1002', esCode: 'F002', label: '资金违规使用' },
    { value: '1003', esCode: 'F003', label: '内控制度不完善' },
  ],
  PenaltiesType: [
    { label: '警告', value: '0901', esCode: 'A001' },
    { label: '罚款', value: '0903', esCode: 'A003' },
    { label: '没收违法所得', value: '0904', esCode: 'A004' },
    { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
  ],
}));

jest.mock('libs/constants/risk.change.constants', () => ({
  excludePenaltyResult107Map: [
    { label: '免予处罚', value: 1 },
    { label: '不予处罚', value: 2 },
  ],
  excludePenaltyResult22Map: [
    { label: '免予处罚', value: 1 },
    { label: '不予处罚', value: 2 },
  ],
  InspectionResultTypeMap: [
    { value: 0, label: '合格' },
    { value: 1, label: '不合格' },
    { value: 2, label: '基本合格' },
  ],
  penaltyReason107Map: [
    { label: '弄虚作假', value: 1 },
    { label: '虚假材料', value: 2 },
    { label: '吊销排污许可证', value: 3 },
  ],
  penaltyReason22Map: [
    { label: '未取得排污许可证', value: 1 },
    { label: '伪造排污许可证', value: 2 },
    { label: '弄虚作假', value: 3 },
  ],
  penaltyResult107Map: [
    { label: '责令停产停业', value: 1 },
    { label: '罚款处理', value: 2 },
  ],
  penaltyResult22Map: [
    { label: '吊销排污许可证', value: 1 },
    { label: '弄虚作假', value: 2 },
    { label: '虚假材料', value: 3 },
  ],
  PenaltyUnitType: [
    { value: 1, label: '税务局' },
    { value: 2, label: '市场监督管理局' },
    { value: 3, label: '街道办事处' },
    { value: 4, label: '体育局' },
    { value: 5, label: '交通运输局' },
  ],
}));

import { getCompareResult, getCompareResultForArray } from 'libs/utils/diligence/diligence.utils';
import { containsKeywords } from 'libs/utils/utils';

describe('PenaltyHelper 单元测试', () => {
  let helper: PenaltyHelper;
  let mockGetCompareResult: jest.MockedFunction<typeof getCompareResult>;
  let mockGetCompareResultForArray: jest.MockedFunction<typeof getCompareResultForArray>;
  let mockContainsKeywords: jest.MockedFunction<typeof containsKeywords>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PenaltyHelper],
    }).compile();

    helper = module.get<PenaltyHelper>(PenaltyHelper);

    mockGetCompareResult = getCompareResult as jest.MockedFunction<typeof getCompareResult>;
    mockGetCompareResultForArray = getCompareResultForArray as jest.MockedFunction<typeof getCompareResultForArray>;
    mockContainsKeywords = containsKeywords as jest.MockedFunction<typeof containsKeywords>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('penaltyRedCardFieldCategory107 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;
    });

    it('应该正确命中B字段包含处罚原因关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '弄虚作假相关内容',
          C: null,
        },
      };

      mockContainsKeywords.mockReturnValueOnce(true).mockReturnValueOnce(false);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledWith('弄虚作假相关内容', ['弄虚作假', '虚假材料', '吊销排污许可证']);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });

    it('应该正确命中C字段包含处罚结果关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: null,
          C: '责令停产停业处理',
        },
      };

      mockContainsKeywords.mockReturnValueOnce(false).mockReturnValueOnce(true);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledWith('责令停产停业处理', ['责令停产停业', '罚款处理'], ['免予处罚', '不予处罚']);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });

    it('应该正确命中B和C字段都包含关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '弄虚作假相关内容',
          C: '责令停产停业处理',
        },
      };

      mockContainsKeywords.mockReturnValue(true);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });

    it('应该正确处理B和C字段都不包含关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '其他内容',
          C: '其他处理结果',
        },
      };

      mockContainsKeywords.mockReturnValue(false);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理B字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: null,
          C: '责令停产停业处理',
        },
      };

      mockContainsKeywords.mockReturnValueOnce(true);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledTimes(1);
    });

    it('应该正确处理C字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '弄虚作假相关内容',
          C: null,
        },
      };

      mockContainsKeywords.mockReturnValueOnce(true);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledTimes(1);
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockContainsKeywords).not.toHaveBeenCalled();
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理item为空的情况', () => {
      // Arrange
      const item = null;

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockContainsKeywords).not.toHaveBeenCalled();
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          B: '弄虚作假相关内容',
          C: '责令停产停业处理',
        },
      };

      mockContainsKeywords.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理getCompareResult返回false的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '弄虚作假相关内容',
          C: '责令停产停业处理',
        },
      };

      mockContainsKeywords.mockReturnValue(true);
      mockGetCompareResult.mockReturnValue(false);

      // Act
      const result = helper.penaltyRedCardFieldCategory107(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });
  });

  describe('penaltyRedCardFieldCategory22 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1];
      field.compareType = DimensionFieldCompareTypeEnums.Equal;
    });

    it('应该正确命中A字段包含处罚原因关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: '未取得排污许可证相关内容',
          B: null,
        },
      };

      mockContainsKeywords.mockReturnValueOnce(true).mockReturnValueOnce(false);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory22(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledWith('未取得排污许可证相关内容', ['未取得排污许可证', '伪造排污许可证', '弄虚作假']);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });

    it('应该正确命中B字段包含处罚结果关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: null,
          B: '吊销排污许可证处理',
        },
      };

      mockContainsKeywords.mockReturnValueOnce(false).mockReturnValueOnce(true);
      mockGetCompareResult.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory22(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockContainsKeywords).toHaveBeenCalledWith('吊销排污许可证处理', ['吊销排污许可证', '弄虚作假', '虚假材料'], ['免予处罚', '不予处罚']);
      expect(mockGetCompareResult).toHaveBeenCalledWith(1, 1, DimensionFieldCompareTypeEnums.Equal);
    });

    it('应该正确处理A和B字段都不包含关键词的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: '其他内容',
          B: '其他处理结果',
        },
      };

      mockContainsKeywords.mockReturnValue(false);

      // Act
      const result = helper.penaltyRedCardFieldCategory22(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          A: '未取得排污许可证相关内容',
          B: '吊销排污许可证处理',
        },
      };

      mockContainsKeywords.mockReturnValue(true);

      // Act
      const result = helper.penaltyRedCardFieldCategory22(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.penaltyRedCardFieldCategory22(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockContainsKeywords).not.toHaveBeenCalled();
      expect(mockGetCompareResult).not.toHaveBeenCalled();
    });
  });

  describe('penaltyUnitField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中包含处罚单位的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: '税务局处罚决定书',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });

    it('应该正确处理ExceptAny比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAny;
      const item = {
        ChangeExtend: {
          A: '其他内容',
        },
      };

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ExceptAll比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAll;
      const item = {
        ChangeExtend: {
          A: '其他内容',
        },
      };

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理A字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: null,
        },
      };

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          A: '税务局处罚决定书',
        },
      };

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理getCompareResultForArray返回false的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          A: '税务局处罚决定书',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(false);

      // Act
      const result = helper.penaltyUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });
  });

  describe('penaltyIssuingUnitField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['1001', '1002'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
      field.options = [
        { value: '1001', label: '选项1' },
        { value: '1002', label: '选项2' },
        { value: '1003', label: '选项3' },
      ];
    });

    it('应该正确命中G1字段匹配options的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          G1: '1001',
          G2: null,
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['1001'], ['1001', '1002']);
    });

    it('应该正确命中G2为9907的特殊情况', () => {
      // Arrange
      field.fieldValue = ['9907'];
      const item = {
        ChangeExtend: {
          G1: null,
          G2: '9907',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['9907'], ['9907']);
    });

    it('应该正确处理G1和G2都匹配的情况', () => {
      // Arrange
      field.fieldValue = ['1001', '9907'];
      const item = {
        ChangeExtend: {
          G1: '1001',
          G2: '9907',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['1001', '9907'], ['1001', '9907']);
    });

    it('应该正确处理ExceptAny比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAny;
      const item = {
        ChangeExtend: {
          G1: '9999', // 不匹配的值
          G2: '8888', // 不匹配的值
        },
      };

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ExceptAll比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAll;
      const item = {
        ChangeExtend: {
          G1: '9999', // 不匹配的值
          G2: '8888', // 不匹配的值
        },
      };

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理G1字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          G1: null,
          G2: null,
        },
      };

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          G1: '1001',
          G2: null,
        },
      };

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为null的情况', () => {
      // Arrange
      field.fieldValue = null;
      const item = {
        ChangeExtend: {
          G1: '1001',
          G2: null,
        },
      };

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理G1为数字类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          G1: 1001, // 数字类型
          G2: null,
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyIssuingUnitField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['1001'], ['1001', '1002']);
    });
  });

  describe('penaltyUnitField31 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
      field.options = [
        { value: 1, label: '税务局' },
        { value: 2, label: '市场监督管理局' },
        { value: 3, label: '街道办事处' },
      ];
    });

    it('应该正确命中D字段包含处罚单位的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: '税务局处罚决定书',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyUnitField31(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });

    it('应该正确处理ExceptAny比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAny;
      const item = {
        ChangeExtend: {
          D: '其他内容',
        },
      };

      // Act
      const result = helper.penaltyUnitField31(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理D字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: null,
        },
      };

      // Act
      const result = helper.penaltyUnitField31(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.penaltyUnitField31(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('penaltyUnitField117 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
      field.options = [
        { value: '1', label: '税务局' },
        { value: '2', label: '市场监督管理局' },
        { value: '3', label: '街道办事处' },
      ];
    });

    it('应该正确命中B字段包含处罚单位的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '税务局处罚决定书',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyUnitField117(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });

    it('应该正确处理ExceptAny比较类型且源值为空的情况', () => {
      // Arrange
      field.compareType = DimensionFieldCompareTypeEnums.ExceptAny;
      const item = {
        ChangeExtend: {
          B: '其他内容',
        },
      };

      // Act
      const result = helper.penaltyUnitField117(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理B字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: null,
        },
      };

      // Act
      const result = helper.penaltyUnitField117(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理value转换为数字的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          B: '税务局处罚决定书',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.penaltyUnitField117(field, item);

      // Assert
      expect(result).toBe(true);
      // 验证Number(t.value)的转换
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });
  });

  describe('punishTypeField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['0901', '0903'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中L字段包含处罚类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          L: 'A001相关内容A003其他',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.punishTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['0901', '0903'], ['0901', '0903']);
    });

    it('应该正确处理L字段不包含处罚类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          L: '其他内容',
        },
      };

      // Act
      const result = helper.punishTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理L字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          L: null,
        },
      };

      // Act
      const result = helper.punishTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          L: 'A001相关内容',
        },
      };

      // Act
      const result = helper.punishTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.punishTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('businessAbnormalTypeField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['0801', '0802'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中D.key字段包含异常类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: {
            key: '1相关内容2其他',
          },
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.businessAbnormalTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['0801', '0802'], ['0801', '0802']);
    });

    it('应该正确处理D.key字段不包含异常类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: {
            key: '其他内容',
          },
        },
      };

      // Act
      const result = helper.businessAbnormalTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理D.key字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: {
            key: null,
          },
        },
      };

      // Act
      const result = helper.businessAbnormalTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理D字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          D: null,
        },
      };

      // Act
      const result = helper.businessAbnormalTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.businessAbnormalTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('punishEnvTypeField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['0901', '0903'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中H字段包含环保处罚类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          H: 'A001相关内容A003其他',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.punishEnvTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['0901', '0903'], ['0901', '0903']);
    });

    it('应该正确处理H字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          H: null,
        },
      };

      // Act
      const result = helper.punishEnvTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('financialPenaltyCauseTypeField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = ['1001', '1002'];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中I字段包含金融监管处罚原因的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          I: 'F001相关内容F002其他',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.financialPenaltyCauseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['1001', '1002'], ['1001', '1002']);
    });

    it('应该正确处理重复值去重的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          I: 'F001相关内容F001重复F002其他',
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.financialPenaltyCauseTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      // 验证去重后的结果
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, ['1001', '1002'], ['1001', '1002']);
    });

    it('应该正确处理I字段为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          I: null,
        },
      };

      // Act
      const result = helper.financialPenaltyCauseTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });

  describe('inspectionResultTypeField 方法测试', () => {
    let field: DimensionHitStrategyFieldsEntity;

    beforeEach(() => {
      field = new DimensionHitStrategyFieldsEntity();
      field.fieldValue = [1, 2];
      field.compareType = DimensionFieldCompareTypeEnums.ContainsAny;
    });

    it('应该正确命中E字段等于检查结果类型的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          E: 1, // 不合格
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [1], [1, 2]);
    });

    it('应该正确处理E字段为0的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          E: 0, // 合格
        },
      };

      mockGetCompareResultForArray.mockReturnValue(true);

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(true);
      expect(mockGetCompareResultForArray).toHaveBeenCalledWith(DimensionFieldCompareTypeEnums.ContainsAny, [0], [1, 2]);
    });

    it('应该正确处理E字段为null的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          E: null,
        },
      };

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理E字段不匹配InspectionResultTypeMap的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: {
          E: 999, // 不存在的值
        },
      };

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理fieldValue为空的情况', () => {
      // Arrange
      field.fieldValue = [];
      const item = {
        ChangeExtend: {
          E: 1,
        },
      };

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });

    it('应该正确处理ChangeExtend为空的情况', () => {
      // Arrange
      const item = {
        ChangeExtend: null,
      };

      // Act
      const result = helper.inspectionResultTypeField(field, item);

      // Assert
      expect(result).toBe(false);
      expect(mockGetCompareResultForArray).not.toHaveBeenCalled();
    });
  });
});
