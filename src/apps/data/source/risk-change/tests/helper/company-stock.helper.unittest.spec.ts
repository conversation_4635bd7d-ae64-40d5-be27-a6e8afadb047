import { Test, TestingModule } from '@nestjs/testing';
import { CompanyStockHelper } from '../../helper/company-stock.helper';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { PersonHelper } from '../../../helper/person.helper';
import { PersonData } from 'libs/model/data/source/PersonData';
// import { generateUniqueTestIds, getTestUser } from 'apps/test_utils_module/test.user'; // 保留注释，等待手工修复

describe('CompanyStockHelper 单元测试', () => {
  let helper: CompanyStockHelper;
  let mockPersonHelper: jest.Mocked<PersonHelper>;

  beforeEach(async () => {
    mockPersonHelper = {
      getPartnerList: jest.fn(),
      getFinalActualController: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanyStockHelper, { provide: PersonHelper, useValue: mockPersonHelper }],
    }).compile();

    helper = module.get<CompanyStockHelper>(CompanyStockHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('category12Field 方法测试', () => {
    it('应该正确处理股权出质占所持股份比例(T=2)', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95]; // 95%阈值
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '98.5%',
          T: 2, // 占所持股份比例
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(true); // 98.5% >= 95%
    });

    it('应该正确处理股权出质占该公司股权比例(T=1)', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [90]; // 90%阈值
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '85.5%',
          T: 1, // 占该公司股权比例
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false); // 85.5% < 90%
    });

    it('应该正确处理Percent为0的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '0',
          T: 2,
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false); // Percent为0时hasPercent为false
    });

    it('应该正确处理缺少Percent字段的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          T: 2,
          // 缺少Percent字段
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理T字段不是1或2的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '98.5%',
          T: 3, // 不是1或2
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理无效的Percent格式', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: 'invalid format',
          T: 2,
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false); // 无法解析的格式应该返回false
    });
  });

  describe('category50Field 方法测试', () => {
    it('应该正确处理股权质押比例匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [80]; // 80%阈值
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: '85.5%',
        },
      };

      // Act
      const result = helper.category50Field(typeField, item);

      // Assert
      expect(result).toBe(true); // 85.5% >= 80%
    });

    it('应该正确处理股权质押比例不匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [90]; // 90%阈值
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: '75.5%',
        },
      };

      // Act
      const result = helper.category50Field(typeField, item);

      // Assert
      expect(result).toBe(false); // 75.5% < 90%
    });

    it('应该正确处理C为0的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [80];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: '0',
        },
      };

      // Act
      const result = helper.category50Field(typeField, item);

      // Assert
      expect(result).toBe(false); // C为0时hasRatioValue为false
    });

    it('应该正确处理缺少C字段的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [80];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少C字段
        },
      };

      // Act
      const result = helper.category50Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理无效的C格式', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [80];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: 'invalid format',
        },
      };

      // Act
      const result = helper.category50Field(typeField, item);

      // Assert
      expect(result).toBe(false); // 无法解析的格式应该返回false
    });
  });

  describe('limitPriceTypeField 方法测试', () => {
    it('应该正确处理司法拍卖限额匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1000000]; // 100万限额
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: 1500000, // 150万
        },
      };

      // Act
      const result = helper.limitPriceTypeField(typeField, item);

      // Assert
      expect(result).toBe(true); // 150万 >= 100万
    });

    it('应该正确处理司法拍卖限额不匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [2000000]; // 200万限额
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: 1500000, // 150万
        },
      };

      // Act
      const result = helper.limitPriceTypeField(typeField, item);

      // Assert
      expect(result).toBe(false); // 150万 < 200万
    });

    it('应该正确处理没有设置targetValue的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = []; // 空数组，相当于没设置
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: 1500000,
        },
      };

      // Act
      const result = helper.limitPriceTypeField(typeField, item);

      // Assert
      expect(result).toBe(true); // 没设置限额时返回true
    });

    it('应该正确处理缺少C字段的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1000000];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少C字段
        },
      };

      // Act
      const result = helper.limitPriceTypeField(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少ChangeExtend的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1000000];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        // 缺少ChangeExtend
      };

      // Act
      const result = helper.limitPriceTypeField(typeField, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('categoryAnnouncementReportField 方法测试', () => {
    it('应该正确处理企业公告类型匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1, 2, 3]; // 目标公告类型
      typeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: '1,2,5', // 公告类型字符串
        },
      };

      // Act
      const result = helper.categoryAnnouncementReportField(typeField, item);

      // Assert
      expect(result).toBe(true); // 包含1和2，匹配
    });

    it('应该正确处理企业公告类型不匹配', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1, 2, 3]; // 目标公告类型
      typeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: '4,5,6', // 公告类型字符串，不匹配
        },
      };

      // Act
      const result = helper.categoryAnnouncementReportField(typeField, item);

      // Assert
      expect(result).toBe(false); // 不包含1,2,3，不匹配
    });

    it('应该正确处理没有设置targetValue的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = []; // 空数组，相当于没设置
      typeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: '1,2,3',
        },
      };

      // Act
      const result = helper.categoryAnnouncementReportField(typeField, item);

      // Assert
      expect(result).toBe(true); // 没设置时返回true
    });

    it('应该正确处理空的TS字段', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1, 2, 3];
      typeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: '', // 空字符串
        },
      };

      // Act
      const result = helper.categoryAnnouncementReportField(typeField, item);

      // Assert
      expect(result).toBe(false); // 空字符串过滤后为空数组
    });

    it('应该正确处理包含无效数字的TS字段', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1, 2, 3];
      typeField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          TS: '1,abc,2,0,3', // 包含无效数字和0
        },
      };

      // Act
      const result = helper.categoryAnnouncementReportField(typeField, item);

      // Assert
      expect(result).toBe(true); // 过滤后得到[1,2,3]，匹配
    });
  });

  describe('hitHolderRoleField 方法测试', () => {
    it('应该正确识别大股东身份', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['partner-1', 'partner-2'];
      const targetKeyNo = 'target-company';

      // Mock大股东数据
      const mockPartners = [
        Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] }),
        Object.assign(new PersonData(), { keyNo: 'partner-2', name: '李四', tags: ['大股东'] }),
        Object.assign(new PersonData(), { keyNo: 'partner-3', name: '王五', tags: ['股东'] }),
      ];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(true);
      expect(result.hitKeyNos).toEqual(['partner-1', 'partner-2']);
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith(targetKeyNo, 'all');
    });

    it('应该正确识别实际控制人身份', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['ac-1', 'ac-2'];
      const targetKeyNo = 'target-company';

      // Mock实际控制人数据
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-1', name: '张三' }),
        Object.assign(new PersonData(), { keyNo: 'ac-2', name: '李四' }),
      ];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(true);
      expect(result.hitKeyNos).toEqual(['ac-1', 'ac-2']);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(targetKeyNo, false);
    });

    it('应该正确处理同时包含大股东和实际控制人的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1, 2]; // 大股东和实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['partner-1', 'ac-1', 'other-1'];
      const targetKeyNo = 'target-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      const mockActualControllers = [Object.assign(new PersonData(), { keyNo: 'ac-1', name: '李四' })];

      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(true);
      expect(result.hitKeyNos).toEqual(['partner-1', 'ac-1']);
    });

    it('应该正确处理没有匹配身份的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['other-1', 'other-2'];
      const targetKeyNo = 'target-company';

      // Mock数据 - 没有匹配的大股东
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });

    it('应该正确处理空的keyNos', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = []; // 空数组
      const targetKeyNo = 'target-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });
  });

  describe('holderRoleFieldCategory26 方法测试', () => {
    it('应该正确处理股权冻结大股东匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'partner-1',
          CompanyId: 'test-company',
        },
      };

      const keyNo = 'test-company';

      // Mock大股东数据
      const mockPartners = [
        Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] }),
        Object.assign(new PersonData(), { keyNo: 'partner-2', name: '李四', tags: ['股东'] }),
      ];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory26(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith(keyNo, 'all');
    });

    it('应该正确处理股权冻结实际控制人匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'ac-1',
          CompanyId: 'test-company',
        },
      };

      const keyNo = 'test-company';

      // Mock实际控制人数据
      const mockActualControllers = [Object.assign(new PersonData(), { keyNo: 'ac-1', name: '张三', tags: ['实际控制人'] })];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.holderRoleFieldCategory26(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });

    it('应该正确处理CompanyId不匹配的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'partner-1',
          CompanyId: 'other-company', // 不匹配的公司ID
        },
      };

      const keyNo = 'test-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory26(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理没有员工列表的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'partner-1',
          CompanyId: 'test-company',
        },
      };

      const keyNo = 'test-company';

      // Mock空的大股东数据
      mockPersonHelper.getPartnerList.mockResolvedValue([]);

      // Act
      const result = await helper.holderRoleFieldCategory26(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少KeyNo或CompanyId的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少KeyNo或CompanyId
        },
      };

      const keyNo = 'test-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory26(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('holderRoleFieldCategory50 方法测试', () => {
    it('应该正确处理股权质押大股东匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          HolderArray: [{ KeyNo: 'partner-1' }, { KeyNo: 'partner-2' }],
          Company: { K: 'test-company' },
        },
      };

      const keyNo = 'test-company';

      // Mock大股东数据
      const mockPartners = [
        Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] }),
        Object.assign(new PersonData(), { keyNo: 'partner-2', name: '李四', tags: ['大股东'] }),
      ];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory50(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith(keyNo, 'all');
    });

    it('应该正确处理股权质押实际控制人匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          HolderArray: [{ KeyNo: 'ac-1' }],
          Company: { K: 'test-company' },
        },
      };

      const keyNo = 'test-company';

      // Mock实际控制人数据
      const mockActualControllers = [Object.assign(new PersonData(), { keyNo: 'ac-1', name: '张三', tags: ['实际控制人'] })];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.holderRoleFieldCategory50(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });

    it('应该正确处理Company.K不匹配的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          HolderArray: [{ KeyNo: 'partner-1' }],
          Company: { K: 'other-company' }, // 不匹配的公司ID
        },
      };

      const keyNo = 'test-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory50(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少HolderArray或Company.K的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少HolderArray或Company.K
        },
      };

      const keyNo = 'test-company';

      // Mock数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory50(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('equityFrozenAmountFieldCategory26 方法测试', () => {
    it('应该正确处理股权冻结金额匹配', () => {
      // Arrange
      const equityFrozenAmountField = new DimensionHitStrategyFieldsEntity();
      equityFrozenAmountField.fieldValue = [1000, 5000]; // 1000-5000万元范围
      equityFrozenAmountField.compareType = DimensionFieldCompareTypeEnums.Between;

      const item = {
        ChangeExtend: {
          EquityAmount: '3000万元人民币',
        },
      };

      // Act
      const result = helper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, item);

      // Assert
      expect(result).toBe(true); // 3000在1000-5000范围内
    });

    it('应该正确处理股权冻结金额不匹配', () => {
      // Arrange
      const equityFrozenAmountField = new DimensionHitStrategyFieldsEntity();
      equityFrozenAmountField.fieldValue = [1000]; // 大于等于1000万元
      equityFrozenAmountField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          EquityAmount: '500万元人民币',
        },
      };

      // Act
      const result = helper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, item);

      // Assert
      expect(result).toBe(false); // 500 < 1000
    });

    it('应该正确处理缺少EquityAmount的情况', () => {
      // Arrange
      const equityFrozenAmountField = new DimensionHitStrategyFieldsEntity();
      equityFrozenAmountField.fieldValue = [1000];
      equityFrozenAmountField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少EquityAmount
        },
      };

      // Act
      const result = helper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理无效的EquityAmount格式', () => {
      // Arrange
      const equityFrozenAmountField = new DimensionHitStrategyFieldsEntity();
      equityFrozenAmountField.fieldValue = [1000];
      equityFrozenAmountField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          EquityAmount: 'invalid format',
        },
      };

      // Act
      const result = helper.equityFrozenAmountFieldCategory26(equityFrozenAmountField, item);

      // Assert
      expect(result).toBe(false); // processAmountString返回null
    });
  });

  describe('stockPledgeRatioFieldCategory50 方法测试', () => {
    it('应该正确处理股权质押占总股本比例匹配', () => {
      // Arrange
      const stockPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      stockPledgeRatioField.fieldValue = [50]; // 50%阈值
      stockPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: '65.5%',
        },
      };

      // Act
      const result = helper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);

      // Assert
      expect(result).toBe(true); // 65.5% >= 50%
    });

    it('应该正确处理股权质押占总股本比例不匹配', () => {
      // Arrange
      const stockPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      stockPledgeRatioField.fieldValue = [70]; // 70%阈值
      stockPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          C: '65.5%',
        },
      };

      // Act
      const result = helper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);

      // Assert
      expect(result).toBe(false); // 65.5% < 70%
    });

    it('应该正确处理缺少C字段的情况', () => {
      // Arrange
      const stockPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      stockPledgeRatioField.fieldValue = [50];
      stockPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少C字段
        },
      };

      // Act
      const result = helper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理范围比较', () => {
      // Arrange
      const stockPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      stockPledgeRatioField.fieldValue = [30, 70]; // 30%-70%范围
      stockPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.Between;

      const item = {
        ChangeExtend: {
          C: '50.5%',
        },
      };

      // Act
      const result = helper.stockPledgeRatioFieldCategory50(stockPledgeRatioField, item);

      // Assert
      expect(result).toBe(true); // 50.5%在30%-70%范围内
    });
  });

  describe('stockPledgeQuantityFieldCategory50 方法测试', () => {
    it('应该正确处理股权质押股份数量匹配', () => {
      // Arrange
      const stockPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      stockPledgeQuantityField.fieldValue = [1000]; // 1000万股阈值
      stockPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          B: '1500.00万',
        },
      };

      // Act
      const result = helper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, item);

      // Assert
      expect(result).toBe(true); // 1500 >= 1000
    });

    it('应该正确处理股权质押股份数量不匹配', () => {
      // Arrange
      const stockPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      stockPledgeQuantityField.fieldValue = [2000]; // 2000万股阈值
      stockPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          B: '1500.00万',
        },
      };

      // Act
      const result = helper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, item);

      // Assert
      expect(result).toBe(false); // 1500 < 2000
    });

    it('应该正确处理缺少B字段的情况', () => {
      // Arrange
      const stockPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      stockPledgeQuantityField.fieldValue = [1000];
      stockPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少B字段
        },
      };

      // Act
      const result = helper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理范围比较', () => {
      // Arrange
      const stockPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      stockPledgeQuantityField.fieldValue = [1000, 3000]; // 1000-3000万股范围
      stockPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.Between;

      const item = {
        ChangeExtend: {
          B: '2000.00万',
        },
      };

      // Act
      const result = helper.stockPledgeQuantityFieldCategory50(stockPledgeQuantityField, item);

      // Assert
      expect(result).toBe(true); // 2000在1000-3000范围内
    });
  });

  describe('sharePledgeStatusFieldCategory50 方法测试', () => {
    it('应该正确处理股权质押状态匹配', () => {
      // Arrange
      const sharePledgeStatusField = new DimensionHitStrategyFieldsEntity();
      sharePledgeStatusField.fieldValue = [1, 2]; // 未达预警线、已解除质押
      sharePledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          T: '未达预警线', // 对应value为1
        },
      };

      // Act
      const result = helper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理股权质押状态不匹配', () => {
      // Arrange
      const sharePledgeStatusField = new DimensionHitStrategyFieldsEntity();
      sharePledgeStatusField.fieldValue = [1]; // 只要未达预警线
      sharePledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          T: '已达预警线未达平仓线', // 对应value为3，不匹配
        },
      };

      // Act
      const result = helper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少T字段的情况', () => {
      // Arrange
      const sharePledgeStatusField = new DimensionHitStrategyFieldsEntity();
      sharePledgeStatusField.fieldValue = [1];
      sharePledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少T字段
        },
      };

      // Act
      const result = helper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理空的fieldValue', () => {
      // Arrange
      const sharePledgeStatusField = new DimensionHitStrategyFieldsEntity();
      sharePledgeStatusField.fieldValue = []; // 空数组
      sharePledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          T: '未达预警线',
        },
      };

      // Act
      const result = helper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理未知的T值', () => {
      // Arrange
      const sharePledgeStatusField = new DimensionHitStrategyFieldsEntity();
      sharePledgeStatusField.fieldValue = [1];
      sharePledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          T: '未知状态', // 不在SharePledgeStatusType中
        },
      };

      // Act
      const result = helper.sharePledgeStatusFieldCategory50(sharePledgeStatusField, item);

      // Assert
      expect(result).toBe(false); // 找不到对应的value
    });
  });

  describe('holderRoleFieldCategory12 方法测试', () => {
    it('应该正确处理股权出质大股东匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1]; // 大股东
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'partner-1',
          CompanyId: 'test-company',
        },
      };

      const keyNo = 'test-company';

      // Mock大股东数据
      const mockPartners = [Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] })];
      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);

      // Act
      const result = await helper.holderRoleFieldCategory12(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith(keyNo, 'all');
    });

    it('应该正确处理股权出质实际控制人匹配', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [2]; // 实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          KeyNo: 'ac-1',
          CompanyId: 'test-company',
        },
      };

      const keyNo = 'test-company';

      // Mock实际控制人数据
      const mockActualControllers = [Object.assign(new PersonData(), { keyNo: 'ac-1', name: '张三', tags: ['实际控制人'] })];
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.holderRoleFieldCategory12(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true);
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });
  });

  describe('equityPledgeStatusFieldCategory12 方法测试', () => {
    it('应该正确处理股权出质状态匹配', () => {
      // Arrange
      const equityPledgeStatusField = new DimensionHitStrategyFieldsEntity();
      equityPledgeStatusField.fieldValue = [1]; // 有效
      equityPledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          Status: '有效', // 对应value为1
        },
      };

      // Act
      const result = helper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);

      // Assert
      expect(result).toBe(true);
    });

    it('应该正确处理股权出质状态不匹配', () => {
      // Arrange
      const equityPledgeStatusField = new DimensionHitStrategyFieldsEntity();
      equityPledgeStatusField.fieldValue = [1]; // 只要有效
      equityPledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          Status: '无效', // 对应value为2，不匹配
        },
      };

      // Act
      const result = helper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少Status字段的情况', () => {
      // Arrange
      const equityPledgeStatusField = new DimensionHitStrategyFieldsEntity();
      equityPledgeStatusField.fieldValue = [1];
      equityPledgeStatusField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          // 缺少Status字段
        },
      };

      // Act
      const result = helper.equityPledgeStatusFieldCategory12(equityPledgeStatusField, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('equityPledgeRatioFieldCategory12 方法测试', () => {
    it('应该正确处理股权出质比例匹配', () => {
      // Arrange
      const equityPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      equityPledgeRatioField.fieldValue = [80]; // 80%阈值
      equityPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '100.00%',
        },
      };

      // Act
      const result = helper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);

      // Assert
      expect(result).toBe(true); // 100% >= 80%
    });

    it('应该正确处理股权出质比例不匹配', () => {
      // Arrange
      const equityPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      equityPledgeRatioField.fieldValue = [90]; // 90%阈值
      equityPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '75.00%',
        },
      };

      // Act
      const result = helper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);

      // Assert
      expect(result).toBe(false); // 75% < 90%
    });

    it('应该正确处理缺少Percent字段的情况', () => {
      // Arrange
      const equityPledgeRatioField = new DimensionHitStrategyFieldsEntity();
      equityPledgeRatioField.fieldValue = [80];
      equityPledgeRatioField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少Percent字段
        },
      };

      // Act
      const result = helper.equityPledgeRatioFieldCategory12(equityPledgeRatioField, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('equityPledgeQuantityFieldCategory12 方法测试', () => {
    it('应该正确处理出质股份数量匹配', () => {
      // Arrange
      const equityPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      equityPledgeQuantityField.fieldValue = [100]; // 100万股阈值
      equityPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          PledgedAmount: '126.9472万股',
        },
      };

      // Act
      const result = helper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, item);

      // Assert
      expect(result).toBe(true); // 126.9472 >= 100
    });

    it('应该正确处理出质股份数量不匹配', () => {
      // Arrange
      const equityPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      equityPledgeQuantityField.fieldValue = [200]; // 200万股阈值
      equityPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          PledgedAmount: '126.9472万股',
        },
      };

      // Act
      const result = helper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, item);

      // Assert
      expect(result).toBe(false); // 126.9472 < 200
    });

    it('应该正确处理PledgedAmount不包含"股"的情况', () => {
      // Arrange
      const equityPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      equityPledgeQuantityField.fieldValue = [100];
      equityPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          PledgedAmount: '126.9472万元', // 不包含"股"
        },
      };

      // Act
      const result = helper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理缺少PledgedAmount字段的情况', () => {
      // Arrange
      const equityPledgeQuantityField = new DimensionHitStrategyFieldsEntity();
      equityPledgeQuantityField.fieldValue = [100];
      equityPledgeQuantityField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          // 缺少PledgedAmount字段
        },
      };

      // Act
      const result = helper.equityPledgeQuantityFieldCategory12(equityPledgeQuantityField, item);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('边界情况和综合测试', () => {
    it('应该正确处理 ChangeExtend 为空的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {}, // 空对象
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理 item 为空的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [95];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {}; // 空对象

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理 fieldValue 为 null 的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = null; // null值
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '98.5%',
          T: 2,
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理 fieldValue 为空数组的情况', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = []; // 空数组
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const item = {
        ChangeExtend: {
          Percent: '98.5%',
          T: 2,
        },
      };

      // Act
      const result = helper.category12Field(typeField, item);

      // Assert
      expect(result).toBe(false);
    });

    it('应该正确处理PersonHelper返回空数组的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['partner-1'];
      const targetKeyNo = 'target-company';

      // Mock空的大股东数据
      mockPersonHelper.getPartnerList.mockResolvedValue([]);

      // Act
      const result = await helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo);

      // Assert
      expect(result.hit).toBe(false);
      expect(result.hitKeyNos).toEqual([]);
    });

    it('应该正确处理PersonHelper抛出异常的情况', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1];
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const keyNos = ['partner-1'];
      const targetKeyNo = 'target-company';

      // Mock异常
      mockPersonHelper.getPartnerList.mockRejectedValue(new Error('Network error'));

      // Act & Assert
      await expect(helper.hitHolderRoleField(holderRoleField, keyNos, targetKeyNo)).rejects.toThrow('Network error');
    });

    it('应该正确处理百分比字符串的各种格式', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [50];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const testCases = [
        { percent: '75%', expected: true },
        { percent: '75.5%', expected: true },
        { percent: '25%', expected: false },
        { percent: '50%', expected: true },
        { percent: '0%', expected: false },
        { percent: '100%', expected: true },
        { percent: '75.123%', expected: true },
        { percent: '', expected: false },
        { percent: 'invalid', expected: false },
        { percent: '75', expected: false }, // 缺少%符号
      ];

      testCases.forEach(({ percent, expected }) => {
        const item = {
          ChangeExtend: {
            C: percent,
          },
        };

        // Act
        const result = helper.category50Field(typeField, item);

        // Assert
        expect(result).toBe(expected);
      });
    });

    it('应该正确处理金额字符串的各种格式', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1000];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const testCases = [
        { amount: '1500万元人民币', expected: true },
        { amount: '500万元', expected: false },
        { amount: '1000万', expected: true },
        { amount: '2000万元人民币', expected: true },
        { amount: '999万元', expected: false },
        { amount: '1000.5万元', expected: true },
        { amount: '', expected: false },
        { amount: 'invalid', expected: false },
        { amount: '1500元', expected: false }, // 单位不对
      ];

      testCases.forEach(({ amount, expected }) => {
        const item = {
          ChangeExtend: {
            EquityAmount: amount,
          },
        };

        // Act
        const result = helper.equityFrozenAmountFieldCategory26(typeField, item);

        // Assert
        expect(result).toBe(expected);
      });
    });

    it('应该正确处理数字字符串的各种格式', () => {
      // Arrange
      const typeField = new DimensionHitStrategyFieldsEntity();
      typeField.fieldValue = [1000];
      typeField.compareType = DimensionFieldCompareTypeEnums.GreaterThanOrEqual;

      const testCases = [
        { quantity: '1500.00万', expected: true },
        { quantity: '500万', expected: false },
        { quantity: '1000万', expected: true },
        { quantity: '2000.123万', expected: true },
        { quantity: '999.99万', expected: false },
        { quantity: '', expected: false },
        { quantity: 'invalid万', expected: false },
        { quantity: '1500', expected: false }, // 缺少万字
      ];

      testCases.forEach(({ quantity, expected }) => {
        const item = {
          ChangeExtend: {
            B: quantity,
          },
        };

        // Act
        const result = helper.stockPledgeQuantityFieldCategory50(typeField, item);

        // Assert
        expect(result).toBe(expected);
      });
    });

    it('应该正确处理不同比较类型的组合', () => {
      // Arrange
      const testCases = [
        {
          compareType: DimensionFieldCompareTypeEnums.Equal,
          fieldValue: [50],
          testValue: 50,
          expected: true,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.Equal,
          fieldValue: [50],
          testValue: 60,
          expected: false,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.GreaterThan,
          fieldValue: [50],
          testValue: 60,
          expected: true,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.GreaterThan,
          fieldValue: [50],
          testValue: 50,
          expected: false,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.LessThan,
          fieldValue: [50],
          testValue: 40,
          expected: true,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.Between,
          fieldValue: [30, 70],
          testValue: 50,
          expected: true,
        },
        {
          compareType: DimensionFieldCompareTypeEnums.Between,
          fieldValue: [30, 70],
          testValue: 80,
          expected: false,
        },
      ];

      testCases.forEach(({ compareType, fieldValue, testValue, expected }) => {
        const typeField = new DimensionHitStrategyFieldsEntity();
        typeField.fieldValue = fieldValue;
        typeField.compareType = compareType;

        const item = {
          ChangeExtend: {
            C: `${testValue}%`,
          },
        };

        // Act
        const result = helper.category50Field(typeField, item);

        // Assert
        expect(result).toBe(expected);
      });
    });

    it('应该正确处理复杂的嵌套数据结构', async () => {
      // Arrange
      const holderRoleField = new DimensionHitStrategyFieldsEntity();
      holderRoleField.fieldValue = [1, 2]; // 大股东和实际控制人
      holderRoleField.compareType = DimensionFieldCompareTypeEnums.ContainsAny;

      const item = {
        ChangeExtend: {
          HolderArray: [{ KeyNo: 'partner-1' }, { KeyNo: 'ac-1' }, { KeyNo: 'other-1' }],
          Company: { K: 'test-company' },
        },
      };

      const keyNo = 'test-company';

      // Mock复杂的数据结构
      const mockPartners = [
        Object.assign(new PersonData(), { keyNo: 'partner-1', name: '张三', tags: ['大股东'] }),
        Object.assign(new PersonData(), { keyNo: 'partner-2', name: '李四', tags: ['股东'] }),
      ];
      const mockActualControllers = [
        Object.assign(new PersonData(), { keyNo: 'ac-1', name: '王五' }),
        Object.assign(new PersonData(), { keyNo: 'ac-2', name: '赵六' }),
      ];

      mockPersonHelper.getPartnerList.mockResolvedValue(mockPartners);
      mockPersonHelper.getFinalActualController.mockResolvedValue(mockActualControllers);

      // Act
      const result = await helper.holderRoleFieldCategory50(holderRoleField, item, keyNo);

      // Assert
      expect(result).toBe(true); // partner-1和ac-1都匹配
      expect(mockPersonHelper.getPartnerList).toHaveBeenCalledWith(keyNo, 'all');
      expect(mockPersonHelper.getFinalActualController).toHaveBeenCalledWith(keyNo, false);
    });
  });
});
