/**
 * Jest 配置文件 - 风险动态模块单元测试
 * 
 * 这个配置文件专门用于运行风险动态模块的单元测试
 * 提供了测试环境、覆盖率报告和模块解析等配置
 */

module.exports = {
  // 测试显示名称
  displayName: '风险动态模块单元测试',

  // 测试环境
  testEnvironment: 'node',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/**/*.unittest.spec.ts',
  ],

  // 忽略的测试文件
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
  ],

  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // TypeScript 转换配置
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },

  // 模块路径映射（与项目的 tsconfig.json 保持一致）
  moduleNameMapping: {
    '^libs/(.*)$': '<rootDir>/../../../../libs/$1',
    '^apps/(.*)$': '<rootDir>/../../../$1',
  },

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],

  // 覆盖率配置
  collectCoverage: true,
  collectCoverageFrom: [
    '../**/*.ts',
    '!../**/*.spec.ts',
    '!../**/*.test.ts',
    '!../tests/**',
    '!../node_modules/**',
  ],

  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json',
  ],

  // 覆盖率输出目录
  coverageDirectory: '<rootDir>/coverage',

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    // 针对特定文件的覆盖率要求
    '../risk-change-es.source.ts': {
      branches: 85,
      functions: 95,
      lines: 90,
      statements: 90,
    },
    '../dimension-hit-detail.processor.ts': {
      branches: 80,
      functions: 90,
      lines: 85,
      statements: 85,
    },
    '../related-dimension-hit-detail.processor.ts': {
      branches: 80,
      functions: 90,
      lines: 85,
      statements: 85,
    },
  },

  // 测试超时时间（毫秒）
  testTimeout: 30000,

  // 详细输出
  verbose: true,

  // 错误时停止
  bail: false,

  // 清除 mock 调用历史
  clearMocks: true,

  // 恢复 mock 实现
  restoreMocks: true,

  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2018',
          lib: ['es2018'],
          experimentalDecorators: true,
          emitDecoratorMetadata: true,
          skipLibCheck: true,
          strict: false,
        },
      },
    },
  },

  // 模块解析
  resolver: undefined,

  // 测试结果处理器
  testResultsProcessor: undefined,

  // 监听模式配置
  watchman: true,
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
  ],

  // 并行测试
  maxWorkers: '50%',

  // 缓存
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',

  // 错误报告
  errorOnDeprecated: true,

  // 测试序列化
  testSequencer: '@jest/test-sequencer',

  // 自定义匹配器
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
};
