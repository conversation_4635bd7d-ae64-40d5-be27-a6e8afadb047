/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { ConfigService } from 'libs/config/config.service';
import { Cacheable } from '@type-cacheable/core';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { Client } from '@elastic/elasticsearch';
import { AggBucketItemPO, JudgementAggBucketItemPO } from 'libs/model/data/source/credit.analyze/CreditAggBucketItemPO';
import * as Bluebird from 'bluebird';
import { toRoundFixed, transferToNumber } from '../../../libs/utils/utils';
import { find, flatMap, flatten, intersection, some } from 'lodash';
import { PersonData } from '../../../libs/model/data/source/PersonData';
import { BaseEsAnalyzeService } from './base-es-analyze.service';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { DetailsParamEnums } from '../../../libs/enums/diligence/DetailsParamEnums';
import { PersonHelper } from '../helper/person.helper';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { getCompareResult, getIsValidValue, getStartTimeByCycle } from '../../../libs/utils/diligence/diligence.utils';
import { DocTypeMap, JudgeResultMap } from '../../../libs/constants/judgement.constants';
import { DimensionFieldCompareTypeEnums, EsOperator } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { TargetInvestigationEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import { DateDetailsQueryParams } from '../../../libs/model/data/request/HitDetailsBaseQueryParams';
import { CompanyDetailService } from '../../company/company-detail.service';

interface ESBoolShould {
  should: object[];
}

/**
 * 裁判文书
 */
@Injectable()
export class JudgementSource extends BaseEsAnalyzeService {
  constructor(
    private readonly configService: ConfigService,
    private readonly personHelper: PersonHelper,
    private readonly companyDetailsService: CompanyDetailService,
  ) {
    super(
      JudgementSource.name,
      new Client({
        nodes: configService.esConfig.judgement.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.judgement.indexName,
    );
  }

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      let hitCount = 0;
      switch (d.key) {
        case DimensionTypeEnums.Judgement: {
          hitCount = await this.judgementCompareResult(d, companyId);
          break;
        }
      }
      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount > 0) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取风险维度的详情
   * @param dimension
   * @param params
   * @param analyzeParams
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp: HitDetailsBaseResponse = await super.getDimensionDetail(dimension, params, analyzeParams);
    return resp;
  }

  /**
   * 有的返回结果也调用接口去补充数据
   * 对DimensionDetail返回结果的处理，如果是isScanRisk,则跳出返回结果的处理
   * @param resp
   * @param dimension
   * @param params
   * @param analyzeParams
   * @protected
   */
  protected async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    const { keyNo, companyName } = params;
    if (resp?.Result?.length) {
      this.formatResponse(resp);
      switch (dimension?.key) {
        case DimensionTypeEnums.Judgement: {
          const targetInvestigations = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation)?.fieldValue; //排查对象
          const ids = [keyNo];
          const keyNoMap = new Map<string, any>();
          keyNoMap.set(keyNo, { name: companyName, job: '企业主体' });
          const personData = await this.getRelatedCompanyAndPerson(keyNo, targetInvestigations, []);
          personData.forEach((x) => {
            keyNoMap.set(x.keyNo, { name: x.name, job: x.job });
            ids.push(x.keyNo);
          });
          resp.Result.forEach((d) => {
            this.addTagAndRole(d, ids, keyNoMap);
          });
          break;
        }
        case DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve:
        case DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory: {
          const ids = [keyNo];
          const keyNoMap = new Map<string, any>();
          keyNoMap.set(keyNo, { name: companyName, job: '企业主体' });
          // 关联对象
          const associateObjects = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.associateObject)?.fieldValue;
          const associateExcludes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.associateExclude)?.fieldValue;
          const personData = await this.getRelatedCompanyAndPerson(keyNo, associateObjects, associateExcludes);
          personData.forEach((x) => {
            keyNoMap.set(x.keyNo, { name: x.name, job: x.job });
            ids.push(x.keyNo);
          });
          resp.Result.forEach((d) => {
            this.addTagAndRole(d, ids, keyNoMap);
          });
          break;
        }
        default:
      }
    }
    return resp;
  }

  protected async getDimensionQuery(companyId: string, dimension: DimensionHitStrategyPO): Promise<object> {
    let ids = [companyId];

    const subBool = {
      filter: [],
      must_not: [],
      should: [],
    };
    const subShould: ESBoolShould = { should: [] };
    subShould.should.push({ term: { delflag: 0 } });
    subShould.should.push({ bool: { must: [{ term: { delflag: 1 } }, { terms: { isvalid: getIsValidValue(-1).split(',') } }] } });
    if (subShould?.should?.length > 0) {
      subShould['minimum_should_match'] = 1;
      subBool.filter.push({ bool: subShould });
    }

    const cycleField = dimension.getCycleField();
    let cycle = cycleField?.fieldValue ? (cycleField.fieldValue?.[0] as number) : 0;
    let rangeOperator = cycleField?.compareType ? EsOperator[cycleField.compareType] : 'gte';

    // 数据范围
    const isValid = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid)?.fieldValue[0] ?? -1;
    if (isValid >= 0) {
      subBool.filter.push({ terms: { isvalid: getIsValidValue(isValid).split(',') } });
    }

    switch (dimension.key) {
      case DimensionTypeEnums.Judgement: {
        // 排查对象
        const targetInvestigation = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.targetInvestigation);
        if (targetInvestigation?.fieldValue.length) {
          const targetValue = targetInvestigation.fieldValue;
          ids = [];
          if (targetValue.includes(TargetInvestigationEnums.Self)) {
            ids.push(companyId);
          }
          if (targetValue.includes(TargetInvestigationEnums.Legal)) {
            const personlist = await this.personHelper.getLegalPerson(companyId);
            const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.HisLegal)) {
            const personlist = await this.personHelper.getHisLegalPerson(companyId);
            const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.MainStaff)) {
            const personlist = await this.personHelper.getEmployeeList(companyId);
            const personNos = personlist.map((p) => p.keyNo).filter((t) => t);
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.HisMainStaff)) {
            const personlist = await this.personHelper.getHisEmployeeData(companyId);
            const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.Branch)) {
            const companyBranches = await this.companyDetailsService.getBranchKeyNos(companyId);
            if (companyBranches?.length) {
              ids.push(...companyBranches);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.ActualController)) {
            const personlist = await this.personHelper.getFinalActualController(companyId);
            const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
          if (targetValue.includes(TargetInvestigationEnums.Benefit)) {
            const personlist = await this.personHelper.getBenefitList(companyId, false);
            const personNos = personlist?.length ? personlist?.map((p) => p.keyNo).filter((t) => t) : [];
            if (personNos?.length) {
              ids.push(...personNos);
            }
          }
        }
        if (ids?.length) {
          subBool.should.push({ terms: { comprelkeywords: ids } }, { terms: { companynames: ids } });
          subBool['minimum_should_match'] = 1;
        }
        // 涉案总金额
        const amountParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.amountInvolved);
        if (amountParam) {
          subBool.filter.push({ range: { amountinvolved: { [EsOperator[amountParam.compareType]]: Number(amountParam.fieldValue[0]) } } });
        }
        // 案由类别
        const caseReasonType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseReasonType);
        if (caseReasonType?.fieldValue.length) {
          switch (caseReasonType.compareType) {
            // 不包含任意
            case DimensionFieldCompareTypeEnums.ExceptAny: {
              subBool.must_not.push({ terms: { casereasontype: flatMap(caseReasonType.fieldValue) } });
              break;
            }
            default: {
              subBool.filter.push({ terms: { casereasontype: flatMap(caseReasonType.fieldValue) } });
            }
          }
        }
        // 案件类型
        const CaseType = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CaseType);
        if (CaseType?.fieldValue.length) {
          subBool.filter.push({ terms: { casetype: flatMap(CaseType.fieldValue) } });
        }

        // 裁判文书身份类型（包含）
        const rShould: ESBoolShould = {
          should: [],
        };
        const judgementRoleIncludes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judgementRoleInclude)?.fieldValue;
        if (judgementRoleIncludes?.includes('prosecutor')) {
          rShould.should.push({ terms: { prosecutor: ids } });
        }
        if (judgementRoleIncludes?.includes('defendant')) {
          rShould.should.push({ terms: { defendant: ids } });
        }
        if (judgementRoleIncludes?.includes('thirdpartyrole')) {
          rShould.should.push({ terms: { thirdpartyrole: ids } });
        }
        if (rShould.should.length) {
          rShould['minimum_should_match'] = 1;
          subBool.filter.push({ bool: rShould });
        }
        break;
      }
      case DimensionTypeEnums.SalesContractDispute: {
        subBool.filter.push({ terms: { casereasontype: ['B040109', 'B04010905', 'B040110', 'C02040109', 'C02040110'] } });
        break;
      }
      case DimensionTypeEnums.MajorDispute: {
        const rShould: ESBoolShould = {
          should: [],
        };
        const judgementRoleExclude = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.judgementRoleExclude);
        rShould.should.push({ terms: { defendant: ids } });
        if (judgementRoleExclude?.fieldValue?.length) {
          if (!judgementRoleExclude.fieldValue.includes('prosecutor')) {
            rShould.should.push({ terms: { prosecutor: ids } });
          }
          if (!judgementRoleExclude.fieldValue.includes('thirdpartyrole')) {
            rShould.should.push({ terms: { thirdpartyrole: ids } });
          }
        } else {
          rShould.should.push({ terms: { prosecutor: ids } });
          rShould.should.push({ terms: { thirdpartyrole: ids } });
        }
        if (rShould.should.length) {
          rShould['minimum_should_match'] = 1;
          subBool.filter.push({ bool: rShould });
        }
        const amountParam = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.amountInvolved);
        subBool.filter.push({ range: { amountinvolved: { gte: Number(amountParam.fieldValue[0]) } } });
        break;
      }
      case DimensionTypeEnums.CompanyOrMainMembersCriminalInvolve:
      case DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory: {
        // await this.getCriminalInvolveQuery(companyId, dimension, ids, subBool);
        cycle = 3;
        rangeOperator = dimension.key === DimensionTypeEnums.CompanyOrMainMembersCriminalInvolveHistory ? 'lte' : 'gt';

        // 处理相关提及放属性
        const associateObjects = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.associateObject)?.fieldValue;
        const associateExcludes = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.associateExclude)?.fieldValue;
        const personData = await this.getRelatedCompanyAndPerson(companyId, associateObjects, associateExcludes);
        const keyNos = personData.map((x) => x.keyNo);
        ids.push(...keyNos);
        subBool.filter.push({ term: { casetype: 'xs' } }, { terms: { othercns: ids } }, { terms: { caselabel: ['TW00'] } });

        // 隐藏特殊企业的案件
        this.hiddenInfo(companyId, subBool);

        break;
      }
    }

    // 统计周期
    if (cycle > 0) {
      const timestamp = getStartTimeByCycle(cycle);
      subBool.filter.push({ range: { judgedate: { [rangeOperator]: Math.ceil(timestamp / 1000) } } });
    }
    return { bool: subBool };
  }

  protected async createAggs(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]) {
    const aggs: any = {};
    await Bluebird?.map(DimensionHitStrategyPOs, async (po) => {
      const dimQuery = await this.getDimensionQuery(companyId, po);
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      aggs[aggsName] = {
        filter: dimQuery,
        aggs: {},
      };
      switch (po.key) {
        case DimensionTypeEnums.SalesContractDispute:
          // 买卖合同纠纷-统计公司作为被告的次数
          aggs[aggsName].aggs['agg_defendant'] = { filter: { terms: { defendant: [companyId] } } };
          break;

        case DimensionTypeEnums.MajorDispute: {
          aggs[aggsName].aggs['amount'] = {
            sum: {
              field: 'amountinvolved',
            },
          };
          break;
        }
      }
    });
    return aggs;
  }

  protected processAggs(aggObj: any, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): AggBucketItemPO[] {
    const bucketData: AggBucketItemPO[] = [];
    DimensionHitStrategyPOs.forEach((po) => {
      const aggsName = `${this.bucketNamePrefix}${po.strategyId}`;
      const bucket = aggObj[aggsName];
      const hitCount = bucket['doc_count'];
      if (hitCount > 0) {
        const res: JudgementAggBucketItemPO = {
          dimensionType: po.strategyId + '',
          hitCount,
        };
        switch (po.key) {
          case DimensionTypeEnums.SalesContractDispute: {
            const defendantBucket = bucket['agg_defendant'];
            const defendantCount = defendantBucket['doc_count'];
            const percentParam = po.getStrategyFieldByKey(DimensionFieldKeyEnums.percentAsDefendant);
            const percent = Number(percentParam.fieldValue[0]);
            if ((defendantCount / hitCount) * 100 >= percent) {
              bucketData.push(res);
            }
            break;
          }
          case DimensionTypeEnums.MajorDispute: {
            if (bucket.amount) {
              res.amount = transferToNumber(bucket.amount.value);
            }
            bucketData.push(res);
            break;
          }
          default:
            bucketData.push(res);
        }
      }
    });
    return bucketData;
  }

  protected processBucketData(bucketData: JudgementAggBucketItemPO[], DimensionHitStrategyPOs: DimensionHitStrategyPO[]): DimensionHitResultPO[] {
    return bucketData
      .map((item) => {
        const d: DimensionHitStrategyPO = find(DimensionHitStrategyPOs, { strategyId: +item.dimensionType });
        const desData = {
          isHidden: '',
          isHiddenY: '',
        };
        switch (d.key) {
          case DimensionTypeEnums.SalesContractDispute: {
            const percentParam = d.getStrategyFieldByKey(DimensionFieldKeyEnums.percentAsDefendant);
            Object.assign(desData, { percent: percentParam.fieldValue[0] });
            break;
          }
          case DimensionTypeEnums.MajorDispute: {
            if (item.amount) {
              Object.assign(desData, { amountW: toRoundFixed(item.amount / 10000, 2) });
            }
            break;
          }
        }
        const { hitCount } = item as AggBucketItemPO;

        let hit = true;
        // 命中记录条数 规则设置
        const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
        if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
          // 不满足 命中记录条数规则 标记未命中
          hit = false;
        }

        if (hit) {
          return processDimHitResPO(d, hitCount, desData);
        }
        return null;
      })
      .filter((t) => t);
  }

  private formatResponse(resp: HitDetailsBaseResponse) {
    resp.Result.forEach((d) => {
      d.casename = d?.casereason + ' ' + d?.trialround + DocTypeMap.get(d?.doctype);
      // 当事人优先用caserolegroupbyrolename，如果caserolegroupbyrolename为空，则用caserole
      if (d.caserolegroupbyrolename) {
        d.caserolegroupbyrolename = JSON.parse(d.caserolegroupbyrolename).map((role) => {
          if (role?.DetailList?.length > 0) {
            role.DetailList.forEach((roleDetail) => {
              if (roleDetail?.JR && roleDetail?.JudgeResult) {
                roleDetail.JudgeResultDescription = JudgeResultMap.get(roleDetail?.JR);
              }
            });
          }
          return role;
        });
      }
      if (d.caserole) {
        d.caserole = JSON.parse(d.caserole);
      }
      d.involveTags = []; // 涉案标签（案外人、被提及）
      d.involveRole = []; // 涉案标签（案外人、被提及）
    });
  }

  /**
   * 需要特殊处理隐藏企查查的案件数据
   */
  private hiddenInfo(companyId: string, subBool: any) {
    //需要特殊处理隐藏企查查的案件数据
    const hiddenCompanyId = 'f625a5b661058ba5082ca508f99ffe1b'; // 企查查的企业id
    const hiddenCaseIds = [
      '6429b14f7367fe496e20757cab0024160',
      'acb4d9bf9f0974cb2529cb3b4e28ccc90',
      'f57d1150a3048a4231eef8894f916fef0',
      '6237c63a13dcfdfa68d370eb6ab219ba0',
      'eac103253dd62dda0d945f0038f2e5900',
    ];
    if (companyId == hiddenCompanyId) {
      subBool['must_not'] = [{ terms: { id: hiddenCaseIds } }];
    }
  }

  /**
   * 获取关联企业以及人员
   * @param companyId
   * @param associateObjects 关联对象
   * @param associateExcludes 关联排除
   * @private
   */
  @Cacheable({ ttlSeconds: 100 })
  private async getRelatedCompanyAndPerson(companyId: string, associateObjects: any[], associateExcludes: any[]): Promise<PersonData[]> {
    const investExcludeCompanyIds = [
      '715f4a0a608f1e6d4a772c31ade1b171', //梧桐树 投资平台有限责任公司
      'he7910e63f79702ea812d50f082dfaec', //香港中央結算有限公司
      'h1a3ef7ad8ec2e56ba43e250225d1d28', //香港中央結算（代理人）有限公司
      'g599bafb4da74aa069cc9b67a39992e0', //全国社会保障基金理事会
      'a0ed6d61d21271179021367cac5d4420', //国开发展基金有限公司
      'b2fbc4155d4781978f94ef67bc61c1e7', //国开基础设施基金有限公司
      'ga7b26dace8da0c3b2fbdc6acefb4739', //国务院
      'g38ed9b86b70ab7a169f5ba36ab4bfb9', //财政部
      'g4bdd6c24056bb434978acfc5f40d168', //国资委
    ];
    //投资机构Tags
    const investTags = ['私募基金', '投资', '基金', '资产管理', '私募', '资本', '城投', '证券', '保险', '信托', '银行', '财富关联'];
    const promiseArr = [this.personHelper.getEmployeeList(companyId)];
    const isLegal = find(associateObjects, { key: DetailsParamEnums.LegalRepresentative })?.status == 1;
    const isActualControl = find(associateObjects, { key: DetailsParamEnums.ActualController })?.status == 1;
    const isMajorShareholder = find(associateObjects, { key: DetailsParamEnums.MajorShareholder })?.status == 1;
    const isShareholder = find(associateObjects, { key: DetailsParamEnums.Shareholder })?.status == 1;
    const ignoreInvest = associateExcludes ? find(associateExcludes, { key: DetailsParamEnums.ShareHolderInvest })?.status == 1 : false;
    if (isLegal) {
      promiseArr.push(this.personHelper.getLegalPerson(companyId));
    }
    if (isActualControl) {
      promiseArr.push(this.personHelper.getFinalActualController(companyId));
    }
    if (isShareholder || isMajorShareholder) {
      promiseArr.push(this.personHelper.getPartnerList(companyId, 'all', ignoreInvest));
    }
    let personData: PersonData[] = flatten(await Bluebird.all(promiseArr));
    if (isShareholder || isMajorShareholder) {
      //股东包含大股东，如果只包含大股东，则只显示大股东
      if (!isShareholder && isMajorShareholder) {
        personData = personData.filter((x) => x.tags.includes('大股东'));
      }
    }
    const keyNosSet: Set<string> = new Set<string>();
    const uniqPersonData = [];
    personData
      .filter((x) => x?.keyNo)
      .forEach((x) => {
        if (!keyNosSet.has(x.keyNo)) {
          if (
            ignoreInvest &&
            (investExcludeCompanyIds.includes(x.keyNo) ||
              some(x.tags, (t) => investTags.includes(t)) ||
              x.keyNo.startsWith('g') ||
              some(x.name, (t) => investTags.includes(t)))
          ) {
            //如果关联派出设置了排除股东类型为投资机构，则排除投资机构，keyNo g开头的是机关单位，在本需求中也当投资机构处理
            return;
          }
          uniqPersonData.push(x);
          keyNosSet.add(x.keyNo);
        }
      });
    return uniqPersonData;
  }

  /**
   * 添加涉案标签和角色
   * @param d
   * @param ids
   * @param keyNoMap
   * @private
   */
  private async addTagAndRole(d: any, ids: string[], keyNoMap: Map<string, any>) {
    if (d?.caserole) {
      const personIds = d.caserole.filter((t) => t.O === 2).map((r) => r.N);
      if (personIds?.length) {
        const defendantIds = intersection(personIds, ids);
        d.involveTags.push('被告人');
        defendantIds.forEach((id) => {
          const info = keyNoMap.get(id);
          d.involveRole.push({ Tag: '被告人', KeyNo: id, Name: info.name, Job: info.job });
        });
      }
    }
    if (d.outsider && d.outsider?.length > 0) {
      const outsiders = intersection(d.outsider, ids);
      if (outsiders.length > 0) {
        d.involveTags.push('案外人');
        outsiders.forEach((id) => {
          const info = keyNoMap.get(id);
          d.involveRole.push({ Tag: '案外人', KeyNo: id, Name: info.name, Job: info.job });
        });
      }
    }
    if (d.involveTags?.length == 0) {
      if (d.othercns && d.othercns?.length > 0) {
        const othercns = intersection(d.othercns, ids);
        if (othercns.length > 0) {
          d.involveTags.push('被提及');
          othercns.forEach((id) => {
            const info = keyNoMap.get(id);
            d.involveRole.push({ Tag: '被提及', KeyNo: id, Name: info.name, Job: info.job });
          });
        }
      }
    }
  }

  /**
   * 裁判文书
   */
  private async judgementCompareResult(dimension: DimensionHitStrategyPO, companyId: string) {
    const params = new HitDetailsBaseQueryParams();
    params.keyNo = companyId;
    const response = await this.getDimensionDetail(
      dimension,
      params,
      Object.assign(new DimensionAnalyzeParamsPO(), {
        isScanRisk: true,
        keyNo: companyId,
      }),
    );
    return response?.Result?.length ? response.Paging.TotalRecords : 0;
  }

  /**
   * 直接查询裁判文书数据
   * @param params
   * @returns
   */
  async getJudgementDataByIds(params: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    if (!params.ids?.length) {
      return HitDetailsBaseResponse.ok();
    }

    try {
      const ids = params.ids.map((id) => id + '0');
      const pageIndex = params?.pageIndex || 1;
      const pageSize = params?.pageSize || 5;
      const sortField = params?.sortField || 'judgedate';
      const sortOrder = params?.sortOrder || 'DESC';

      const body = {
        from: (pageIndex && pageIndex > 0 ? pageIndex - 1 : 0) * pageSize,
        size: pageSize || 10,
        sort: { [sortField]: { order: sortOrder } },
        query: {
          bool: {
            filter: [{ terms: { id: ids } }],
          },
        },
      };

      const response = await this.searchEs(body, 'judgement');

      const resp = Object.assign(new HitDetailsBaseResponse(), {
        Paging: {
          PageSize: pageSize,
          PageIndex: pageIndex,
          TotalRecords: response?.body?.hits?.total?.value || 0,
        },
        Result: response?.body?.hits?.hits?.map((d) => d._source) || [],
      });

      if (resp?.Result?.length) {
        this.formatResponse(resp);
      }
      return resp;
    } catch (error) {
      this.logger.error('getJudgementDataByIds error: ', error.merssage);
      this.logger.error(error);
      return HitDetailsBaseResponse.failed();
    }
  }
}
