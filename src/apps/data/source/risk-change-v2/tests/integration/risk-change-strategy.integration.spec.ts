import { Test, TestingModule } from '@nestjs/testing';
import { RiskChangeEsSourceV2 } from '../../risk-change-es.source.v2';
import { RiskChangeEsSource } from '../../../risk-change/risk-change-es.source';
import { LegalChangeStrategy } from '../../strategies/legal-change.strategy';
import { CapitalChangeStrategy } from '../../strategies/capital-change.strategy';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from '../../../../helper/risk.change.helper';
import { ConfigService } from 'libs/config/config.service';
import { ModuleRef } from '@nestjs/core';
import { AppTestModule } from '../../../../app/app.test.module';
import { DataModule } from '../../../data.module';

describe('RiskChangeStrategy Integration Tests', () => {
  let service: RiskChangeEsSourceV2;
  let legalChangeStrategy: LegalChangeStrategy;
  let capitalChangeStrategy: CapitalChangeStrategy;
  let module: TestingModule;

  beforeEach(async () => {
    const mockRiskChangeHelper = {
      hitLayTypesField: jest.fn().mockReturnValue(true),
      hitMainInfoUpdateCapitalChange: jest.fn().mockReturnValue(true),
      hitCategory123CurrencyChangeField: jest.fn().mockReturnValue(true),
      capitalReduceSelectCompareResult: jest.fn().mockReturnValue(true),
      hitPeriodRegisCapitalField123: jest.fn().mockReturnValue(true),
    };

    const mockConfigService = {
      get: jest.fn().mockReturnValue('test-config'),
    };

    const mockModuleRef = {
      get: jest.fn(),
    };

    module = await Test.createTestingModule({
      providers: [
        RiskChangeEsSourceV2,
        LegalChangeStrategy,
        CapitalChangeStrategy,
        {
          provide: RiskChangeHelper,
          useValue: mockRiskChangeHelper,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ModuleRef,
          useValue: mockModuleRef,
        },
      ],
    }).compile();

    service = module.get<RiskChangeEsSourceV2>(RiskChangeEsSourceV2);
    legalChangeStrategy = module.get<LegalChangeStrategy>(LegalChangeStrategy);
    capitalChangeStrategy = module.get<CapitalChangeStrategy>(CapitalChangeStrategy);
  });

  afterEach(async () => {
    await module.close();
  });

  describe('策略注册和查找', () => {
    it('应该正确注册所有策略', () => {
      expect(service).toBeDefined();
      expect(legalChangeStrategy).toBeDefined();
      expect(capitalChangeStrategy).toBeDefined();
    });

    it('应该能够根据维度类型找到对应的策略', () => {
      // 创建测试维度
      const legalPersonDimension = new DimensionHitStrategyPO();
      legalPersonDimension.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;

      const capitalDimension = new DimensionHitStrategyPO();
      capitalDimension.key = DimensionTypeEnums.MainInfoUpdateCapital;

      // 测试策略查找
      const legalStrategy = service['findStrategyForDimension'](legalPersonDimension);
      const capitalStrategy = service['findStrategyForDimension'](capitalDimension);

      expect(legalStrategy).toBeInstanceOf(LegalChangeStrategy);
      expect(capitalStrategy).toBeInstanceOf(CapitalChangeStrategy);
    });
  });

  describe('维度支持检查', () => {
    it('LegalChangeStrategy 应该支持法定代表人变更维度', () => {
      const supportedDimensions = legalChangeStrategy.getSupportedDimensions();
      expect(supportedDimensions).toContain(DimensionTypeEnums.MainInfoUpdateLegalPerson);
    });

    it('CapitalChangeStrategy 应该支持资本变更维度', () => {
      const supportedDimensions = capitalChangeStrategy.getSupportedDimensions();
      expect(supportedDimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapital);
      expect(supportedDimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapitalChange);
    });
  });

  describe('查询条件生成', () => {
    it('应该为法定代表人变更生成正确的查询条件', async () => {
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;

      const query = await legalChangeStrategy.generateDimensionQuery('test-company-id', dimension);

      expect(query).toBeDefined();
      expect(query).toHaveProperty('bool');
      expect(query['bool']).toHaveProperty('must');
      expect(query['bool']['must']).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            term: { KeyNo: 'test-company-id' },
          }),
        ]),
      );
    });

    it('应该为资本变更生成正确的查询条件', async () => {
      const dimension = new DimensionHitStrategyPO();
      dimension.key = DimensionTypeEnums.MainInfoUpdateCapital;

      const query = await capitalChangeStrategy.generateDimensionQuery('test-company-id', dimension);

      expect(query).toBeDefined();
      expect(query).toHaveProperty('bool');
      expect(query['bool']).toHaveProperty('must');
      expect(query['bool']['must']).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            term: { KeyNo: 'test-company-id' },
          }),
        ]),
      );
    });
  });

  describe('二次判断逻辑', () => {
    it('应该正确识别需要二次分析的维度', () => {
      const riskChangeDimension = new DimensionHitStrategyPO();
      riskChangeDimension.key = DimensionTypeEnums.RiskChange;

      const capitalChangeDimension = new DimensionHitStrategyPO();
      capitalChangeDimension.key = DimensionTypeEnums.MainInfoUpdateCapitalChange;

      const normalDimension = new DimensionHitStrategyPO();
      normalDimension.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;

      expect(service['needsSecondaryAnalysis'](riskChangeDimension)).toBe(true);
      expect(service['needsSecondaryAnalysis'](capitalChangeDimension)).toBe(true);
      expect(service['needsSecondaryAnalysis'](normalDimension)).toBe(false);
    });
  });

  describe('关联方维度识别', () => {
    it('应该正确识别关联方维度', () => {
      const relatedDimension = new DimensionHitStrategyPO();
      relatedDimension.key = DimensionTypeEnums.RecentInvestCancellationsRiskChange;

      const normalDimension = new DimensionHitStrategyPO();
      normalDimension.key = DimensionTypeEnums.MainInfoUpdateLegalPerson;

      expect(service['isRelatedDimension'](relatedDimension)).toBe(true);
      expect(service['isRelatedDimension'](normalDimension)).toBe(false);
    });
  });
});
