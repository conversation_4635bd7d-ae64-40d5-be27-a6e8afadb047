import { Test, TestingModule } from '@nestjs/testing';
import { EntityManager, getManager } from 'typeorm';
import { CapitalChangeStrategy } from '../../strategies/capital-change.strategy';
import { RiskChangeEsSourceV2 } from '../../risk-change-es.source.v2';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { getDimensionHitStrategyPO } from '../../../../test_utils_module/dimension.test.utils';
import { CurrencyChangeMap, RegisCapitalTrendMap } from 'libs/constants/risk.change.constants';
import { RiskChangeHelper } from '../../../../helper/risk.change.helper';

jest.setTimeout(60 * 1000);

/**
 * 资本变更策略对比测试
 * 将原始测试用例拆分到对应策略，验证新旧逻辑一致性
 */
describe('CapitalChangeStrategy - 对比测试', () => {
  let capitalChangeStrategy: CapitalChangeStrategy;
  let riskChangeServiceV2: RiskChangeEsSourceV2;
  let entityManager: EntityManager;
  let module: TestingModule;

  beforeAll(async () => {
    // 这里需要根据实际情况调整模块导入
    // 暂时使用简化的测试模块配置
    const mockRiskChangeHelper = {
      hitCategory123CurrencyChangeField: jest.fn().mockReturnValue(true),
      capitalReduceSelectCompareResult: jest.fn().mockReturnValue(true),
      hitPeriodRegisCapitalField123: jest.fn().mockReturnValue(true),
      hitMainInfoUpdateCapitalChange: jest.fn().mockReturnValue(true),
    };

    module = await Test.createTestingModule({
      providers: [
        CapitalChangeStrategy,
        RiskChangeEsSourceV2,
        {
          provide: RiskChangeHelper,
          useValue: mockRiskChangeHelper,
        },
        // 其他必要的依赖...
      ],
    }).compile();

    capitalChangeStrategy = module.get<CapitalChangeStrategy>(CapitalChangeStrategy);
    riskChangeServiceV2 = module.get<RiskChangeEsSourceV2>(RiskChangeEsSourceV2);
    
    // 如果需要数据库连接，取消注释
    // entityManager = getManager();
  });

  afterAll(async () => {
    await module.close();
    // if (entityManager) {
    //   await entityManager.connection.close();
    // }
  });

  describe('注册资本变更测试用例', () => {
    /**
     * 原始测试用例：【注册资本-发生减资】
     * 来源：risk.change.es.debug.spec.ts 第59行
     */
    it('【注册资本-发生减资】- 策略模式实现', async () => {
      const testData = {
        id: 'c6ccf9a7cc25213187131a77f327c6d6',
        companyId: 'f1fa0fc445c7af80acb2fa6474b7a3f8',
        companyName: '深圳市南网传媒有限公司',
      };

      // 创建维度配置 - 与原始测试完全一致
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.riskCategories,
          fieldValue: [37],
          options: [{ value: 37, label: '注册资本' }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.currencyChange,
          fieldValue: [0],
          options: CurrencyChangeMap,
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
        {
          fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
          fieldValue: [1],
          options: RegisCapitalTrendMap,
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
      ]);

      dimension.dimensionFilter = {
        id: testData.id,
      };

      // 测试策略查询生成
      const query = await capitalChangeStrategy.generateDimensionQuery(testData.companyId, dimension);
      expect(query).toBeDefined();
      expect(query).toHaveProperty('bool');

      // 验证查询条件包含正确的过滤器
      const queryBool = query as any;
      expect(queryBool.bool.must).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            term: { KeyNo: testData.companyId },
          }),
          expect.objectContaining({
            terms: { Category: [37] },
          }),
        ]),
      );

      // 测试详情处理 - 模拟响应数据
      const mockResponse = {
        Result: [
          {
            Id: testData.id,
            Category: 37,
            KeyNo: testData.companyId,
            Name: testData.companyName,
            ChangeExtend: JSON.stringify({
              A: '1000万元人民币', // 变更前
              B: '500万元人民币',  // 变更后
              T: 1, // 减少
            }),
            CreateDate: '2021-06-01',
          },
        ],
        Paging: {
          TotalRecords: 1,
          PageSize: 10,
          PageIndex: 1,
        },
      };

      const params: HitDetailsBaseQueryParams = {
        keyNo: testData.companyId,
        pageIndex: 1,
        pageSize: 10,
      };

      const result = await capitalChangeStrategy.processDimensionDetail(mockResponse, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBeGreaterThan(0);
      expect(result.Result.length).toBeGreaterThan(0);

      // 验证数据增强 - 检查是否添加了资本变更信息
      const firstResult = result.Result[0];
      expect(firstResult).toHaveProperty('BeforeCapital');
      expect(firstResult).toHaveProperty('AfterCapital');
      expect(firstResult).toHaveProperty('CapitalTrend');
      expect(firstResult.CapitalTrend).toBe('减少');
    });

    /**
     * 原始测试用例：【减资公告】相关逻辑
     * 测试 Category 123 的处理
     */
    it('【减资公告】- 策略模式实现', async () => {
      const testData = {
        companyId: 'test-company-123',
        companyName: '测试减资公告公司',
      };

      // 创建减资公告维度配置
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateCapital, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.riskCategories,
          fieldValue: [123],
          options: [{ value: 123, label: '减资公告' }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.currencyChange,
          fieldValue: [0],
          options: CurrencyChangeMap,
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
        {
          fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
          fieldValue: [5], // 减资比例大于5%
          options: [{ unit: '%', min: 0, max: 100 }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.GreaterThan,
        },
      ]);

      // 测试查询生成
      const query = await capitalChangeStrategy.generateDimensionQuery(testData.companyId, dimension);
      expect(query).toBeDefined();

      // 验证包含减资公告类别
      const queryBool = query as any;
      expect(queryBool.bool.must).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            terms: { Category: [123] },
          }),
        ]),
      );

      // 测试详情处理
      const mockResponse = {
        Result: [
          {
            Id: 'test-123',
            Category: 123,
            KeyNo: testData.companyId,
            Name: testData.companyName,
            ChangeExtend: JSON.stringify({
              A: '1000万元人民币',
              B: '800万元人民币',
              T: 1, // 减少
              BC: '人民币', // 变更前币种
              AC: '人民币', // 变更后币种
            }),
            CreateDate: '2021-06-01',
          },
        ],
        Paging: {
          TotalRecords: 1,
          PageSize: 10,
          PageIndex: 1,
        },
      };

      const params: HitDetailsBaseQueryParams = {
        keyNo: testData.companyId,
        pageIndex: 1,
        pageSize: 10,
      };

      const result = await capitalChangeStrategy.processDimensionDetail(mockResponse, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Result.length).toBeGreaterThan(0);

      // 验证减资比例计算
      const firstResult = result.Result[0];
      expect(firstResult).toHaveProperty('CapitalChangeRatio');
      expect(firstResult.CapitalChangeRatio).toBeGreaterThan(0);
    });
  });

  describe('MainInfoUpdateCapitalChange 维度测试', () => {
    /**
     * 测试 MainInfoUpdateCapitalChange 维度的特殊处理逻辑
     */
    it('【MainInfoUpdateCapitalChange】- 二次判断逻辑', async () => {
      const testData = {
        companyId: 'test-capital-change-123',
        companyName: '测试资本变更公司',
      };

      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.MainInfoUpdateCapitalChange, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
          fieldValue: [{
            valuePeriodBaseLine: 12, // 12个月周期
            threshold: 5, // 5%阈值
          }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
      ]);

      // 测试查询生成
      const query = await capitalChangeStrategy.generateDimensionQuery(testData.companyId, dimension);
      expect(query).toBeDefined();

      // 模拟响应数据
      const mockResponse = {
        Result: [
          {
            Id: 'test-capital-change',
            Category: 37,
            KeyNo: testData.companyId,
            Name: testData.companyName,
            ChangeExtend: JSON.stringify({
              A: '1000万元人民币',
              B: '1200万元人民币',
              T: 2, // 增加
            }),
            CreateDate: '2021-06-01',
          },
        ],
        Paging: {
          TotalRecords: 1,
          PageSize: 10,
          PageIndex: 1,
        },
      };

      const params: HitDetailsBaseQueryParams = {
        keyNo: testData.companyId,
        pageIndex: 1,
        pageSize: 10,
      };

      const result = await capitalChangeStrategy.processDimensionDetail(mockResponse, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      // 注意：这里的结果取决于 RiskChangeHelper.hitMainInfoUpdateCapitalChange 的返回值
      // 在实际测试中，应该验证该方法被正确调用
    });
  });

  describe('策略方法验证', () => {
    it('应该支持正确的维度类型', () => {
      const supportedDimensions = capitalChangeStrategy.getSupportedDimensions();
      expect(supportedDimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapital);
      expect(supportedDimensions).toContain(DimensionTypeEnums.MainInfoUpdateCapitalChange);
    });

    it('应该返回正确的维度类别映射', () => {
      const categoryMap = capitalChangeStrategy.getDimensionCategoryMap();
      expect(categoryMap[DimensionTypeEnums.MainInfoUpdateCapital]).toContain(37); // 注册资本变更
      expect(categoryMap[DimensionTypeEnums.MainInfoUpdateCapital]).toContain(123); // 减资公告
    });

    it('应该正确判断维度支持', () => {
      const supportedDimension = { key: DimensionTypeEnums.MainInfoUpdateCapital } as any;
      const unsupportedDimension = { key: DimensionTypeEnums.BusinessAbnormal } as any;

      expect(capitalChangeStrategy.supportsDimension(supportedDimension)).toBe(true);
      expect(capitalChangeStrategy.supportsDimension(unsupportedDimension)).toBe(false);
    });
  });
});
