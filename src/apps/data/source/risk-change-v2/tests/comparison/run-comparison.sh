#!/bin/bash

# Risk Change V2 对比测试运行脚本
# 用于快速运行新旧代码的对比测试

echo "🚀 开始 Risk Change V2 对比测试..."
echo "=================================="

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 创建测试结果目录
RESULT_DIR="src/apps/data/source/risk-change-v2/tests/comparison/results"
mkdir -p "$RESULT_DIR"

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}📊 运行原始测试...${NC}"
echo "=================================="

# 运行原始测试
npm test -- --testPathPattern="src/apps/data/source/risk-change/risk.change.es.debug.spec.ts" \
  --json --outputFile="$RESULT_DIR/original_$TIMESTAMP.json" \
  --silent 2>/dev/null

ORIGINAL_EXIT_CODE=$?

if [ $ORIGINAL_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ 原始测试运行完成${NC}"
else
    echo -e "${YELLOW}⚠️ 原始测试运行完成（部分失败）${NC}"
fi

echo ""
echo -e "${BLUE}📊 运行 V2 策略测试...${NC}"
echo "=================================="

# 运行 V2 测试
npm test -- --testPathPattern="src/apps/data/source/risk-change-v2/tests/comparison" \
  --json --outputFile="$RESULT_DIR/v2_$TIMESTAMP.json" \
  --silent 2>/dev/null

V2_EXIT_CODE=$?

if [ $V2_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ V2 测试运行完成${NC}"
else
    echo -e "${YELLOW}⚠️ V2 测试运行完成（部分失败）${NC}"
fi

echo ""
echo -e "${BLUE}📄 生成对比报告...${NC}"
echo "=================================="

# 生成简化的对比报告
REPORT_FILE="$RESULT_DIR/comparison_report_$TIMESTAMP.md"

cat > "$REPORT_FILE" << EOF
# Risk Change V2 测试对比报告

生成时间: $(date)

## 📊 测试执行结果

### 原始测试
- 退出码: $ORIGINAL_EXIT_CODE
- 结果文件: original_$TIMESTAMP.json

### V2 策略测试  
- 退出码: $V2_EXIT_CODE
- 结果文件: v2_$TIMESTAMP.json

## 🎯 快速对比

| 测试类型 | 状态 | 说明 |
|---------|------|------|
| 原始测试 | $([ $ORIGINAL_EXIT_CODE -eq 0 ] && echo "✅ 通过" || echo "⚠️ 部分失败") | 基于原始 risk-change 实现 |
| V2 策略测试 | $([ $V2_EXIT_CODE -eq 0 ] && echo "✅ 通过" || echo "⚠️ 部分失败") | 基于新的策略模式实现 |

## 📋 测试用例覆盖

### 已实现的策略测试
- ✅ 资本变更策略测试 (CapitalChangeStrategy)
- ✅ 法定代表人变更策略测试 (LegalChangeStrategy)  
- ✅ 关联方投资企业注销策略测试 (InvestCompanyCancellationStrategy)

### 测试场景覆盖
- ✅ 注册资本减资场景
- ✅ 法定代表人变更场景
- ✅ 投资企业注销场景
- ✅ 成员变更场景
- ✅ 时间周期过滤
- ✅ 投资比例阈值计算

## 💡 下一步行动

### 如果两个测试都通过
1. 验证具体的测试结果数据一致性
2. 进行性能对比测试
3. 准备生产环境迁移计划

### 如果存在失败的测试
1. 查看详细的测试结果文件
2. 分析失败原因
3. 修复相关问题后重新运行

## 🔍 详细分析

要查看详细的测试结果，请运行：

\`\`\`bash
# 查看原始测试详细结果
cat $RESULT_DIR/original_$TIMESTAMP.json | jq '.'

# 查看 V2 测试详细结果  
cat $RESULT_DIR/v2_$TIMESTAMP.json | jq '.'
\`\`\`

## 📞 问题反馈

如果发现问题，请：
1. 检查测试环境配置
2. 确认依赖服务可用性
3. 查看具体的错误日志
4. 联系开发团队

---

*报告生成时间: $(date)*
EOF

echo -e "${GREEN}✅ 对比报告已生成: $REPORT_FILE${NC}"

echo ""
echo -e "${BLUE}📈 测试总结${NC}"
echo "=================================="

if [ $ORIGINAL_EXIT_CODE -eq 0 ] && [ $V2_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试都通过了！${NC}"
    echo -e "${GREEN}   新的策略模式实现与原始代码功能一致${NC}"
    echo -e "${GREEN}   可以考虑进行下一步的集成测试${NC}"
elif [ $ORIGINAL_EXIT_CODE -ne 0 ] && [ $V2_EXIT_CODE -eq 0 ]; then
    echo -e "${YELLOW}⚠️ V2 测试通过，但原始测试有问题${NC}"
    echo -e "${YELLOW}   可能是测试环境或数据问题${NC}"
    echo -e "${YELLOW}   建议检查原始测试的运行环境${NC}"
elif [ $ORIGINAL_EXIT_CODE -eq 0 ] && [ $V2_EXIT_CODE -ne 0 ]; then
    echo -e "${RED}❌ V2 测试失败，需要修复${NC}"
    echo -e "${RED}   请检查 V2 策略实现的问题${NC}"
    echo -e "${RED}   查看详细错误日志进行调试${NC}"
else
    echo -e "${RED}❌ 两个测试都有问题${NC}"
    echo -e "${RED}   建议先检查测试环境配置${NC}"
    echo -e "${RED}   然后逐个解决问题${NC}"
fi

echo ""
echo -e "${BLUE}📁 测试结果文件位置:${NC}"
echo "   $RESULT_DIR/"
echo ""

# 如果安装了 jq，显示简化的结果统计
if command -v jq &> /dev/null; then
    echo -e "${BLUE}📊 快速统计 (如果 JSON 文件有效):${NC}"
    
    if [ -f "$RESULT_DIR/original_$TIMESTAMP.json" ]; then
        echo -n "   原始测试: "
        jq -r '.numPassedTests // 0' "$RESULT_DIR/original_$TIMESTAMP.json" 2>/dev/null | xargs -I {} echo "{} 通过" || echo "无法解析"
    fi
    
    if [ -f "$RESULT_DIR/v2_$TIMESTAMP.json" ]; then
        echo -n "   V2 测试: "
        jq -r '.numPassedTests // 0' "$RESULT_DIR/v2_$TIMESTAMP.json" 2>/dev/null | xargs -I {} echo "{} 通过" || echo "无法解析"
    fi
fi

echo ""
echo -e "${GREEN}🏁 对比测试完成！${NC}"
