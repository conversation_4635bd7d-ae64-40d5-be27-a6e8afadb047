import { Test, TestingModule } from '@nestjs/testing';
import { InvestCompanyCancellationStrategy } from '../../strategies/invest-company-cancellation.strategy';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from 'libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { getDimensionHitStrategyPO } from '../../../../test_utils_module/dimension.test.utils';
import { RelatedTypeEnums } from 'libs/enums/dimension/RelatedTypeEnums';

jest.setTimeout(60 * 1000);

/**
 * 关联方维度对比测试
 * 将原始测试用例拆分到对应策略，验证新旧逻辑一致性
 */
describe('RelatedCompany - 对比测试', () => {
  let investCancellationStrategy: InvestCompanyCancellationStrategy;
  let module: TestingModule;

  beforeAll(async () => {
    const mockRelatedCompanySource = {
      getInvestCompanies: jest.fn().mockResolvedValue([
        { keyNo: 'invest-company-1', name: '投资公司1', fundedRatio: 60 },
        { keyNo: 'invest-company-2', name: '投资公司2', fundedRatio: 30 },
      ]),
    };

    const mockCompanyDetailService = {
      getCompanyBasicInfo: jest.fn().mockResolvedValue({
        keyNo: 'test-company',
        name: '测试公司',
        status: 1,
      }),
    };

    module = await Test.createTestingModule({
      providers: [
        InvestCompanyCancellationStrategy,
        {
          provide: 'RelatedCompanySource',
          useValue: mockRelatedCompanySource,
        },
        {
          provide: 'CompanyDetailService',
          useValue: mockCompanyDetailService,
        },
      ],
    }).compile();

    investCancellationStrategy = module.get<InvestCompanyCancellationStrategy>(InvestCompanyCancellationStrategy);
  });

  afterAll(async () => {
    await module.close();
  });

  describe('投资企业注销测试用例', () => {
    /**
     * 原始测试用例：投资变动-近12个月内公司对外投资企业大量注销或吊销
     * 来源：risk.change.es.debug.spec.ts 第679行
     */
    it('【投资变动-近12个月内公司对外投资企业大量注销或吊销】- 策略模式实现', async () => {
      const testData = {
        companyId: '013bf839e4e1c85ba19e6206f66e0bb2',
        companyName: '新农创美丽乡村（枣庄市山亭区）产业发展有限公司',
      };

      // 创建维度配置 - 与原始测试完全一致
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecentInvestCancellationsRiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 1,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.timePeriod,
          fieldValue: [12],
          options: [{ unit: '月', min: 1, max: 12 }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
        {
          fieldKey: DimensionFieldKeyEnums.hitCount,
          fieldValue: [1],
          compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
        {
          fieldKey: DimensionFieldKeyEnums.relatedRoleType,
          fieldValue: [RelatedTypeEnums.InvestCompany],
          options: [{ value: RelatedTypeEnums.InvestCompany, label: '对外投资' }],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.fundedRatioLevel,
          fieldValue: [0],
          options: [
            { value: 0, label: '不限' },
            { value: 1, label: '<=5%' },
            { value: 2, label: '>5%' },
            { value: 3, label: '>20%' },
            { value: 4, label: '>50%' },
            { value: 5, label: '>66.66%' },
            { value: 6, label: '=100%' },
          ],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.riskCategories,
          fieldValue: [38],
          options: [{ value: 38, label: '经营状态' }],
          accessScope: 2,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.businessStatus,
          fieldValue: [90, 99],
          options: [
            { label: '吊销', value: 90 },
            { label: '注销', value: 99 },
          ],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
      ]);

      // 测试策略查询生成
      const query = await investCancellationStrategy.generateDimensionQuery(testData.companyId, dimension);
      expect(query).toBeDefined();
      expect(query).toHaveProperty('bool');

      // 验证查询条件包含正确的过滤器
      const queryBool = query as any;
      expect(queryBool.bool.must).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            terms: { KeyNo: expect.any(Array) }, // 应该包含关联企业的 KeyNo
          }),
          expect.objectContaining({
            terms: { Category: [38] },
          }),
          expect.objectContaining({
            terms: { 'Extend1.BusinessStatus': [90, 99] },
          }),
        ]),
      );

      // 测试详情处理 - 模拟响应数据
      const mockResponse = {
        Result: [
          {
            Id: 'invest-risk-1',
            Category: 38,
            KeyNo: 'invest-company-1',
            Name: '投资公司1',
            ChangeExtend: JSON.stringify({
              A: '正常', // 变更前状态
              B: '注销', // 变更后状态
              C: '2021-06-01', // 变更日期
              BusinessStatus: 99,
            }),
            CreateDate: '2021-06-01',
          },
          {
            Id: 'invest-risk-2',
            Category: 38,
            KeyNo: 'invest-company-2',
            Name: '投资公司2',
            ChangeExtend: JSON.stringify({
              A: '正常',
              B: '吊销',
              C: '2021-05-15',
              BusinessStatus: 90,
            }),
            CreateDate: '2021-05-15',
          },
        ],
        Paging: {
          TotalRecords: 2,
          PageSize: 10,
          PageIndex: 1,
        },
      };

      const params: HitDetailsBaseQueryParams = {
        keyNo: testData.companyId,
        pageIndex: 1,
        pageSize: 10,
      };

      const result = await investCancellationStrategy.processDimensionDetail(mockResponse, dimension, params);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBeGreaterThan(0);
      expect(result.Result.length).toBeGreaterThan(0);

      // 验证数据增强 - 检查是否添加了投资关系信息
      const firstResult = result.Result[0];
      expect(firstResult).toHaveProperty('InvestCompanyName');
      expect(firstResult).toHaveProperty('FundedRatio');
      expect(firstResult).toHaveProperty('BusinessStatusChange');
      expect(firstResult).toHaveProperty('ParentCompanyId');
      expect(firstResult.ParentCompanyId).toBe(testData.companyId);
    });

    /**
     * 测试不同投资比例阈值的处理
     */
    it('【投资企业注销-投资比例过滤】- 策略模式实现', async () => {
      const testData = {
        companyId: 'test-invest-ratio',
        companyName: '测试投资比例公司',
      };

      // 测试投资比例 > 50% 的过滤
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecentInvestCancellationsRiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.isValid,
          fieldValue: [1],
          accessScope: 1,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.timePeriod,
          fieldValue: [12],
          options: [{ unit: '月', min: 1, max: 12 }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
        {
          fieldKey: DimensionFieldKeyEnums.fundedRatioLevel,
          fieldValue: [4], // >50%
          options: [
            { value: 0, label: '不限' },
            { value: 1, label: '<=5%' },
            { value: 2, label: '>5%' },
            { value: 3, label: '>20%' },
            { value: 4, label: '>50%' },
            { value: 5, label: '>66.66%' },
            { value: 6, label: '=100%' },
          ],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.relatedRoleType,
          fieldValue: [RelatedTypeEnums.InvestCompany],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.riskCategories,
          fieldValue: [38],
          accessScope: 2,
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
        {
          fieldKey: DimensionFieldKeyEnums.businessStatus,
          fieldValue: [90, 99],
          compareType: DimensionFieldCompareTypeEnums.ContainsAny,
        },
      ]);

      // 测试关联企业过滤逻辑
      const relatedCompanies = await investCancellationStrategy['getFilteredInvestCompanies'](
        testData.companyId,
        dimension,
      );

      // 验证只返回投资比例 > 50% 的企业
      expect(relatedCompanies).toBeDefined();
      expect(relatedCompanies.length).toBeGreaterThan(0);
      relatedCompanies.forEach((company) => {
        expect(company.fundedRatio).toBeGreaterThan(50);
      });
    });
  });

  describe('时间周期处理测试', () => {
    /**
     * 测试不同时间周期的处理逻辑
     */
    it('【时间周期过滤】- 策略模式实现', async () => {
      const testData = {
        companyId: 'test-time-period',
        companyName: '测试时间周期公司',
      };

      // 测试6个月周期
      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecentInvestCancellationsRiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.timePeriod,
          fieldValue: [6],
          options: [{ unit: '月', min: 1, max: 12 }],
          accessScope: 0,
          compareType: DimensionFieldCompareTypeEnums.Equal,
        },
      ]);

      const query = await investCancellationStrategy.generateDimensionQuery(testData.companyId, dimension);
      const queryBool = query as any;

      // 验证时间范围过滤
      const timeRangeFilter = queryBool.bool.must.find((filter: any) => filter.range?.CreateDate);
      expect(timeRangeFilter).toBeDefined();
      expect(timeRangeFilter.range.CreateDate).toHaveProperty('gte');

      // 验证时间计算正确性
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const expectedTimestamp = Math.floor(sixMonthsAgo.getTime() / 1000);

      expect(timeRangeFilter.range.CreateDate.gte).toBeCloseTo(expectedTimestamp, -2); // 允许小的时间差异
    });
  });

  describe('阈值计算测试', () => {
    /**
     * 测试命中数量阈值的计算逻辑
     */
    it('【命中数量阈值计算】- 策略模式实现', async () => {
      const testData = {
        companyId: 'test-threshold',
        companyName: '测试阈值公司',
      };

      const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RecentInvestCancellationsRiskChange, [
        {
          fieldKey: DimensionFieldKeyEnums.hitCount,
          fieldValue: [3], // 命中数量 >= 3
          compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
        },
      ]);

      // 模拟多个命中结果
      const mockResponse = {
        Result: [
          { Id: '1', KeyNo: 'invest-1', Category: 38 },
          { Id: '2', KeyNo: 'invest-2', Category: 38 },
          { Id: '3', KeyNo: 'invest-3', Category: 38 },
          { Id: '4', KeyNo: 'invest-4', Category: 38 },
        ],
        Paging: {
          TotalRecords: 4,
          PageSize: 10,
          PageIndex: 1,
        },
      };

      const params: HitDetailsBaseQueryParams = {
        keyNo: testData.companyId,
        pageIndex: 1,
        pageSize: 10,
      };

      const result = await investCancellationStrategy.processDimensionDetail(mockResponse, dimension, params);

      // 验证阈值计算
      expect(result).toBeDefined();
      expect(result.Paging.TotalRecords).toBeGreaterThanOrEqual(3);

      // 验证结果包含阈值信息
      expect(result).toHaveProperty('ThresholdInfo');
      expect(result.ThresholdInfo).toHaveProperty('RequiredCount');
      expect(result.ThresholdInfo).toHaveProperty('ActualCount');
      expect(result.ThresholdInfo.RequiredCount).toBe(3);
      expect(result.ThresholdInfo.ActualCount).toBe(4);
    });
  });

  describe('策略方法验证', () => {
    it('应该支持正确的维度类型', () => {
      const supportedDimensions = investCancellationStrategy.getSupportedDimensions();
      expect(supportedDimensions).toContain(DimensionTypeEnums.RecentInvestCancellationsRiskChange);
    });

    it('应该正确判断维度支持', () => {
      const supportedDimension = { key: DimensionTypeEnums.RecentInvestCancellationsRiskChange } as any;
      const unsupportedDimension = { key: DimensionTypeEnums.BusinessAbnormal } as any;

      expect(investCancellationStrategy.supportsDimension(supportedDimension)).toBe(true);
      expect(investCancellationStrategy.supportsDimension(unsupportedDimension)).toBe(false);
    });

    it('应该正确处理投资比例过滤', () => {
      const companies = [
        { keyNo: 'c1', fundedRatio: 30 },
        { keyNo: 'c2', fundedRatio: 60 },
        { keyNo: 'c3', fundedRatio: 80 },
      ];

      // 测试 >50% 过滤
      const filtered50 = investCancellationStrategy['filterByFundedRatio'](companies, 4);
      expect(filtered50).toHaveLength(2);
      expect(filtered50.every((c) => c.fundedRatio > 50)).toBe(true);

      // 测试 >20% 过滤
      const filtered20 = investCancellationStrategy['filterByFundedRatio'](companies, 3);
      expect(filtered20).toHaveLength(3);
      expect(filtered20.every((c) => c.fundedRatio > 20)).toBe(true);
    });
  });
});
