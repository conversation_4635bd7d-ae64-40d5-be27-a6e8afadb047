# Risk Change V2 对比测试框架

## 🎯 目标

通过将原始 `risk-change` 的测试用例拆分并重新实现，验证新的策略模式重构是否正确保持了原有的业务逻辑。

## 📁 文件结构

```
tests/comparison/
├── README.md                           # 本文档
├── capital-change-comparison.spec.ts   # 资本变更策略对比测试
├── legal-change-comparison.spec.ts     # 法定代表人变更策略对比测试
├── related-company-comparison.spec.ts  # 关联方维度对比测试
├── test-comparison-runner.ts           # 自动化对比测试运行器
├── run-comparison.sh                   # 快速运行脚本
└── results/                            # 测试结果输出目录
```

## 🚀 快速开始

### 方法一：使用脚本运行（推荐）

```bash
# 给脚本执行权限
chmod +x src/apps/data/source/risk-change-v2/tests/comparison/run-comparison.sh

# 运行对比测试
./src/apps/data/source/risk-change-v2/tests/comparison/run-comparison.sh
```

### 方法二：手动运行

```bash
# 运行原始测试
npm test -- --testPathPattern="src/apps/data/source/risk-change/risk.change.es.debug.spec.ts"

# 运行 V2 策略测试
npm test -- --testPathPattern="src/apps/data/source/risk-change-v2/tests/comparison"
```

### 方法三：使用 TypeScript 运行器

```bash
# 编译并运行对比测试运行器
npx ts-node src/apps/data/source/risk-change-v2/tests/comparison/test-comparison-runner.ts
```

## 📊 测试用例映射

### 资本变更相关测试

| 原始测试用例 | V2 策略测试 | 状态 |
|-------------|------------|------|
| 【注册资本-发生减资】 | CapitalChangeStrategy - 注册资本减资 | ✅ 已实现 |
| 【减资公告】 | CapitalChangeStrategy - 减资公告 | ✅ 已实现 |
| MainInfoUpdateCapitalChange 维度 | CapitalChangeStrategy - 二次判断逻辑 | ✅ 已实现 |

### 法定代表人变更相关测试

| 原始测试用例 | V2 策略测试 | 状态 |
|-------------|------------|------|
| 【第一大股东变更-成员变更】 | LegalChangeStrategy - 成员变更 | ✅ 已实现 |
| 【法定代表人变更-Category39】 | LegalChangeStrategy - 法定代表人变更 | ✅ 已实现 |
| 【实控人变更-成员变更】 | LegalChangeStrategy - 实控人变更 | ✅ 已实现 |

### 关联方维度相关测试

| 原始测试用例 | V2 策略测试 | 状态 |
|-------------|------------|------|
| 【投资变动-近12个月内公司对外投资企业大量注销或吊销】 | InvestCompanyCancellationStrategy | ✅ 已实现 |
| 【投资比例过滤】 | InvestCompanyCancellationStrategy - 投资比例过滤 | ✅ 已实现 |
| 【时间周期处理】 | InvestCompanyCancellationStrategy - 时间周期过滤 | ✅ 已实现 |

## 🔍 测试验证重点

### 1. 功能一致性验证

- **查询条件生成**: 验证新策略生成的 ES 查询条件与原始逻辑一致
- **数据处理逻辑**: 验证数据增强和格式化逻辑正确
- **业务规则**: 验证各种业务规则（阈值、过滤条件等）的实现
- **返回数据结构**: 验证返回的数据结构与原始代码一致

### 2. 边界条件测试

- **空数据处理**: 验证无数据时的处理逻辑
- **异常数据处理**: 验证异常数据的容错能力
- **阈值边界**: 验证各种阈值的边界条件
- **时间范围**: 验证时间范围过滤的准确性

### 3. 性能对比

- **查询性能**: 对比查询执行时间
- **内存使用**: 对比内存占用情况
- **并发处理**: 验证并发处理能力

## 📈 测试结果分析

### 成功标准

- ✅ 所有对应的测试用例都能通过
- ✅ 返回数据结构与原始代码一致
- ✅ 业务逻辑验证结果一致
- ✅ 性能不低于原始实现
- ✅ 错误处理逻辑正确

### 失败处理

如果测试失败，按以下步骤排查：

1. **检查测试环境**
   - 数据库连接是否正常
   - ES 服务是否可用
   - 依赖服务是否启动

2. **分析具体失败原因**
   - 查看详细的错误日志
   - 对比预期结果和实际结果
   - 检查数据格式是否正确

3. **修复问题**
   - 修复策略实现中的问题
   - 更新测试用例（如果原始逻辑有变化）
   - 完善错误处理逻辑

## 🛠️ 开发指南

### 添加新的对比测试

1. **创建测试文件**
   ```typescript
   // 文件名: {strategy-name}-comparison.spec.ts
   describe('{StrategyName} - 对比测试', () => {
     // 测试实现
   });
   ```

2. **从原始测试中提取用例**
   - 找到对应的原始测试用例
   - 提取测试数据和预期结果
   - 重新实现为策略测试

3. **验证一致性**
   - 确保测试数据完全一致
   - 验证业务逻辑实现正确
   - 检查返回数据格式

### 测试数据管理

- **使用真实数据**: 尽量使用与原始测试相同的真实数据
- **数据隔离**: 确保测试数据不会相互影响
- **数据清理**: 测试后及时清理临时数据

### Mock 策略

- **外部依赖**: Mock 外部服务调用
- **数据库操作**: Mock 数据库查询
- **时间相关**: Mock 时间函数确保测试稳定性

## 📋 检查清单

### 测试实现检查

- [ ] 所有原始测试用例都有对应的 V2 实现
- [ ] 测试数据与原始测试完全一致
- [ ] 预期结果验证逻辑正确
- [ ] 错误场景处理完善
- [ ] 性能测试覆盖

### 代码质量检查

- [ ] 测试代码结构清晰
- [ ] 注释和文档完善
- [ ] 遵循测试最佳实践
- [ ] Mock 使用合理
- [ ] 断言充分且准确

### 集成验证

- [ ] 可以独立运行
- [ ] 与 CI/CD 集成
- [ ] 测试报告生成正确
- [ ] 失败时提供有用信息

## 🎯 最佳实践

1. **保持测试独立**: 每个测试用例应该能够独立运行
2. **使用描述性名称**: 测试名称应该清楚描述测试场景
3. **充分的断言**: 验证所有重要的返回值和副作用
4. **适当的 Mock**: 只 Mock 必要的外部依赖
5. **性能考虑**: 避免测试运行时间过长
6. **错误处理**: 测试异常情况和边界条件
7. **文档更新**: 及时更新测试文档和说明

## 🔗 相关文档

- [Risk Change V2 架构文档](../readme/stage2/readme.md)
- [策略模式实现指南](../readme/stage1/steps_overview.md)
- [原始测试文件](../../risk-change/risk.change.es.debug.spec.ts)
- [测试工具函数](../../../test_utils_module/dimension.test.utils.ts)

---

*最后更新: 2024年*
