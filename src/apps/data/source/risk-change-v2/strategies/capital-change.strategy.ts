import { Injectable } from '@nestjs/common';
import * as Bluebird from 'bluebird';
import { cloneDeep, orderBy } from 'lodash';
import { DimensionTypeEnums } from '../../../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../../../libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from '../../../../../libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionAnalyzeParamsPO } from '../../../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitDetailsBaseQueryParams } from '../../../../../libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from '../../../../../libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from '../../../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from '../../../../data/helper/risk.change.helper';
import { RiskChangeUtils } from '../utils/risk-change.utils';
import { BaseRiskChangeStrategy, DimensionCategoryMap } from './base-risk-change.strategy';

/**
 * 资本变更策略类
 * 处理注册资本增减相关维度
 */
@Injectable()
export class CapitalChangeStrategy extends BaseRiskChangeStrategy {
  /**
   * 资本变更相关维度列表
   */
  private readonly capitalChangeDimensions = [DimensionTypeEnums.MainInfoUpdateCapital, DimensionTypeEnums.MainInfoUpdateCapitalChange];

  /**
   * 资本变更维度与风险变更类别映射
   */
  private readonly capitalChangeDimensionCategoryMap: DimensionCategoryMap = {
    [DimensionTypeEnums.MainInfoUpdateCapital]: [
      RiskChangeCategoryEnum.category37, // 注册资本变更
      RiskChangeCategoryEnum.category123, // 减资公告
    ],
    [DimensionTypeEnums.MainInfoUpdateCapitalChange]: [
      RiskChangeCategoryEnum.category37, // 注册资本变更
    ],
  };

  /**
   * 构造函数
   * @param riskChangeHelper 风险变更辅助服务
   */
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super(CapitalChangeStrategy.name);
  }

  /**
   * 获取支持的维度类型列表
   */
  getSupportedDimensions(): DimensionTypeEnums[] {
    return this.capitalChangeDimensions;
  }

  /**
   * 获取维度类型与风险变更类别的映射
   */
  getDimensionCategoryMap(): DimensionCategoryMap {
    return this.capitalChangeDimensionCategoryMap;
  }

  /**
   * 检查维度类型是否由该策略处理
   * @param dimension 维度策略
   */
  supportsDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.getSupportedDimensions().includes(dimension.key);
  }

  /**
   * 生成维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  async generateDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    try {
      // 使用工具类创建基础查询对象
      const query = RiskChangeUtils.createBaseQuery(companyId) as any;

      // 添加风险类别过滤
      const categories = this.getDimensionCategoryMap()[dimension.key];
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加维度过滤条件
      const dimensionFilter = dimension?.dimensionFilter;
      if (dimensionFilter?.startTime && dimensionFilter?.endTime) {
        query.bool.must.push({
          range: {
            CreateDate: {
              gte: Math.ceil(dimensionFilter.startTime),
              lte: Math.ceil(dimensionFilter.endTime),
            },
          },
        });
      }

      // 添加有效性过滤
      const isValidField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      if (isValidField && Number(isValidField.fieldValue[0]) >= 0) {
        query.bool.must.push({
          term: {
            IsValid: Number(isValidField.fieldValue[0]),
          },
        });
      } else {
        // 默认只查询有效记录
        query.bool.must.push({
          term: {
            IsValid: 1,
          },
        });
      }

      return query;
    } catch (error) {
      this.logError('生成资本变更查询条件失败', error);
      return null;
    }
  }

  /**
   * 处理维度详情数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async processDimensionDetail(
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    try {
      if (!response?.Result?.length) {
        return response;
      }

      const hitData: any[] = [];

      // 根据维度类型进行不同的处理
      if (dimension.key === DimensionTypeEnums.CapitalReduction) {
        await this.processCapitalReduction(hitData, response, dimension, params);
      } else if (dimension.key === DimensionTypeEnums.MainInfoUpdateCapitalChange) {
        await this.processMainInfoUpdateCapitalChange(hitData, response, dimension, params);
      }

      // 创建结果
      const result = new HitDetailsBaseResponse();
      const pageSize = params?.pageSize || 10;
      const pageIndex = params?.pageIndex || 1;
      const start = (pageIndex - 1) * pageSize;
      const end = start + pageSize;

      result.Paging = {
        PageSize: pageSize,
        PageIndex: pageIndex,
        TotalRecords: hitData.length,
      };

      // 排序并分页
      const sortedData = orderBy(hitData, 'CreateDate', 'desc');
      result.Result = sortedData.slice(start, end);

      return result;
    } catch (error) {
      this.logError('处理资本变更详情失败', error);
      return response;
    }
  }

  /**
   * 处理减资公告
   * @param hitData 命中数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   */
  private async processCapitalReduction(
    hitData: any[],
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
  ): Promise<void> {
    await Bluebird.map(response.Result, async (itemRaw) => {
      try {
        const item = cloneDeep(itemRaw);

        // 使用工具类解析JSON字段
        Object.keys(item).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            item[key] = RiskChangeUtils.parseJsonField(item[key]);
          }
        });

        let isHit = true;

        // 处理减资公告
        if (item.Category === RiskChangeCategoryEnum.category123) {
          // 币种变更
          const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
          if (currencyChangeField && isHit) {
            isHit = this.riskChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, item);
          }

          // 资本减少比率
          const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
          if (regisCapitalChangeRatioField && isHit) {
            isHit = this.riskChangeHelper.capitalReduceSelectCompareResult(regisCapitalChangeRatioField, item);
          }

          // 注册资本变更周期
          const changeRangeRegisCapitalCycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
          if (changeRangeRegisCapitalCycle && isHit) {
            const valuePeriodBaseLine = changeRangeRegisCapitalCycle.fieldValue[0]?.valuePeriodBaseLine;
            if (valuePeriodBaseLine) {
              const periodRes = await this.getCommonCivilRiskChange(params.keyNo, [RiskChangeCategoryEnum.category37], valuePeriodBaseLine, 'year', 10000);

              if (periodRes?.Result?.length) {
                isHit = this.riskChangeHelper.hitPeriodRegisCapitalField123(changeRangeRegisCapitalCycle, periodRes.Result, item);
              } else {
                isHit = false;
              }
            }
          }
        }

        if (isHit) {
          // 添加计算的资本变更比例
          this.enrichCapitalChangeInfo(item);
          hitData.push(item);
        }
      } catch (error) {
        this.logError('处理减资公告详情项失败', error);
      }
    });
  }

  /**
   * 处理注册资本变更
   * @param hitData 命中数据
   * @param response 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   */
  private async processMainInfoUpdateCapitalChange(
    hitData: any[],
    response: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
  ): Promise<void> {
    if (response?.Result?.length) {
      // 先处理原始数据，解析JSON字段并添加计算的资本变更比例
      const processedResults = response.Result.map((item) => {
        const processed = cloneDeep(item);

        // 解析JSON字段
        Object.keys(processed).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            processed[key] = RiskChangeUtils.parseJsonField(processed[key]);
          }
        });

        // 添加计算的资本变更比例
        this.enrichCapitalChangeInfo(processed);

        return processed;
      });

      // 检查是否有周期限制字段
      const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
      if (strategyFieldByKey) {
        // 使用辅助服务进行命中判断
        const hit = this.riskChangeHelper.hitMainInfoUpdateCapitalChange(strategyFieldByKey, processedResults);
        if (hit) {
          hitData.push(...processedResults);
        }
      } else {
        // 如果没有特定的策略字段，默认命中所有结果
        hitData.push(...processedResults);
      }
    }
  }

  /**
   * 获取通用风险动态
   * @param companyId 企业ID
   * @param categories 风险类别
   * @param periodTime 周期时间
   * @param periodUnit 周期单位
   * @param pageSize 页大小
   */
  private async getCommonCivilRiskChange(
    companyId: string,
    categories: RiskChangeCategoryEnum[],
    periodTime: number,
    periodUnit: string,
    pageSize = 1,
  ): Promise<HitDetailsBaseResponse> {
    try {
      // 创建基础查询对象
      const query = RiskChangeUtils.createBaseQuery(companyId) as any;

      // 添加有效性过滤
      query.bool.must.push({
        term: {
          IsValid: 1,
        },
      });

      // 添加版本过滤
      query.bool.must.push({
        range: {
          Es_Version: {
            lt: 999999,
          },
        },
      });

      // 添加类别过滤
      if (categories?.length) {
        query.bool.must.push({
          terms: {
            Category: categories,
          },
        });
      }

      // 添加时间范围过滤
      const periodDate = RiskChangeUtils.getTimePeriodDate(periodTime, periodUnit);
      if (periodDate) {
        query.bool.must.push({
          range: {
            CreateDate: {
              time_zone: '+08:00',
              gte: periodDate,
            },
          },
        });
      }

      // 创建模拟响应 - 在实际环境中这里应该调用 ES 查询
      // 由于当前无法直接调用父类的 ES 查询方法，这里返回空结果
      // 在实际集成时，应该通过依赖注入获取 ES 服务并执行查询
      const response = new HitDetailsBaseResponse();
      response.Paging = {
        PageSize: pageSize,
        PageIndex: 1,
        TotalRecords: 0,
      };
      response.Result = [];

      this.logger.debug(`获取通用风险动态查询条件: ${JSON.stringify(query)}`);

      return response;
    } catch (error) {
      this.logError('获取通用风险动态失败', error);
      return new HitDetailsBaseResponse();
    }
  }

  /**
   * 丰富资本变更信息，添加计算的资本变更比例和相关信息
   * @param item 数据项
   */
  private enrichCapitalChangeInfo(item: any): void {
    try {
      const changeExtend = item.ChangeExtend || {};

      // 提取前后资本信息
      item.BeforeCapital = changeExtend.A || '';
      item.AfterCapital = changeExtend.B || '';
      item.CapitalTrend = changeExtend.T === 1 ? '减少' : changeExtend.T === 2 ? '增加' : '变更';

      // 计算变更比例
      const beforeCapital = RiskChangeUtils.parseCapital(changeExtend.A);
      const afterCapital = RiskChangeUtils.parseCapital(changeExtend.B);

      if (beforeCapital > 0 && afterCapital > 0) {
        item.CapitalChangeRatio = RiskChangeUtils.calculateCapitalChangeRatio(beforeCapital, afterCapital, changeExtend.T);
      }
    } catch (error) {
      this.logger.error(`丰富资本变更信息失败: ${error?.message || error}`);
    }
  }
}
