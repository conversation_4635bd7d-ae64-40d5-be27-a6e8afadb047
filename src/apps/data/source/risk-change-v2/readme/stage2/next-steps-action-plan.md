# Risk Change V2 下一步行动计划

## 🎯 立即可执行的优化任务

### 任务 1: 完善依赖注入 (预计 2 小时)

#### 目标
将现有的 Helper 类正确注入到策略类中，使业务逻辑能够正常调用。

#### 具体步骤
1. **修改主服务类构造函数**
   ```typescript
   // src/apps/data/source/risk-change-v2/risk-change-es.source.v2.ts
   constructor(
     readonly configService: ConfigService,
     private readonly moduleRef: ModuleRef,
     private readonly riskChangeHelper: RiskChangeHelper,
     private readonly caseReasonHelper: CaseReasonHelper,
     private readonly dimensionHitDetailProcessor: DimensionHitDetailProcessor,
   ) {
     super(configService);
     this.initializeStrategies();
   }
   ```

2. **更新模块配置**
   ```typescript
   // 在对应的模块文件中添加 providers
   providers: [
     RiskChangeEsSourceV2,
     RiskChangeHelper,
     CaseReasonHelper,
     DimensionHitDetailProcessor,
     // ... 其他策略类
   ]
   ```

3. **验证依赖注入**
   - 运行集成测试确保依赖正确注入
   - 检查策略类能否正常调用 Helper 方法

### 任务 2: 集成 RiskChangeHelper 方法 (预计 4 小时)

#### 目标
在策略类中调用具体的业务逻辑方法，替换当前的占位符代码。

#### 具体步骤
1. **LegalChangeStrategy 集成**
   ```typescript
   // 替换占位符逻辑
   if (item.Category === RiskChangeCategoryEnum.category39) {
     const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
     if (layTypesField && isHit) {
       isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, item);
     }
   }
   ```

2. **CapitalChangeStrategy 集成**
   ```typescript
   // 在 processMainInfoUpdateCapitalChange 方法中
   if (strategyFieldByKey) {
     const hit = this.riskChangeHelper.hitMainInfoUpdateCapitalChange(strategyFieldByKey, processedResults);
     if (hit) {
       hitData.push(...processedResults);
     }
   }
   ```

3. **测试验证**
   - 编写单元测试验证 Helper 方法调用
   - 确保业务逻辑正确执行

### 任务 3: 完善数据格式化逻辑 (预计 3 小时)

#### 目标
添加特定维度的数据格式化处理，确保返回数据结构与原始代码一致。

#### 具体步骤
1. **创建数据格式化工具类**
   ```typescript
   // src/apps/data/source/risk-change-v2/utils/data-formatter.utils.ts
   export class DataFormatterUtils {
     static formatSecurityNotice(item: any): any {
       const changeExt = item.ChangeExtend || {};
       return {
         name: changeExt.A,
         reason: changeExt.H,
         publishUnit: changeExt.D,
         publishDate: changeExt.C,
         isValid: changeExt.IsValid,
         keyNo: item.KeyNo,
         riskId: item.Id,
         id: changeExt.J,
       };
     }
     
     static formatCapitalReduction(item: any): any {
       const changeExt = item.ChangeExtend || {};
       return {
         Id: item.Id,
         DecideDate: changeExt.B,
         Content: changeExt.D,
         NoticeDate: changeExt.A,
         NoticePeriod: changeExt.C,
         NoticeTitle: changeExt.F,
         Name: item.Name,
         KeyNo: item.KeyNo,
       };
     }
   }
   ```

2. **在策略类中应用格式化**
   ```typescript
   // 在 processDimensionDetail 方法中
   if (dimension.key === DimensionTypeEnums.SecurityNotice) {
     response.Result = response.Result.map(item => 
       DataFormatterUtils.formatSecurityNotice(item)
     );
   }
   ```

### 任务 4: 集成案件数据关联 (预计 2 小时)

#### 目标
集成 CaseReasonHelper 的案件数据关联逻辑。

#### 具体步骤
1. **在 handleRiskChangeDimension 方法中集成**
   ```typescript
   if (response.Result.length > 0) {
     const changeExtendMap = RiskChangeUtils.buildChangeExtendMap(response.Result);
     response.Result = response.Result.map((d) => getRiskListDesc(d));
     
     // 集成案件数据关联
     await this.caseReasonHelper.getCaseTitleDescData(
       response.Result, 
       false, 
       changeExtendMap
     );
   }
   ```

2. **添加错误处理**
   ```typescript
   try {
     await this.caseReasonHelper.getCaseTitleDescData(response.Result, false, changeExtendMap);
   } catch (error) {
     this.logError('案件数据关联失败', error);
     // 继续执行，不影响主流程
   }
   ```

## 📋 中期优化任务 (1-2 周内完成)

### 任务 5: ES 查询集成 (预计 6 小时)

#### 目标
将策略类中的模拟查询替换为实际的 ES 查询调用。

#### 具体步骤
1. **创建 ES 查询服务接口**
   ```typescript
   interface EsQueryService {
     search(index: string, query: any): Promise<any>;
   }
   ```

2. **在策略类中注入 ES 服务**
   ```typescript
   constructor(
     private readonly riskChangeHelper: RiskChangeHelper,
     private readonly esQueryService: EsQueryService,
   ) {
     super('CapitalChangeStrategy');
   }
   ```

3. **替换模拟查询**
   ```typescript
   // 在 getCommonCivilRiskChange 方法中
   const esResult = await this.esQueryService.search('risk_change_index', query);
   return this.transformEsResult(esResult);
   ```

### 任务 6: 性能优化 (预计 4 小时)

#### 目标
优化查询性能和内存使用。

#### 具体步骤
1. **添加查询结果缓存**
   ```typescript
   @Cacheable({ ttlSeconds: 300 })
   async getDimensionDetail(dimension, params, analyzeParams) {
     // 查询逻辑
   }
   ```

2. **优化内存分页**
   ```typescript
   // 使用流式处理大数据集
   const processDataStream = (data: any[]) => {
     return data.reduce((acc, item, index) => {
       if (index >= start && index < end) {
         acc.push(this.processItem(item));
       }
       return acc;
     }, []);
   };
   ```

### 任务 7: 扩展测试覆盖 (预计 6 小时)

#### 目标
提高测试覆盖率，确保代码质量。

#### 具体步骤
1. **添加策略类单元测试**
   ```typescript
   describe('LegalChangeStrategy', () => {
     it('应该正确处理法定代表人变更数据', async () => {
       // 测试逻辑
     });
   });
   ```

2. **添加端到端测试**
   ```typescript
   describe('Risk Change E2E Tests', () => {
     it('应该完整处理风险变更流程', async () => {
       // 端到端测试逻辑
     });
   });
   ```

## 🔧 长期优化规划 (1 个月内完成)

### 任务 8: 监控和日志完善

#### 目标
添加完整的监控和日志系统。

#### 具体内容
- 性能指标监控
- 错误率统计
- 查询耗时分析
- 业务指标监控

### 任务 9: 文档和示例完善

#### 目标
提供完整的使用文档和示例。

#### 具体内容
- API 使用文档
- 策略扩展指南
- 最佳实践文档
- 故障排查指南

## ✅ 验收标准

### 功能验收
- [ ] 所有策略类能正确处理对应的维度类型
- [ ] 二次判断逻辑正确执行
- [ ] 数据格式化输出与原始代码一致
- [ ] 关联方维度处理正确
- [ ] 案件数据关联正常工作

### 性能验收
- [ ] 查询响应时间 < 2 秒
- [ ] 内存使用合理，无内存泄漏
- [ ] 并发处理能力满足需求

### 质量验收
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过率 100%
- [ ] 代码质量检查通过
- [ ] 无严重安全漏洞

## 📞 需要协调的事项

1. **依赖服务确认**
   - 确认 RiskChangeHelper 的可用性
   - 确认 CaseReasonHelper 的接口稳定性
   - 确认 ES 服务的访问权限

2. **测试环境准备**
   - 准备测试数据
   - 配置测试环境
   - 确认测试流程

3. **发布计划协调**
   - 确定发布时间窗口
   - 制定回滚计划
   - 准备监控预案

## 🎯 成功指标

- **完成度**: 从当前 80% 提升到 95%
- **代码质量**: 通过所有质量检查
- **性能指标**: 满足性能要求
- **测试覆盖**: 达到 80% 以上覆盖率
- **文档完整**: 提供完整的使用文档
