# Risk Change V2 修复建议

## 🎯 立即需要修复的关键问题

### 1. **集成 DimensionHitDetailProcessor 逻辑到策略类**

#### 问题描述
原始代码中的 `DimensionHitDetailProcessor.fetchHits()` 包含了大量的业务逻辑，这些逻辑在新的策略类中缺失。

#### 修复方案
在每个策略类的 `processDimensionDetail` 方法中集成对应的处理逻辑：

```typescript
// 在 LegalChangeStrategy 中添加
async processDimensionDetail(response, dimension, params, analyzeParams) {
  if (!response?.Result?.length) return response;
  
  const hitData: any[] = [];
  
  for (const itemRaw of response.Result) {
    const item = cloneDeep(itemRaw);
    
    // 解析 JSON 字段
    Object.keys(item).forEach((key) => {
      if (['Extend1', 'ChangeExtend'].includes(key)) {
        item[key] = RiskChangeUtils.parseJsonField(item[key]);
      }
    });
    
    let isHit = true;
    
    // 根据 Category 应用不同的业务规则
    switch (item.Category) {
      case RiskChangeCategoryEnum.category39: {
        // 法定代表人变更逻辑
        const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
        if (layTypesField && isHit) {
          isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, item);
        }
        break;
      }
      // 添加其他 Category 的处理逻辑
    }
    
    if (isHit) {
      hitData.push(item);
    }
  }
  
  // 内存分页和返回
  return this.createPaginatedResponse(hitData, params);
}
```

### 2. **完善 RiskChange 维度的特殊处理**

#### 问题描述
`handleRiskChangeDimension` 方法中缺少了关键的二次筛选逻辑。

#### 修复方案
```typescript
private async handleRiskChangeDimension(dimension, params, analyzeParams) {
  const resp = await super.getDimensionDetail(
    dimension,
    { ...params, pageIndex: 1, pageSize: 1000, field: 'CreateDate', order: 'DESC' },
    analyzeParams,
  );

  // 使用 DimensionHitDetailProcessor 进行二次筛选
  const hitData = await this.dimensionHitDetailProcessor.fetchHits(resp, dimension, params);

  // 内存分页
  const response = new HitDetailsBaseResponse();
  const { result, paging } = RiskChangeUtils.createPaginatedResponse(
    hitData, 
    params.pageIndex, 
    params.pageSize
  );
  
  response.Paging = paging;
  response.Result = result;
  
  if (response.Result.length > 0) {
    const changeExtendMap = RiskChangeUtils.buildChangeExtendMap(response.Result);
    
    // 返回数据结构处理
    response.Result = response.Result.map((d) => getRiskListDesc(d));
    
    // 集成案件数据
    await this.caseReasonHelper.getCaseTitleDescData(response.Result, false, changeExtendMap);
  }
  
  return response;
}
```

### 3. **添加依赖注入**

#### 修复方案
在 `RiskChangeEsSourceV2` 构造函数中添加必要的依赖：

```typescript
constructor(
  readonly configService: ConfigService,
  private readonly moduleRef: ModuleRef,
  private readonly dimensionHitDetailProcessor: DimensionHitDetailProcessor,
  private readonly caseReasonHelper: CaseReasonHelper,
  private readonly riskChangeHelper: RiskChangeHelper,
) {
  // ...
}
```

### 4. **实现关联方维度处理**

#### 创建关联方策略基类
```typescript
// src/apps/data/source/risk-change-v2/strategies/base-related-risk-change.strategy.ts
export abstract class BaseRelatedRiskChangeStrategy extends BaseRiskChangeStrategy {
  constructor(
    strategyName: string,
    protected readonly companySearchService: CompanySearchService,
    protected readonly companyDetailService: CompanyDetailService,
    protected readonly personHelper: PersonHelper,
  ) {
    super(strategyName);
  }

  // 关联方特有的方法
  protected async getRelatedCompanies(companyId: string, dimension: DimensionHitStrategyPO): Promise<string[]> {
    // 实现获取关联方企业的逻辑
  }
}
```

#### 实现具体关联方策略
```typescript
// src/apps/data/source/risk-change-v2/strategies/recent-invest-cancellations.strategy.ts
@Injectable()
export class RecentInvestCancellationsStrategy extends BaseRelatedRiskChangeStrategy {
  getSupportedDimensions(): DimensionTypeEnums[] {
    return [DimensionTypeEnums.RecentInvestCancellationsRiskChange];
  }

  async processDimensionDetail(response, dimension, params, analyzeParams) {
    // 实现近期对外投资企业大量注销或吊销的逻辑
    // 参考原始代码 related-dimension-hit-detail.processor.ts 第 228-285 行
  }
}
```

## 🔧 具体修复步骤

### 步骤 1: 修复依赖注入
1. 在 `RiskChangeEsSourceV2` 中添加必要的依赖注入
2. 在模块配置中注册这些依赖

### 步骤 2: 完善现有策略类
1. 在 `LegalChangeStrategy` 中添加完整的 Category 39 处理逻辑
2. 在 `CapitalChangeStrategy` 中添加 Category 37, 123 处理逻辑
3. 集成 `RiskChangeHelper` 的方法调用

### 步骤 3: 修复特殊维度处理
1. 完善 `handleRiskChangeDimension` 方法
2. 完善 `handleMainInfoUpdateCapitalChange` 方法
3. 添加案件数据关联逻辑

### 步骤 4: 实现关联方维度
1. 创建 `BaseRelatedRiskChangeStrategy` 基类
2. 实现三个关联方策略类
3. 在 `RiskChangeRelatedEsSource` 中集成这些策略

### 步骤 5: 数据格式化完善
1. 在策略类中添加特定维度的数据格式化逻辑
2. 确保返回数据结构与原始代码一致
3. 添加错误处理和日志记录

## 📝 代码示例

### 完整的策略类示例
```typescript
@Injectable()
export class LegalChangeStrategy extends BaseRiskChangeStrategy {
  constructor(
    private readonly riskChangeHelper: RiskChangeHelper,
    private readonly dimensionHitDetailProcessor: DimensionHitDetailProcessor,
  ) {
    super('LegalChangeStrategy');
  }

  async processDimensionDetail(response, dimension, params, analyzeParams) {
    if (!response?.Result?.length) return response;
    
    const hitData: any[] = [];
    
    for (const itemRaw of response.Result) {
      try {
        const item = cloneDeep(itemRaw);
        
        // 预处理 JSON 字段
        Object.keys(item).forEach((key) => {
          if (['Extend1', 'ChangeExtend'].includes(key)) {
            item[key] = RiskChangeUtils.parseJsonField(item[key]);
          }
        });
        
        let isHit = true;
        
        // 应用业务规则
        switch (item.Category) {
          case RiskChangeCategoryEnum.category39: {
            const layTypesField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.layTypes);
            if (layTypesField && isHit) {
              isHit = this.riskChangeHelper.hitLayTypesField(layTypesField, item);
            }
            break;
          }
        }
        
        if (isHit) {
          // 数据增强
          this.enrichLegalPersonChangeInfo(item);
          hitData.push(item);
        }
      } catch (error) {
        this.logError('处理法定代表人变更详情项失败', error);
      }
    }
    
    // 创建分页响应
    const { result, paging } = RiskChangeUtils.createPaginatedResponse(
      hitData,
      params.pageIndex,
      params.pageSize
    );
    
    const response = new HitDetailsBaseResponse();
    response.Paging = paging;
    response.Result = result;
    
    return response;
  }
  
  private enrichLegalPersonChangeInfo(item: any): void {
    // 数据增强逻辑
  }
}
```

## ⚠️ 注意事项

1. **保持业务逻辑一致性**: 确保新代码的业务逻辑与原始代码完全一致
2. **错误处理**: 添加适当的错误处理和日志记录
3. **性能考虑**: 注意内存使用和查询性能
4. **测试验证**: 每个修复都需要充分的测试验证
5. **向后兼容**: 确保修复不会破坏现有功能

## 🧪 测试建议

1. **单元测试**: 为每个策略类编写单元测试
2. **集成测试**: 测试整个流程的端到端功能
3. **对比测试**: 对比新旧代码的输出结果
4. **性能测试**: 确保性能不会下降
5. **边界测试**: 测试各种边界情况和异常情况
