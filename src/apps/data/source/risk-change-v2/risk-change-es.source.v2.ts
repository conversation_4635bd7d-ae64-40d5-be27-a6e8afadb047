import { Injectable } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import { ConfigService } from 'libs/config/config.service';
import { BaseEsAnalyzeService } from '../base-es-analyze.service';
import { RiskChangeStrategy } from './interfaces/risk-change-strategy.interface';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionAnalyzeParamsPO } from 'libs/model/data/source/DimensionAnalyzeParamsPO';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { ModuleRef } from '@nestjs/core';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { orderBy } from 'lodash';
import { getCompareResult } from 'libs/utils/diligence/diligence.utils';
import { getDimensionDescription, processDimHitResPO } from 'libs/utils/diligence/dimension.utils';
import { getRiskListDesc } from '../../risk.copy.from.c/risk';
import { LegalChangeStrategy } from './strategies/legal-change.strategy';
import { CapitalChangeStrategy } from './strategies/capital-change.strategy';
import { NegativeNewsStrategy } from './strategies/negative-news.strategy';
import { BusinessAbnormalStrategy } from './strategies/business-abnormal.strategy';
import { JudicialCaseStrategy } from './strategies/judicial-case.strategy';
import { AdministrativePenaltyStrategy } from './strategies/administrative-penalty.strategy';
import { EquityChangeStrategy } from './strategies/equity-change.strategy';
import { FinancialIndicatorStrategy } from './strategies/financial-indicator.strategy';

/**
 * 风险动态ES数据源服务（处理普通维度）
 * 使用策略模式处理不同维度的风险变更数据
 */
@Injectable()
export class RiskChangeEsSourceV2 extends BaseEsAnalyzeService {
  protected readonly riskChangeLogger: Logger = QccLogger.getLogger(RiskChangeEsSourceV2.name);
  private strategies: RiskChangeStrategy[] = [];

  /**
   * 关联方风险变更维度列表
   */
  private readonly relatedCompanyDimensions = [
    DimensionTypeEnums.RecentInvestCancellationsRiskChange,
    DimensionTypeEnums.ActualControllerRiskChange,
    DimensionTypeEnums.ListedEntityRiskChange,
  ];

  constructor(readonly configService: ConfigService, private readonly moduleRef: ModuleRef) {
    super(
      'RiskChangeEsSourceV2',
      new Client({
        nodes: configService.esConfig.riskChangeList.nodes,
        ssl: { rejectUnauthorized: false },
      }),
      configService.esConfig.riskChangeList.indexName,
    );
    // 初始化策略类列表
    this.initStrategies();
  }

  /**
   * 初始化策略类列表
   */
  private initStrategies(): void {
    try {
      // 获取所有策略类实例
      this.strategies = [
        this.moduleRef.get(LegalChangeStrategy),
        this.moduleRef.get(CapitalChangeStrategy),
        this.moduleRef.get(NegativeNewsStrategy),
        this.moduleRef.get(BusinessAbnormalStrategy),
        this.moduleRef.get(JudicialCaseStrategy),
        this.moduleRef.get(AdministrativePenaltyStrategy),
        this.moduleRef.get(EquityChangeStrategy),
        this.moduleRef.get(FinancialIndicatorStrategy),
        // 后续会添加更多策略类
      ];
    } catch (error) {
      this.riskChangeLogger.error(`初始化策略类列表失败: ${error instanceof Error ? error.message : String(error)}`, error);
    }
  }

  /**
   * 分析当前数据源所有维度命中情况
   * @param companyId 企业ID
   * @param dimensionHitStrategyPOs 维度策略列表
   * @param params 分析参数
   */
  async analyze(companyId: string, dimensionHitStrategyPOs: DimensionHitStrategyPO[], params?: DimensionAnalyzeParamsPO): Promise<DimensionHitResultPO[]> {
    // 过滤出普通维度（非关联方维度）
    const normalDimensions = dimensionHitStrategyPOs.filter((d) => !this.isRelatedDimension(d));

    if (!normalDimensions.length) {
      return [];
    }

    // 调用父类方法进行基础分析
    const dimHitRes = await super.analyze(companyId, normalDimensions, params);
    const analyzeResult: DimensionHitResultPO[] = [];

    if (dimHitRes?.length > 0) {
      // 对每个命中的维度进行二次判断
      await Promise.all(
        dimHitRes.map(async (dimHit: DimensionHitResultPO) => {
          const dimension = normalDimensions.find((po) => po.strategyId === dimHit.strategyId);
          if (!dimension) return;

          const desData = {
            isHidden: '',
            isHiddenY: '',
          };
          let hitCount = 0;

          // 对特定维度进行二次判断
          if (this.needsSecondaryAnalysis(dimension)) {
            const res = await this.getDimensionDetail(dimension, {
              keyNo: companyId,
              pageIndex: 1,
              pageSize: dimHit.totalHits,
            });
            if (res?.Paging.TotalRecords) {
              hitCount = res?.Paging.TotalRecords || 0;
            }

            // 再过滤命中记录条数
            const hitCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
            if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
              hitCount = 0;
            }
          } else {
            hitCount = dimHit.totalHits;
          }

          if (hitCount > 0) {
            dimHit.totalHits = hitCount;
            if (dimension.template && desData) {
              // 返回描述处理
              desData['name'] = dimension.strategyName;
              dimHit.description = getDimensionDescription(dimension.template, Object.assign(desData, { count: hitCount }));
            }
            analyzeResult.push(dimHit);
          }
        }),
      );
    } else {
      // 处理不命中但需要提示的维度
      await Promise.all(
        normalDimensions.map(async (d: DimensionHitStrategyPO) => {
          const isShowTipField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.isShowTip);
          if (isShowTipField && isShowTipField.fieldValue[0] === 1) {
            const desData = {
              isHidden: '',
              isHiddenY: '',
            };
            const dimHitResPO = processDimHitResPO(d, 0, desData);
            analyzeResult.push(dimHitResPO);
          }
        }),
      );
    }

    return analyzeResult;
  }

  /**
   * 获取风险动态维度详情
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 如果是关联方维度，不在此处理
    if (this.isRelatedDimension(dimension)) {
      return new HitDetailsBaseResponse();
    }

    // 特殊处理 RiskChange 维度
    if (dimension.key === DimensionTypeEnums.RiskChange) {
      return this.handleRiskChangeDimension(dimension, params, analyzeParams);
    }

    // 特殊处理 MainInfoUpdateCapitalChange 维度
    if (dimension.key === DimensionTypeEnums.MainInfoUpdateCapitalChange) {
      return this.handleMainInfoUpdateCapitalChange(dimension, params, analyzeParams);
    }

    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      // 获取基础数据，然后交由策略处理
      const response = await super.getDimensionDetail(dimension, params, analyzeParams);
      return strategy.processDimensionDetail(response, dimension, params, analyzeParams);
    }

    // 没有找到对应策略，使用默认处理
    return super.getDimensionDetail(dimension, params, analyzeParams);
  }

  /**
   * 查找维度对应的策略
   * @param dimension 维度策略
   */
  private findStrategyForDimension(dimension: DimensionHitStrategyPO): RiskChangeStrategy | null {
    return this.strategies.find((strategy) => strategy.supportsDimension(dimension)) || null;
  }

  /**
   * 判断是否是关联方维度
   * @param dimension 维度策略
   */
  private isRelatedDimension(dimension: DimensionHitStrategyPO): boolean {
    return this.relatedCompanyDimensions.includes(dimension.key);
  }

  /**
   * 判断维度是否需要二次分析
   * @param dimension 维度策略
   */
  private needsSecondaryAnalysis(dimension: DimensionHitStrategyPO): boolean {
    return dimension.key === DimensionTypeEnums.RiskChange || dimension.key === DimensionTypeEnums.MainInfoUpdateCapitalChange;
  }

  /**
   * 实现抽象方法 - 获取维度查询条件
   * @param companyId 企业ID
   * @param dimension 维度策略
   * @param params 分析参数
   * @param analyzeParams 额外分析参数
   */
  protected async getDimensionQuery(
    companyId: string,
    dimension: DimensionHitStrategyPO,
    params?: DimensionAnalyzeParamsPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<object> {
    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      return strategy.generateDimensionQuery(companyId, dimension, params, analyzeParams);
    }

    // 默认查询
    return {
      bool: {
        must: [
          {
            term: {
              KeyNo: companyId,
            },
          },
        ],
      },
    };
  }

  /**
   * 实现抽象方法 - 处理维度详情数据
   * @param res 响应数据
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  protected async getDimensionDetailItemData(
    res: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 查找对应的策略
    const strategy = this.findStrategyForDimension(dimension);
    if (strategy) {
      return strategy.processDimensionDetail(res, dimension, params, analyzeParams);
    }

    // 默认处理
    return res;
  }

  /**
   * 处理 RiskChange 维度的特殊逻辑
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  private async handleRiskChangeDimension(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // 获取原始数据，按时间倒序排列，获取更多数据用于二次筛选
    const resp = await super.getDimensionDetail(dimension, { ...params, pageIndex: 1, pageSize: 1000, field: 'CreateDate', order: 'DESC' }, analyzeParams);

    // 这里需要使用 DimensionHitDetailProcessor 进行二次筛选
    // 由于我们在策略模式中，暂时返回基础数据
    // 在实际集成时，应该调用对应策略的 processDimensionDetail 方法

    // 内存分页
    const response = new HitDetailsBaseResponse();
    const pageSize = params?.pageSize || 10;
    const pageIndex = params?.pageIndex || 1;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;

    response.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: resp.Result?.length || 0,
    };

    const sortedData = orderBy(resp.Result || [], 'CreateDate', 'desc');
    response.Result = sortedData.slice(start, end);

    if (response.Result.length > 0) {
      const changeExtendMap: Record<string, any[]> = {};
      response.Result.forEach((item) => {
        const { ChangeExtend, Id } = item;
        changeExtendMap[Id] ??= [];
        changeExtendMap[Id].push(ChangeExtend);
      });

      // 返回数据结构处理
      response.Result = response.Result.map((d) => getRiskListDesc(d));
      // 在实际集成时，应该调用 CaseReasonHelper 的逻辑
      // await this.caseReasonHelper.getCaseTitleDescData(response.Result, false, changeExtendMap);
    }

    return response;
  }

  /**
   * 处理 MainInfoUpdateCapitalChange 维度的特殊逻辑
   * @param dimension 维度策略
   * @param params 查询参数
   * @param analyzeParams 分析参数
   */
  private async handleMainInfoUpdateCapitalChange(
    dimension: DimensionHitStrategyPO,
    params: HitDetailsBaseQueryParams,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    const resp = await super.getDimensionDetail(dimension, { ...params, pageIndex: 1, pageSize: 1000, field: 'CreateDate', order: 'DESC' }, analyzeParams);

    const hitData = [];
    if (resp?.Result?.length) {
      const strategyFieldByKey = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
      if (strategyFieldByKey) {
        // 在实际集成时，应该调用 RiskChangeHelper 的逻辑
        // const hit = this.riskChangeHelper.hitMainInfoUpdateCapitalChange(strategyFieldByKey, resp?.Result);
        // if (hit) {
        //   hitData.push(resp?.Result[0]);
        // }

        // 暂时返回第一条数据
        if (resp.Result.length > 0) {
          hitData.push(resp.Result[0]);
        }
      }
    }

    // 内存分页
    const response = new HitDetailsBaseResponse();
    const pageSize = params?.pageSize || 10;
    const pageIndex = params?.pageIndex || 1;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;

    response.Paging = {
      PageSize: pageSize,
      PageIndex: pageIndex,
      TotalRecords: hitData.length,
    };

    const sortedData = orderBy(hitData, 'CreateDate', 'desc');
    response.Result = sortedData.slice(start, end);

    return response;
  }
}
