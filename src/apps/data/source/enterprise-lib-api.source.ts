import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { Cacheable } from '@type-cacheable/core';
import * as Bluebird from 'bluebird';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { ContractBreachDegree } from 'libs/constants/model.constants';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { toRoundFixed } from 'libs/utils/utils';
import * as _ from 'lodash';
import { flatMap, map, pick, sum } from 'lodash';
import { Logger } from 'log4js';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionAnalyzeParamsPO } from '../../../libs/model/data/source/DimensionAnalyzeParamsPO';
import { HitEnterpriseDimensionQueryParam } from '../../../libs/model/diligence/details/request/HitEnterpriseDimensionQueryParam';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { getCompareResult } from '../../../libs/utils/diligence/diligence.utils';
import { processDimHitResPO } from '../../../libs/utils/diligence/dimension.utils';
import { CompanyDetailService } from '../../company/company-detail.service';
import { EnterpriseLibHelper } from '../helper/enterprise.lib.helper';
import { PersonHelper } from '../helper/person.helper';

import { IAnalyzeService } from './analyze.interface';
import { ShareholdRoleConstant } from '../../../libs/constants/company.constants';

/**
 * 企业库数据源接口( 企业库 + 风险动态 )
 */
@Injectable()
export class EnterpriseLibApiSource implements IAnalyzeService<HitEnterpriseDimensionQueryParam, DimensionAnalyzeParamsPO, HitDetailsBaseResponse> {
  private readonly logger: Logger = QccLogger.getLogger(EnterpriseLibApiSource.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpUtils: HttpUtilsService,
    private readonly companyDetailsService: CompanyDetailService,
    private readonly enterpriseLibHelper: EnterpriseLibHelper,
    private readonly personHelper: PersonHelper,
  ) {}

  async analyze(companyId: string, DimensionHitStrategyPOs: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const countInfo = await this.getCountInfo(companyId);
    return Bluebird.map(DimensionHitStrategyPOs, async (d: DimensionHitStrategyPO) => {
      let hitCount = 0;
      /**
       * 处理命中描述信息需要的参数
       */
      const desData = {
        isHidden: '',
        isHiddenY: '',
      };
      switch (d.dimensionDef.key) {
        case DimensionTypeEnums.ActuralControllerInformation: {
          // 实控人信息
          const acturalControllerInformationRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (acturalControllerInformationRes?.Result?.length) {
            hitCount = acturalControllerInformationRes.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.ShareholderInformation: {
          // 股东信息
          const shareholderInformationRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (shareholderInformationRes?.Result?.length) {
            hitCount = shareholderInformationRes.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.ProvincialHonor: {
          const provincialHonorResp = await this.getDimensionDetail(d, { keyNo: companyId });
          hitCount = provincialHonorResp.Paging.TotalRecords || 0;
          break;
        }
        case DimensionTypeEnums.MainMembersChangeFrequency: {
          const mainMembersChangeFrequencyResp = await this.getDimensionDetail(d, { keyNo: companyId });
          if (mainMembersChangeFrequencyResp?.Result?.length) {
            hitCount = mainMembersChangeFrequencyResp.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation: {
          const resp = await this.getDimensionDetail(d, { keyNo: companyId });
          if (resp?.Result?.length) {
            hitCount = resp.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.EmployeeStockPlatform: {
          const hitDetailsBaseResponse = await this.getDimensionDetail(d, { keyNo: companyId });
          if (hitDetailsBaseResponse?.Result?.length) {
            hitCount = hitDetailsBaseResponse.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.EquityStructureAbnormal: {
          const equityStructureAbnormalRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (equityStructureAbnormalRes?.Result?.length) {
            hitCount = equityStructureAbnormalRes.Paging.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.ControllerCompany: {
          // 控制企业
          const sameActualControllerRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (sameActualControllerRes?.Result?.length) {
            hitCount = sameActualControllerRes.Paging.TotalRecords || 0;
          }
          break;
        }
        // 国际专利查询
        case DimensionTypeEnums.InternationPatent: {
          const internationPatentRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (internationPatentRes?.Result?.length) {
            hitCount = internationPatentRes?.Paging?.TotalRecords || 0;
          }
          break;
        }
        // 专利查询
        case DimensionTypeEnums.PatentInfo: {
          const patentRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (patentRes?.Result?.length) {
            hitCount = patentRes?.Paging?.TotalRecords || 0;
          }
          break;
        }
        // 专利分析
        case DimensionTypeEnums.PatentAnalysis: {
          const patentAnalysisRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (patentAnalysisRes?.Result?.length) {
            hitCount = patentAnalysisRes?.Paging?.TotalRecords || 0;
          }
          break;
        }
        // 招聘分析
        case DimensionTypeEnums.RecruitmentAnalysis: {
          const recruitmentAnalysisRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (recruitmentAnalysisRes?.Result?.length) {
            hitCount = recruitmentAnalysisRes?.Paging?.TotalRecords || 0;
          }
          break;
        }
        // 融资信息
        case DimensionTypeEnums.EquityFinancing: {
          const equityFinancingRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (equityFinancingRes?.Result?.length) {
            hitCount = equityFinancingRes?.Paging?.TotalRecords || 0;
          }
          break;
        }
        case DimensionTypeEnums.TaxArrearsNotice: {
          //欠税公告:
          const taxArrearsNoticeRes = await this.getDimensionDetail(d, { keyNo: companyId });
          if (taxArrearsNoticeRes) {
            const bAmount1 = parseFloat(taxArrearsNoticeRes?.Paging?.['TaxBalanceAmount']);
            hitCount = taxArrearsNoticeRes?.Paging?.TotalRecords || 0;
            if (bAmount1) {
              Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
            }
          }
          break;
        }
        case DimensionTypeEnums.TaxCallNoticeV2: {
          //税务催缴
          const taxCallResult = await this.getDimensionDetail(d, { keyNo: companyId });
          if (taxCallResult) {
            const totalAmount = parseFloat(taxCallResult?.Paging?.['TotalAmount']);
            hitCount = taxCallResult?.Paging?.TotalRecords || 0;
            if (totalAmount) {
              Object.assign(desData, { amountW: toRoundFixed(totalAmount / 10000, 2) });
            }
          }
          break;
        }
        case DimensionTypeEnums.GuaranteeRisk: // '担保风险': countInfo.GuarantorRiskCount
          hitCount = countInfo?.['GuarantorRiskCount'] || 0;
          if (hitCount) {
            const guaranteeRes = await this.getDimensionDetail(d, { keyNo: companyId });
            const riskAmount = sum(map(guaranteeRes?.GroupItems.filter((e) => e.key === 'amount')[0]?.items, 'value'));
            hitCount = guaranteeRes?.Paging?.TotalRecords || 0;
            if (riskAmount) {
              Object.assign(desData, { amountW: toRoundFixed(riskAmount / 10000, 2) });
            }
          }
          break;
        case DimensionTypeEnums.GuaranteeInfo: // '担保信息':  countInfo.GuarantorCount
          hitCount = countInfo?.['GuarantorCount'] || 0;
          if (hitCount) {
            const guaranteeRes2 = await this.getDimensionDetail(d, { keyNo: companyId });
            const riskAmount2 = sum(map(guaranteeRes2?.Result, 'GuaranteeMoney').map(Number));
            if (riskAmount2) {
              Object.assign(desData, { amountW: riskAmount2 });
            }
          }
          break;
        case DimensionTypeEnums.PersonCreditHistory: // '历史失信被执行人'
        case DimensionTypeEnums.PersonCreditCurrent: {
          // '被列入失信被执行人（当前有效）'
          const pccRes = await this.getDimensionDetail(d, {
            keyNo: companyId,
          });
          hitCount = pccRes?.Paging?.TotalRecords || 0;
          const executedAmount1 = pccRes?.GroupItems?.find((e) => e.key === 'amountsum')?.items[0]?.['sum'];
          //被执行金额
          if (executedAmount1) {
            Object.assign(desData, { amountW: toRoundFixed(executedAmount1 / 10000, 2) });
          }
          break;
        }
        case DimensionTypeEnums.PersonExecution: {
          // '被执行人'
          hitCount = countInfo?.['ZhiXingCount'] || 0;
          const peRes = await this.getDimensionDetail(d, {
            keyNo: companyId,
          });
          hitCount = peRes?.Paging?.TotalRecords || 0;
          const peAmount = parseInt(peRes?.GroupItems?.find((e) => e.key === 'companynames')?.items[0]?.['sum']);
          if (peAmount) {
            Object.assign(desData, { amountW: toRoundFixed(peAmount / 10000, 2) });
          }
          break;
        }
        case DimensionTypeEnums.ContractBreach: {
          // '合同违约',  countInfo.ContractBreachCount
          if (!countInfo?.['ContractBreachCount']) {
            hitCount = 0;
            break;
          }
          const result3 = await this.getDimensionDetail(d, { keyNo: companyId });
          hitCount = result3?.Paging?.TotalRecords || 0;
          // 涉及金额
          if (result3) {
            const bAmount1 = parseFloat(result3?.Result[0]?.TotalAmt);
            const revel = result3?.Result[0]?.Revel;
            const groupCount = parseFloat(result3?.Result[0]?.TotalNum);
            if (bAmount1) {
              Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
            }
            if (revel) {
              Object.assign(desData, { degree: ContractBreachDegree?.[revel] });
            }
            if (groupCount) {
              hitCount = groupCount;
            }
          }
          break;
        }
        case DimensionTypeEnums.BillDefaults: {
          //票据违约
          hitCount = countInfo?.['BillDefaultCount'] || 0;
          const peRes = await this.getDimensionDetail(d, {
            keyNo: companyId,
          });
          hitCount = peRes?.Paging?.TotalRecords || 0;
          break;
        }
        case DimensionTypeEnums.EndExecutionCase: {
          //终本案件
          const endCaseRes = await this.getDimensionDetail(d, { keyNo: companyId });
          hitCount = endCaseRes?.Paging?.TotalRecords || 0;
          if (endCaseRes) {
            const bAmount1 = parseFloat(endCaseRes?.GroupItems?.find((e) => e.key === 'failureGroup')?.items[0]?.['sum']);
            if (bAmount1) {
              Object.assign(desData, { amountW: toRoundFixed(bAmount1 / 10000, 2) });
            }
          }
          break;
        }
        // case DimensionTypeEnums.TaxUnnormals:
        // case DimensionTypeEnums.ProductQualityProblem9: // 食品安全检查不合格
        // case DimensionTypeEnums.CompanyCredit: // '被列入严重违法失信企业名录':
        // case DimensionTypeEnums.TaxReminder: // 税务催报
        // case DimensionTypeEnums.CompanyCreditHistory:
        // case DimensionTypeEnums.ChattelMortgage: // 动产抵押  MPledgeCount
        // case DimensionTypeEnums.JudicialAuction: //司法拍卖
        // case DimensionTypeEnums.JudicialAuction1: // '司法拍卖(机器设备'):  JudicialSaleCount
        // case DimensionTypeEnums.CompanyOrMainMembersCriminalOffence: // 公司、法定代表人/股东/董监高存在涉贿、不正当竞争等刑事犯罪行为
        // case DimensionTypeEnums.LandMortgage: // '土地抵押': countInfo.LMCount
        // case DimensionTypeEnums.MainInfoUpdateScope: // '经营范围变更',
        // case DimensionTypeEnums.MainInfoUpdateAddress: // '注册地址变更',
        // case DimensionTypeEnums.MainInfoUpdateName: // '企业名称变更',
        // case DimensionTypeEnums.MainInfoUpdateLegalPerson: // '法定代表人变更',
        // case DimensionTypeEnums.MainInfoUpdateHolder: // '大股东变更',
        // case DimensionTypeEnums.MainInfoUpdateBeneficiary: // '近期变更受益所有人',
        // case DimensionTypeEnums.MainInfoUpdateManager: // '董监高变更',
        // case DimensionTypeEnums.MainInfoUpdatePerson: // '实际控制人变更',
        // case DimensionTypeEnums.CancellationOfFiling: // 注销备案
        // case DimensionTypeEnums.NoCertification:
        // case DimensionTypeEnums.Certification:
        // case DimensionTypeEnums.Liquidation:
        // case DimensionTypeEnums.NoQualityCertification:
        // case DimensionTypeEnums.FinancialHealth:
        // case DimensionTypeEnums.ExternalRelatedRisk:
        // case DimensionTypeEnums.BusinessAbnormal2:
        // case DimensionTypeEnums.CourtSessionAnnouncement: {
        //   const result2 = await this.getDimensionDetail(d, { keyNo: companyId });
        //   hitCount = result2?.Paging?.TotalRecords || 0;
        //   break;
        // }
        default: {
          const result2 = await this.getDimensionDetail(d, { keyNo: companyId });
          hitCount = result2?.Paging?.TotalRecords || 0;
          break;
        }
      }

      // 不命中如果配置了提示
      const isShowTipField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.isShowTip);
      if (hitCount === 0 && isShowTipField?.fieldValue[0] === 1) {
        return processDimHitResPO(d, hitCount, desData);
      }

      // 命中记录条数 规则设置
      const hitCountField = d.getStrategyFieldByKey(DimensionFieldKeyEnums.hitCount);
      if (hitCountField && !getCompareResult(hitCount, hitCountField.fieldValue[0], hitCountField.compareType)) {
        // 不满足 命中记录条数规则 标记未命中
        hitCount = 0;
      }
      if (hitCount) {
        return processDimHitResPO(d, hitCount, desData);
      }
      return null;
    }).then((item) => item.filter((t) => t));
  }

  /**
   * 获取指定维度详情列表
   * @param dimension
   * @param data
   * @returns
   */
  async getDimensionDetail(
    dimension: DimensionHitStrategyPO,
    data: HitEnterpriseDimensionQueryParam,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    // return this.enterpriseLibHelper.getDimensionDetail(dimension, data);

    // this.logger.info(`get hit details for dimension: ${dimension.key}`);
    const sourcePath = dimension?.dimensionDef?.detailSourcePath ? dimension.dimensionDef?.detailSourcePath : dimension.dimensionDef?.sourcePath;

    let dimensionDetails = HitDetailsBaseResponse.ok();
    try {
      const validField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isValid);
      switch (dimension?.dimensionDef?.key) {
        case DimensionTypeEnums.ActuralControllerInformation: {
          // 实际控制人信息
          let isHit = true;
          dimensionDetails = await this.enterpriseLibHelper.getActuralControllerInformation(data, dimension, sourcePath);
          if (dimensionDetails?.Result?.length) {
            // 判断实际控制人控制年限
            const controllerTimeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.controllerTime);
            if (controllerTimeField && isHit) {
              isHit = false;
              // 获取当前企业的实际控制人变更的动态
              // 获取企业的成立日期
            }

            // 判断实际控制人的企业性质
            const isStateOwnedField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isStateOwned);
            if (isStateOwnedField && isHit) {
              isHit = false;
              const isStateOwnedFieldValue = isStateOwnedField.fieldValue[0];
              const Result: any[] = [];
              const filterResult = dimensionDetails.Result.filter((t) => t.Name.includes('国有资产监督管理委员会') || t.Name.includes('国有资产监督管理局'));
              if (isStateOwnedFieldValue === 1 && filterResult.length) {
                isHit = true;
                Result.push(...filterResult);
              } else if (isStateOwnedFieldValue === 2 && !filterResult?.length) {
                isHit = true;
                Result.push(...dimensionDetails.Result);
              }
              if (isHit) {
                dimensionDetails.Result = Result;
              } else {
                dimensionDetails.Result = [];
              }
            }
          }
          break;
        }
        case DimensionTypeEnums.ShareholderInformation: {
          // 股东信息
          let isHit = true;
          dimensionDetails = await this.enterpriseLibHelper.getShareholderInformation(data, dimension, sourcePath);
          if (dimensionDetails?.Result?.length) {
            // 过滤股东角色
            const shareholdRoleField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.shareholdRole);
            if (shareholdRoleField && isHit) {
              isHit = false;
              const shareHoldRole = shareholdRoleField.fieldValue[0];
              // ShareholdRoleConstant 中的 shareHoldRole 等于 majorShareholder的label
              const shareHoldRoleLabel = ShareholdRoleConstant.find((item) => item.value === shareHoldRole)?.label;
              const filterResult = dimensionDetails?.Result.filter((item) => (item?.Tags || []).includes(shareHoldRoleLabel));
              if (filterResult?.length) {
                isHit = true;
              }
              // 判断返回值
              if (isHit) {
                dimensionDetails.Result = filterResult;
              } else {
                dimensionDetails.Result = [];
              }
            }
          }
          break;
        }
        case DimensionTypeEnums.ControllerCompany: {
          // 控制企业
          dimensionDetails = await this.enterpriseLibHelper.getBatchControllerCompany(data, dimension, sourcePath);
          break;
        }
        case DimensionTypeEnums.InternationPatent: {
          // 国际专利
          dimensionDetails = await this.enterpriseLibHelper.getInternationPatent(data, dimension, sourcePath);
          break;
        }
        case DimensionTypeEnums.PatentInfo: {
          dimensionDetails = await this.enterpriseLibHelper.getPatentInfo(data, dimension, sourcePath);
          break;
        }
        case DimensionTypeEnums.PatentAnalysis: {
          // 专利分析
          let isHit = true;
          const patentAnalysisRes = await this.enterpriseLibHelper.getPatentAnalysis(data, dimension, sourcePath);
          if (patentAnalysisRes?.Result?.length) {
            // 区间左值
            const leftRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.leftRatio);
            if (leftRatioDimensionField && isHit) {
              const patentRatio = patentAnalysisRes?.Result[0]?.pantentStatistics[0]?.ratio || 0;
              isHit = false;
              if (getCompareResult(patentRatio, leftRatioDimensionField.fieldValue[0], leftRatioDimensionField.compareType)) {
                isHit = true;
              }
            }
            // 区间右值
            const rightRatioDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.rightRatio);
            if (rightRatioDimensionField && isHit) {
              const patentRatio = patentAnalysisRes?.Result[0]?.pantentStatistics[0]?.ratio || 0;
              isHit = false;
              if (getCompareResult(patentRatio, rightRatioDimensionField.fieldValue[0], rightRatioDimensionField.compareType)) {
                isHit = true;
              }
            }
            // 发明专利稳定性
            const patentStableDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.patentStable);
            if (patentStableDimensionField && isHit) {
              isHit = false;
              let anyYear = null;
              if (patentAnalysisRes.Result[0].pantentStatistics.filter((p) => p.N > 0).length >= 3) {
                anyYear = 3;
              } else if (patentAnalysisRes.Result[0].pantentStatistics.filter((p) => p.N > 0).length >= 2) {
                anyYear = 2;
              } else if (patentAnalysisRes.Result[0].pantentStatistics.some((p) => p.N > 0)) {
                anyYear = 1;
              }
              if (anyYear) {
                if (getCompareResult(anyYear, patentStableDimensionField.fieldValue[0], patentStableDimensionField.compareType)) {
                  isHit = true;
                }
              }
            }
            // n期平均值X
            const avgXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.avgXn);
            if (avgXnDimensionField && isHit) {
              isHit = false;
              const targetAvgXn = avgXnDimensionField.fieldValue[0];
              const sourceAvgXn = patentAnalysisRes?.Result[0]?.mean;
              if (getCompareResult(sourceAvgXn, targetAvgXn, avgXnDimensionField.compareType)) {
                isHit = true;
              }
            }
            // n期变异系数
            const cvXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cvXn);
            if (cvXnDimensionField && isHit) {
              isHit = false;
              const sourceCvXn = patentAnalysisRes?.Result[0]?.cvXn;
              const targetCvXn = cvXnDimensionField.fieldValue[0];
              if (sourceCvXn && targetCvXn && getCompareResult(sourceCvXn, targetCvXn, cvXnDimensionField.compareType)) {
                isHit = true;
              }
            }
          }
          if (isHit) {
            dimensionDetails = patentAnalysisRes;
          }
          break;
        }
        case DimensionTypeEnums.EquityFinancing: {
          // 股权融资
          let isHit = true;
          const equityFinancingRes = await this.enterpriseLibHelper.getEquityFinancing(data, dimension, sourcePath);
          if (equityFinancingRes?.Result?.length) {
            // 是否是投资机构
            const isLimitedPartnershipDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isInstitutionalInvestor);
            const limitedPartnershipList = [];
            if (isLimitedPartnershipDimensionField && isHit) {
              isHit = false;
              const participantDetails = equityFinancingRes?.Result?.map((t) => t.ParticipantDetails);
              // ParticipantType === 1 为投资方
              const participantDetailsList = flatMap(participantDetails)?.filter((t) => t.ParticipantType === 1) || [];
              // category = 2 的时候需要过滤有限合伙
              const category2 = participantDetailsList?.filter((t) => t.Category === 2 && t?.Name?.includes('（有限合伙）'));
              if (category2?.length) {
                limitedPartnershipList.push(...category2);
              }
              // category = 1 的时候不需要过滤有限合伙
              const category1 = participantDetailsList.filter((t) => t.Category === 1);
              if (category1?.length) {
                category1.forEach((t) => {
                  const RelationInfo = [{ Name: t.Name, KeyNo: t.KeyNo }];
                  t.RelationInfo = RelationInfo;
                });
                limitedPartnershipList.push(...category1);
              }
              if (isLimitedPartnershipDimensionField.fieldValue[0] === 1) {
                // 有投资机构
                const hasInvestorList = limitedPartnershipList?.filter((t) => t?.RelationInfo?.length);
                if (hasInvestorList?.length) {
                  isHit = true;
                }
              } else if (isLimitedPartnershipDimensionField.fieldValue[0] === 2) {
                // 无投资机构
                const hasInvestorList = limitedPartnershipList?.filter((t) => t?.RelationInfo?.length);
                if (!hasInvestorList?.length) {
                  isHit = true;
                }
              }
            }
            // 投资机构的上榜榜单来源
            const sourcesInvestInstiteRankField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.sourcesInvestInstiteRank);
            if (sourcesInvestInstiteRankField && isHit) {
              isHit = false;
              if (limitedPartnershipList?.length) {
                const relationInfos = limitedPartnershipList?.map((t) => t?.RelationInfo);
                const relationKeyNos = _.uniq(flatMap(relationInfos)?.map((t) => t.KeyNo) || []);
                if (relationKeyNos.length) {
                  const investList = await this.enterpriseLibHelper.getInvestorSingleAppBdV3(relationKeyNos, sourcesInvestInstiteRankField.fieldValue);
                  if (investList?.length) {
                    isHit = true;
                  }
                }
              }
            }
            // 产业链
            const industrialChainCoreCompanyContantField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isIndustrialChainCoreCompany);
            if (industrialChainCoreCompanyContantField && isHit) {
              isHit = false;
              // 获取主体企业的产业链code
              const companyGraphResult = await this.enterpriseLibHelper.getCompanyGraph(data.keyNo);
              if (companyGraphResult?.Result?.IndustryChains?.length) {
                // 获取产业链上的核心企业
                const investorKeyNos = limitedPartnershipList?.map((t) => t.KeyNo);
                const hitKeyNos = await this.enterpriseLibHelper.getIndustrialChainCoreCompany(companyGraphResult?.Result?.IndustryChains, '（有限合伙）');
                if (investorKeyNos?.length && hitKeyNos?.length && _.intersection(investorKeyNos, hitKeyNos).length) {
                  isHit = true;
                }
              }
            }
          }
          if (isHit) {
            dimensionDetails = equityFinancingRes;
          }
          break;
        }
        case DimensionTypeEnums.RecruitmentAnalysis: {
          // 招聘分析
          let isHit = true;
          const recruitmentAnalysisRes = await this.enterpriseLibHelper.getRecruitmentAnalysis(data, dimension, sourcePath);
          // n期平均值X
          if (recruitmentAnalysisRes?.Result?.length) {
            const avgXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.avgXn);
            if (avgXnDimensionField && isHit) {
              isHit = false;
              const targetAvgXn = avgXnDimensionField.fieldValue[0];
              const sourceAvgXn = recruitmentAnalysisRes?.Result[0]?.mean;
              if (getCompareResult(sourceAvgXn, targetAvgXn, avgXnDimensionField.compareType)) {
                isHit = true;
              }
            }
            // n期变异系数
            const cvXnDimensionField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.cvXn);
            if (cvXnDimensionField && isHit) {
              isHit = false;
              const sourceCvXn = recruitmentAnalysisRes?.Result[0]?.cvXn;
              const targetCvXn = cvXnDimensionField.fieldValue[0];
              if (sourceCvXn && targetCvXn && getCompareResult(sourceCvXn, targetCvXn, cvXnDimensionField.compareType)) {
                isHit = true;
              }
            }
          }
          if (isHit) {
            dimensionDetails = recruitmentAnalysisRes;
          }
          break;
        }
        case DimensionTypeEnums.FinancialInstitution: {
          dimensionDetails = await this.enterpriseLibHelper.getFinancialInstitution(data, dimension);
          break;
        }
        case DimensionTypeEnums.TaxUnnormals: {
          dimensionDetails = await this.companyDetailsService.getTaxUnnormals(data);
          break;
        }
        case DimensionTypeEnums.CourtSessionAnnouncement: {
          dimensionDetails = await this.enterpriseLibHelper.getCourtSessionAnnouncement(data, dimension, validField, sourcePath);
          break;
        }
        case DimensionTypeEnums.ProductQualityProblem9:
          // 食品安全检查不合格
          dimensionDetails = await this.enterpriseLibHelper.getFoodQualityProblem(data, dimension, sourcePath);
          break;
        case DimensionTypeEnums.ContractBreach:
          //合同违约
          dimensionDetails = await this.enterpriseLibHelper.getContractBreach(data, sourcePath);
          break;
        case DimensionTypeEnums.ChattelMortgage:
          //动产抵押
          dimensionDetails = await this.enterpriseLibHelper.getChattelMortgage(data, validField, sourcePath);
          break;
        case DimensionTypeEnums.LandMortgage:
          //土地抵押
          dimensionDetails = await this.enterpriseLibHelper.getLandMortgage(data, dimension, validField, sourcePath);
          break;
        case DimensionTypeEnums.CancellationOfFiling:
          //注销备案
          dimensionDetails = await this.enterpriseLibHelper.getCancellationOfFiling(data, validField, sourcePath);
          break;
        case DimensionTypeEnums.TaxArrearsNotice:
          //欠税公告
          dimensionDetails = await this.enterpriseLibHelper.getTaxArrearsNotice(data, dimension, validField, sourcePath);
          break;
        case DimensionTypeEnums.TaxReminder:
          //税务催报
          dimensionDetails = await this.enterpriseLibHelper.getTaxReminder(data, validField, sourcePath, dimension);
          break;
        case DimensionTypeEnums.TaxCallNoticeV2:
          //税务催缴
          dimensionDetails = await this.enterpriseLibHelper.getTaxCallNotice(data, validField, sourcePath, dimension);
          break;
        case DimensionTypeEnums.PersonCreditCurrent:
        case DimensionTypeEnums.PersonCreditHistory:
          //被列入失信被执行人 //历史失信被执行人
          dimensionDetails = await this.enterpriseLibHelper.getPersonCredit(data, dimension, sourcePath);
          break;
        case DimensionTypeEnums.PersonExecution:
          //被执行人
          dimensionDetails = await this.enterpriseLibHelper.getPersonExecution(data, dimension, validField, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.EndExecutionCase:
          //终本案件
          dimensionDetails = await this.enterpriseLibHelper.getEndExecutionCase(data, dimension, validField, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.MainInfoUpdateHolder: {
          // 大股东变更
          const holderRes = await this.enterpriseLibHelper.getMainInfoUpdateHolder(dimension, sourcePath, data, '24');
          dimensionDetails = Object.assign(dimensionDetails, pick(holderRes?.data, ['Result', 'Paging', 'GroupItems']));
          break;
        }
        case DimensionTypeEnums.MainInfoUpdateBeneficiary: {
          //近期变更受益所有人
          const holderRes = await this.enterpriseLibHelper.getMainInfoUpdateHolder(dimension, sourcePath, data, '114');
          dimensionDetails = Object.assign(dimensionDetails, pick(holderRes?.data, ['Result', 'Paging', 'GroupItems']));
          break;
        }
        case DimensionTypeEnums.MainInfoUpdatePerson: {
          // 实际控制人变更
          const personRes = await this.enterpriseLibHelper.getMainInfoUpdateHolder(dimension, sourcePath, data, '25');
          dimensionDetails = Object.assign(dimensionDetails, pick(personRes?.data, ['Result', 'Paging', 'GroupItems']));
          break;
        }
        case DimensionTypeEnums.MainInfoUpdateScope: // 经营范围变更,
        case DimensionTypeEnums.MainInfoUpdateAddress: // 注册地址变更
        case DimensionTypeEnums.MainInfoUpdateName: // 企业名称变更
        case DimensionTypeEnums.MainInfoUpdateLegalPerson: // 法定代表人变更
          dimensionDetails = await this.enterpriseLibHelper.getCoyHisInfo(dimension, sourcePath, data);
          break;
        case DimensionTypeEnums.MainInfoUpdateManager: // 董监高变更
          dimensionDetails = await this.enterpriseLibHelper.getMainInfoUpdate(dimension, sourcePath, data);
          break;
        case DimensionTypeEnums.JudicialAuction:
        case DimensionTypeEnums.CompanyCredit:
        case DimensionTypeEnums.GuaranteeRisk:
        case DimensionTypeEnums.CompanyCreditHistory:
          dimensionDetails = await this.enterpriseLibHelper.getQccDimensionDetail(dimension, sourcePath, data);
          break;
        case DimensionTypeEnums.NoQualityCertification:
          dimensionDetails = await this.enterpriseLibHelper.getNoQualityCertification(data, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.NoCertification:
          dimensionDetails = await this.enterpriseLibHelper.getNoCertification(data, sourcePath, dimension, dimensionDetails);
          break;
        case DimensionTypeEnums.Certification:
          // 资质筛查
          dimensionDetails = await this.enterpriseLibHelper.getCertification(data, sourcePath, dimension, dimensionDetails);
          break;
        case DimensionTypeEnums.BillDefaults:
          //票据违约
          dimensionDetails = await this.enterpriseLibHelper.getBillDefaults(data, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.BondDefaults:
          //债券违约
          dimensionDetails = await this.enterpriseLibHelper.getBondDefaults(data, validField, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.Liquidation: //清算信息
          dimensionDetails = await this.enterpriseLibHelper.getLiquidation(data, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.FinancialHealth:
          // 财务健康度
          dimensionDetails = await this.enterpriseLibHelper.getFinancialHealth(data, sourcePath, dimension, dimensionDetails);
          break;
        case DimensionTypeEnums.BusinessAbnormal2:
          // 简易注销
          dimensionDetails = await this.enterpriseLibHelper.getBusinessAbnormal2(data, sourcePath, dimensionDetails);
          break;
        case DimensionTypeEnums.ExternalRelatedRisk: {
          dimensionDetails = await this.enterpriseLibHelper.getExternalRelatedRisk(dimension, data, dimensionDetails);
          break;
        }
        case DimensionTypeEnums.Bankruptcy: {
          dimensionDetails = await this.enterpriseLibHelper.getBankruptcy(data, dimension, validField, sourcePath);
          break;
        }
        case DimensionTypeEnums.QCCCreditRate: {
          dimensionDetails = await this.enterpriseLibHelper.getQCCCreditRate(data, dimension, dimensionDetails);
          break;
        }
        case DimensionTypeEnums.EquityStructureAbnormal: {
          const circularField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.CircularShareholdingBetweenRelatedParties);
          if (circularField) {
            const hit = await this.enterpriseLibHelper.getEquityStructureAbnormal(data, dimension, sourcePath);
            if (hit) {
              Object.assign(dimensionDetails, {
                Result: [data.keyNo],
                Paging: { TotalRecords: 1 },
              });
              break;
            }
          }
          const primaryFiled = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.primaryShareholderAbnormality);
          if (primaryFiled) {
            const hit = await this.enterpriseLibHelper.getPrimaryShareholderAbnormality(data);
            if (hit) {
              Object.assign(dimensionDetails, {
                Result: [data.keyNo],
                Paging: { TotalRecords: 1 },
              });
              break;
            }
          }
          const secondaryField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.secondaryShareholderAbnormality);
          if (secondaryField) {
            const hit = await this.enterpriseLibHelper.getSecondaryShareholderAbnormality(data);
            if (hit) {
              Object.assign(dimensionDetails, {
                Result: [data.keyNo],
                Paging: { TotalRecords: 1 },
              });
              break;
            }
          }
          break;
        }
        case DimensionTypeEnums.EmployeeStockPlatform: {
          dimensionDetails = await this.enterpriseLibHelper.getEmployeeStockPlatform(data, dimension, dimensionDetails);
          break;
        }
        case DimensionTypeEnums.RelatedCompanyMassRegistrationCancellation: {
          dimensionDetails = await this.enterpriseLibHelper.getRelatedCompanyMassRegistrationCancellation(data, dimension, dimensionDetails, sourcePath);
          break;
        }
        case DimensionTypeEnums.MainMembersChangeFrequency: {
          if (dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.equityChangeFrequency)) {
            dimensionDetails = await this.enterpriseLibHelper.getShareholdingChangeFrequency(data, dimension, dimensionDetails, sourcePath);
            break;
          }
          dimensionDetails = await this.enterpriseLibHelper.getMainMembersChangeFrequency(data, dimension, dimensionDetails, sourcePath);
          break;
        }
        case DimensionTypeEnums.ProvincialHonor: {
          if (dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.HasCertificationRevoked)) {
            dimensionDetails = await this.enterpriseLibHelper.getCertificationRevoked(data, dimension, dimensionDetails, sourcePath);
            break;
          }
          dimensionDetails = await this.enterpriseLibHelper.getProvincialHonor(data, dimension, dimensionDetails, sourcePath);
          break;
        }
        default:
          // 其他默认
          break;
      }
      const resResult = await this.getDimensionDetailItemData(dimensionDetails, dimension, analyzeParams);
      return resResult;
    } catch (error) {
      this.logger.error(`EnterpriseLibService getDimensionDetail err: ${error},dimension: ${dimension?.dimensionDef.key}`);
      dimensionDetails = HitDetailsBaseResponse.failed(error.response?.error || error.message, DimensionSourceEnums.EnterpriseLib, error.response?.code);
    }
    return dimensionDetails;
  }

  private async getDimensionDetailItemData(
    resp: HitDetailsBaseResponse,
    dimension: DimensionHitStrategyPO,
    analyzeParams?: DimensionAnalyzeParamsPO,
  ): Promise<HitDetailsBaseResponse> {
    if (analyzeParams?.isScanRisk) {
      return resp;
    }
    switch (dimension.dimensionDef.key) {
      case DimensionTypeEnums.PatentInfo: {
        if (resp?.Result?.length) {
          resp.Result.forEach((source) => {
            Object.assign(source, {
              Publishdate: source?.ApplicationDate,
            });
          });
        }
        break;
      }
      case DimensionTypeEnums.ControllerCompany: {
        if (resp?.Result?.length) {
          resp.Result.forEach((source) => {
            Object.assign(source, {
              Publishdate: source?.StartDate,
            });
          });
        }
        break;
      }
    }
    return resp;
  }

  /**
   * 获取公司各维度的CountInfo
   * @param keyNo
   * @returns
   */
  @Cacheable({ ttlSeconds: 500 })
  private async getCountInfo(keyNo: string) {
    return this.httpUtils.postRequest(this.configService.dataServer.getCountInfo, { keyNo });
  }

  // /**
  //  * 获取子公司列表keyNo list
  //  * @param keyNo
  //  */
  // async getBranchKeyNos(keyNo: string): Promise<string[]> {
  //   return await this.companyDetailsService.getBranchKeyNos(keyNo);
  // }

  // /**
  //  * 分支机构
  //  * @param keyNo
  //  * @returns
  //  */
  // public async getBranch(keyNo: string): Promise<string[]> {
  //   return this.companyDetailsService.getBranchKeyNos(keyNo);
  // }

  // /**
  //  * 控制企业
  //  * @param keyNo
  //  * @returns
  //  */
  // public async getHoldingCompany(keyNo: string) {
  //   return this.companyDetailsService.getHoldingCompany({ keyNo });
  // }

  // public async getChangeInfo(keyNo: string, pageSize = 10, pageIndex = 1) {
  //   const params = {
  //     keyNo,
  //     pageSize,
  //     pageIndex,
  //   };
  //   return this.companyDetailsService.getChangeInfoList(params);
  // }
}
