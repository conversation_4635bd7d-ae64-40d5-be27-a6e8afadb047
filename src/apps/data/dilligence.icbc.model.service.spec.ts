import { EnterpriseLibApiSource } from './source/enterprise-lib-api.source';
import { Test, TestingModule } from '@nestjs/testing';
import { AppTestModule } from '../app/app.test.module';
import { DataModule } from './data.module';
import { getDimensionHitStrategyPO } from '../test_utils_module/dimension.test.utils';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../libs/enums/dimension/dimension.filter.params';
import { IsStateOwnedConstant, ShareholdRoleConstant } from '../../libs/constants/company.constants';
import { DimensionFieldCompareTypeEnums } from '../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { HitDetailsBaseQueryParams } from '../../libs/model/diligence/details/request';
import { RelatedTypeEnums } from '../../libs/enums/dimension/RelatedTypeEnums';
import { CompanyApiSource } from './source/company-api.source';
import { LayTypeMap } from '../../libs/constants/risk.change.constants';
import { RiskChangeEsSource } from './source/risk-change/risk-change-es.source';

jest.setTimeout(60 * 10000);
describe('icbc贷前尽调指标集成测试', () => {
  let enterpriseLibService: EnterpriseLibApiSource;
  let companyApiSource: CompanyApiSource;
  let riskChangeEsSource: RiskChangeEsSource;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, DataModule],
    }).compile();
    enterpriseLibService = module.get<EnterpriseLibApiSource>(EnterpriseLibApiSource);
    companyApiSource = module.get<CompanyApiSource>(CompanyApiSource);
    riskChangeEsSource = module.get<RiskChangeEsSource>(RiskChangeEsSource);
  });

  it('【第一大股东持股比例大于20%】-第一大股东持股比例大于20%', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ShareholderInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.shareholdRole,
        fieldValue: ['majorShareholder'],
        options: ShareholdRoleConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
      {
        fieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [{ field: 'stockpercent', order: 'DESC', fieldSnapshot: 'StockPercent' }],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.Equal,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人控股比例比例大于20%】-实控人控股比例比例大于20%', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActuralControllerInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [20],
        options: [{ unit: '%', min: 0, max: 100 }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人名称、是否国资委】-实控人名称、是否国资委', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ActuralControllerInformation, [
      {
        fieldKey: DimensionFieldKeyEnums.isStateOwned,
        fieldValue: [2],
        options: IsStateOwnedConstant,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toEqual(1);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toEqual(1);
  });

  it('【实控人控股公司总数量】-实控人控股公司总数量', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.ControllerCompany, [
      {
        fieldKey: DimensionFieldKeyEnums.relatedRoleType,
        fieldValue: [RelatedTypeEnums.ActualController],
        accessScope: 2,
        options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.percentTotal,
        fieldValue: [50],
        accessScope: 2,
        options: [{ unit: '%', min: 0, max: 100 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [3],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await enterpriseLibService.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await enterpriseLibService.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业经营状态】-在业/存续/迁入/迁出/注销/吊销/撤销/清算/歇业/除名/责令关闭', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyStatus,
        fieldValue: ['10', '20', '40', '90', '85', '70', '99', '80', '75', '50', '60'],
        accessScope: 2,
        options: [
          { label: '在业', value: '10' },
          { label: '存续', value: '20' },
          { label: '清算', value: '40' },
          { label: '吊销', value: '90' },
          { label: '责令关闭', value: '85' },
          { label: '停业', value: '70' },
          { label: '注销', value: '99' },
          { label: '撤销', value: '80' },
          { label: '歇业', value: '75' },
          { label: '迁入', value: '50' },
          { label: '迁出', value: '60' },
        ],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业成立天数】-企业成立天数大于30月', async () => {
    const companyId = 'f625a5b661058ba5082ca508f99ffe1b';
    const companyName = '企查查科技股份有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.duration,
        fieldValue: [30],
        options: [{ unit: '月', min: 0, max: 9999 }],
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【法人代表变更】-近5年法代变更次数大于2次', async () => {
    const companyId = '3f9aafffc19676e1e4676ee87c8d85ee';
    const companyName = '邵阳市东石科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);
    const detail = await riskChangeEsSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await riskChangeEsSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【近5年其他高管变更次数】-近5年其他高管变更次数大于2次', async () => {
    const companyId = '3f9aafffc19676e1e4676ee87c8d85ee';
    const companyName = '邵阳市东石科技有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.RiskChange, [
      {
        fieldKey: DimensionFieldKeyEnums.isValid,
        fieldValue: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.cycle,
        fieldValue: [5],
        options: [1],
        accessScope: 1,
        compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
      {
        fieldKey: DimensionFieldKeyEnums.riskCategories,
        fieldValue: [72],
        options: [{ value: 72, label: '成员变更' }],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.layTypes,
        fieldValue: [1],
        options: LayTypeMap,
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.ExceptAny,
      },
      {
        fieldKey: DimensionFieldKeyEnums.hitCount,
        fieldValue: [2],
        accessScope: 2,
        compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
      },
    ]);
    const detail = await riskChangeEsSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await riskChangeEsSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业所属行业-国标行业】- 房地产开发经营', async () => {
    const companyId = '08d510c2ca7cdeee72ec147dd8fec1ec';
    const companyName = '河北中天房地产开发集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.companyIndustry,
        fieldValue: ['K-70-701'],
        accessScope: 2,
        options: [{ value: 'K-70-701', label: '房地产开发经营' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });

  it('【企业所属行业-企查查行业】- 房地产开发经营', async () => {
    const companyId = '08d510c2ca7cdeee72ec147dd8fec1ec';
    const companyName = '河北中天房地产开发集团有限公司';
    const dimension = getDimensionHitStrategyPO(DimensionTypeEnums.CompanyDetail, [
      {
        fieldKey: DimensionFieldKeyEnums.qccIndustry,
        fieldValue: ['28-2801'],
        accessScope: 2,
        options: [{ value: '28-2801', label: '房地产开发' }],
        compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      },
    ]);
    const detail = await companyApiSource.analyze(companyId, [dimension]);
    expect(detail[0]?.totalHits).toBeGreaterThan(0);
    const result = await companyApiSource.getDimensionDetail(
      dimension,
      Object.assign(
        new HitDetailsBaseQueryParams(),
        {
          keyNo: companyId,
          pageIndex: 1,
          pageSize: 10,
        },
        { keyNo: companyId, companyName },
      ),
    );
    expect(result).not.toBeNull();
    expect(result.Paging.TotalRecords).toBeGreaterThan(0);
  });
});
