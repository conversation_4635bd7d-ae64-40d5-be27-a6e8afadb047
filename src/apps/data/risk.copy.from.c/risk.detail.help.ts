export class ChangeInfoModel {
  A: any;
  A1;
  B: any;
  C: any;
  C1: any;
  D: any;
  E: any;
  F: any;
  G: any;
  H: any;
  I: any;
  J: any;
  K: any;
  T: any;
  M: any;
  O;
  L;
  RC: any;
  T2: any;
  B1;
  P;
  Q;
  PledgedAmount;
  Percent;
  PledgorInfo;
  CompanyId;
  CompanyName: any;
  PledgeeInfo: any;
  category: any;
  RegDate;
  Status;
  Org;
  Name;
  KeyNo;
  Pledgor;
  Pledgee;
  IsBP;
  Increase;
  length;
  ExecutionNoticeNum;
  EquityAmount;
  UnFreezeDate;
  CompanyOrg;
  FreezeStartDate;
  FreezeEndDate;
  Type;
  Secured;
  HolderArray;
  Holder;
  Company;
  JgArray;
  JgK;
  JgO;
  Title;
  Publishtime;
  TS;
  url;
  links;
  eventArray;
  compName;
  overdueBalance;
  endDate;
  map;
  PercentTotal;
  sname;
  newStatus;
  overdueCapital;
  overdueInterest;
  maturityDate;
  dtChange;
  slice;
  AccountingArray;
  Stage;
  Code;
  DispatchedOffice;
  SponsorArray;
  LawArray;
  RecordDate;
  ListSection;
  ListExchange;
  ApprovalDate;
  DerivedSymbol;
  IssueMktvalue;
  ListDate;
  Sname;
}

export class ContentInfoModel {
  tMsg: any[];
  highlight: any;
  bigEventDesc?: any;
  relateChange?;
  contentArray?;
}

export class DescModel {
  Title;
  Subtitle;
  ChangeExtend;
  Content;
  Highlight;
  OtherHighlight;
  RelateChange;
  IsDecrypted?;
  CompanyType?;
  IsHotNews?;
  HotDynamicDesc?;
  ContentArray?;
  BigEventDesc?;
  SubtitleHighlight?;
  TrademarkLogo?;
  Category;
  ObjectId: string;
  Id: string;
  Name: string;
  ChangeDate: number;
  CreateDate: number;
  UpdateDate: number;
  PublishTime: number;
  URL?;
  ChangeInfo?;
}
