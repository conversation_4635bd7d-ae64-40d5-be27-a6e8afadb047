/* eslint-disable no-shadow */
// import _ from 'lodash';
import * as _ from 'lodash';
import { chain, endsWith, find, forEach, head, includes, last, reduce, some, split, startsWith, uniq } from 'lodash';

import * as moment from 'moment';
// import callContext from '@qcc/call-context';
import riskHelper from './riskHelper';
import CommonHelper from './commonHelper';
import shared from './shared';
import { dynamicMap } from './dynamicAll';
import { ChangeInfoModel, ContentInfoModel, DescModel } from './risk.detail.help';
import dynamicConstant from './dynamic-constant';
import { md5 } from '@kezhaozhao/qcc-utils/dist/qichacha/qichacha.util';

const titleMap = chain(dynamicMap)
  .map((item) => ({
    Desc: item.Desc,
    Category: head(item.Includes.filter((it) => startsWith(it.Desc, '全部')))?.Category,
  }))
  .map((it) => ({
    Desc: it.Desc,
    Category: split(it.Category, ',').map((c) => split(c, '(')[0]),
  }))
  .value();

// Category 98
const PRODUCT_TYPE_MAP = new Map([
  [0, '未披露'],
  [1, '食品'],
  [2, '化妆品'],
  [3, '工业品'],
]);

// 招标角色类型
// const TENDER_ROLE_TYPE_MAP = new Map([
//   [1, '招标单位'],
//   [2, '中标单位'],
//   [3, '建设单位'],
//   [4, '投标单位'],
//   [5, '提及单位']
// ])

const _getTitle = (cate) => {
  // 合并工商特殊类型
  if (cate === 9999) {
    return '工商';
  }
  const node = find(titleMap, (m) => includes(m.Category, `${cate}`));
  return node?.Desc ?? '';
};

// 是否能跳转keyno
// let _isValidHighlisghtKeyNo = keyno => {
//   return !!keyno && ((/^[a-f0-9]{32}$/.test(keyno)) || keyno.startsWith('p'));
// };

// 处理招投标公司显示逻辑

const processTMsg = (items: any[], tMsg: any[], typeName: string, desc: DescModel) => {
  if (!(items?.length > 0)) {
    return;
  }
  const tNames = [];
  let tSufffix = '';
  const nameCount = Math.min(items.length, 3);
  if (items.length > 3) {
    tSufffix = `等${items.length}个`;
  }
  for (let i = 0; i < nameCount; i++) {
    const item = items[i];
    tNames.push(item.A);
    desc.Highlight.push({
      Id: item.K || '',
      Name: item.A || '',
      Org: item.O || 0,
    });
  }
  riskHelper.pushContentKV({ contents: tMsg, k: typeName, v: tNames.join('、') + tSufffix });
};

const RISK_CASE_SET = new Set([
  22, 107, 121, 63, 56, 216, 55, 208, 91, 231, 3, 206, 2, 205, 58, 4, 221, 49, 220, 90, 232, 18, 219, 7, 218, 27, 217, 26, 212, 59, 224, 76, 228, 238,
]);
//mongodb中不存在的objectId对应的category
const DIFF_RISK_CASE_SET = new Set([22, 107, 121, 63, 90, 232, 238]);

const getCaseNo = (data, category) => {
  if ([26, 212].includes(category)) {
    return data.ExecutionNoticeNum;
  } else if ([49, 220, 90, 232].includes(category)) {
    return data.E;
  } else if ([18, 219, 27, 217].includes(category)) {
    return data.C;
  } else if ([7, 218].includes(category)) {
    return data.F;
  } else if ([22, 107, 238].includes(category)) {
    return data.D;
  }
  return data.A;
};

// const _getCaseSearchIds = async (ids) => {
//   const data = await entity.CaseSearchRelation.find({ _id: { $in: ids } })
//     .lean()
//     .exec();
//   // const data = [];
//   if (data?.length) {
//     return _.fromPairs(_.map(data, (d) => [d.WdId, d.CaseSearchId]));
//   }
//   return null;
// };

export const getFilterCaseIds = async (items, isDisplay) => {
  const list = isDisplay ? items.DisplayList : items;
  // 找出所有需要关联的数据
  return list
    .filter((t) => RISK_CASE_SET.has(t.Category) && !DIFF_RISK_CASE_SET.has(t.Category))
    .map((t) => {
      if ([4, 221].includes(t.Category)) {
        return md5(`${t.ObjectId}0${shared.CASE_RELATION.get(t.Category)}`);
      }
      return md5(`${t.ObjectId}${shared.CASE_RELATION.get(t.Category)}`);
    });
};

export const buildCaseTitleDesc = async (items, isDisplay, objectKeys, changeExtendMap) => {
  const list = isDisplay ? items.DisplayList : items;
  // 组合信息
  _.forEach(list, (item) => {
    const objectId = [4, 221].includes(item.Category) ? `${item.ObjectId}0` : item.ObjectId;
    if (RISK_CASE_SET.has(item.Category) && objectKeys[objectId]) {
      let c = {};
      if (changeExtendMap[item.Id]) {
        c = JSON.parse(changeExtendMap[item.Id][0]);
      }
      if (_.isEmpty(c) && item.ChangeExtend) {
        c = JSON.parse(item.ChangeExtend);
      }
      // 添加描述
      item.OtherHighlight.push({
        Id: objectKeys[objectId],
        Name: getCaseNo(c, item.Category),
        Type: 'CASE',
      });
    }
  });
  return items;
};

/**
 * 获取风险动态列表的描述
 * @method
 * @param  {[item.datatype]}  1:公司 2:人员
 * @description Highlight Array
 'Id': '',
 'Name': '',
 'Org': '',
 * @return {Object}
 *
 * <AUTHOR> Zhang <<EMAIL>>
 * @since  2019-07-29T16:12:12+0800
 */
const getRiskListDesc = (item, useH5Style = false) => {
  const { lineBreak, prefixLineBreak, redStyle, labelStyleStart, labelStyleStartOneLine, labelStyleEnd } = riskHelper.getH5Style(useH5Style);
  /**
   * Title: 当前分类的父分类
   * @type {Object}
   */
  const desc: DescModel = {
    Title: '',
    Subtitle: '',
    ChangeExtend: item.ChangeExtend, // 返回changExtend
    Content: '',
    ContentArray: [],
    Highlight: [],
    OtherHighlight: [],
    SubtitleHighlight: '',
    RelateChange: '',
    Category: item.Category,
    Id: item.Id,
    Name: item.Name,
    ObjectId: item.ObjectId,
    CreateDate: item?.CreateDate > 0 ? item?.CreateDate : null,
    ChangeDate: item?.ChangeDate > 0 ? item?.ChangeDate : null,
    UpdateDate: item?.UpdateDate > 0 ? item?.UpdateDate : null,
    PublishTime: item?.CreateDate > 0 ? item?.CreateDate : null,
  };

  let contentInfo: ContentInfoModel;
  // const context = callContext.get({});
  // let isEncryData = context.custom?.isEncryData || false;
  // 判断是否是来自B端的请求,C端请求需对自然人脱密(裁判文书)
  // const useEncryName = !CommonHelper.isFromBSide();
  const useEncryName = false;
  const isHideDyContent = false;
  const isNormalUser = false;
  let changeInfo: ChangeInfoModel;
  let extend = null;

  item.ChangeStatus = Number(item.ChangeStatus);
  item.Category = Number(item.Category);

  desc.Title = _getTitle(item.Category);
  try {
    changeInfo = item.ChangeExtend ? JSON.parse(item.ChangeExtend) : {};
    if (item.Category === 28) {
      changeInfo.A = changeInfo.A ? JSON.parse(changeInfo.A) : [];
    }
    if (item.Category === 58) {
      changeInfo.B = changeInfo.B ? JSON.parse(changeInfo.B) : [];
      changeInfo.C = changeInfo.C ? JSON.parse(changeInfo.C) : [];
      changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD') : '';
    }
    if (item.Category === 59 || item.Category === 224) {
      changeInfo.B = changeInfo.B ? JSON.parse(changeInfo.B) : {};
      changeInfo.F = changeInfo.F ? JSON.parse(changeInfo.F) : {};
      changeInfo.G = changeInfo.G ? JSON.parse(changeInfo.G) : {};
      changeInfo.H = changeInfo.H ? JSON.parse(changeInfo.H) : {};
      changeInfo.I = changeInfo.I ? JSON.parse(changeInfo.I) : {};
    }
    if (item.Category === 76 || item.Category === 228) {
      changeInfo.C = changeInfo.C ? JSON.parse(changeInfo.C) : [];
    }
    if (item.Category === 78) {
      changeInfo.D = changeInfo.D ? JSON.parse(changeInfo.D) : [];
    }
    if (item.Category === 79) {
      changeInfo.F = changeInfo.F ? JSON.parse(changeInfo.F) : [];
      changeInfo.G = changeInfo.G ? JSON.parse(changeInfo.G) : [];
    }
  } catch (error) {
    // 如果是反序列化异常，这里在单独记录一下
    if (error.code === 'ERR_SERIALIZER') {
      throw error;
      // appLogger.error(JSON.stringify(item), error);
    }
    return desc;
  }

  // 处理changeInfo中的日期格式
  if (item.Category === 56 || item.Category === 216) {
    changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
    changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD') : '';
  }
  if (item.Category === 57) {
    changeInfo.D = changeInfo.D ? moment.unix(changeInfo.D).format('YYYY-MM-DD HH:mm:ss') : '';
  }
  if (item.Category === 61) {
    changeInfo.B = changeInfo.B ? moment.unix(changeInfo.B).format('YYYY-MM-DD') : '';
    changeInfo.C = changeInfo.C ? moment.unix(changeInfo.C).format('YYYY-MM-DD') : '';
  }
  if (item.Category === 63) {
    changeInfo.G = changeInfo.G ? moment.unix(changeInfo.G).format('YYYY-MM-DD') : '';
  }

  let dtChange = item.ChangeDate ? moment.unix(item.ChangeDate).format('YYYY-MM-DD') : '-';
  const dtCreate = item.CreateDate > 0 ? moment.unix(item.CreateDate).format('YYYY-MM-DD') : '';
  if (dtChange === '1900-01-01') {
    dtChange = '-';
  }

  let beforeContent = item.BeforeContent;
  let afterContent = item.AfterContent;
  const beforeNames = [];
  const afterNames = [];
  const tMsg = [];
  let tNames = [];
  const tNames2 = [];
  const tNames3 = [];
  const tNames4 = [];
  const tNames6 = [];
  let tSufffix = '';
  let hasAmount;
  let hasPercent;
  let t = '';
  const tBigEventList = [];
  let extend1 = null;
  let beforeChange = null;
  let afterChange = null;

  const needJsonParseCat = new Set([21, 24, 25]);
  if (needJsonParseCat.has(item.Category)) {
    beforeContent = JSON.parse(beforeContent);
    afterContent = JSON.parse(afterContent);
    if ((Number(changeInfo.T) === 1 && Number(item.Category) === 21) || Number(item.Category) === 24) {
      beforeContent.forEach((item) => {
        beforeNames.push(item.Name);
        desc.Highlight.push({
          Id: item.KeyNo || '',
          Name: item.Name || '',
          Org: item.Org || 0,
        });
      });
      afterContent.forEach((item) => {
        afterNames.push(item.Name);
        desc.Highlight.push({
          Id: item.KeyNo || '',
          Name: item.Name || '',
          Org: item.Org || 0,
        });
      });
    }
  }

  // 处理 content
  if (item.Category === 41) {
    beforeContent = beforeContent ? CommonHelper.replaceAll(beforeContent, '<em>', redStyle) : '';
    beforeContent = beforeContent ? CommonHelper.replaceAll(beforeContent, '</em>', labelStyleEnd) : '';
    afterContent = afterContent ? CommonHelper.replaceAll(afterContent, '<em>', redStyle) : '';
    afterContent = afterContent ? CommonHelper.replaceAll(afterContent, '</em>', labelStyleEnd) : '';
  }

  switch (item.Category) {
    case 2:
    case 205:
      desc.Subtitle = '被列入失信被执行人';
      desc.SubtitleHighlight = '失信被执行人';
      riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '涉案金额（元）',
          v: CommonHelper.handleNum(Number(changeInfo.F)),
        });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.E || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (item.Category === 205) {
        desc.HotDynamicDesc = '近期被列为<em>失信被执行人</em>';
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，履行情况：${changeInfo.B}`;
      break;
    case 3:
      desc.Subtitle = '被列入被执行人';
      desc.SubtitleHighlight = '被执行人';
      if (Number(changeInfo.T) === 1) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: changeInfo.B ? CommonHelper.handleNum(Number(changeInfo.B)) : '-',
          showEm: changeInfo.B > 2000000,
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.E || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，执行标的（元）：${changeInfo.B}`;
      } else {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.C || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，案号：${changeInfo.A}`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 4:
    case 221:
      // 裁判文书
      desc.Subtitle = '新增裁判文书';
      riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '案由', v: changeInfo.F || '-' });
      if (changeInfo.I) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '案件金额（元）',
          v: CommonHelper.handleNum(Number(changeInfo.I)),
        });
      }
      if (changeInfo.K?.length) {
        CommonHelper.processCaseRole({
          field: changeInfo.K,
          tMsg,
          useH5Style,
          item,
          useEncryName,
          highlight: desc.Highlight,
        });
      }
      if (changeInfo.M) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '裁判日期',
          v: moment.unix(changeInfo.M).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.F ? `，案由：${changeInfo.F}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${extend}`;
      break;
    case 7:
    case 218:
      // 法院公告
      desc.Subtitle = `新增法院公告${changeInfo.category === '其他法院公告' ? '' : '-' + changeInfo.category}`;
      contentInfo = riskHelper.getContent7And218(changeInfo, useH5Style, useEncryName, item);
      desc.ContentArray = contentInfo.tMsg;
      desc.Content = riskHelper.getContent(contentInfo.tMsg, useH5Style);
      desc.Highlight = contentInfo.highlight;
      extend = contentInfo.bigEventDesc.length ? `，${contentInfo.bigEventDesc.join('，')}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${extend}`;
      break;
    case 11:
      desc.Subtitle = '被列入经营异常';
      desc.SubtitleHighlight = '经营异常';
      riskHelper.pushContentKV({ contents: tMsg, k: '列入原因', v: changeInfo.B || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '列入机关', v: changeInfo.C || '-' });
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '列入日期',
          v: moment.unix(changeInfo.A).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，列入原因：${changeInfo.B || '-'}`;
      break;
    case 12:
    case 213:
      desc.Subtitle = '新增股权出质';
      if (changeInfo.T2 === 1) {
        // Status 无效
        desc.Subtitle = `股权出质${changeInfo.Status || ''}`;
      }
      hasAmount = changeInfo.PledgedAmount && Number(changeInfo.PledgedAmount) !== 0;
      hasPercent = changeInfo.Percent && Number(changeInfo.Percent) !== 0;
      // 出质人（企业）
      if (Number(changeInfo.T) === 2) {
        desc.Subtitle = changeInfo.T2 === 1 ? '有股权出质变更为无效' : '出质了在外持有的股权';
        if (changeInfo.PledgorInfo && changeInfo.PledgorInfo.Name) {
          riskHelper.pushContentKV({ contents: tMsg, k: '出质人', v: changeInfo.PledgorInfo.Name });
        }
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          riskHelper.pushContentKV({ contents: tMsg, k: '质权人', v: changeInfo.PledgeeInfo.Name });
        }
        riskHelper.pushContentKV({ contents: tMsg, k: '出质股权标的企业', v: changeInfo.CompanyName || '-' });
        if (hasAmount) {
          riskHelper.pushContentKV({ contents: tMsg, k: '出质股权数额', v: changeInfo.PledgedAmount });
        }
        if (hasPercent) {
          riskHelper.pushContentKV({ contents: tMsg, k: '占所持股份比例', v: changeInfo.Percent });
        }
        if (changeInfo.RegDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '登记日期',
            v: moment.unix(changeInfo.RegDate).format('YYYY-MM-DD'),
          });
        }
        desc.ContentArray = tMsg;
        desc.Content = riskHelper.getContent(tMsg, useH5Style);
        if (changeInfo.PledgorInfo && changeInfo.PledgorInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgorInfo.KeyNo || '',
            Name: changeInfo.PledgorInfo.Name || '',
            Org: changeInfo.PledgorInfo.Org || 0,
          });
        }
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgeeInfo.KeyNo || '',
            Name: changeInfo.PledgeeInfo.Name || '',
            Org: changeInfo.PledgeeInfo.Org || 0,
          });
        }
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.Org || 0,
        });
      } else if (Number(changeInfo.T) === 1) {
        // 出质股权标的企业
        desc.Subtitle = changeInfo.T2 === 1 ? '股东股权出质变更为无效' : '股东出质了该企业股权';
        riskHelper.pushContentKV({ contents: tMsg, k: '出质人', v: changeInfo.Name || '-' });
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          riskHelper.pushContentKV({ contents: tMsg, k: '质权人', v: changeInfo.PledgeeInfo.Name });
        }
        if (changeInfo.CompanyName) {
          riskHelper.pushContentKV({ contents: tMsg, k: '出质股权标的企业', v: changeInfo.CompanyName });
        }
        if (hasAmount) {
          riskHelper.pushContentKV({ contents: tMsg, k: '出质股权数额', v: changeInfo.PledgedAmount });
        }
        if (hasPercent) {
          riskHelper.pushContentKV({ contents: tMsg, k: '占该公司股权比例', v: changeInfo.Percent });
        }
        if (changeInfo.RegDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '登记日期',
            v: moment.unix(changeInfo.RegDate).format('YYYY-MM-DD'),
          });
        }
        desc.ContentArray = tMsg;
        desc.Content = riskHelper.getContent(tMsg, useH5Style);
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        if (changeInfo.PledgeeInfo && changeInfo.PledgeeInfo.Name) {
          desc.Highlight.push({
            Id: changeInfo.PledgeeInfo.KeyNo || '',
            Name: changeInfo.PledgeeInfo.Name || '',
            Org: changeInfo.PledgeeInfo.Org || 0,
          });
        }
        if (changeInfo.CompanyName) {
          desc.Highlight.push({
            Id: changeInfo.CompanyId || '',
            Name: changeInfo.CompanyName || '',
            Org: 0,
          });
        }
      }
      extend = hasAmount ? `，出质股权数额：：${changeInfo.PledgedAmount}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${extend}`;
      break;
    case 13:
    case 225:
      desc.Subtitle = '被行政处罚';
      desc.SubtitleHighlight = '行政处罚';
      riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.C || '-', isLink: true });
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '违法行为类型', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '违法事实', v: changeInfo.B });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '决定机关', v: changeInfo.D || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '决定日期', v: dtChange });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.B ? `，违法事实：${changeInfo.B}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 96:
    case 233:
    case 107:
    case 238:
      desc.Subtitle = '被行政处罚';
      desc.SubtitleHighlight = '行政处罚';
      riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.D || '-', isLink: true });
      if (changeInfo.B) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '违法事实',
          v: useEncryName ? changeInfo.B1 || changeInfo.B : changeInfo.B,
          line: 3,
        });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处罚金额（万元）',
          v: CommonHelper.div(changeInfo.F, 10000).toFixed(2),
          showEm:
            (includes(changeInfo.D, '反垄断') && Number(changeInfo.F) > 5000000) ||
            ((changeInfo.M === 'A01' || changeInfo.M === 'A02') && Number(changeInfo.F) > 3000000) ||
            (changeInfo.M !== 'A01' && changeInfo.M !== 'A02' && !includes(changeInfo.D, '反垄断') && Number(changeInfo.F) > 100000),
        });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '处罚单位', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '处罚日期', v: dtChange });
      // 将文案拼接等规则放入公共方法调用
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (changeInfo.B) {
        // 主页滚动文案优先级展示
        extend = `，违法事实：${useEncryName ? changeInfo.B1 || changeInfo.B : changeInfo.B}`;
      } else if (changeInfo.C) {
        extend = `，处罚结果：${useEncryName ? changeInfo.C1 || changeInfo.C : changeInfo.C}`;
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend || ''}`;
      break;
    case 14:
      desc.Subtitle = '被抽查检查';
      if (!CommonHelper.isNotEmpty(changeInfo.G) || !changeInfo.G) {
        riskHelper.pushContentKV({ contents: tMsg, k: '结果', v: changeInfo.A || '-' });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '检查实施机关', v: changeInfo.B });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '日期', v: dtChange });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，结果：${changeInfo.A || '-'}`;
      break;
    case 15:
      // 动产抵押 使用加密的id做为ObjectId
      desc.Subtitle = '对外抵押了动产';
      if (changeInfo.Pledgor && typeof changeInfo.Pledgor === 'object') {
        afterNames.push(changeInfo.Pledgor.A);
        desc.Highlight.push({
          Id: changeInfo.Pledgor.K || '',
          Name: changeInfo.Pledgor.A || '',
          Org: changeInfo.Pledgor.O || 0,
        });
      }
      if (changeInfo.Pledgee && changeInfo.Pledgee.length && typeof changeInfo.Pledgee === 'object') {
        tSufffix = '';
        if (changeInfo.Pledgee.length > 3) {
          tSufffix = `等${changeInfo.Pledgee.length}家`;
          changeInfo.Pledgee = changeInfo.Pledgee.slice(0, 3);
        }
        changeInfo.Pledgee.forEach((item) => {
          beforeNames.push(item.A);
          desc.Highlight.push({
            Id: item.K || '',
            Name: item.A || '',
            Org: item.O || 0,
          });
        });
      }
      if (afterNames.length) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抵押人', v: afterNames.join('，') });
      }
      if (beforeNames.length) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抵押权人', v: beforeNames.join('，') + tSufffix });
      }
      if (changeInfo.A) {
        changeInfo.A = changeInfo.A.trim();
        riskHelper.pushContentKV({ contents: tMsg, k: '被担保债权数额', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '债务人履行债务期限', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '登记日期',
          v: moment(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.IsDecrypted = 0;
      extend = changeInfo.A ? `，被担保债权数额：${changeInfo.A}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${extend}`;
      break;
    case 16: // 该维度数据页面不展示，暂时保留
      desc.Subtitle = '开始清算';
      desc.ContentArray = [
        {
          Key: '清算组负责人',
          Value: changeInfo.A,
        },
        {
          Key: '清算组成员',
          Value: changeInfo.B,
        },
      ];
      desc.Content = `${labelStyleStart}清算组负责人：${labelStyleEnd}${changeInfo.A}，${labelStyleStart}清算组成员：${labelStyleEnd}${changeInfo.B}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 17:
    case 203:
      // 撤出
      if (Number(item.ChangeStatus) === 2) {
        desc.Subtitle = '退出对外投资';
        riskHelper.pushContentKV({ contents: tMsg, k: '退出投资企业', v: changeInfo.A || '-' });
        if (!beforeContent) {
          beforeContent = '企业未公示';
          riskHelper.pushContentKV({ contents: tMsg, k: '退出前持股比例', v: beforeContent });
        } else {
          riskHelper.pushContentKV({ contents: tMsg, k: '退出前持股比例', v: beforeContent });
          const holdValue = Number(beforeContent.slice(0, -1));
          if (holdValue <= 50 && changeInfo?.IsBP === '2') {
            riskHelper.pushContentKV({ contents: tMsg, k: '', v: '不再是该企业的大股东' });
            desc.RelateChange = '不再是该企业的大股东';
          }
        }
        desc.BigEventDesc = `${dtCreate} 退出投资企业：${changeInfo.A} 退出前持股比例：${beforeContent}`;
      } else if (Number(item.ChangeStatus) === 1) {
        // 新增
        desc.Subtitle = '新增对外投资';
        riskHelper.pushContentKV({ contents: tMsg, k: '被投资企业', v: changeInfo.A || '-' });
        if (!afterContent) {
          afterContent = '企业未公示';
          riskHelper.pushContentKV({ contents: tMsg, k: '投资比例', v: afterContent });
        } else {
          riskHelper.pushContentKV({ contents: tMsg, k: '投资比例', v: afterContent });
          if (item.Extend2) {
            extend = JSON.parse(item.Extend2);
            if (extend?.C) {
              extend1 = reduce(
                split(extend.C, ','),
                (sum, e) => {
                  return sum + (Number(e) || 0);
                },
                0,
              );
              riskHelper.pushContentKV({
                contents: tMsg,
                k: '认缴出资额',
                v: `${extend1}${extend?.B ? CommonHelper.removeRmb(extend.B.replace(/\d+\.?\d*/g, '')) : '万元'}`,
              });
            }
          }
          const holdValue = Number(afterContent.slice(0, -1));
          if (holdValue <= 50 && changeInfo?.IsBP === '1') {
            riskHelper.pushContentKV({ contents: tMsg, k: '', v: '成为该企业的大股东' });
            desc.RelateChange = '成为该企业的大股东';
          }
        }
        desc.BigEventDesc = `${dtCreate} 被投资企业：${changeInfo.A} 投资比例：${afterContent}`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 18:
    case 219:
      // 开庭公告
      desc.Subtitle = '新增开庭公告';
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.C || '-' });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案由', v: changeInfo.B || '-' });
      }
      if (changeInfo.D?.length) {
        CommonHelper.processCaseRole({
          field: changeInfo.D,
          tMsg,
          useEncryName,
          useH5Style,
          item,
          highlight: desc.Highlight,
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({ contents: tMsg, k: '法院', v: changeInfo.E || '-' });
      }
      if (changeInfo.A) {
        t = moment.unix(changeInfo.A).format('YYYY-MM-DD HH:mm');
        t = t.replace(' 00:00', '');
        riskHelper.pushContentKV({ contents: tMsg, k: '开庭时间', v: t });
        extend = moment.unix(changeInfo.A).format('YYYY-MM-DD');
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${extend ? '开庭时间：' + extend : dtCreate} ${desc.Subtitle}，案由：${changeInfo.B || '-'}`;
      break;
    case 20:
      desc.Subtitle = '被列入严重违法';
      desc.SubtitleHighlight = '严重违法';
      riskHelper.pushContentKV({ contents: tMsg, k: '列入原因', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '决定机关', v: changeInfo.C || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '列入日期', v: dtChange });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，列入原因：${changeInfo.A || '-'}`;
      break;
    case 21:
      if (Number(changeInfo.T) === 1) {
        desc.Subtitle = '受益所有人变更';
        desc.ContentArray = [
          {
            Key: '',
            Value: `从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”${labelStyleEnd}`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
      } else if (Number(changeInfo.T) === 2) {
        if (changeInfo.Increase === 1) {
          // 上升
          desc.Subtitle = '最终受益人受益股份上升';
          desc.ContentArray = [
            {
              Key: '',
              Value: `${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`,
            },
          ];
          desc.Content = `${labelStyleStartOneLine}${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”${labelStyleEnd}`;
          desc.BigEventDesc = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`;
          desc.Highlight.push({
            Id: beforeContent.KeyNo || '',
            Name: beforeContent.Name || '',
            Org: beforeContent.Org || 0,
          });
        } else {
          desc.Subtitle = '最终受益人受益股份下降';
          desc.ContentArray = [
            {
              Key: '',
              Value: `${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`,
            },
          ];
          desc.Content = `${labelStyleStartOneLine}${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”${labelStyleEnd}`;
          desc.BigEventDesc = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`;
          desc.Highlight.push({
            Id: beforeContent.KeyNo || '',
            Name: beforeContent.Name || '',
            Org: beforeContent.Org || 0,
          });
        }
      }
      break;
    case 114:
      tNames = chain(changeInfo)
        .map((c) => `${JSON.parse(c.ChangeExtend).T}_${JSON.parse(c.ChangeExtend).Increase || ''}`)
        .uniq()
        .value();
      if (tNames.length > 1 || tNames[0] === '1_') {
        desc.Subtitle = '受益所有人变更';
      } else if (tNames[0] === '2_1') {
        desc.Subtitle = '受益所有人受益股份上升';
      } else {
        desc.Subtitle = '受益所有人受益股份下降';
      }
      tNames = [];
      // 生成文案和高亮
      forEach(changeInfo, (item) => {
        beforeContent = JSON.parse(item.BeforeContent || '{}');
        afterContent = JSON.parse(item.AfterContent || '{}');
        extend1 = JSON.parse(item.ChangeExtend);
        if (extend1.T === 1) {
          for (const beforeName of beforeContent) {
            beforeNames.push(beforeName.Name);
            desc.Highlight.push({
              Id: beforeName.KeyNo || '',
              Name: beforeName.Name || '',
              Org: beforeName.Org || 0,
            });
          }
          for (const afterName of afterContent) {
            afterNames.push(afterName.Name);
            desc.Highlight.push({
              Id: afterName.KeyNo || '',
              Name: afterName.Name || '',
              Org: afterName.Org || 0,
            });
          }
          tNames.push(...beforeNames);
          tNames.push(...afterNames);
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '',
            v: `从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`,
          });
          if (changeInfo.length === 1) {
            desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
          }
        } else if (extend1.T === 2) {
          if (extend1.Increase === 1) {
            // 上升
            riskHelper.pushContentKV({
              contents: tMsg,
              k: '',
              v: `${beforeContent.Name}的最终受益股份从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`,
            });
            desc.Highlight.push({
              Id: beforeContent.KeyNo || '',
              Name: beforeContent.Name || '',
              Org: beforeContent.Org || 0,
            });
            if (changeInfo.length === 1) {
              desc.BigEventDesc = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”上升到“${afterContent.PercentTotal}”`;
            }
            tNames.push(beforeContent.Name);
          } else {
            riskHelper.pushContentKV({
              contents: tMsg,
              k: '',
              v: `${beforeContent.Name}的最终受益股份从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`,
            });
            desc.Highlight.push({
              Id: beforeContent.KeyNo || '',
              Name: beforeContent.Name || '',
              Org: beforeContent.Org || 0,
            });
            if (changeInfo.length === 1) {
              desc.BigEventDesc = `${dtCreate} ${beforeContent.Name}的最终受益股份比例从“${beforeContent.PercentTotal}”下降到“${afterContent.PercentTotal}”`;
            }
            tNames.push(beforeContent.Name);
          }
        }
      });
      tNames = uniq(tNames);
      if (changeInfo.length > 1) {
        desc.BigEventDesc = `${dtCreate} 受益所有人变更：${tNames.length > 3 ? tNames.slice(0, 3).join('、') + '等' : tNames.join('、')}`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 22:
      desc.Subtitle = '被环保处罚';
      desc.SubtitleHighlight = '环保处罚';
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.D, isLink: true });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '违法事实',
          v: useEncryName ? changeInfo.A1 || changeInfo.A : changeInfo.A,
          line: 3,
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处罚金额（万元）',
          v: Number(changeInfo.E).toFixed(2),
          showEm:
            (includes(changeInfo.D, '反垄断') && Number(changeInfo.E) > 500) ||
            ((changeInfo.M === 'A01' || changeInfo.M === 'A02') && Number(changeInfo.E) > 300) ||
            (changeInfo.M !== 'A01' && changeInfo.M !== 'A02' && !includes(changeInfo.D, '反垄断') && Number(changeInfo.E) > 10),
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚结果', v: changeInfo.B, line: 3 });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处罚单位', v: changeInfo.F });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处罚日期',
          v: moment.unix(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.B ? `，处罚结果：${changeInfo.B}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 23:
      desc.Subtitle = '新增简易注销';
      desc.SubtitleHighlight = '简易注销';
      riskHelper.pushContentKV({ contents: tMsg, k: '简易注销结果', v: changeInfo.B || '-' });
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '核准日期', v: changeInfo.D });
      } else if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告期', v: changeInfo.A });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} 简易注销结果：${changeInfo.B || '-'}`;
      break;
    case 24:
      desc.Subtitle = '大股东变更';
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`,
        },
      ];
      desc.Content = `${labelStyleStartOneLine}从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”${labelStyleEnd}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从“${beforeNames.join('，')}”变更为“${afterNames.join('，')}”`;
      break;
    case 25:
      desc.Subtitle = '实际控制人变更';
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${beforeContent.Name}”变更为“${afterContent.Name}”`,
        },
      ];
      desc.Content = `${labelStyleStartOneLine}从“${beforeContent.Name}”变更为“${afterContent.Name}”${labelStyleEnd}`;
      extend = `：从“${beforeContent.Name}”变更为“${afterContent.Name}”`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      desc.Highlight.push({
        Id: beforeContent.KeyNo || '',
        Name: beforeContent.Name || '',
        Org: beforeContent.Org || 0,
      });
      desc.Highlight.push({
        Id: afterContent.KeyNo || '',
        Name: afterContent.Name || '',
        Org: afterContent.Org || 0,
      });
      break;
    case 26:
    case 212:
      // T2 Status: 失效 无效 解除
      if (changeInfo.T2 === 1) {
        desc.Subtitle = `股权冻结${changeInfo.Status}`;
        if (changeInfo?.T === 1) {
          desc.Subtitle = `企业股权冻结被${changeInfo.Status}`;
        } else if (changeInfo?.T === 2) {
          desc.Subtitle = `持有股权冻结被${changeInfo.Status}`;
        }
        if (changeInfo.ExecutionNoticeNum) {
          riskHelper.pushContentKV({ contents: tMsg, k: '执行通知书文号', v: changeInfo.ExecutionNoticeNum });
        }
        if (changeInfo.CompanyName) {
          riskHelper.pushContentKV({ contents: tMsg, k: '冻结股权标的企业', v: changeInfo.CompanyName });
        }
        if (changeInfo.EquityAmount) {
          changeInfo.EquityAmount = changeInfo.EquityAmount.trim();
          riskHelper.pushContentKV({ contents: tMsg, k: '冻结权益数额', v: changeInfo.EquityAmount });
        }
        if (changeInfo.UnFreezeDate) {
          riskHelper.pushContentKV({ contents: tMsg, k: '解冻日期', v: changeInfo.UnFreezeDate });
        }
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.CompanyOrg || 0,
        });
      } else {
        desc.Subtitle = '新增股权冻结';
        if (item.Category === 212) {
          desc.Subtitle = '股权被冻结';
        }
        if (changeInfo?.T === 1) {
          desc.Subtitle = '企业股权被冻结';
        } else if (changeInfo?.T === 2) {
          desc.Subtitle = '持有股权被冻结';
        }
        if (changeInfo.ExecutionNoticeNum) {
          riskHelper.pushContentKV({ contents: tMsg, k: '执行通知书文号', v: changeInfo.ExecutionNoticeNum });
        }
        if (changeInfo.Name) {
          riskHelper.pushContentKV({ contents: tMsg, k: '被执行人', v: changeInfo.Name });
        }
        if (changeInfo.CompanyName) {
          riskHelper.pushContentKV({ contents: tMsg, k: '冻结股权标的企业', v: changeInfo.CompanyName });
        }
        if (changeInfo.EquityAmount) {
          changeInfo.EquityAmount = changeInfo.EquityAmount.trim();
          riskHelper.pushContentKV({ contents: tMsg, k: '冻结权益数额', v: changeInfo.EquityAmount });
        }
        if (changeInfo.FreezeStartDate || changeInfo.FreezeEndDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '冻结期限',
            v: `${changeInfo.FreezeStartDate || '-'}至${changeInfo.FreezeEndDate || '-'}`,
          });
        }
        desc.Highlight.push({
          Id: changeInfo.KeyNo || '',
          Name: changeInfo.Name || '',
          Org: changeInfo.Org || 0,
        });
        desc.Highlight.push({
          Id: changeInfo.CompanyId || '',
          Name: changeInfo.CompanyName || '',
          Org: changeInfo.CompanyOrg || 0,
        });
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，冻结权益数额：${changeInfo.EquityAmount || '-'}`;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 27:
    case 217:
      desc.Subtitle = '新增送达公告';
      riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.C || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '案由', v: changeInfo.F || '-' });
      if (changeInfo.D?.length) {
        CommonHelper.processCaseRole({
          field: changeInfo.D,
          tMsg,
          useEncryName,
          useH5Style,
          item,
          highlight: desc.Highlight,
        });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '法院', v: changeInfo.B || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: changeInfo.A || '-' });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，法院名称：${changeInfo.B || '-'}`;
      break;
    case 28:
      desc.Title = '经营状况';
      desc.Subtitle = '融资动态';
      if (changeInfo.G) {
        riskHelper.pushContentKV({ contents: tMsg, k: '产品名称', v: changeInfo.G });
        tBigEventList.push(`产品名称：${changeInfo.G}`);
        if (changeInfo.I) {
          desc.OtherHighlight.push({
            Id: changeInfo.I,
            Name: changeInfo.G,
            Type: 'PRODUCT',
          });
        }
      }
      if (changeInfo.A?.length) {
        // 兼容老数据
        if (changeInfo.A[0].OriginalName || changeInfo.A[0].Id) {
          changeInfo.A.forEach((item) => {
            if (item.Name && item.Name !== '投资方未知') {
              tNames.push(item.Name);
              if (item.KeyNo) {
                desc.Highlight.push({
                  Id: item.KeyNo,
                  Name: item.Name,
                  Org: item.Org || 0,
                });
              }
            }
          });
        } else {
          changeInfo.A.forEach((item) => {
            // 优先使用投资机构
            if (item.RelationInfo?.length) {
              item.RelationInfo.forEach((r) => {
                tNames.push(r.Name);
                desc.Highlight.push({
                  Id: r.KeyNo,
                  Name: r.Name,
                  Org: r.Org || 13,
                });
              });
            } else {
              tNames.push(item.Name);
              desc.Highlight.push({
                Id: item.KeyNo,
                Name: item.Name,
                Org: item.Org || 0,
              });
            }
          });
        }
        tNames = uniq(tNames); // 名字去重
        if (tNames.length) {
          riskHelper.pushContentKV({ contents: tMsg, k: '投资方', v: tNames.join('、') });
          tBigEventList.push(`投资方：${tNames.join('、')}`);
        }
      }
      if (changeInfo.C) {
        // 融资动态标签细分
        if (includes(dynamicConstant.financingRound, changeInfo.C)) {
          desc.Subtitle = `获得${changeInfo.C}融资`;
        } else if (includes(dynamicConstant.financingType, changeInfo.C)) {
          desc.Subtitle = `新增${changeInfo.C}`;
        } else if (includes(dynamicConstant.addStockType, changeInfo.C)) {
          desc.Subtitle = `${changeInfo.K && changeInfo.K !== '-' ? changeInfo.K + ' ' : ''}${changeInfo.C}`;
        } else if (includes(dynamicConstant.addTBoadType, changeInfo.C)) {
          desc.Subtitle = `新三板${changeInfo.C}${changeInfo.K && changeInfo.K !== '-' ? ' ' + changeInfo.K : ''}`;
        }
        // 兼容数据不规范出现的-,j交易所k股票代码
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '融资轮次',
          v: (changeInfo.J && changeInfo.J !== '-' ? changeInfo.J : '') + changeInfo.C + (changeInfo.K && changeInfo.K !== '-' ? ' ' + changeInfo.K : ''),
        });
        tBigEventList.push(
          `融资轮次：${
            (changeInfo.J && changeInfo.J !== '-' ? changeInfo.J : '') + changeInfo.C + (changeInfo.K && changeInfo.K !== '-' ? ' ' + changeInfo.K : '')
          }`,
        );
      }
      if (changeInfo.B && changeInfo.B !== '金额未知') {
        riskHelper.pushContentKV({ contents: tMsg, k: '融资金额', v: CommonHelper.removeRmb(changeInfo.B) });
        tBigEventList.push(`融资金额：${CommonHelper.removeRmb(changeInfo.B)}`);
      }
      desc.URL = changeInfo.E;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 29:
      desc.Subtitle = '新增税收违法';
      riskHelper.pushContentKV({ contents: tMsg, k: '案件性质', v: changeInfo.A || '-' });
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '罚款金额', v: CommonHelper.handleNum(Number(changeInfo.D)) });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '税务机关', v: changeInfo.C });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '发布日期',
          v: moment.unix(changeInfo.B).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，案件性质：${changeInfo.A}`;
      break;
    case 30:
      desc.Subtitle = '新增土地抵押';
      if (changeInfo.T) {
        if (changeInfo.T === 1) {
          desc.Subtitle = '对外抵押了土地';
        } else if (changeInfo.T === 2) {
          desc.Subtitle = '向该企业抵押了土地';
        }
      }
      if (changeInfo.E && typeof changeInfo.E === 'object') {
        afterNames.push(changeInfo.E.name);
        desc.Highlight.push({
          Id: changeInfo.E.KeyNo || '',
          Name: changeInfo.E.name || '',
          Org: changeInfo.E.Org || 0,
        });
      }
      if (changeInfo.F && typeof changeInfo.F === 'object') {
        beforeNames.push(changeInfo.F.name);
        desc.Highlight.push({
          Id: changeInfo.F.KeyNo || '',
          Name: changeInfo.F.name || '',
          Org: changeInfo.F.Org || 0,
        });
      }
      if (afterNames.length) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抵押人', v: afterNames.join('，') });
      }
      if (beforeNames.length) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抵押权人', v: beforeNames.join('，') });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '评估金额（万元）',
          v: CommonHelper.handleNum(Number(changeInfo.A)),
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '抵押金额（万元）',
          v: CommonHelper.handleNum(Number(changeInfo.B)),
        });
      }
      if (changeInfo.C || changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抵押期', v: `${changeInfo.C || '-'}至${changeInfo.D || '-'}` });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，抵押土地评估金额（万元）：${changeInfo.A || '-'}`;
      break;
    case 31:
      desc.Subtitle = '新增欠税公告';
      desc.SubtitleHighlight = '欠税公告';
      riskHelper.pushContentKV({ contents: tMsg, k: '欠税税种', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '欠税余额（元）',
        v: changeInfo.B ? CommonHelper.handleNum(Number(changeInfo.B)) : '-',
        showEm: includes(changeInfo.A, '企业所得税') && changeInfo.B > 2000000,
      });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布单位', v: changeInfo.D || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: dtChange || '-' });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，欠税税种：${changeInfo.A || '-'}`;
      break;
    case 37:
      extend1 = item.Extend1 ? JSON.parse(item.Extend1) : {};
      if (extend1.T === 1) {
        desc.Subtitle = '注册资本币种变更';
        desc.ContentArray = [
          {
            Key: '',
            Value: `从“${redStyle}${changeInfo.A}${labelStyleEnd}”变更为“${redStyle}${changeInfo.B}${labelStyleEnd}”`,
          },
        ];
        desc.Content = `从“${redStyle}${changeInfo.A}${labelStyleEnd}”变更为“${redStyle}${changeInfo.B}${labelStyleEnd}”`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从${changeInfo.A}变更为${changeInfo.B}`;
      } else {
        beforeChange = CommonHelper.removeRmb(changeInfo.A);
        afterChange = CommonHelper.removeRmb(changeInfo.B);
        if (Number(changeInfo.T) === 2) {
          desc.Subtitle = '注册资本增加';
          extend = extend1.B ? `，增加“${redStyle}${extend1.B}${CommonHelper.removeRmb(extend1.C)}${labelStyleEnd}”` : '';
          t = extend1.B ? `，增加${extend1.B}${CommonHelper.removeRmb(extend1.C)}` : '';
          desc.ContentArray = [
            {
              Key: '',
              Value: `从“${redStyle}${beforeChange}${labelStyleEnd}”增加到“${redStyle}${afterChange}${labelStyleEnd}”${extend}`,
            },
          ];
          desc.Content = `从“${redStyle}${beforeChange}${labelStyleEnd}”增加到“${redStyle}${afterChange}${labelStyleEnd}”${extend}`;
          desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从${beforeChange}增加到${afterChange}${t}`;
        } else {
          desc.Subtitle = '注册资本减少';
          desc.SubtitleHighlight = '注册资本减少';
          extend = extend1.B ? `，减少“${redStyle}${extend1.B}${CommonHelper.removeRmb(extend1.C)}${labelStyleEnd}”` : '';
          t = extend1.B ? `，减少${extend1.B}${CommonHelper.removeRmb(extend1.C)}` : '';
          desc.ContentArray = [
            {
              Key: '',
              Value: `从“${redStyle}${beforeChange}${labelStyleEnd}”减少到“${redStyle}${afterChange}${labelStyleEnd}”${extend}`,
            },
          ];
          desc.Content = `从“${redStyle}${beforeChange}${labelStyleEnd}”减少到“${redStyle}${afterChange}${labelStyleEnd}”${extend}`;
          desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从${beforeChange}减少到${afterChange}${t}`;
        }
      }
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 123:
      desc.Subtitle = '新增减资公告';
      extend1 = changeInfo.E ? JSON.parse(changeInfo.E) : {};
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告标题', v: changeInfo.F, isLink: true });
      }
      if (extend1?.Bm && extend1?.Am) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公告内容',
          v: `注册资本从${extend1.Bm / 10000 + '万' + (CommonHelper.removeRmb(extend1.Bc) || '元')}减少到${
            extend1.Am / 10000 + '万' + (CommonHelper.removeRmb(extend1.Ac) || '元')
          }`,
        });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告期限', v: changeInfo.C });
      } else if (changeInfo.B) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公告日期',
          v: moment.unix(changeInfo.B).format('YYYY-MM-DD'),
        });
      } else if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '做出决定日期',
          v: moment.unix(changeInfo.A).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend =
        extend1?.Bm && extend1?.Am
          ? '：注册资本从' + extend1.Bm / 10000 + '万' + (extend1.Bc || '元') + '减少到' + extend1.Am / 10000 + '万' + (extend1.Ac || '元')
          : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 132:
      desc.Subtitle = changeInfo.I === 1 ? '撤销减资公告' : '终止减资公告';
      extend1 = changeInfo.E ? JSON.parse(changeInfo.E) : {};
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告标题', v: changeInfo.F, isLink: true });
      }
      if (extend1?.Bm && extend1?.Am) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公告内容',
          v: `注册资本从${extend1.Bm / 10000 + '万' + (CommonHelper.removeRmb(extend1.Bc) || '元')}减少到${
            extend1.Am / 10000 + '万' + (CommonHelper.removeRmb(extend1.Ac) || '元')
          }`,
        });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告期限', v: changeInfo.C });
      }
      if (changeInfo.H) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: `${changeInfo.I === 1 ? '撤销' : '终止'}公告时间`,
          v: moment.unix(changeInfo.H).format('YYYY-MM-DD'),
        });
      }
      if (changeInfo.G) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: `${changeInfo.I === 1 ? '撤销' : '终止'}公告理由`,
          v: changeInfo.G,
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${changeInfo.H ? moment.unix(changeInfo.H).format('YYYY-MM-DD') : dtCreate} ${desc.Subtitle}${
        changeInfo.G ? `，${changeInfo.I === 1 ? '撤销' : '终止'}公告理由：${changeInfo.G}` : ''
      }`;
      break;
    case 38:
      desc.Subtitle = '经营状态变更';
      if (includes([1, 2, 3], item.RiskLevel)) {
        desc.Subtitle = '经营状态异常';
        desc.SubtitleHighlight = '经营状态异常';
      }
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${changeInfo.A}”变更为“${changeInfo.B}”`,
        },
      ];
      desc.Content = `${labelStyleStartOneLine}从“${changeInfo.A}”变更为“${changeInfo.B}”${labelStyleEnd}`;
      extend = `：从${changeInfo.A}变更为${changeInfo.B}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 39:
      desc.Subtitle = `${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}变更`;
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${changeInfo.A.N}”变更为“${changeInfo.B.N}”`,
        },
      ];
      desc.Content = `${labelStyleStartOneLine}从“${changeInfo.A.N}”变更为“${changeInfo.B.N}”${labelStyleEnd}`;
      desc.Highlight.push({
        Id: changeInfo.A.K || '',
        Name: changeInfo.A.N || '',
        Org: changeInfo.A.O || 0,
      });
      desc.Highlight.push({
        Id: changeInfo.B.K || '',
        Name: changeInfo.B.N || '',
        Org: changeInfo.B.O || 0,
      });
      extend = `：从“${changeInfo.A.N}”变更为“${changeInfo.B.N}”`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 40:
      desc.Title = '工商变更';
      desc.Subtitle = '企业地址变更';
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更前',
        v: useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.C) || changeInfo.A || '-' : changeInfo.A || '-',
      });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更后',
        v: useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.D) || changeInfo.B || '-' : changeInfo.B || '-',
      });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = `：从“${changeInfo.A || '-'}”变更为“${changeInfo.B || '-'}”`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 139:
      desc.Title = '工商变更';
      desc.Subtitle = '企业经营地址变更';
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更前',
        v: changeInfo.A || '-',
      });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更后',
        v: changeInfo.B || '-',
      });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = `：从“${changeInfo.A || '-'}”变更为“${changeInfo.B || '-'}”`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 41:
      desc.Title = '工商变更';
      desc.Subtitle = '经营范围变更';
      riskHelper.pushContentKV({ contents: tMsg, k: '', v: `从“${beforeContent}”变更为“${afterContent}”`, line: 3 });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      } else {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从“${beforeContent}”变更为“${afterContent}”`;
        desc.BigEventDesc = CommonHelper.replaceAll(
          CommonHelper.replaceAll(CommonHelper.replaceAll(desc.BigEventDesc, labelStyleStartOneLine, ''), labelStyleEnd, ''),
          redStyle,
          '',
        );
      }
      break;
    case 42:
      desc.Subtitle = '企业类型变更';
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${redStyle}${changeInfo.A}${labelStyleEnd}”变更为“${redStyle}${changeInfo.B}${labelStyleEnd}”`,
        },
      ];
      desc.Content = `从“${redStyle}${changeInfo.A}${labelStyleEnd}”变更为“${redStyle}${changeInfo.B}${labelStyleEnd}”`;
      extend = `：从“${changeInfo.A}”变更为“${changeInfo.B}”`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 43:
      desc.Subtitle = '营业期限变更';
      desc.ContentArray = [
        {
          Key: '',
          Value: `从“${moment.unix(changeInfo.A).format('YYYY-MM-DD')}”变更为“${moment.unix(changeInfo.B).format('YYYY-MM-DD')}”`,
        },
      ];
      desc.Content = `${labelStyleStartOneLine}从“${moment.unix(changeInfo.A).format('YYYY-MM-DD')}”变更为“${moment
        .unix(changeInfo.B)
        .format('YYYY-MM-DD')}”${labelStyleEnd}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：从“${moment.unix(changeInfo.A).format('YYYY-MM-DD')}”变更为“${moment
        .unix(changeInfo.B)
        .format('YYYY-MM-DD')}”`;
      break;
    case 44:
      desc.Subtitle = '股东股份变更';
      if (Number(changeInfo.IsBP)) {
        desc.Subtitle += '、大股东变更';
      }
      extend1 = riskHelper.getContent44(changeInfo, useH5Style, item);
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : last(extend1.bigEventDesc)}`;
      desc.Highlight = extend1.highlight;
      desc.ContentArray = extend1.contentArray;
      desc.Content = prefixLineBreak + extend1.tMsg.join(lineBreak);
      desc.ChangeInfo = changeInfo;
      break;
    case 46:
      desc.Subtitle = '主要成员变更';
      processTMsg(changeInfo.D, tMsg, '职务变更', desc);
      processTMsg(changeInfo.E, tMsg, '退出', desc);
      processTMsg(changeInfo.F, tMsg, '新增', desc);
      // if (changeInfo.D && changeInfo.D.length) {
      //   processTMsg(changeInfo.D, tMsg, '职务变更', desc);
      //   tNames = [];
      //   tSufffix = '';
      //   const nameCount = Math.min(changeInfo.D.length, 3);
      //   if (changeInfo.D.length > 3) {
      //     tSufffix = `等${changeInfo.D.length}个`;
      //     // changeInfo.D = changeInfo.D.slice(0, 3);
      //   }
      //   for (let i = 0; i < nameCount; i++) {
      //     const item = changeInfo.D[i];
      //     tNames.push(item.A);
      //     desc.Highlight.push({
      //       Id: item.K || '',
      //       Name: item.A || '',
      //       Org: item.O || 0,
      //     });
      //   }
      //   // changeInfo.D.forEach((item) => {
      //   //   tNames.push(item.A);
      //   //   desc.Highlight.push({
      //   //     Id: item.K || '',
      //   //     Name: item.A || '',
      //   //     Org: item.O || 0,
      //   //   });
      //   // });
      //   riskHelper.pushContentKV({ contents: tMsg, k: '职务变更', v: tNames.join('、') + tSufffix });
      // }
      // if (changeInfo.E && changeInfo.E.length) {
      //   tNames = [];
      //   tSufffix = '';
      //   if (changeInfo.E.length > 3) {
      //     tSufffix = `等${changeInfo.E.length}个`;
      //     changeInfo.E = changeInfo.E.slice(0, 3);
      //   }
      //   changeInfo.E.forEach((item) => {
      //     tNames.push(item.A);
      //     desc.Highlight.push({
      //       Id: item.K || '',
      //       Name: item.A || '',
      //       Org: item.O || 0,
      //     });
      //   });
      //   riskHelper.pushContentKV({ contents: tMsg, k: '退出', v: tNames.join('、') + tSufffix });
      // }
      // if (changeInfo.F && changeInfo.F.length) {
      //   tNames = [];
      //   tSufffix = '';
      //   if (changeInfo.F.length > 3) {
      //     tSufffix = `等${changeInfo.F.length}个`;
      //     changeInfo.F = changeInfo.F.slice(0, 3);
      //   }
      //   changeInfo.F.forEach((item) => {
      //     tNames.push(item.A);
      //     desc.Highlight.push({
      //       Id: item.K || '',
      //       Name: item.A || '',
      //       Org: item.O || 0,
      //     });
      //   });
      //   riskHelper.pushContentKV({ contents: tMsg, k: '新增', v: tNames.join('、') + tSufffix });
      // }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : tMsg.map((t) => `${t.Key}：${t.Value}`).join('，')}`;
      desc.ChangeInfo = changeInfo;
      break;
    case 48:
    case 223:
      desc.Subtitle = '受到违规处理';
      riskHelper.pushContentKV({ contents: tMsg, k: '处罚对象', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '违规类型', v: changeInfo.B || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: changeInfo.dtChange });
      if (changeInfo?.E?.length) {
        changeInfo.E.forEach((item) => {
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
      } else {
        desc.Highlight.push({
          Id: changeInfo.K || '',
          Name: changeInfo.A || '',
          Org: changeInfo.O || 0,
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，处罚对象：${changeInfo.A}`;
      break;
    case 49:
    case 220:
      // 立案信息
      desc.Subtitle = '新增立案';
      contentInfo = riskHelper.getContent49And220(changeInfo, useH5Style, useEncryName, item);
      desc.ContentArray = contentInfo.tMsg;
      desc.Content = riskHelper.getContent(contentInfo.tMsg, useH5Style);
      desc.Highlight = contentInfo.highlight;
      desc.BigEventDesc = `${changeInfo.A ? '立案日期：' + moment.unix(changeInfo.A).format('YYYY-MM-DD') : dtCreate} ${desc.Subtitle}，案由：${
        changeInfo.D || '-'
      }`;
      break;
    case 50:
    case 214:
      // 1: 上市、2: 新三板、3: 普通公司
      // 股权质押需要区分公司的类型, 默认为1, 现在只有上市的数据
      desc.CompanyType = 1;
      if (Number(item.Category) === 214) {
        desc.CompanyType = 3;
      }
      desc.Subtitle = '新增股权质押';
      hasAmount = changeInfo.B && Number(changeInfo.B) !== 0;
      hasPercent = changeInfo.C && Number(changeInfo.C) !== 0;
      if (changeInfo.Type === 1 || changeInfo.Type === 2) {
        // 风险只展示1:股东（企业），2:股权被质押的企业
        desc.Subtitle = changeInfo.Type === 1 ? '质押了在外持有的股权' : '股东质押了该企业股权';
        if (changeInfo.HolderArray?.length) {
          tNames = [];
          tSufffix = '';
          if (changeInfo.HolderArray?.length > 3) {
            tSufffix = `等${changeInfo.HolderArray.length}家`;
            changeInfo.HolderArray = changeInfo.HolderArray.slice(0, 3);
          }
          changeInfo.HolderArray.forEach((item) => {
            tNames.push(item.Name);
            desc.Highlight.push({
              Id: item.KeyNo || '',
              Name: item.Name || '',
              Org: item.Org || 0,
            });
          });
          riskHelper.pushContentKV({ contents: tMsg, k: '质押人', v: tNames.join('、') + tSufffix });
        } else if (changeInfo.Type === 1 && changeInfo.Holder && changeInfo.Holder.A) {
          // 兼容老数据
          riskHelper.pushContentKV({ contents: tMsg, k: '质押人', v: changeInfo.Holder.A });
          desc.Highlight.push({
            Id: changeInfo.Holder.K || '',
            Name: changeInfo.Holder.A || '',
            Org: changeInfo.Holder.O || 0,
          });
        } else if (changeInfo.Type === 2 && changeInfo.A) {
          // 兼容老数据
          riskHelper.pushContentKV({ contents: tMsg, k: '质押人', v: changeInfo.A });
        }
        if (changeInfo.Company && changeInfo.Company.A) {
          riskHelper.pushContentKV({ contents: tMsg, k: '质押人参股企业', v: changeInfo.Company.A });
          desc.Highlight.push({
            Id: changeInfo.Company.K || '',
            Name: changeInfo.Company.A || '',
            Org: changeInfo.Company.O || 0,
          });
        } else if (changeInfo.A) {
          riskHelper.pushContentKV({ contents: tMsg, k: '质押人参股企业', v: changeInfo.A });
        }
        if (hasAmount) {
          riskHelper.pushContentKV({ contents: tMsg, k: '质押股份数量', v: changeInfo.B });
        }
        tBigEventList.push(`质押股份数量：${changeInfo.B || '-'}股`);
        if (hasPercent) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: changeInfo.Type === 1 ? '占所持股份比例' : '占总股本的比例',
            v: changeInfo.C,
          });
        }
        riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: dtChange });
        if (changeInfo.JgArray?.length) {
          tNames = [];
          tSufffix = '';
          if (changeInfo.JgArray?.length > 3) {
            tSufffix = `等${changeInfo.JgArray.length}家`;
            changeInfo.JgArray = changeInfo.JgArray.slice(0, 3);
          }
          changeInfo.JgArray.forEach((item) => {
            tNames.push(item.Name);
            desc.Highlight.push({
              Id: item.KeyNo || '',
              Name: item.Name || '',
              Org: item.Org || 0,
            });
          });
          riskHelper.pushContentKV({ contents: tMsg, k: '质押机构', v: tNames.join('、') + tSufffix });
        } else if (changeInfo.E) {
          // 兼容老数据
          riskHelper.pushContentKV({ contents: tMsg, k: '质押机构', v: changeInfo.E });
          desc.Highlight.push({
            Id: changeInfo.JgK || '',
            Name: changeInfo.E || '',
            Org: changeInfo.JgO || 0,
          });
        }
        desc.Highlight.push({
          Id: changeInfo.K || '',
          Name: changeInfo.A || '',
          Org: changeInfo.O || 0,
        });
        desc.ContentArray = tMsg;
        desc.Content = riskHelper.getContent(tMsg, useH5Style);
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 51:
      desc.Subtitle = '新增公示催告';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '票号', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.B && changeInfo.B.Name) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请人', v: changeInfo.B.Name });
        desc.Highlight.push({
          Id: changeInfo.B.KeyNo || '',
          Name: changeInfo.B.Name || '',
          Org: changeInfo.B.Org || 0,
        });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '票面金额', v: changeInfo.C });
      }
      if (dtChange) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: dtChange });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，票面金额：${changeInfo.C || '-'}`;
      break;
    case 52:
      desc.Subtitle = '投资机构减少';
      desc.ContentArray = [
        {
          Key: '减少机构',
          Value: changeInfo.A,
        },
      ];
      desc.Content = `${labelStyleStart}减少机构：${labelStyleEnd}${changeInfo.A}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，减少机构：${changeInfo.A}`;
      break;
    case 53:
      desc.Subtitle = '新增担保信息';
      desc.SubtitleHighlight = '担保信息';
      if (changeInfo.Secured && typeof changeInfo.Secured === 'object') {
        afterNames.push(changeInfo.Secured.A);
        desc.Highlight.push({
          Id: changeInfo.Secured.K || '',
          Name: changeInfo.Secured.A || '',
          Org: changeInfo.Secured.O || 0,
        });
      }
      if (afterNames.length) {
        riskHelper.pushContentKV({ contents: tMsg, k: '担保方', v: afterNames.join('，') });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '被担保方', v: changeInfo.D || '-' });
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '担保金额（万元）',
          v: CommonHelper.handleNum(Number(changeInfo.A)),
        });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '币种', v: changeInfo.C });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: dtChange });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.D || '',
        Org: changeInfo.O || 0,
      });
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，担保金额（万元）：${changeInfo.A}`;
      break;
    case 243:
    case 101:
      desc.Subtitle = '新增担保信息';
      desc.SubtitleHighlight = '担保信息';
      if (changeInfo.T) {
        if (changeInfo.T === 1) {
          desc.Subtitle = '提供担保';
          desc.SubtitleHighlight = '提供担保';
        } else if (changeInfo.T === 2) {
          desc.Subtitle = '获得担保';
        }
      }
      if (changeInfo.D && typeof changeInfo.D === 'object') {
        tSufffix = '';
        if (changeInfo.D.length > 3) {
          tSufffix = `等${changeInfo.D.length}家`;
          changeInfo.D = changeInfo.D.slice(0, 3);
        }
        changeInfo.D.forEach((c) => {
          afterNames.push(c.Name);
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
        if (afterNames.length) {
          riskHelper.pushContentKV({ contents: tMsg, k: '担保方', v: afterNames.join('，') + tSufffix });
        }
      }
      if (changeInfo.E && typeof changeInfo.E === 'object') {
        tSufffix = '';
        if (changeInfo.E.length > 3) {
          tSufffix = `等${changeInfo.E.length}家`;
          changeInfo.E = changeInfo.E.slice(0, 3);
        }
        changeInfo.E.forEach((c) => {
          beforeNames.push(c.Name);
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
        if (beforeNames.length) {
          riskHelper.pushContentKV({ contents: tMsg, k: '被担保方', v: beforeNames.join('，') + tSufffix });
        }
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '担保金额（万元）',
          v: CommonHelper.handleNum(Number(changeInfo.A)),
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '币种', v: changeInfo.B });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: dtChange });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，担保金额（万元）：${Number(changeInfo.A) ? CommonHelper.handleNum(Number(changeInfo.A)) : '-'}`;
      break;
    case 56:
    case 216:
      desc.Title = '司法诉讼';
      desc.Subtitle = '新增终本案件';
      desc.SubtitleHighlight = '终本案件';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A });
      }
      if (changeInfo.H) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: CommonHelper.handleNum(Number(changeInfo.H)),
          showEm: Number(changeInfo.H) > 2000000,
        });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.B });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '终本日期', v: changeInfo.D });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${changeInfo.D ? '终本日期：' + changeInfo.D : dtCreate} ${desc.Subtitle}，执行法院：${changeInfo.B}`;
      break;
    case 57:
      desc.Title = '司法风险';
      // 司法拍卖标签细分
      desc.Subtitle = '新增司法拍卖';
      desc.SubtitleHighlight = '司法拍卖';
      if (changeInfo.Q === 1) {
        desc.Subtitle = '新增破产拍卖';
        desc.SubtitleHighlight = '破产拍卖';
      }
      if (changeInfo.P && shared.Judicial_Auction_Map.get(changeInfo.P)) {
        desc.Subtitle = `${desc.Subtitle}-${shared.Judicial_Auction_Map.get(changeInfo.P)}`;
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '起拍价（元）', v: CommonHelper.handleNum(Number(changeInfo.C)) });
      }
      if (changeInfo.H) {
        riskHelper.pushContentKV({ contents: tMsg, k: '拍卖时间', v: changeInfo.H });
      } else {
        if (changeInfo.D) {
          riskHelper.pushContentKV({ contents: tMsg, k: '拍卖时间', v: changeInfo.D });
        }
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，${changeInfo.A}`;
      break;
    case 58:
      desc.Title = '经营风险';
      desc.Subtitle = '新增破产重整';
      desc.SubtitleHighlight = '破产重整';
      // 破产重整标签细化
      if (changeInfo.G && shared.Bankruptcy_Type_Map.get(changeInfo.G)) {
        desc.Subtitle = `新增${shared.Bankruptcy_Type_Map.get(changeInfo.G)}`;
        desc.SubtitleHighlight = shared.Bankruptcy_Type_Map.get(changeInfo.G);
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A });
      }
      beforeNames.push(...(find(shared.BANKRUPTCY_MAPPING, (it) => includes(it.Key, changeInfo.E))?.Value ?? ['申请人', '被申请人']));
      if (changeInfo.C && changeInfo.C.length) {
        tSufffix = '';
        if (changeInfo.C.length > 3) {
          tSufffix = `等${changeInfo.C.length}家`;
          changeInfo.C = changeInfo.C.slice(0, 3);
        }
        changeInfo.C.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: beforeNames[1], v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.B && changeInfo.B.length) {
        tSufffix = '';
        if (changeInfo.B.length > 3) {
          tSufffix = `等${changeInfo.B.length}家`;
          changeInfo.B = changeInfo.B.slice(0, 3);
        }
        changeInfo.B.forEach((item) => {
          item.Name = useEncryName ? item.ShowName || item.Name : item.Name;
          tNames2.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: beforeNames[0], v: tNames2.join('、') + tSufffix });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '公开日期', v: changeInfo.D });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = tNames2?.length ? `，申请人：${tNames2.join('、')}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${extend}`;
      break;
    case 59:
    case 224:
      desc.Title = '经营风险';
      desc.Subtitle = '有资产被询价评估';
      desc.SubtitleHighlight = '询价评估';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A });
      }
      if (changeInfo.F?.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.F.length > 3) {
          changeInfo.F = changeInfo.F.slice(0, 3);
          tSufffix = '等';
        }
        changeInfo.F.forEach((item) => {
          tNames.push(item.Target);
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '标的物', v: tNames.join('、') + tSufffix });
        tBigEventList.push(`标的物：${tNames.join('、')}`);
      }
      if (changeInfo.G?.length) {
        tNames = changeInfo.G.sort((a, b) => parseFloat(a.EvaluationPrice) - parseFloat(b.EvaluationPrice));
        if (tNames.length > 1) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '询价结果（元）',
            v: `${CommonHelper.handleNum(Number(tNames[0].EvaluationPrice))} ~ ${CommonHelper.handleNum(Number(tNames[tNames.length - 1].EvaluationPrice))}`,
          });
        } else {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '询价结果（元）',
            v: tNames.length ? CommonHelper.handleNum(Number(tNames[0].EvaluationPrice)) : '-',
          });
        }
      }
      if (changeInfo.H?.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.H.length > 3) {
          tSufffix = `等${changeInfo.H.length}个`;
          changeInfo.H = changeInfo.H.slice(0, 3);
        }
        changeInfo.H.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '标的物所有人', v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.I?.length) {
        tNames = [];
        tSufffix = '';
        if (changeInfo.I.length > 3) {
          tSufffix = `等${changeInfo.I.length}个`;
          changeInfo.I = changeInfo.I.slice(0, 3);
        }
        changeInfo.I.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '案件当事人', v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.J) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '发布日期',
          v: moment.unix(changeInfo.J).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，${tBigEventList.join(' ')}`;
      break;
    case 76:
    case 228:
      desc.Title = '司法风险';
      desc.Subtitle = '有资产选定询价评估机构';
      desc.SubtitleHighlight = '询价评估';
      riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
      riskHelper.pushContentKV({ contents: tMsg, k: '标的物', v: changeInfo.B || '-' });
      if (changeInfo.C?.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.C.length > 3) {
          tSufffix = `等${changeInfo.C.length}个`;
          changeInfo.C = changeInfo.C.slice(0, 3);
        }
        changeInfo.C.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '标的物所有人', v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '摇号日期',
          v: moment.unix(changeInfo.D).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 60:
      desc.Title = '工商变更';
      desc.Subtitle = '企业名称变更';
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更前',
        v: useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.C) || changeInfo.A || '-' : changeInfo.A || '-',
      });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '变更后',
        v: useH5Style ? riskHelper.changeEmToRedStyle(changeInfo.D) || changeInfo.B || '-' : changeInfo.B || '-',
      });
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = `：从${changeInfo.A || '-'}变更为${changeInfo.B || '-'}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 61:
      desc.Title = '经营风险';
      desc.Subtitle = '新增注销备案';
      desc.SubtitleHighlight = '注销备案';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '登记机关', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '清算组备案日期', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '清算组成立日期', v: changeInfo.C });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '注销原因', v: changeInfo.D });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，登记机关：${changeInfo.A || '-'}`;
      break;
    case 63:
      desc.Title = '经营状况';
      desc.Subtitle = '新增双随机抽查';
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '任务编号', v: changeInfo.C, isLink: true });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '任务名称', v: changeInfo.D });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抽查机关', v: changeInfo.F });
      }
      if (changeInfo.G) {
        riskHelper.pushContentKV({ contents: tMsg, k: '完成日期', v: changeInfo.G });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：${changeInfo.D || '-'}`;
      break;
    case 95:
      desc.Title = '经营状况';
      desc.Subtitle = '新增行政许可';
      riskHelper.pushContentKV({ contents: tMsg, k: '许可名称', v: changeInfo.B || '-' });
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '许可机关', v: changeInfo.C });
      }
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '许可内容',
        v: (startsWith(changeInfo.D, '许可项目') ? changeInfo.D.substring(5, changeInfo.D.length) : changeInfo.D) || '-',
        line: 3,
      });
      if (changeInfo.F || changeInfo.G) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '有效期',
          v: `${(changeInfo.F && moment(changeInfo.F, 'YYYYMMDD').format('YYYY-MM-DD')) || '-'}至${
            (changeInfo.G && moment(changeInfo.G, 'YYYYMMDD').format('YYYY-MM-DD')) || '-'
          }`,
        });
      }
      // 将文案拼接等规则放入公共方法调用
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = (startsWith(changeInfo.D, '许可项目：') ? changeInfo.D.substring(5, changeInfo.D.length) : changeInfo.D) || changeInfo.B || '-';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：${extend}`;
      break;
    case 65:
    case 113:
      desc.Title = '经营状况';
      desc.Subtitle = '新增企业公告';
      if (changeInfo.Title) {
        riskHelper.pushContentKV({ contents: tMsg, k: '', v: changeInfo.Title, isLink: true });
      }
      if (
        split(changeInfo.TS, ',')
          .map((t) => shared.ANNOUNCEMENT_REPORT_TYPE.get(Number(t)))
          .filter((t) => t).length
      ) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公告类型',
          v: split(changeInfo.TS, ',')
            .map((t) => shared.ANNOUNCEMENT_REPORT_TYPE.get(Number(t)))
            .filter((t) => t)
            .join('、'),
        });
      }
      if (changeInfo.Publishtime) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '公告日期',
          v: moment.unix(changeInfo.Publishtime).format('YYYY-MM-DD'),
        });
      }
      // 特殊处理，如果url是制定结尾，清除
      if (changeInfo.url && some(['.link', '.htm', '.html', '.rar', '.zip', '.txt'], (ext) => endsWith(changeInfo.url, ext))) {
        // 清除
        changeInfo.url = '';
        item.ChangeExtend = JSON.stringify(changeInfo);
      }
      desc.URL = changeInfo.url;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${changeInfo.Title}`;
      break;
    case 62:
    case 66:
    case 67:
      desc.Subtitle = '新闻';
      desc.ContentArray = [
        {
          Key: '',
          Value: changeInfo.links ? changeInfo.links[0].title : '',
        },
      ];
      desc.Content = `${labelStyleStartOneLine}${changeInfo.links ? changeInfo.links[0].title : ''}${labelStyleEnd}`;
      desc.IsHotNews = false;
      if (some(changeInfo.eventArray, 'isSelf')) {
        desc.IsHotNews = true;
      }
      desc.BigEventDesc = `${dtCreate} ${changeInfo.links ? changeInfo.links[0].title : ''}`;
      break;
    case 72:
      // 公司： 39-企业法人，44-非大股东变动，46-主要成员；72-成员变更  三合一
      desc.Subtitle = riskHelper.getSubtitle72(changeInfo, item.Category);
      contentInfo = riskHelper.getContent72(changeInfo, useH5Style, item);
      desc.ContentArray = contentInfo.contentArray;
      desc.Content = prefixLineBreak;
      forEach(contentInfo.tMsg, (v, i) => {
        if (v === 'br') {
          desc.Content += '<div style="height:10px"></div>';
        } else {
          if (i !== contentInfo.tMsg.length - 1) {
            desc.Content += v + lineBreak;
          } else {
            desc.Content += v;
          }
        }
      });
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : contentInfo.bigEventDesc.join('，')}`;
      desc.Highlight = contentInfo.highlight;
      break;
    case 75:
      desc.Title = '经营风险';
      desc.Subtitle = '有资产被拍卖';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '起拍价（元）', v: changeInfo.B });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '竞拍时间', v: changeInfo.D });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：${changeInfo.A || '-'}`;
      break;
    case 77:
      desc.Title = '监管风险';
      desc.Subtitle = '被列入惩戒名单';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: item.Extend1 === '3' ? '类型' : '标题', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '列入机关', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '列入日期', v: changeInfo.C });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.A ? `：${changeInfo.A}` : '';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 78:
      desc.Title = '监管风险';
      desc.Subtitle = '新增产品召回';
      desc.SubtitleHighlight = '产品召回';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '召回产品', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.D && changeInfo.D.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.D?.length > 3) {
          tSufffix = `等${changeInfo.D.length}家`;
          changeInfo.D = changeInfo.D.slice(0, 3);
        }
        changeInfo.D.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '召回企业', v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '发布日期',
          v: moment.unix(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc.URL = changeInfo.B;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，召回产品：${changeInfo.A || '-'}`;
      break;
    case 79:
      desc.Title = '监管风险';
      desc.Subtitle = '食品抽检不合格';
      desc.SubtitleHighlight = '食品抽检不合格';
      if (changeInfo.H === 1) {
        desc.Subtitle = '食品抽检合格';
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '食品名称', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '抽检结果', v: changeInfo.H === 1 ? '合格' : '不合格' });
      }
      if (changeInfo.G && changeInfo.G.length > 0) {
        tSufffix = '';
        tNames = [];
        if (changeInfo.G.length > 3) {
          tSufffix = `等${changeInfo.G.length}家`;
          changeInfo.G = changeInfo.G.slice(0, 3);
        }
        changeInfo.G.forEach((item) => {
          tNames.push(item.Name);
          desc.Highlight.push({
            Id: item.KeyNo || '',
            Name: item.Name || '',
            Org: item.Org || 0,
          });
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '标称生产企业', v: tNames.join('、') + tSufffix });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '生产日期', v: changeInfo.B });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，食品名称：${changeInfo.A || '-'}`;
      break;
    case 98:
      desc.Title = '监管风险';
      desc.Subtitle = '相关产品被禁止入境';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '产品名称', v: changeInfo.A, isLink: true });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '产品类型', v: PRODUCT_TYPE_MAP.get(changeInfo.B) });
      }
      if (changeInfo.C && changeInfo.C !== '/') {
        riskHelper.pushContentKV({ contents: tMsg, k: '生产企业信息/品牌', v: changeInfo.C });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '原因', v: changeInfo.D });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '报送时间',
          v: moment(changeInfo.E, 'YYYYMM').format('YYYY年MM月'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，产品名称：${changeInfo.A || '-'}`;
      break;
    case 222:
      // 人员：201-企业法人，202-主要成员，203-对外投资；222-成员变更  三合一
      desc.Subtitle = riskHelper.getSubtitle72(changeInfo, item.Category);
      contentInfo = riskHelper.getContent222(changeInfo, item, useH5Style);
      desc.ContentArray = contentInfo.contentArray;
      desc.Content = prefixLineBreak;
      forEach(contentInfo.tMsg, (b, index) => {
        if (index === contentInfo.tMsg.length - 1) {
          desc.Content += b;
        } else {
          b === 'br' ? (desc.Content += '<div style="height:10px"></div>') : (desc.Content += b + lineBreak);
        }
      });
      desc.RelateChange = contentInfo.relateChange;
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : contentInfo.bigEventDesc.join('，')}`;
      desc.Highlight = contentInfo.highlight;
      break;
    // 人员
    case 201:
      desc.Subtitle = `${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}变更`;
      if (item.ChangeStatus === 1) {
        // 新增
        desc.ContentArray = [
          {
            Key: '',
            Value: `担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}${labelStyleEnd}`;
        desc.HotDynamicDesc = `近期成为一家${Number(changeInfo.C) === 4 ? '个体工商户' : '公司'}的<em>${
          shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'
        }</em>`;
        tBigEventList.push(`担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`);
      } else if (item.ChangeStatus === 2) {
        // 退出
        desc.ContentArray = [
          {
            Key: '',
            Value: `不再担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}不再担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}${labelStyleEnd}`;
        desc.HotDynamicDesc = `近期不再是一家${Number(changeInfo.C) === 4 ? '个体工商户' : '公司'}的<em>${
          shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'
        }</em>`;
        tBigEventList.push(`不再担任${changeInfo.A}的${shared.LAR_TYPE_MAP.get(Number(changeInfo.C)) || '法定代表人'}`);
      }
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      extend = `：${tBigEventList.join(' ')}`;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend}`;
      break;
    case 202:
      if (item.ChangeStatus === 1) {
        // 新增
        desc.Subtitle = '新增主要成员';
        if (changeInfo.A) {
          riskHelper.pushContentKV({ contents: tMsg, k: '新增企业', v: changeInfo.A });
        }
        desc.HotDynamicDesc = '近期在一家公司任职';
        if (afterContent) {
          riskHelper.pushContentKV({ contents: tMsg, k: '担任职务', v: afterContent });
          desc.HotDynamicDesc = `近期在一家公司担任<em>${afterContent}</em>`;
        }
      } else if (item.ChangeStatus === 2) {
        // 退出
        desc.Subtitle = '退出主要成员';
        if (changeInfo.A) {
          riskHelper.pushContentKV({ contents: tMsg, k: '退出企业', v: changeInfo.A });
        }
        desc.HotDynamicDesc = '近期在一家公司卸职';
        if (beforeContent) {
          riskHelper.pushContentKV({ contents: tMsg, k: '退出前职务', v: beforeContent });
          desc.HotDynamicDesc = `近期在一家公司卸任<em>${beforeContent}</em>`;
        }
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = tMsg.map((t) => `${t.Key}：${t.Value}`).join('，');
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : extend}`;
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 68:
    case 204:
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '持股比例上升';
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '',
          v: `${labelStyleStartOneLine}在${changeInfo.A}的股份比例${changeInfo.B ? '从' + changeInfo.B : ''}增加到${changeInfo.C}${labelStyleEnd}`,
        });
        const holdValue = changeInfo.C ? Number(changeInfo.C.slice(0, -1)) : 0;
        if (holdValue <= 50 && changeInfo?.IsBP === '1') {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '',
            v: `${labelStyleStart}成为该企业的大股东${labelStyleEnd}`,
          });
          desc.RelateChange = '成为该企业的大股东';
        }
        desc.HotDynamicDesc = `近期在一家公司的股份比例上升至<em>${changeInfo.C}</em>`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：在${changeInfo.A}的股份比例${changeInfo.B ? '从' + changeInfo.B : ''}增加到${changeInfo.C}`;
      } else {
        desc.Subtitle = '持股比例下降';
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '',
          v: `${labelStyleStartOneLine}在${changeInfo.A}的股份比例${changeInfo.B ? '从' + changeInfo.B : ''}下降为${changeInfo.C}${labelStyleEnd}`,
        });
        const beforeHoldValue = changeInfo.B ? Number(changeInfo.B.slice(0, -1)) : 0;
        const afterHoldValue = changeInfo.C ? Number(changeInfo.C.slice(0, -1)) : 0;
        if (!(beforeHoldValue > 50 && afterHoldValue < 50) && changeInfo?.IsBP === '2') {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '',
            v: `${labelStyleStart}不再是该企业的大股东${labelStyleEnd}`,
          });
          desc.RelateChange = '不再是该企业的大股东';
        }
        desc.HotDynamicDesc = `近期在一家公司的股份比例下降至<em>${changeInfo.C}</em>`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：在${changeInfo.A}的股份比例${changeInfo.B ? '从' + changeInfo.B : ''}下降为${changeInfo.C}`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.Highlight.push({
        Id: changeInfo.K || '',
        Name: changeInfo.A || '',
        Org: changeInfo.O || 0,
      });
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 89:
      desc.Title = '经营风险';
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '成为大股东';
        desc.ContentArray = [
          {
            Key: '',
            Value: `${labelStyleStartOneLine}成为${changeInfo.Name}的大股东${labelStyleEnd}`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的大股东${labelStyleEnd}`;
      } else if (item.ChangeStatus === 2) {
        desc.Subtitle = '不再是大股东';
        desc.ContentArray = [
          {
            Key: '',
            Value: `${labelStyleStartOneLine}不再是${changeInfo.Name}的大股东${labelStyleEnd}`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的大股东${labelStyleEnd}`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo,
        Name: changeInfo.Name,
        Org: changeInfo.Org,
      });
      if (item.ChangeStatus === 1) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的大股东`;
      } else {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的大股东`;
      }
      break;
    case 206:
      desc.Subtitle = '被列入被执行人';
      desc.SubtitleHighlight = '被执行人';
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: CommonHelper.handleNum(Number(changeInfo.B)),
          showEm: changeInfo.B > 2000000,
        });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.D || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，执行标的（元）：${changeInfo.B || '-'}`;
      } else {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '执行法院', v: changeInfo.D || '-' });
        riskHelper.pushContentKV({ contents: tMsg, k: '立案日期', v: dtChange || '-' });
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，案号：${changeInfo.A || '-'}`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.HotDynamicDesc = '近期被列为<em>被执行人</em>';
      break;
    case 55:
    case 208:
      desc.Subtitle = '被限制高消费';
      desc.SubtitleHighlight = '限制高消费';
      contentInfo = riskHelper.getContent55And208(changeInfo);
      desc.ContentArray = contentInfo.tMsg;
      desc.Content = riskHelper.getContent(contentInfo.tMsg, useH5Style);
      desc.Highlight = contentInfo.highlight;
      desc.HotDynamicDesc = '近期被列为<em>限制高消费</em>';
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 91:
    case 231:
      desc.Subtitle = '被限制出境';
      desc.SubtitleHighlight = '限制出境';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.A });
      }
      if (changeInfo.B && changeInfo.B.length) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '限制出境对象',
          v: changeInfo.B.map((b) => b.Name).join('，'),
        });
        changeInfo.B.forEach((b) => {
          desc.Highlight.push({
            Id: b.KeyNo || '',
            Name: b.Name || '',
            Org: b.Org || 0,
          });
        });
      }
      if (changeInfo.C && changeInfo.C.length) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '被执行人',
          v: changeInfo.C.map((c) => c.Name).join('，'),
        });
        changeInfo.C.forEach((c) => {
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '执行标的（元）',
          v: CommonHelper.handleNum(Number(changeInfo.E)),
        });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: changeInfo.F });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 90:
    case 232:
      desc.Subtitle = '新增诉前调解';
      if (changeInfo.E) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案号', v: changeInfo.E || '-', isLink: true });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '案由', v: changeInfo.D || '-' });
      }
      if (changeInfo.I?.length) {
        CommonHelper.processCaseRole({
          field: changeInfo.I,
          tMsg,
          useEncryName,
          useH5Style,
          item,
          highlight: desc.Highlight,
        });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '立案日期',
          v: moment.unix(changeInfo.A).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}，案由：${changeInfo.D || '-'}`;
      break;
    case 86:
      desc.Subtitle = '新增知识产权出质';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '名称', v: changeInfo.A });
      }
      if (changeInfo.I) {
        riskHelper.pushContentKV({ contents: tMsg, k: '出质登记号', v: changeInfo.I });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '出质知产类型', v: changeInfo.F === 1 ? '专利' : '商标' });
      }
      if (changeInfo.B && changeInfo.B.length) {
        changeInfo.B = JSON.parse(changeInfo.B);
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '出质人',
          v: changeInfo.B.map((b) => b.Name).join('，') || '-',
        });
        changeInfo.B.forEach((b) => {
          desc.Highlight.push({
            Id: b.KeyNo || '',
            Name: b.Name || '',
            Org: b.Org || 0,
          });
        });
      }
      if (changeInfo.C && changeInfo.C.length) {
        changeInfo.C = JSON.parse(changeInfo.C);
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '质权人',
          v: changeInfo.C.map((c) => c.Name).join('，') || '-',
        });
        changeInfo.C.forEach((c) => {
          desc.Highlight.push({
            Id: c.KeyNo || '',
            Name: c.Name || '',
            Org: c.Org || 0,
          });
        });
      }
      riskHelper.pushContentKV({ contents: tMsg, k: '公告日期', v: dtChange });
      if (changeInfo.D) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '出质期限',
          v: `${moment.unix(changeInfo.D).format('YYYY-MM-DD')} 至 ${(changeInfo.E && moment.unix(changeInfo.E).format('YYYY-MM-DD')) || '-'}`,
        });
      }
      if (changeInfo.G) {
        desc['IPRPledgeId'] = changeInfo.G;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      break;
    case 209:
      desc.Subtitle = '实际控制人变更';
      if (item.ChangeStatus === 1) {
        desc.ContentArray = [
          {
            Key: '',
            Value: `成为${changeInfo.Name}的实际控制人`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的实际控制人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期成为一家公司的<em>实际控制人</em>';
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的实际控制人`;
      } else if (item.ChangeStatus === 2) {
        desc.ContentArray = [
          {
            Key: '',
            Value: `不再是${changeInfo.Name}的实际控制人`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的实际控制人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期不再是一家公司的<em>实际控制人</em>';
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的实际控制人`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.Org || 0,
      });
      break;
    case 240:
      desc.Subtitle = '实际控制人变更';
      forEach(changeInfo, (item) => {
        extend1 = JSON.parse(item.ChangeExtend);
        if (item.ChangeStatus === 1) {
          riskHelper.pushContentKV({ contents: tMsg, k: '', v: `成为${extend1.Name}的实际控制人` });
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期成为一家公司的<em>实际控制人</em>';
            desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：成为${extend1.Name}的实际控制人`;
          }
        } else if (item.ChangeStatus === 2) {
          riskHelper.pushContentKV({ contents: tMsg, k: '', v: `不再是${extend1.Name}的实际控制人` });
          if (changeInfo.length === 1) {
            desc.HotDynamicDesc = '近期不再是一家公司的<em>实际控制人</em>';
            desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：不再是${extend1.Name}的实际控制人`;
          }
        }
        tNames.push(extend1.Name);
        desc.Highlight.push({
          Id: extend1.KeyNo || '',
          Name: extend1.Name || '',
          Org: extend1.Org || 0,
        });
        t = extend1.Name;
      });
      if (changeInfo.length > 1) {
        desc.HotDynamicDesc = '近期发生<em>实际控制人</em>变更';
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：涉及${t}等实际控制人变更`;
      }
      if (tNames.length > 3) {
        tSufffix = `等${tNames.length}个`;
        desc.ContentArray = [
          {
            Key: '',
            Value: `${changeInfo[0].ChangeStatus === 1 ? '成为' : '不再是'}${tNames.slice(0, 3).join('、')}${tSufffix}企业的实际控制人`,
          },
        ];
        desc.Content = prefixLineBreak + `${changeInfo[0].ChangeStatus === 1 ? '成为' : '不再是'}${tNames.slice(0, 3).join('、')}${tSufffix}企业的实际控制人`;
        desc.Highlight = desc.Highlight.slice(0, 3);
      } else {
        desc.ContentArray = tMsg;
        desc.Content = riskHelper.getContent(tMsg, useH5Style);
      }
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 210:
      desc.Subtitle = '受益所有人变更';
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '成为受益所有人';
        desc.ContentArray = [
          {
            Key: '',
            Value: `成为${changeInfo.Name}的受益人所有人`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的受益人所有人${labelStyleEnd}`;
        desc.HotDynamicDesc = '近期成为一家公司的<em>受益人所有人</em>';
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的受益人所有人`;
      } else if (item.ChangeStatus === 2) {
        desc.Subtitle = '退出受益所有人';
        riskHelper.pushContentKV({ contents: tMsg, k: '', v: `退出${changeInfo.Name}的受益人所有人` });
        if (changeInfo.PercentTotal) {
          riskHelper.pushContentKV({ contents: tMsg, k: '变更前受益股份', v: changeInfo.PercentTotal });
        }
        desc.ContentArray = tMsg;
        desc.Content = riskHelper.getContent(tMsg, useH5Style);
        desc.HotDynamicDesc = '近期退出一家公司的<em>受益所有人</em>';
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：退出${changeInfo.Name}的受益所有人`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.Org || 0,
      });
      break;
    case 241:
      desc.Subtitle = '受益所有人变更';
      extend = changeInfo[0]?.ChangeStatus;
      if (extend === 1) {
        desc.Subtitle = '成为受益所有人';
      } else if (extend === 2) {
        desc.Subtitle = '退出受益所有人';
      }
      // 名字拼接和高亮
      tSufffix = '';
      if (changeInfo.length > 3) {
        tSufffix = `等${changeInfo.length}家`;
        changeInfo = changeInfo.slice(0, 3);
      }
      forEach(changeInfo, (i) => {
        extend1 = JSON.parse(i.ChangeExtend);
        tNames.push(extend1.Name);
        desc.Highlight.push({
          Id: extend1.KeyNo || '',
          Name: extend1.Name || '',
          Org: extend1.Org || 0,
        });
      });
      riskHelper.pushContentKV({
        contents: tMsg,
        k: '',
        v: `${extend === 1 ? '成为' : '退出'}${tNames.join('、') + tSufffix}的受益所有人`,
      });
      // 如果只有一个公司，则显示变更前股份
      if (changeInfo.length === 1) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: `${extend === 1 ? '变更后' : '退出前'}受益股份`,
          v: JSON.parse(changeInfo[0]?.ChangeExtend)?.PercentTotal || '-',
        });
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：${extend === 1 ? '成为' : '退出'}${tNames.join('、') + tSufffix}的受益所有人`;
      desc.HotDynamicDesc = `近期${extend === 1 ? '成为' : '退出'}${tNames.join('、') + tSufffix}的<em>受益所有人</em>`;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (isHideDyContent) {
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}`;
      }
      break;
    case 215:
      if (item.ChangeStatus === 1) {
        desc.Subtitle = '成为大股东';
        desc.ContentArray = [
          {
            Key: '',
            Value: `成为${changeInfo.Name}的大股东`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}成为${changeInfo.Name}的大股东${labelStyleEnd}`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：成为${changeInfo.Name}的大股东`;
      } else if (item.ChangeStatus === 2) {
        desc.Subtitle = '不再是大股东';
        desc.ContentArray = [
          {
            Key: '',
            Value: `不再是${changeInfo.Name}的大股东`,
          },
        ];
        desc.Content = `${labelStyleStartOneLine}不再是${changeInfo.Name}的大股东${labelStyleEnd}`;
        desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}：不再是${changeInfo.Name}的大股东`;
      }
      desc.Highlight.push({
        Id: changeInfo.KeyNo || '',
        Name: changeInfo.Name || '',
        Org: changeInfo.O || 0,
      });
      break;
    case 108:
      desc.Subtitle = '新增票据违约';
      if (changeInfo.compName) {
        riskHelper.pushContentKV({ contents: tMsg, k: '承兑人', v: changeInfo.compName });
      }
      if (changeInfo.overdueBalance) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '逾期余额（万元）',
          v: CommonHelper.div(changeInfo.overdueBalance, 10000),
        });
      }
      if (changeInfo.endDate) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '截止日期',
          v: moment.unix(changeInfo.endDate).format('YYYY-MM-DD'),
        });
      }
      desc.Highlight.push({
        Id: changeInfo?.map?.KeyNo || '',
        Name: changeInfo?.map?.Name || '',
        Org: changeInfo?.map?.Org || 0,
      });
      if (changeInfo.overdueBalance) {
        desc.BigEventDesc = `${dtCreate} 该企业存在${CommonHelper.div(changeInfo.overdueBalance, 10000)}万元的票据违约`;
      } else {
        desc.BigEventDesc = `${dtCreate} 该企业存在票据违约`;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 109:
    case 239:
      desc.Subtitle = '新增公安通告';
      desc.SubtitleHighlight = '公安通告';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '涉案企业', v: changeInfo.A });
      }
      if (changeInfo.H) {
        riskHelper.pushContentKV({ contents: tMsg, k: '涉嫌案由', v: changeInfo.H.replaceAll('|', '、') });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '发布单位', v: changeInfo.D });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '发布日期',
          v: moment(changeInfo.C, 'YYYYMMDD').format('YYYY-MM-DD'),
        });
      }
      desc.Highlight.push({
        Id: changeInfo?.I?.KeyNo || '',
        Name: changeInfo?.I?.Name || '',
        Org: changeInfo?.I?.Org || 0,
      });
      desc.BigEventDesc = `${dtCreate} 该企业存在${changeInfo.H}的相关公告`;
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    case 110:
      desc.Subtitle = '新增债券违约';
      if (changeInfo.sname) {
        riskHelper.pushContentKV({ contents: tMsg, k: '债券简称', v: changeInfo.sname });
      }
      if (CommonHelper.isNotEmpty(changeInfo.newStatus) && shared.BondDefaultStatusMap.get(changeInfo.newStatus)) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '违约状态',
          v: shared.BondDefaultStatusMap.get(changeInfo.newStatus),
        });
      }
      if (changeInfo.overdueCapital && Number(changeInfo.overdueCapital)) {
        riskHelper.pushContentKV({ contents: tMsg, k: '累计违约本金（亿元）', v: changeInfo.overdueCapital });
      }
      if (changeInfo.overdueInterest && Number(changeInfo.overdueInterest)) {
        riskHelper.pushContentKV({ contents: tMsg, k: '累计违约利息（亿元）', v: changeInfo.overdueInterest });
      }
      if (changeInfo.maturityDate) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '到期日期',
          v: moment.unix(changeInfo.maturityDate).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} 新增债券违约${
        changeInfo.overdueCapital && Number(changeInfo.overdueCapital) ? '，累计违约本金（亿元）：' + changeInfo.overdueCapital : ''
      }`;
      break;
    case 116:
      desc.Subtitle = '登记机关变更';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '变更前登记机关', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '变更后登记机关', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '核准日期',
          v: moment.unix(changeInfo.C).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = `变更登记机关为：${changeInfo.B || '-'}`;
      desc.BigEventDesc = `${dtCreate} ${isHideDyContent ? desc.Subtitle : extend}`;
      break;
    case 125:
      desc.Subtitle = '新增非标资产违约';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '产品名称', v: changeInfo.A });
      }
      if (CommonHelper.isNotEmpty(changeInfo.B) && shared.NonStandardAssertsDefaultProductTypeMap.get(Number(changeInfo.B))) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '产品类型',
          v: shared.NonStandardAssertsDefaultProductTypeMap.get(Number(changeInfo.B)),
        });
      }
      if (changeInfo.C && Number(changeInfo.C)) {
        riskHelper.pushContentKV({ contents: tMsg, k: '金额（万元）', v: CommonHelper.handleNum(Number(changeInfo.C)) });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '发布日期',
          v: moment.unix(changeInfo.D).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} 新增非标资产违约${changeInfo.A ? '，产品名称：' + changeInfo.A : ''}`;
      break;
    case 69:
      desc.Subtitle = '商标注册申请';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请/注册号', v: changeInfo.A });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '国际分类',
          v: `${changeInfo.B ? `${changeInfo.B}类 ` : ''}${changeInfo.E}`,
        });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '申请日期',
          v: moment(changeInfo.F, 'YYYYMMDD').format('YYYY-MM-DD'),
        });
      }
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      // 商标图像
      if (changeInfo.D) {
        desc.TrademarkLogo = changeInfo.D;
      }
      desc.ContentArray = tMsg;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${changeInfo.C ? '，' + changeInfo.C : ''}`;
      break;
    case 169:
      desc.Subtitle = '商标注册成功';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请/注册号', v: changeInfo.A });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '国际分类',
          v: `${changeInfo.B ? `${changeInfo.B}类 ` : ''}${changeInfo.E}`,
        });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '注册公告日期',
          v: moment(changeInfo.F, 'YYYYMMDD').format('YYYY-MM-DD'),
        });
      }
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      // 商标图像
      if (changeInfo.D) {
        desc.TrademarkLogo = changeInfo.D;
      }
      desc.ContentArray = tMsg;
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${changeInfo.C ? '，' + changeInfo.C : ''}`;
      break;
    case 70:
      desc.Subtitle = shared.Patent_Type_Map.get(changeInfo.G) || '新增专利';
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '申请号', v: changeInfo.C });
      }
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '专利名称', v: changeInfo.A, isLink: true });
      }
      // 只有为发明公布时需要展示申请日期
      if (changeInfo.F && changeInfo.G === '发明公布') {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '申请日期',
          v: moment.unix(changeInfo.F).format('YYYY-MM-DD'),
        });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: changeInfo.G === '发明公布' ? '公开日期' : '授权日期',
          v: moment.unix(changeInfo.E).format('YYYY-MM-DD'),
        });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${changeInfo.A ? '，' + changeInfo.A : ''}`;
      break;
    case 121:
    case 242:
      desc.Subtitle = '新增金融监管';
      riskHelper.pushContentKV({ contents: tMsg, k: '决定文书号', v: changeInfo.A || '-', isLink: true });
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '违规事实', v: changeInfo.B, line: 3 });
      } else if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处理结果', v: changeInfo.D, line: 3 });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '处理单位', v: changeInfo.F });
      }
      if (changeInfo.G) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '处理日期',
          v: moment.unix(changeInfo.G).format('YYYY-MM-DD'),
        });
      }
      // 将文案拼接等规则放入公共方法调用
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      if (changeInfo.B) {
        // 主页滚动文案优先级展示
        extend = `，违规事实：${changeInfo.B}`;
      } else if (changeInfo.D) {
        extend = `，处理结果：${changeInfo.D}`;
      }
      desc.BigEventDesc = `${dtCreate} ${desc.Subtitle}${isHideDyContent ? '' : extend || ''}`;
      break;
    case 129:
      desc.Subtitle = '新增上市进程';
      if (changeInfo.Stage === 1) {
        desc.Subtitle = shared.ListpRrocessMap.get(changeInfo.Code) || '';
        if (changeInfo.DispatchedOffice) {
          riskHelper.pushContentKV({ contents: tMsg, k: '备案证监局', v: changeInfo.DispatchedOffice });
        }
        if (changeInfo?.SponsorArray?.length) {
          forEach(changeInfo.SponsorArray, (i) => {
            tNames.push(i.Name);
            if (i.Keyno) {
              desc.Highlight.push({
                Id: i.Keyno || '',
                Name: i.Name || '',
                Org: i.Org || 0,
              });
            }
          });
          if (tNames.length) {
            riskHelper.pushContentKV({ contents: tMsg, k: '辅导机构', v: tNames.join('、') });
          }
        }
        if (changeInfo?.AccountingArray?.length) {
          forEach(changeInfo.AccountingArray, (i) => {
            tNames2.push(i.Name);
            if (i.Keyno) {
              desc.Highlight.push({
                Id: i.Keyno || '',
                Name: i.Name || '',
                Org: i.Org || 0,
              });
            }
          });
          if (tNames2.length) {
            riskHelper.pushContentKV({ contents: tMsg, k: '会计事务所', v: tNames2.join('、') });
          }
        }
        if (changeInfo?.LawArray?.length) {
          forEach(changeInfo.LawArray, (i) => {
            tNames3.push(i.Name);
            if (i.Keyno) {
              desc.Highlight.push({
                Id: i.Keyno || '',
                Name: i.Name || '',
                Org: i.Org || 0,
              });
            }
          });
          if (tNames3.length) {
            riskHelper.pushContentKV({ contents: tMsg, k: '律师事务所', v: tNames3.join('、') });
          }
        }
        if (changeInfo.RecordDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '备案时间',
            v: moment.unix(changeInfo.RecordDate).format('YYYY-MM-DD'),
          });
        }
      } else if (changeInfo.Stage === 2) {
        desc.Subtitle = shared.ListpRrocessMap.get(changeInfo.Code) || '';
        if (changeInfo.ListSection && shared.ListSectionMap.get(changeInfo.ListSection)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市板块',
            v: shared.ListSectionMap.get(changeInfo.ListSection),
          });
        }
        if (changeInfo.ListExchange && shared.ListExchangeMap.get(changeInfo.ListExchange)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市交易所',
            v: shared.ListExchangeMap.get(changeInfo.ListExchange),
          });
        }
        if (changeInfo.RecordDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '辅导备案日期',
            v: moment.unix(changeInfo.RecordDate).format('YYYY-MM-DD'),
          });
        }
        if (changeInfo.ApprovalDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '受理日期',
            v: moment.unix(changeInfo.ApprovalDate).format('YYYY-MM-DD'),
          });
        }
        if (item.ChangeDate && item.ChangeDate !== changeInfo.ApprovalDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '更新日期',
            v: moment.unix(item.ChangeDate).format('YYYY-MM-DD'),
          });
        }
      } else if (changeInfo.Stage === 3 || changeInfo.Stage === 4) {
        desc.Subtitle = (changeInfo.DerivedSymbol || '') + (shared.ListpRrocessMap.get(changeInfo.Code) || '');
        if (changeInfo.ListSection && shared.ListSectionMap.get(changeInfo.ListSection)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市板块',
            v: shared.ListSectionMap.get(changeInfo.ListSection),
          });
        }
        if (changeInfo.ListExchange && shared.ListExchangeMap.get(changeInfo.ListExchange)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市交易所',
            v: shared.ListExchangeMap.get(changeInfo.ListExchange),
          });
        }
        if (changeInfo.DerivedSymbol) {
          riskHelper.pushContentKV({ contents: tMsg, k: '股票代码', v: changeInfo.DerivedSymbol });
        }
        if (changeInfo.IssueMktvalue) {
          riskHelper.pushContentKV({ contents: tMsg, k: '发行总市值', v: changeInfo.IssueMktvalue });
        }
        if (changeInfo.ListDate && changeInfo.Stage === 4) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市日期',
            v: moment.unix(changeInfo.ListDate).format('YYYY-MM-DD'),
          });
        }
      } else if (changeInfo.Stage === 5) {
        desc.Subtitle = (changeInfo.DerivedSymbol || '') + (shared.ListpRrocessMap.get(changeInfo.Code) || '');
        if (changeInfo.Sname) {
          riskHelper.pushContentKV({ contents: tMsg, k: '股票简称', v: changeInfo.Sname });
        }
        if (changeInfo.DerivedSymbol) {
          riskHelper.pushContentKV({ contents: tMsg, k: '股票代码', v: changeInfo.DerivedSymbol });
        }
        if (changeInfo.ListSection && shared.ListSectionMap.get(changeInfo.ListSection)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市板块',
            v: shared.ListSectionMap.get(changeInfo.ListSection),
          });
        }
        if (changeInfo.ListExchange && shared.ListExchangeMap.get(changeInfo.ListExchange)) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市交易所',
            v: shared.ListExchangeMap.get(changeInfo.ListExchange),
          });
        }
        if (changeInfo.ListDate) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '上市日期',
            v: moment.unix(changeInfo.ListDate).format('YYYY-MM-DD'),
          });
        }
      }
      // 将文案拼接等规则放入公共方法调用
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      desc.BigEventDesc = `${dtCreate} 上市进程变动：${desc.Subtitle}`;
      break;
    case 130:
      desc.Subtitle = '新增税务催报';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '税种', v: changeInfo.A });
      }
      if (changeInfo.B && changeInfo.C) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '所属期',
          v: `${moment.unix(changeInfo.B).format('YYYY-MM-DD')}至${moment.unix(changeInfo.C).format('YYYY-MM-DD')}`,
        });
      }
      if (changeInfo.D) {
        riskHelper.pushContentKV({ contents: tMsg, k: '主管税务机关', v: changeInfo.D });
      }
      if (changeInfo.E) {
        riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: moment.unix(changeInfo.E).format('YYYY-MM-DD') });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.A ? `，税种：${changeInfo.A}` : '';
      desc.BigEventDesc = `${dtCreate} 新增税务催报${extend}`;
      break;
    case 131:
      desc.Subtitle = '新增税务催缴';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '税种', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '欠缴金额（元）',
          v: CommonHelper.handleNum(Number(changeInfo.B)),
        });
      }
      if (changeInfo.C && changeInfo.D) {
        riskHelper.pushContentKV({
          contents: tMsg,
          k: '所属期',
          v: `${moment.unix(changeInfo.C).format('YYYY-MM-DD')}至${moment.unix(changeInfo.D).format('YYYY-MM-DD')}`,
        });
      }
      if (changeInfo.F) {
        riskHelper.pushContentKV({ contents: tMsg, k: '主管税务机关', v: changeInfo.F });
      }
      if (changeInfo.G) {
        riskHelper.pushContentKV({ contents: tMsg, k: '发布日期', v: moment.unix(changeInfo.G).format('YYYY-MM-DD') });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.A ? `，税种：${changeInfo.A}` : '';
      desc.BigEventDesc = `${dtCreate} 新增税务催缴${extend}`;
      break;
    case 117:
      desc.Subtitle = '被列入税务非正常户';
      if (changeInfo.A) {
        riskHelper.pushContentKV({ contents: tMsg, k: '纳税人识别号', v: changeInfo.A });
      }
      if (changeInfo.B) {
        riskHelper.pushContentKV({ contents: tMsg, k: '列入机关', v: changeInfo.B });
      }
      if (changeInfo.C) {
        riskHelper.pushContentKV({ contents: tMsg, k: '列入日期', v: moment.unix(changeInfo.C).format('YYYY-MM-DD') });
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      extend = changeInfo.B ? `，列入机关：${changeInfo.B}` : '';
      desc.BigEventDesc = `${dtCreate} 被列入税务非正常户${extend}`;
      break;
    case 9999:
      desc.Title = '工商变更';
      desc.Subtitle = '发生工商信息变更';
      if (item.MergeList?.length) {
        if (item.MergeList.length > 3) {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '',
            v: `${labelStyleStartOneLine}${item.MergeList.slice(0, 3).join('、')}${labelStyleEnd}等${labelStyleStartOneLine}${
              item.MergeList.length
            }${labelStyleEnd}类`,
          });
        } else {
          riskHelper.pushContentKV({
            contents: tMsg,
            k: '',
            v: `${labelStyleStartOneLine}${item.MergeList.join('、')}${labelStyleEnd}`,
          });
        }
        delete item.MergeList;
      }
      desc.ContentArray = tMsg;
      desc.Content = riskHelper.getContent(tMsg, useH5Style);
      break;
    default:
  }
  // if (useH5Style && !desc.ContentArray) {
  //   desc.ContentArray = []
  //   forEach(tMsg, t => {
  //     const regex = /<span[^>]*>(.*?)(?::<\/span>|<\/span>)/
  //     const matches = t.match(regex)
  //     if (matches?.length) {
  //       desc.ContentArray.push({
  //         Key: matches[1], // 获取匹配的第一个捕获组作为 key
  //         Value: matches[2] || '' // 获取匹配的第二个捕获组作为 value
  //       })
  //     }
  //   })
  // }

  return desc;
};

export default {
  getRiskListDesc,
};

export { getRiskListDesc };
