import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { ApiGuardExternal } from 'libs/guards/api.guard.external';
import { JudgementSource } from './source/judgement.source';
import { DateDetailsQueryParams } from '../../libs/model/data/request';
import { HitDetailsBaseResponse } from '../../libs/model/diligence/details/response';
import { CreditEsSource } from './source/credit-es.source';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { EnterpriseLibHelper } from './helper/enterprise.lib.helper';
import { CompanyRelatedListParams } from '../../libs/model/data/request/CompanyRelatedListParams';
import { RelatedCompanySource } from './source/related-company.source';
import { PlatformUser } from '../../libs/model/common';

@Controller('data/external')
@ApiTags('数据源查询展示')
@ApiBearerAuth()
@UseGuards(ApiGuardExternal)
export class DataExternalController {
  private readonly logger: Logger = QccLogger.getLogger(DataExternalController.name);

  constructor(
    private readonly judgementService: JudgementSource,
    private readonly creditESService: CreditEsSource,
    private readonly entLibDimService: EnterpriseLibHelper,
    private readonly nebulaGraphService: RelatedCompanySource,
  ) {}

  @Post('Judgement')
  @ApiOperation({ summary: '通过记录id查询裁判文书数据', description: '通过记录id查询裁判文书数据' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  //   @Cacheable({ ttlSeconds: 36000, cacheKey: 'getAreaKV' })
  public async getJudgementList(@Request() req, @Body() body: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    return this.judgementService.getJudgementDataByIds(body);
  }

  @Post('TaxationOffences')
  @ApiOperation({ summary: '通过记录id查询税收违法数据', description: '通过记录id查询税收违法数据' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  //   @Cacheable({ ttlSeconds: 36000, cacheKey: 'getAreaKV' })
  public async getTaxationOffencesList(@Request() req, @Body() body: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    return this.creditESService.getDateByIds(DimensionTypeEnums.TaxationOffences, body);
  }

  @Post('OperationAbnormal')
  @ApiOperation({ summary: '通过记录id查询经营异常数据', description: '通过记录id查询经营异常数据' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  //   @Cacheable({ ttlSeconds: 36000, cacheKey: 'getAreaKV' })
  public async getOperationAbnormal(@Request() req, @Body() body: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    return this.creditESService.getDateByIds(DimensionTypeEnums.OperationAbnormal, body);
  }

  @Post('PersonCreditCurrent')
  @ApiOperation({ summary: '通过记录id查询失信被执行人数据', description: '通过记录id查询失信被执行人数据' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  public async getPersonCredit(@Request() req, @Body() body: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    const ids = body?.ids?.map((id) => id + '2').join(',');
    const { resultItems, GroupItems } = await this.entLibDimService.getCompanyShiXingInfo(body.keyNo, '/api/Court/SearchShiXin', { ids });

    return Object.assign(new HitDetailsBaseResponse(), {
      Paging: {
        PageSize: body.pageSize,
        PageIndex: body.pageIndex,
        TotalRecords: resultItems.length || 0,
      },
      Result: resultItems,
      GroupItems,
    });
  }

  @Post('CompanyCredit')
  @ApiOperation({ summary: '通过记录id查询严重违法数据', description: '通过记录id查询严重违法数据' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  public async getCompanyCredit(@Request() req, @Body() body: DateDetailsQueryParams): Promise<HitDetailsBaseResponse> {
    return this.creditESService.getDateByIds(DimensionTypeEnums.CompanyCredit, body);
  }

  @Post('CompanyRelatedList')
  @ApiOperation({ summary: '企业关联方列表', description: '查询指定公司的关联方企业列表' })
  @ApiResponse({ type: HitDetailsBaseResponse })
  // @Cacheable({ ttlSeconds: 3600 })
  public async getCompanyRelatedList(@Request() req, @Body() body: CompanyRelatedListParams) {
    const currentUser: PlatformUser = req.user;
    const { currentOrg: orgId, currentProduct: product } = currentUser;
    return this.nebulaGraphService.getCompanyRelatedList(body, orgId, product);
  }
}
