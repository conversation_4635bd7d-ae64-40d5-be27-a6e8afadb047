import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionHitStrategyPO } from '../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { EntityManager } from 'typeorm';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { GroupEntity } from '../../libs/entities/GroupEntity';
import { pick } from 'lodash';
import { getFullRiskModel } from '../../libs/db_helpers/resource.publish.helper';
import { DataStatusEnums } from '../../libs/enums/DataStatusEnums';
import { DimensionFilterParams } from '../../libs/model/diligence/dimension/DimensionStrategyPO';
import { DiligenceTargetTypeEnum } from '../../libs/model/metric/MetricDynamicStrategy';

/**
 * 获取排查模型维度数组 (按维度打平)
 * 不包含一级维度
 * @param orgModel
 * @param ignoreStatus 需要过滤的状态, 默认全部返回
 * @param params 独立于模型之外的维度过滤条件
 * @returns
 */
// @Cacheable({ ttlSeconds: 60 })
export const getAllDimensionHitStrategy = (orgModel: RiskModelEntity, ignoreStatus = [], params?: DimensionFilterParams): DimensionHitStrategyPO[] => {
  const { isRelated } = params || {};
  const dimensionStrategies: DimensionHitStrategyPO[] = [];

  orgModel.groups.forEach((group: GroupEntity) => {
    if (!ignoreStatus.includes(group.status)) {
      group.groupMetrics.forEach((metrics) => {
        let skip = false;
        const metricTarget = metrics.metricEntity?.detailsJson?.dynamicStrategy?.target;
        // 关联方企业只查询关联方指标
        if (metricTarget) {
          if (isRelated) {
            // 关联方企业 忽略主体企业的指标
            skip = metricTarget == DiligenceTargetTypeEnum.PrimaryCompnay;
          } else {
            // 主体企业 忽略关联方企业的指标
            skip = metricTarget == DiligenceTargetTypeEnum.RelatedCompany;
          }
        }

        if (!ignoreStatus.includes(metrics.metricEntity.status) && !skip) {
          // 指标下需要用到的维度策略
          const usedDimHitStrategy = [];
          metrics.metricEntity.hitStrategy.forEach((s) => {
            // 只取指标下已开启的维度策略组
            if (s.status == DataStatusEnums.Enabled) {
              if (s.should) {
                usedDimHitStrategy.push(...s.should);
              }
              if (s.must) {
                usedDimHitStrategy.push(...s.must);
              }
              if (s.must_not) {
                usedDimHitStrategy.push(...s.must_not);
              }
            }
          });

          metrics.metricEntity.dimensionHitStrategies.forEach((dimHitStrategy) => {
            // 维度策略不是需要过滤的状态 并且 维度策略在指标已开启的维度策略组中
            if (!dimHitStrategy.dimensionHitStrategyEntity) {
              console.log('debug');
            }
            if (!ignoreStatus.includes(dimHitStrategy.dimensionHitStrategyEntity.status) && usedDimHitStrategy.includes(dimHitStrategy.dimensionStrategyId)) {
              dimensionStrategies.push(
                Object.assign(
                  new DimensionHitStrategyPO(dimHitStrategy.dimensionHitStrategyEntity.dimensionDef),
                  pick(dimHitStrategy.dimensionHitStrategyEntity, [
                    'strategyId',
                    'strategyName',
                    'strategyRole',
                    'status',
                    'hitStrategy',
                    'dimensionDef',
                    'strategyFields',
                    'template',
                  ]),
                  {
                    dimensionFilter: params,
                  },
                ),
              );
            }
          });
        }
      });
    }
  });

  return dimensionStrategies;
};

/**
 * 获取模型中指定维度的维度策略
 * @param orgModelId
 * @param dimensionKeys
 * @returns
 */
export const getDimesionHitStrategies = async (
  orgModelId: number,
  dimensionKeys: DimensionTypeEnums[] = [],
  manager: EntityManager,
): Promise<DimensionHitStrategyPO[]> => {
  const riskModel = await getFullRiskModel(orgModelId, manager);
  const allDimensionStrategies = getAllDimensionHitStrategy(riskModel);
  if (dimensionKeys.length < 1) {
    return allDimensionStrategies;
  }
  return allDimensionStrategies.filter((ds) => {
    return dimensionKeys.includes(ds?.dimensionDef?.key);
  });
};

/**
 * 获取模型中指定维度的维度策略
 * @param riskModel 直接传对象，方便在上层在必要时候对对象进行缓存
 * @param dimensionKey
 * @returns
 */
export const getDimensionHitStrategy = async (
  riskModel: RiskModelEntity,
  dimensionKey: DimensionTypeEnums,
  strategyId: number,
  manager: EntityManager,
): Promise<DimensionHitStrategyPO> => {
  // const riskModel = await getFullRiskModel(orgModelId, manager);
  const allDimensionStrategies = getAllDimensionHitStrategy(riskModel);
  return allDimensionStrategies.find((ds) => ds.dimensionDef.key === dimensionKey && ds.strategyId == strategyId);
};
