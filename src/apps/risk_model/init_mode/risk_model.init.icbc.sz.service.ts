import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { PlatformUser } from '../../../libs/model/common';
import { RiskModelService } from '../risk_model.service';
import * as Bluebird from 'bluebird';
import { createDimensionFields, createGroup, createMetric } from '../../test_utils_module/dimension.test.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionService } from '../../dimension/dimension.service';
import { DimensionFieldsService } from '../../dimension/dimension.fields.service';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { IsStateOwnedConstant, ShareholdRoleConstant } from '../../../libs/constants/company.constants';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { LayTypeMap } from '../../../libs/constants/risk.change.constants';

/**
 * icbc SZ 贷前指标
 */
@Injectable()
export class RiskModelICBCSZService {
  private logger: Logger = QccLogger.getLogger(RiskModelICBCSZService.name);

  constructor(
    private readonly riskModelService: RiskModelService,
    private readonly dimensionService: DimensionService,
    private readonly dimensionFieldsService: DimensionFieldsService,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
  ) {}

  /**
   * 手动创建ICBC 贷前指标模型
   * @param user 创建模型用户
   * @param modelName  模型名称
   * @param toOrgId  分发给的orgId
   * @param modelType
   * @returns
   */
  async createICBCSZModel(
    user: PlatformUser,
    modelName: string,
    toOrgId?: number,
    modelType: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel,
  ): Promise<RiskModelEntity> {
    // 1. 创建风险模型
    const riskModel = await this.riskModelService.addRiskModel(
      {
        modelName,
        product: user.currentProduct,
        comment: modelName,
        status: DataStatusEnums.Developing,
        modelType,
      },
      user,
    );

    const manager = this.riskModelRepo.manager;

    // 2. 初始化维度，维度属性
    await createDimensionFields(manager, user);

    // 3. 创建分组
    const [g1, g2, g3, g4, g5, g6, g7, g8, g9, g10, g11, g12, g13] = await Bluebird.all([
      createGroup(manager, user, riskModel.modelId, '股权', 1),
      createGroup(manager, user, riskModel.modelId, '基础工商', 2),
      createGroup(manager, user, riskModel.modelId, '上市情况', 3),
      createGroup(manager, user, riskModel.modelId, '投资数量', 4),
      createGroup(manager, user, riskModel.modelId, '投资波动', 5),
      createGroup(manager, user, riskModel.modelId, '专利信息', 6),
      createGroup(manager, user, riskModel.modelId, '投资行业', 7),
      createGroup(manager, user, riskModel.modelId, '失信记录', 8),
      createGroup(manager, user, riskModel.modelId, '涉诉信息', 9),
      createGroup(manager, user, riskModel.modelId, '关联方失信', 10),
      createGroup(manager, user, riskModel.modelId, '投资异常', 11),
      createGroup(manager, user, riskModel.modelId, '实控行业', 12),
      createGroup(manager, user, riskModel.modelId, '关联方失信', 13),
    ]);

    // 4. 给分组内添加指标
    await Bluebird.all([
      createMetric(manager, user, g1.groupId, '第一大股东持股比例', '直接持股比例', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '第一大股东持股比例大于20%',
              comment: '命中 【1】持股比例>20%, 【2】角色：大股东 ',
              dimKey: DimensionTypeEnums.ShareholderInformation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.directShareholdPercentage,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.shareholdRole,
                  fieldValue: ['majorShareholder'],
                  options: ShareholdRoleConstant,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'stockpercent', order: 'DESC', fieldSnapshot: 'StockPercent' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '实控人控股比例比例', '总持股比例', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '实控人控股比例比例大于20%',
              comment: '命中 【1】总持股比例>20% ',
              dimKey: DimensionTypeEnums.ActuralControllerInformation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.percentTotal,
                  fieldValue: [20],
                  options: [{ unit: '%', min: 0, max: 100 }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人实控年限',
        '按当前实控人向前推，如果能找到上一条实控人变更的动态，年限就是时间差值，如果向前没有实控人变更动态，时间就是从成立以来到现在',
        3,
        [
          {
            order: 0,
            maxScore: 1,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '实控人实控年限>5年',
                comment: '命中 【1】控制年限 ',
                dimKey: DimensionTypeEnums.ActuralControllerInformation,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.controllerTime,
                    fieldValue: [5],
                    options: [{ unit: '年', min: 1, max: 10 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人是否国资委',
        "companyName.includes('国有资产监督管理委员会') || companyName.includes('国有资产监督管理局');",
        4,
        [
          {
            order: 0,
            maxScore: 1,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '实控人是国资',
                comment: '命中 【1】是否国资 ',
                dimKey: DimensionTypeEnums.ActuralControllerInformation,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isStateOwned,
                    fieldValue: [1],
                    options: IsStateOwnedConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
      ),

      createMetric(manager, user, g1.groupId, '实控人控股公司总数量', '控制企业列表中总持股>50%的企业', 5, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '实控人控股公司总数量',
              comment: '命中 【1】控制企业，【2】总持股>50%  ',
              dimKey: DimensionTypeEnums.ControllerCompany,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [RelatedTypeEnums.ActualController],
                  accessScope: 2,
                  options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.percentTotal,
                  fieldValue: [50],
                  accessScope: 2,
                  options: [{ unit: '%', min: 0, max: 100 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '企业经营状态', '在业/存续/迁入/迁出/注销/吊销/撤销/清算/歇业/除名/责令关闭', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '企业经营状态',
              comment: '命中 【1】清算/吊销/责令关闭/停业/注销/撤销/歇业/迁入/迁出',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['10', '20', '40', '90', '85', '70', '99', '80', '75', '50', '60'],
                  accessScope: 2,
                  options: [
                    { label: '清算', value: '40' },
                    { label: '吊销', value: '90' },
                    { label: '责令关闭', value: '85' },
                    { label: '停业', value: '70' },
                    { label: '注销', value: '99' },
                    { label: '撤销', value: '80' },
                    { label: '歇业', value: '75' },
                    { label: '迁入', value: '50' },
                    { label: '迁出', value: '60' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '企业成立天数', '企业成立日期、截止尽调时成立天数（单位：月）', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '企业成立天数大于30月',
              comment: '命中 【1】注册时间',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.duration,
                  fieldValue: [30],
                  options: [{ unit: '月', min: 0, max: 9999 }],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '法人代表变更', '主要人员变更 （法人）', 3, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近5年法代变更次数大于2次',
              comment:
                '命中：【1】riskCategories=72， 【2】企业负责人layTypes(ChangeExtend.C)=[法定代表人,执行事务合伙人,负责人,经营者,投资人,董事长,理事长,代表人]',
              dimKey: DimensionTypeEnums.RiskChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [72],
                  options: [{ value: 72, label: '成员变更' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.layTypes,
                  fieldValue: [1],
                  options: LayTypeMap,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '近5年其他高管变更次数', '主要人员变更（剔除法人）', 4, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '近5年其他高管变更次数大于2次',
              comment: '命中：【1】riskCategories=72， 【2】排除非法人的变更次数',
              dimKey: DimensionTypeEnums.RiskChange,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.riskCategories,
                  fieldValue: [72],
                  options: [{ value: 72, label: '成员变更' }],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.layTypes,
                  fieldValue: [1],
                  options: LayTypeMap,
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '企业所属行业-国标行业', 'k701', 5, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '房地产开发经营',
              comment: '命中 【1】国标行业',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyIndustry,
                  fieldValue: ['K-70-701'],
                  accessScope: 2,
                  options: [{ value: 'K-70-701', label: '房地产开发经营' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g2.groupId, '企业所属行业-企查查行业', '房地产开发', 6, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '房地产开发经营',
              comment: '命中 【1】企查查行业',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.qccIndustry,
                  fieldValue: ['28-2801'],
                  accessScope: 2,
                  options: [{ value: '28-2801', label: '房地产开发' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),

      createMetric(manager, user, g3.groupId, '是否境内上市公司', '', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g3.groupId, '是否拟上市', '关注当前最新是否有辅导备案，排除既往中止/终止', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g4.groupId, '企业控股子公司总数量', '对外投资企业控股>50%,直接投资 状态是：存续+在业', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g4.groupId, '近一年企业控股子公司新增成立数量', '对外投资企业控股>50%,直接投资，成立时间近1年 状态是：在业+存续', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(
        manager,
        user,
        g5.groupId,
        '近一年企业对外投资企业注销占比',
        '近1年对外投资企业数，近1年对外投资企业中已注销的企业数，注销占比（%），返回结果确认：比例，分子，分母',
        3,
        [
          {
            order: 0,
            maxScore: 1,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [],
          },
        ],
      ),

      createMetric(manager, user, g6.groupId, '企业的授权发明专利数量', '当前专利，专利类型（发明授权，发明公布），法律状态（授权），全量数据', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g6.groupId, '企业被转让的授权发明专利数量', '被转让的定义（方向：专利转入）', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g6.groupId, '企业近三年有授权发明专利', '企业近三年有授权发明专利', 3, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g7.groupId, '对外投资企业涉房', '对外投资企业涉房，对外投资（直接）>50%', 1, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),

      createMetric(manager, user, g7.groupId, '对外投资企业涉金融', '对外投资企业涉金融，对外投资（直接）>50%，剔除“有限合伙”', 2, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [],
        },
      ]),
    ]);
    // 5. 分发模型给租户
    if (toOrgId) {
      await this.riskModelService.distributeRiskModel(
        {
          riskModelId: riskModel.modelId,
          orgId: toOrgId,
        },
        user,
      );
    }
    return riskModel;
  }
}
