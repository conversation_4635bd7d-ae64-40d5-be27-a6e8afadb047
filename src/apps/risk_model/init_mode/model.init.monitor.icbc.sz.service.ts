import { Injectable } from '@nestjs/common';
import { RiskModelService } from '../risk_model.service';
import { InjectRepository } from '@nestjs/typeorm';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { Repository } from 'typeorm';
import { PlatformUser } from '../../../libs/model/common';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import * as Bluebird from 'bluebird';
import { createDimensionFields, createGroup, createMetric } from '../../test_utils_module/dimension.test.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { MetricTypeEnums } from '../../../libs/enums/metric/MetricTypeEnums';
import { DiligenceTargetTypeEnum, MetricDynamicDataSourceTypeEnum } from '../../../libs/model/metric/MetricDynamicStrategy';
import { ScoreStrategyEnums } from '../../../libs/enums/ScoreStrategyEnums';
import {
  ChangeStatusMap,
  CurrencyChangeMap,
  HolderRoleType,
  IsBPMap,
  isFilterRelatedCompanyMap,
  IsPEVCMap,
  LayTypeMap,
  RealRegistrationErrorMap,
  RegisCapitalTrendMap,
  ShareChangeStatusMap,
  SimpleCancelTypeConstant,
} from '../../../libs/constants/risk.change.constants';
import { ProcessingAgencyLevelOneMap } from '../../../libs/constants/punish.constants';
import { DimensionFieldInputTypeEnums } from '../../../libs/enums/dimension/DimensionFieldInputTypeEnums';
import { CustomizedRelatedEnums, RelatedChangeTypeEnums } from 'libs/constants/related.constants';
import { NegativePositiveTopicTypes } from 'libs/constants/news.constants';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { IsHistoryPatentConstant, PatentStatusConstant, PatentTypeConstant } from '../../../libs/constants/company.constants';

/**
 * 工行深圳分行监控模型初始化
 */
@Injectable()
export class ModelInitICBCSZMonitorService {
  constructor(
    private readonly riskModelService: RiskModelService,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
  ) {}

  async createMonitorModel(user: PlatformUser, modelName: string, toOrgId: number, modelType: RiskModelTypeEnums = RiskModelTypeEnums.MonitorModel) {
    // 1. 创建风险模型
    const riskModel = await this.riskModelService.addRiskModel(
      {
        modelName,
        product: user.currentProduct,
        comment: modelName,
        status: DataStatusEnums.Developing,
        modelType,
      },
      user,
    );

    const manager = this.riskModelRepo.manager;

    // 2. 初始化维度，维度属性
    await createDimensionFields(manager, user);

    // 3. 创建分组
    const [g1, g5, g6, g7] = await Bluebird.all([
      createGroup(manager, user, riskModel.modelId, '工商风险', 1),
      createGroup(manager, user, riskModel.modelId, '经营风险', 5),
      createGroup(manager, user, riskModel.modelId, '司法事件', 6),
      createGroup(manager, user, riskModel.modelId, '财务变动', 7),
    ]);

    // 4. 给分组内添加指标
    await Bluebird.all([
      createMetric(
        manager,
        user,
        g1.groupId,
        '关联方',
        '关联方',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '关联方新增',
                comment: '新增加符合关联方规则的企业',
                dimKey: DimensionTypeEnums.RelatedCompanyChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.customizedRelated,
                    fieldValue: [CustomizedRelatedEnums.IcbcSFRelated],
                    options: [
                      // { label: '汇添富定义关联方(Nebula查询)', value: CustomizedRelatedEnums.HtfRelated },
                      { label: 'IcbcSF关联方(独立接口)', value: CustomizedRelatedEnums.IcbcSFRelated },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFilterRelatedCompany,
                    fieldValue: [1],
                    options: isFilterRelatedCompanyMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedChangeType,
                    fieldValue: [RelatedChangeTypeEnums.Added],
                    options: [{ label: '新增加符合关联方规则的企业', value: RelatedChangeTypeEnums.Added }],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                ],
              },
              {
                dimStrategyName: '关联方失效',
                comment: '已监控关联方中不在符合关联方规则的企业',
                dimKey: DimensionTypeEnums.RelatedCompanyChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.customizedRelated,
                    fieldValue: [CustomizedRelatedEnums.IcbcSFRelated],
                    options: [
                      // { label: '汇添富定义关联方(Nebula查询)', value: CustomizedRelatedEnums.HtfRelated },
                      { label: 'IcbcSF关联方(独立接口)', value: CustomizedRelatedEnums.IcbcSFRelated },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFilterRelatedCompany,
                    fieldValue: [1],
                    options: isFilterRelatedCompanyMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedChangeType,
                    fieldValue: [RelatedChangeTypeEnums.Removed],
                    options: [{ label: '新增加符合关联方规则的企业', value: RelatedChangeTypeEnums.Removed }],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Persistent,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '经营状态变更',
        '经营状态变更',
        0,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '吊销',
                comment: '命中：【1】riskCategories=38, 【2】businessStatus[ChangeExtend.B]= [清算,吊销,责令关闭,停业,注销,撤销,歇业,迁入,迁出]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [38],
                    options: [{ value: 38, label: '经营状态' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessStatus,
                    fieldValue: [90],
                    options: [{ label: '吊销', value: 90 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '简易注销',
                comment: '命中riskCategories=23',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [23],
                    options: [{ value: 23, label: '简易注销' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.simpleCancelType,
                    fieldValue: [1, 2, 3, 4, 5, 6],
                    options: SimpleCancelTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '注销备案',
                comment: '命中riskCategories=61',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [61],
                    options: [{ value: 61, label: '注销备案' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '第一大股东持股变化',
        '第一大股东',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '第一大股东持股比例变化幅度超过20%',
                comment: '命中：【1】riskCategories=44，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 20%, 股东身份是第一大股东',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股比变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    accessScope: 2,
                    options: ShareChangeStatusMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.differenceRatio,
                    fieldValue: [20],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1],
                    accessScope: 2,
                    options: HolderRoleType,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '第一大股东持股比例变化绝对值减少超过10%',
                comment: '命中：【1】riskCategories=44，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 10%, 股东身份是第一大股东',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股比变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    accessScope: 2,
                    options: ShareChangeStatusMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.absRatio,
                    fieldValue: [10],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1],
                    accessScope: 2,
                    options: HolderRoleType,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人持股变化',
        '实控人',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '实控人持股比例变化幅度超过20%',
                comment: '命中：【1】riskCategories=44，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 20%, 股东身份是实控人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股比变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    accessScope: 2,
                    options: ShareChangeStatusMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.differenceRatio,
                    fieldValue: [20],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [2],
                    accessScope: 2,
                    options: HolderRoleType,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实控人持股比例变化绝对值减少超过10%',
                comment: '命中：【1】riskCategories=44，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 10%, 股东身份是实控人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股比变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    accessScope: 2,
                    options: ShareChangeStatusMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.absRatio,
                    fieldValue: [10],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [2],
                    accessScope: 2,
                    options: HolderRoleType,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '控股股东持股变化',
        '控股股东股比从大于50% 降低至小于50%',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '控股股东股比从大于50% 降低至小于50%',
                comment:
                  '命中：【1】riskCategories=44，【2】股份减少：shareChangeStatus=0, 【3】股东身份是第一大股东【4】beforeContent >50%  【4】afterContent <50%',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股比变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    accessScope: 2,
                    options: ShareChangeStatusMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1],
                    accessScope: 2,
                    options: HolderRoleType,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [50],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '第一大股东变更',
        '第一大股东变更',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '大股东变更',
                comment: '命中：【1】riskCategories=24',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [24],
                    options: [{ value: 24, label: '大股东变动' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '成员变更-是大股东变更',
                comment: '命中：【1】riskCategories=72, 【2】是大股东变更',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [72],
                    options: [{ value: 72, label: '成员变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [1],
                    accessScope: 2,
                    options: IsBPMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '新增股权冻结',
        '新增股权冻结',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '股权冻结',
                comment:
                  '命中 【1】riskCategories=26， 【2】equityFreezeScope 股权冻结范围：changeInfo.T2 === 1  不等1 （失效，无效，解除） changeInfo?.T === 1 企业股权被冻结  , changeInfo?.T === 2 持有股权被冻结',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityFreezeScope,
                    fieldValue: [1, 2],
                    accessScope: 2,
                    options: [
                      { value: 1, label: '企业股权被冻结' },
                      { value: 2, label: '持有股权被冻结' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '对外投资企业大量注销或吊销',
        '对外投资企业大量注销或吊销',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '近12个月内公司对外投资企业大量注销或吊销',
                comment: '命中：[吊销,注销]的对外投资企业，其中近12个月内有经营状态变更成注销吊销的动态发生的数量占对外投资及企业的比例',
                dimKey: DimensionTypeEnums.RecentInvestCancellationsRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [12],
                    options: [{ unit: '月', min: 1, max: 12 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.hitCount,
                    fieldValue: [5],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  // {
                  //   fieldKey: DimensionFieldKeyEnums.thresholdRule,
                  //   fieldValue: [
                  //     { investCount: [1, 5], threshold: 60 },
                  //     { investCount: [6, 15], threshold: 50 },
                  //     { investCount: [16, null], threshold: 40 },
                  //   ],
                  //   options: [
                  //     {
                  //       investCount: {
                  //         label: '投资企业数',
                  //         sort: 1,
                  //         min: 1,
                  //         inputType: DimensionFieldInputTypeEnums.NumberRange,
                  //       },
                  //       threshold: {
                  //         label: '阈值',
                  //         sort: 2,
                  //         unit: '%',
                  //         min: 0,
                  //         max: 100,
                  //         inputType: DimensionFieldInputTypeEnums.Text,
                  //       },
                  //     },
                  //   ],
                  //   accessScope: 0,
                  //   compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  // },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.InvestCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.InvestCompany, label: '对外投资' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.fundedRatioLevel,
                    fieldValue: [0],
                    accessScope: 2,
                    options: [
                      { value: 0, label: '不限' },
                      { value: 1, label: '<=5%' },
                      { value: 2, label: '>5%' },
                      { value: 3, label: '>20%' },
                      { value: 4, label: '>50%' },
                      { value: 5, label: '>66.66%' },
                      { value: 6, label: '=100%' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [38],
                    options: [{ value: 38, label: '经营状态' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessStatus,
                    fieldValue: [90, 99],
                    accessScope: 2,
                    options: [
                      { label: '吊销', value: 90 },
                      { label: '注销', value: 99 },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人变更',
        '实际控制人变更',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '实际控制人变更',
                comment: '命中: 【1】riskCategories=25',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [25],
                    options: [{ value: 25, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      /* createMetric(
               manager,
               user,
               g4.groupId,
               '实控变动',
               '近n月实控人持股比例增持减持幅度',
               6,
               [
                 {
                   order: 0,
                   maxScore: 10,
                   riskLevel: DimensionRiskLevelEnum.Medium,
                   operation: 'should',
                   dimsFields: [
                     {
                       dimStrategyName: '实控人持股比例减少幅度超过10%',
                       comment: '命中：【1】riskCategories=68，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 10%, 股东身份是第一大股东或者实控人',
                       dimKey: DimensionTypeEnums.RiskChange,
                       fields: [
                         {
                           fieldKey: DimensionFieldKeyEnums.isValid,
                           fieldValue: [1],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.riskCategories,
                           fieldValue: [68],
                           options: [
                             { value: 68, label: '持股比例变更' },
                             { value: 204, label: '持股比例变更' },
                           ],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                           fieldValue: [0],
                           options: ShareChangeStatusMap,
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.Equal,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.shareChangeRate,
                           fieldValue: [10],
                           options: [{ unit: '%', min: 0, max: 100 }],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.holderRole,
                           fieldValue: [2],
                           options: HolderRoleType,
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.timePeriod,
                           fieldValue: [3],
                           options: [{ unit: '月', min: 1, max: 12 }],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.Equal,
                         },
                       ],
                     },
                   ],
                 },
                 {
                   order: 1,
                   maxScore: 10,
                   riskLevel: DimensionRiskLevelEnum.Medium,
                   operation: 'should',
                   dimsFields: [
                     {
                       dimStrategyName: '实控人持股比例从超过20%以上下降到20%以下',
                       comment: '命中：【1】riskCategories=68，【2】股份减少：shareChangeStatus=0, 【3】减少幅度 > 10%, 股东身份是第一大股东或者实控人',
                       dimKey: DimensionTypeEnums.RiskChange,
                       fields: [
                         {
                           fieldKey: DimensionFieldKeyEnums.isValid,
                           fieldValue: [1],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.riskCategories,
                           fieldValue: [68],
                           options: [
                             { value: 68, label: '持股比例变更' },
                             { value: 204, label: '持股比例变更' },
                           ],
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                           fieldValue: [0],
                           options: ShareChangeStatusMap,
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.Equal,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.beforeContent,
                           fieldValue: [20],
                           options: [{ unit: '%', min: 0, max: 100 }],
                           accessScope: 2,
                           compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.afterContent,
                           fieldValue: [20],
                           options: [{ unit: '%', min: 0, max: 100 }],
                           accessScope: 2,
                           compareType: DimensionFieldCompareTypeEnums.LessThan,
                         },
                         {
                           fieldKey: DimensionFieldKeyEnums.holderRole,
                           fieldValue: [2],
                           options: HolderRoleType,
                           accessScope: 0,
                           compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                         },
                       ],
                     },
                   ],
                 },
               ],
               MetricTypeEnums.MonitorBusinessMetric,
               {
                 mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
                 allowMultipleDynamics: false,
                 allowRepeatedHits: true,
               },
               ScoreStrategyEnums.MaxLevel,
             ),
       */
      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人减持',
        '实控人总持股（非直接持股）合计减持超过10%',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近2年内实控人总持股（非直接持股）合计减持超过10%，排除VC、PE等节点',
                comment: '命中：【1】riskCategories=72, 【2】周期内变更趋势和幅度',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [72],
                    options: [{ value: 72, label: '成员变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [2],
                    options: HolderRoleType,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isPEVC,
                    fieldValue: [0],
                    options: IsPEVCMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.periodShareRatioChange,
                    fieldValue: [
                      {
                        timePeriod: 24,
                        shareChangeRate: 10,
                        shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
                      },
                    ],
                    accessScope: 2,
                    options: [
                      {
                        timePeriod: {
                          label: '时间周期',
                          unit: '月',
                          min: 1,
                          max: 24,
                          inputType: DimensionFieldInputTypeEnums.Text,
                        },
                        shareChangeRate: {
                          label: '变更比例>',
                          unit: '%',
                          min: 0,
                          max: 100,
                          inputType: DimensionFieldInputTypeEnums.Text,
                        },
                        // shareChangeRateCompareType: {
                        //   label: '占比比较(大于/小于)',
                        //   value: DimensionFieldCompareTypeEnums.GreaterThan,
                        // },
                      },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人新增或减少控股子公司',
        '实控人近12个月内新增或减少控股子公司数量超过xx个则触发动态预警',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '公司实控人近12个月内新增控股子公司数量超过1个则触发动态预警',
                comment: '命中：近12个月 实际控制人, 新增对外投资，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [12],
                    options: [{ unit: '月', min: 1, max: 12 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.thresholdCount,
                    fieldValue: [1],
                    options: [{ unit: '个', min: 1, max: 1000 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
              {
                dimStrategyName: '公司实控人近12个月内减少控股子公司数量超过1个则触发动态预警',
                comment: '命中：近12个月 实际控制人， 对外投资失去控股 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [12],
                    options: [{ unit: '月', min: 1, max: 12 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.thresholdCount,
                    fieldValue: [1],
                    options: [{ unit: '个', min: 1, max: 1000 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [2],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人新增子公司涉房或金融',
        '实际控制人近期新增涉房控股公司',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '实际控制人新增涉房控股子公司',
                comment: '命中： 实际控制人， 新增对外投资经营范围涉房关键词，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.companySocpe,
                    fieldValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
                    accessScope: 2,
                    options: [
                      { value: '土地开发', label: '土地开发' },
                      { value: '地产开发', label: '地产开发' },
                      { value: '商品房销售', label: '商品房销售' },
                      { value: '房地产项目投资', label: '房地产项目投资' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实际控制人新增涉房控股子公司-国标行业',
                comment: '命中：近3个月 实际控制人， 新增对外投资 行业为 K-70-701，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.companyIndustry,
                    fieldValue: ['K-70-701'],
                    accessScope: 2,
                    options: [{ value: 'K-70-701', label: '房地产开发经营' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实际控制人新增涉房控股子公司-企查查行业',
                comment: '命中：近3个月 实际控制人， 新增对外投资 行业为 企查查行业code，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.qccIndustry,
                    fieldValue: ['28-2801'],
                    accessScope: 2,
                    options: [{ value: '28-2801', label: '房地产开发' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '实际控制人新增控股子公司涉金融',
                comment: '命中：近3个月 实际控制人， 新增对外投资企业名称涉房、涉金融关键词，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.companyName,
                    fieldValue: ['小额贷款', '互联网金融', '典当', '保理', '担保', '融资租赁'],
                    accessScope: 2,
                    options: [
                      { value: '小额贷款', label: '小额贷款' },
                      { value: '互联网金融', label: '互联网金融' },
                      { value: '典当', label: '典当' },
                      { value: '保理', label: '保理' },
                      { value: '担保', label: '担保' },
                      { value: '融资租赁', label: '融资租赁' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实际控制人新增控股子公司涉金融-国标行业',
                comment: '命中：近3个月 实际控制人， 新增对外投资 行业为 J，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.companyIndustry,
                    fieldValue: ['J'],
                    accessScope: 2,
                    options: [{ value: 'J', label: '金融业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实际控制人新增控股子公司涉金融-企查查行业',
                comment: '命中：近3个月 实际控制人， 新增对外投资 行业为 企查查行业code，且成为控股股东 xx条动态',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.qccIndustry,
                    fieldValue: ['29'],
                    accessScope: 2,
                    options: [{ value: '29', label: '金融业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人新增子公司-行业不一致',
        '新增控股子公司中，大多数的行业与本公司行业不一致',
        11,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '新增控股子公司中，大多数的行业与本公司行业不一致（国标行业）',
                comment: '命中：近12个月 实际控制人， 新增控股子公司中，大多数的行业与本公司行业不一致',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [12],
                    accessScope: 2,
                    options: [{ unit: '月', min: 1, max: 12 }],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.industryThreshold,
                    fieldValue: [1],
                    accessScope: 2,
                    options: [
                      { label: '新增控股子公司中，大多数的行业与本公司行业不一致(国标行业)', value: 1 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业一致(国标行业)', value: 0 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业不一致(企查查行业)', value: 2 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业一致(企查查行业)', value: 3 },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '新增控股子公司中，大多数的行业与本公司行业不一致（企查查行业）',
                comment: '命中：近12个月 实际控制人， 新增控股子公司中，大多数的行业与本公司行业不一致',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17, 203],
                    options: [
                      { value: 17, label: '对外投资' },
                      { value: 203, label: '对外投资(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [12],
                    accessScope: 2,
                    options: [{ unit: '月', min: 1, max: 12 }],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 2, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.industryThreshold,
                    fieldValue: [2],
                    accessScope: 2,
                    options: [
                      { label: '新增控股子公司中，大多数的行业与本公司行业不一致(国标行业)', value: 1 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业一致(国标行业)', value: 0 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业不一致(企查查行业)', value: 2 },
                      { label: '新增控股子公司中，大多数的行业与本公司行业一致(企查查行业)', value: 3 },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      // 实际控制人近3个月内，对外投资的企业，控股比例大于50的，并且企业注册时间在三个月内的，实缴资本小于注册资本的
      createMetric(
        manager,
        user,
        g1.groupId,
        '实控人控制企业集中注册且实缴异常',
        '实际控制人的控制企业集中注册且实缴异常',
        12,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '实际控制人的控制企业集中注册且无实缴资本',
                comment: '实际控制人的控制企业集中注册且无实缴资本',
                dimKey: DimensionTypeEnums.ControllerCompany,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.registerDate,
                    fieldValue: [90],
                    accessScope: 2,
                    options: [{ unit: '天', min: 1, max: 365 }],
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.percentTotal,
                    fieldValue: [50],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.realRegistrationError,
                    fieldValue: [1],
                    accessScope: 2,
                    options: RealRegistrationErrorMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.hitCount,
                    fieldValue: [2],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '实际控制人的控制企业集中注册且当前企业的注册资本大于母公司(向上一层)的实缴资本',
                comment: '实际控制人的控制企业集中注册且当前企业的注册资本大于母公司(向上一层)的实缴资本',
                dimKey: DimensionTypeEnums.ControllerCompany,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.registerDate,
                    fieldValue: [90],
                    accessScope: 2,
                    options: [{ unit: '天', min: 1, max: 365 }],
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.percentTotal,
                    fieldValue: [50],
                    accessScope: 2,
                    options: [{ unit: '%', min: 0, max: 100 }],
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.realRegistrationError,
                    fieldValue: [2],
                    accessScope: 2,
                    options: RealRegistrationErrorMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.hitCount,
                    fieldValue: [2],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          isSameMetricStrategy: 1,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '注册资本变更',
        '注册资本减少',
        13,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '发生减资',
                comment: '命中【1】riskCategories=37, 【2】是否是币种变更(Extend1.T)currencyChange=0, 【3】注册资本变更趋势减少(ChangeExtend.T=1)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [37],
                    options: [{ value: 37, label: '注册资本' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.currencyChange,
                    fieldValue: [0],
                    options: CurrencyChangeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
                    fieldValue: [1],
                    options: RegisCapitalTrendMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '法代变更',
        '法代变更',
        14,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业负责人变更',
                comment:
                  '命中：【1】riskCategories=39， 【2】企业负责人layTypes(ChangeExtend.C)=[法定代表人,执行事务合伙人,负责人,经营者,投资人,董事长,理事长,代表人]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [72],
                    options: [{ value: 72, label: '成员变更' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.layTypes,
                    fieldValue: [1],
                    options: LayTypeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '重要高管变更',
        '重要高管变更',
        15,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '重要高管变更',
                comment: '命中：【1】riskCategories=46',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '红牌环保处罚',
        '红牌环保处罚',
        0,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增红牌环保处罚',
                comment: '命中 【1】riskCategories=22',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishRedCard,
                    fieldValue: [1],
                    options: [
                      { label: '非红牌处罚', value: 0 },
                      { label: '是红牌处罚', value: 1 },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增红牌行政处罚',
                comment: '命中 【1】riskCategories=107',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishRedCard,
                    fieldValue: [1],
                    options: [
                      { label: '非红牌处罚', value: 0 },
                      { label: '是红牌处罚', value: 1 },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '其他环保处罚',
        '新增其他环保处罚,仅罚款且罚款金额较小',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '新增其他环保处罚',
                comment: '仅罚款且罚款金额较小',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0903'],
                    options: [{ label: '罚款', value: '0903', esCode: 'A003' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    // 罚款金额（单位：万元）
                    fieldKey: DimensionFieldKeyEnums.penaltiesAmount,
                    fieldValue: [[0, 200]],
                    options: [{ label: '200万元以下', value: [0, 200] }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '行政处罚',
        '新增行政处罚',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName:
                  '处罚类别为：吊销许可证/执照、责令关闭、移送司法机关、责令停业停产、行政拘留、限制从业、暂扣许可证件、降低资质等级、限制开展生产经营活动、经营异常名录',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['1', '3', '5', '6', '7', '8', '12', '13', '14', '9907'],
                    options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0906', '0907', '0909', '0912'],
                    options: [
                      { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
                      { label: '责令关闭', value: '0911', esCode: 'A011' },
                      { label: '移送司法机关', value: '0915', esCode: 'A015' },
                      { label: '责令停产停业', value: '0910', esCode: 'A010' },
                      { label: '限制从业', value: '0913', esCode: 'A013' },
                      { label: '行政拘留', value: '0914', esCode: 'A014' },
                      { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
                      { label: '降低资质等级', value: '0907', esCode: 'A007' },
                      { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
                      { label: '经营异常名录', value: '0912', esCode: 'A012' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增证监会处罚',
        '新增证监会处罚',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '被证监会行政处罚',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['2'],
                    options: [
                      ...ProcessingAgencyLevelOneMap,
                      { label: '农业农村部', value: '9907' },
                      { label: '中国证券监督管理委员会', value: '16' },
                      { label: '证监局', value: '17' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '实控人新增证监会处罚',
        '实控人新增证监会处罚',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人被证监会行政处罚',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['2'],
                    options: [
                      ...ProcessingAgencyLevelOneMap,
                      { label: '农业农村部', value: '9907' },
                      { label: '中国证券监督管理委员会', value: '16' },
                      { label: '证监局', value: '17' },
                    ],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107, 238],
                    options: [
                      { value: 107, label: '行政处罚' },
                      { value: 238, label: '行政处罚(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增交易所处罚',
        '新增交易所处罚',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '上市公司被交易所行政处罚',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['4'],
                    options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '实控人新增交易所处罚',
        '实控人新增交易所处罚',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人上市公司被交易所行政处罚',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['4'],
                    options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107, 238],
                    options: [
                      { value: 107, label: '行政处罚' },
                      { value: 238, label: '行政处罚(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增税务处罚',
        '新增行政处罚罚款XXX万元以上',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '处罚类型：罚款0万元以上、发布单位：税务局',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.E, 【3】处罚单位：ChangeExtend.A 税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
                    accessScope: 2,
                    fieldValue: ['5'],
                    options: [...ProcessingAgencyLevelOneMap],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0903'],
                    options: [{ label: '罚款', value: '0903', esCode: 'A003' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishAmount,
                    fieldValue: [0],
                    options: [{ unit: '元', min: 0, max: ********999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '税务非正常户',
        '税务非正常户',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '列入税务非正常户',
                comment: '命中 【1】riskCategories=117, 【2】处罚单位：ChangeExtend.A 税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [5],
                    options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [117],
                    options: [{ value: 117, label: '被列入税务非正常户' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增欠税公告',
        '新增欠税公告',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增欠税公告',
                comment: '命中 【1】riskCategories=31',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 2,
                    fieldValue: ['5'],
                    options: [...ProcessingAgencyLevelOneMap, { label: '农业农村部', value: '9907' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [31],
                    options: [{ value: 31, label: '欠税公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishAmount,
                    fieldValue: [0],
                    options: [{ unit: '元', min: 0, max: ********999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '纳税人资质状态变化',
        '纳税人资质状态变化',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '新增纳税人资质状态变化',
                comment: '纳税人资质状态变化',
                dimKey: DimensionTypeEnums.TaxpayerCertificationChange,
                fields: [],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Persistent,
          allowMultipleDynamics: false,
          allowRepeatedHits: false,
          isSameMetricStrategy: 1,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '新增民事类非金融涉诉案件',
        '民事案件',
        0,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '立案信息' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
                    fieldValue: [0],
                    options: [{ unit: '万元', min: 0, max: ******** }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增诉前调解',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90],
                    options: [{ value: 90, label: '诉前调解' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增送达公告',
                comment: '命中riskCategories=27',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27],
                    options: [{ value: 27, label: '送达公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增法院公告',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '实控人新增民事类非金融涉诉案件',
        '实控人新增民事类非金融涉诉案件',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49, 220],
                    options: [
                      { value: 49, label: '立案信息' },
                      { value: 220, label: '立案信息(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18, 219],
                    options: [
                      { value: 18, label: '开庭公告' },
                      { value: 219, label: '开庭公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4, 221],
                    options: [
                      { value: 4, label: '裁判文书' },
                      { value: 221, label: '裁判文书(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
                    fieldValue: [0],
                    options: [{ unit: '万元', min: 0, max: ******** }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增诉前调解',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90, 232],
                    options: [
                      { value: 90, label: '诉前调解' },
                      { value: 232, label: '诉前调解(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增送达公告',
                comment: '命中riskCategories=27',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27, 217],
                    options: [
                      { value: 27, label: '送达公告' },
                      { value: 217, label: '送达公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增法院公告',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7, 218],
                    options: [
                      { value: 7, label: '法院公告' },
                      { value: 218, label: '法院公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [0],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isContractDispute,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '是合同纠纷' },
                      { value: 0, label: '非合同纠纷' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '新增民事类金融涉诉案件',
        '新增民事类金融涉诉案件',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '立案信息' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
                    fieldValue: [0],
                    options: [{ unit: '万元', min: 0, max: ******** }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增诉前调解',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90],
                    options: [{ value: 90, label: '诉前调解' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增送达公告',
                comment: '命中riskCategories=27',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27],
                    options: [{ value: 27, label: '送达公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增法院公告',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '实控人新增民事类金融涉诉案件',
        '实控人新增民事类金融涉诉案件',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49, 220],
                    options: [
                      { value: 49, label: '立案信息' },
                      { value: 220, label: '立案信息(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18, 219],
                    options: [
                      { value: 18, label: '开庭公告' },
                      { value: 219, label: '开庭公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4, 221],
                    options: [
                      { value: 4, label: '裁判文书' },
                      { value: 221, label: '裁判文书(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
                    fieldValue: [0],
                    options: [{ unit: '万元', min: 0, max: ******** }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增诉前调解',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90, 232],
                    options: [
                      { value: 90, label: '诉前调解' },
                      { value: 232, label: '诉前调解(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 4,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增送达公告',
                comment: '命中riskCategories=27',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27, 217],
                    options: [
                      { value: 27, label: '送达公告' },
                      { value: 217, label: '送达公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 5,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '实控人新增法院公告',
                comment: '命中riskCategories=90',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7, 218],
                    options: [
                      { value: 7, label: '法院公告' },
                      { value: 218, label: '法院公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
                    fieldValue: [1],
                    options: [
                      { label: '非金融涉诉', value: 0 },
                      { label: '是金融涉诉', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
                    fieldValue: [1],
                    options: [
                      { label: '原告非金融租赁或者银行', value: 0 },
                      { label: '原告是金融租赁或者银行', value: 1 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['ms'],
                    options: [{ label: '民事案件', value: 'ms', caseReasonValue: 'B' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '破产重整',
        '企业被申请破产重整',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '被申请破产重整',
                comment: '命中 【1】riskCategories=58',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [58],
                    options: [{ value: 58, label: '破产重整' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '新增刑事案件',
        '新增刑事案件',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增刑事立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '立案信息' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增刑事开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增刑事判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增刑事法院公告',
                comment: '命中riskCategories=7',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '实控人新增刑事案件',
        '实控人新增刑事案件',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人新增刑事立案信息',
                comment: '命中riskCategories=49',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49, 220],
                    options: [
                      { value: 49, label: '立案信息' },
                      { value: 220, label: '立案信息(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人新增刑事法院公告',
                comment: '命中riskCategories=7',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7, 218],
                    options: [
                      { value: 7, label: '法院公告' },
                      { value: 218, label: '法院公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人新增刑事开庭公告',
                comment: '命中riskCategories=18',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18, 219],
                    options: [
                      { value: 18, label: '开庭公告' },
                      { value: 219, label: '开庭公告(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 3,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人新增刑事判决文书',
                comment: '命中riskCategories=4',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4, 221],
                    options: [
                      { value: 4, label: '裁判文书' },
                      { value: 221, label: '裁判文书(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: ['A0248', 'A0249'],
                    options: [
                      { label: '交通肇事罪', value: 'A0248' },
                      { label: '危险驾驶罪', value: 'A0249' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseType,
                    fieldValue: ['xs'],
                    options: [{ label: '刑事案件', value: 'xs', caseReasonValue: 'A' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '新增限制高消费',
        '自身未履行法定义务；法定代表人未履行法定义务',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '自身未履行法定义务',
                comment: '命中 【1】riskCategories=55, 【2】Restricter (ChangeExtend.C = 企业本身，ChangeExtend.E=法人代表)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [55],
                    options: [{ value: 55, label: '限制高消费' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.restricterType,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '企业本身' },
                      //{ value: 2, label: '法人代表' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '实控人新增限制高消费',
        '实控人新增限制高消费',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人自身未履行法定义务',
                comment: '命中 【1】riskCategories=55, 【2】Restricter (ChangeExtend.C = 企业本身，ChangeExtend.E=法人代表)',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [55, 208],
                    options: [
                      { value: 55, label: '限制高消费' },
                      { value: 208, label: '限制高消费(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.restricterType,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '企业本身' },
                      //{ value: 2, label: '法人代表' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '新增被执行人或失信被执行人',
        '刑事案件',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增被执行人',
                comment: '命中riskCategories=3',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [3],
                    options: [{ value: 3, label: '被执行人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业成为失信被执行人',
                comment: '命中riskCategories=2',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [2],
                    options: [{ value: 2, label: '失信被执行人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '实控人新增被执行人或失信被执行人',
        '实控人新增被执行人或失信被执行人',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人新增被执行人',
                comment: '命中riskCategories=3',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [3, 206],
                    options: [
                      { value: 3, label: '被执行人' },
                      { value: 206, label: '被执行人(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '实控人成为失信被执行人',
                comment: '命中riskCategories=2',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [2, 205],
                    options: [
                      { value: 2, label: '失信被执行人' },
                      { value: 205, label: '失信被执行人(人员)' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g6.groupId,
        '司法拍卖',
        '企业涉及司法拍卖',
        11,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业涉及司法拍卖',
                comment: "命中 【1】riskCategories=57， 【2】拍卖类型：新增${changeInfo.Q === 1 ? '破产' : '司法'}拍卖",
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [57],
                    options: [{ value: 57, label: '司法拍卖' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.auctionType,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '破产拍卖' },
                      { value: 2, label: '司法拍卖' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.listingPrice,
                    fieldValue: [],
                    options: [{ unit: '元', min: 0, max: ********, label: '起拍价（元）' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期净利润小于0',
        '近一期净利润小于0',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '近一期的净利润小于0',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 净利润 =0',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.netProfitAmount,
                    fieldValue: [0],
                    options: [{ unit: '元', min: 0, max: ******** }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的收入或净利润同比<=30%',
        '近一期的收入或净利润同比<=30%',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '近一期的营业收入同比 <= 30%',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 营业收入同比 < 30%',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.revenueRatio,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                ],
              },
              {
                dimStrategyName: '近一期的净利润同比 <= 30%',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202 净利润同比 <= 30%',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.netProfitRatio,
                    fieldValue: [30],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的应收账款同比>=20%',
        '财务变动',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的应收账款同比>=20%',
                comment:
                  '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202， 资产负债-应收账款/资产负债-应收账款(去年同期)>=20 : 其中 应收账款有值取应收账款没有值取应收票据及应收账款',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.AccountsReceivableRatio,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的存货同比>=20%',
        '财务变动',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的存货同比>=20%',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202，资产负债-存货/资产负债-存货(去年同期)>=20',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.inventoryRatio,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的有息负债同比>=20%',
        '财务变动',
        5,
        [
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的有息负债同比>=20%',
                comment:
                  '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202, 资产负债-有息负债/资产负债-有息负债(去年同期)>=20； 其中有利负债的定义：短期借款+应付票据+一年内到期非流动负债+长期借款+应付债券',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.interestBearingLiabilitiesRatio,
                    fieldValue: [20],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的有息负债/年度收入（或近期报表收入/12*近期报表月份）>=40%',
        '近一期的有息负债/年度收入（或近期报表收入/12*近期报表月份）>=40%',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的有息负债/年度收入（或近期报表收入/12*近期报表月份）>=40%',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202, 有息负债/年度收入',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.ibdAnnualRevRatio,
                    fieldValue: [40],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期资债比',
        '近一期资债比',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的（货币资金+交易性金融资产）/（短期借款+应付票据）<1',
                comment:
                  '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202, （货币资金+交易性金融资产(衍生金融资产)）/（短期借款+应付票据）',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cmAndStbRatio,
                    fieldValue: [100],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '近一期的负债合计/资产合计>=80%',
        '近一期的负债合计/资产合计>=80%',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '近一期的负债合计/资产合计>=80%',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202, 负债合计/资产合计',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.totalLiabToAssetsRatio,
                    fieldValue: [80],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g7.groupId,
        '连续三年经营性净现金流均<0',
        '连续三年经营性净现金流均<0',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '连续三年经营性净现金流均<0',
                comment: '命中条件：企业公告riskCategories=113，年报类型annualReportType = 203，204，202, 负经营性净现金流 <0',
                dimKey: DimensionTypeEnums.ListedEntityRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [113],
                    options: [{ value: 113, label: '企业公告' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.StockControlCompany],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.annualReportType,
                    fieldValue: [203, 204, 202],
                    options: [
                      { value: 203, label: '年报' },
                      { value: 204, label: '季报' },
                      { value: 202, label: '半年报' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.cashFlowFromActivitiesAmount,
                    fieldValue: [
                      {
                        consecutiveYearCount: 3,
                        cashFlowFromActivitiesAmount: 0,
                        cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.LessThan,
                      },
                    ],
                    options: [
                      {
                        yearScope: { label: '连续X年', value: 3 },
                        cashFlowFromActivitiesAmount: { unit: '元', min: 0, max: ******** },
                        cashFlowFromActivitiesAmountCompareType: {
                          label: '占比比较(大于/小于)',
                          value: DimensionFieldCompareTypeEnums.LessThan,
                        },
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),

      /*createMetric(
        manager,
        user,
        g8.groupId,
        '资产查冻',
        '资产查冻',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '新增主体资产查冻扣',
                comment: '命中条件：【1】数据范围有效',
                dimKey: DimensionTypeEnums.AssetInvestigationAndFreezing,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),*/

      createMetric(
        manager,
        user,
        g5.groupId,
        '上市进度',
        '上市进度',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '主体企业上市进程',
                comment: '命中条件：上市进程 riskCategories=129, ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [129],
                    options: [{ value: 129, label: '上市进程' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '股权质押或出质',
        '股权质押或出质',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '公司本身新增股权质押',
                comment: '命中 【1】riskCategories=50； ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '大股东新增股权质押',
                comment: '命中 【1】riskCategories=50；',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50, 214],
                    options: [
                      { value: 50, label: '股权质押' },
                      { value: 214, label: '股权质押（人）' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.MajorShareholder],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.MajorShareholder, label: '大股东' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实控人新增股权质押',
                comment: '命中 【1】riskCategories=50',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50, 214],
                    options: [
                      { value: 50, label: '股权质押' },
                      { value: 214, label: '股权质押（人）' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '公司本身新增股权出质',
                comment: '命中 【1】riskCategories=50； ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '大股东新增股权出质',
                comment: '命中 【1】riskCategories=50；',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12, 213],
                    options: [
                      { value: 12, label: '股权出质' },
                      { value: 213, label: '股权出质（人）' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.MajorShareholder],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.MajorShareholder, label: '大股东' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实控人新增股权出质',
                comment: '命中 【1】riskCategories=50；',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12, 213],
                    options: [
                      { value: 12, label: '股权出质' },
                      { value: 213, label: '股权出质（人）' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '舆情',
        '舆情',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '实控人负面/正面新闻',
                comment: '命中：【1】riskCategories=[中立：66，消极：67，积极：62], 【2】 主体身份是实控人',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [66, 67, 62],
                    options: [
                      { value: 66, label: '中立' },
                      { value: 67, label: '消极' },
                      { value: 62, label: '积极' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    accessScope: 2,
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    // 新闻主体类型： 负面/正面新闻
                    fieldKey: DimensionFieldKeyEnums.topics,
                    fieldValue: ['all'],
                    accessScope: 2,
                    options: NegativePositiveTopicTypes,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '主体负面/正面新闻',
                comment: '命中：【1】riskCategories=[中立：66，消极：67，积极：62], 【2】 主体身份是公司主体',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [66, 67, 62],
                    options: [
                      { value: 66, label: '中立' },
                      { value: 67, label: '消极' },
                      { value: 62, label: '积极' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    // 新闻主体类型： 负面/正面新闻
                    fieldKey: DimensionFieldKeyEnums.topics,
                    fieldValue: ['all'],
                    accessScope: 2,
                    options: NegativePositiveTopicTypes,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增股权融资',
        '新增股权融资',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业获得融资',
                comment: '命中riskCategories=28',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [28],
                    options: [{ value: 28, label: '融资动态' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增动产抵押',
        '新增动产抵押',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '新增动产抵押',
                comment: '命中条件：【1】数据范围有效',
                dimKey: DimensionTypeEnums.PledgeMerger,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '新增担保',
        '新增担保',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '公司主体新增担保信息',
                comment: '命中riskCategories=53,101',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [53, 101],
                    options: [
                      { value: 53, label: '担保信息' },
                      { value: 101, label: '担保信息' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '实控人新增担保信息',
                comment: '命中riskCategories=53,101',
                dimKey: DimensionTypeEnums.ActualControllerRiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [243],
                    options: [{ value: 243, label: '担保信息' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [RelatedTypeEnums.ActualController],
                    options: [{ value: RelatedTypeEnums.ActualController, label: '实际控制人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.PrimaryCompnay,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g5.groupId,
        '授权发明专利转出',
        '授权发明专利转出',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '授权发明专利转出',
                comment: '命中条件：【1】数据范围近一年,【2】是历史专利，【3】发明专利 【4】状态：授权',
                dimKey: DimensionTypeEnums.PatentInfo,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.cycle,
                    fieldValue: [1],
                    options: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isHistoryPatent,
                    fieldValue: [1],
                    options: IsHistoryPatentConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentType,
                    fieldValue: ['1', '2'],
                    options: PatentTypeConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.patentStatus,
                    fieldValue: ['ZT002001'],
                    options: PatentStatusConstant,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
          target: DiligenceTargetTypeEnum.All,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
    ]);
    // 4. 分发模型给租户
    await this.riskModelService.distributeRiskModel(
      {
        riskModelId: riskModel.modelId,
        orgId: toOrgId,
      },
      user,
    );
    return riskModel;
  }
}
