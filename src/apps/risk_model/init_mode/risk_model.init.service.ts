import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { PlatformUser } from '../../../libs/model/common';
import { RiskModelService } from '../risk_model.service';
import * as Bluebird from 'bluebird';
import { createDimensionFields, createGroup, createMetric } from '../../test_utils_module/dimension.test.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import { TargetInvestigationEnums } from '../../../libs/enums/dimension/FieldValueEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { pick } from 'lodash';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { NebulaRelatedEdgeEnums } from '../../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { BaseDimensionDefinitionForGenerating } from '../../../libs/constants/dimension.constants';
import { CreateDimensionPO } from '../../dimension/po/CreateDimensionPO';
import { CreateDimensionFieldPO } from '../../dimension/po/CreateDimensionFieldPO';
import { DimensionService } from '../../dimension/dimension.service';
import { DimensionFieldsService } from '../../dimension/dimension.fields.service';
import { StrategyRoleEnums } from '../../../libs/enums/StrategyRoleEnums';
import { AllTopicTypes } from '../../../libs/constants/news.constants';

/**
 * 汇添富尽调模型初始化服务
 */
@Injectable()
export class RiskModelInitService {
  private logger: Logger = QccLogger.getLogger(RiskModelInitService.name);

  constructor(
    private readonly riskModelService: RiskModelService,
    private readonly dimensionService: DimensionService,
    private readonly dimensionFieldsService: DimensionFieldsService,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
  ) {}
  /**
   * 手动创建汇添富模型
   * @param user 创建模型用户
   * @param modelName  模型名称
   * @param toOrgId  分发给的orgId
   * @param modelType
   * @returns
   */
  async createHTFModel(
    user: PlatformUser,
    modelName: string,
    toOrgId?: number,
    modelType: RiskModelTypeEnums = RiskModelTypeEnums.GeneralRiskModel,
  ): Promise<RiskModelEntity> {
    // 1. 创建风险模型
    const riskModel = await this.riskModelService.addRiskModel(
      {
        modelName,
        product: user.currentProduct,
        comment: modelName,
        status: DataStatusEnums.Developing,
        modelType,
      },
      user,
    );

    const manager = this.riskModelRepo.manager;

    // 2. 初始化维度，维度属性
    await createDimensionFields(manager, user);

    // 3. 创建分组
    const [g1, g2, g3, g4, g7, g8, g5, g6] = await Bluebird.all([
      createGroup(manager, user, riskModel.modelId, '法律风险', 1),
      createGroup(manager, user, riskModel.modelId, '行政处罚风险', 2),
      createGroup(manager, user, riskModel.modelId, '经营合规性风险', 3),
      createGroup(manager, user, riskModel.modelId, '经营稳定性风险', 4),
      createGroup(manager, user, riskModel.modelId, '关联企业风险', 5),
      createGroup(manager, user, riskModel.modelId, '黑名单', 6),
      createGroup(manager, user, riskModel.modelId, '国别风险', 7),
      createGroup(manager, user, riskModel.modelId, '企业资质正向激励', 8),
    ]);

    // 4. 给分组内添加指标
    await Bluebird.all([
      // 法律风险
      createMetric(manager, user, g1.groupId, '曾发生刑事案件', '目标企业近三年曾发生过刑事案件', 1, [
        {
          order: 0,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '裁判文书刑事案件(非交通肇事罪)的且是被告的企业，数据范围不限,统计周期近5年',
              dimKey: DimensionTypeEnums.Judgement,
              fields: [
                // 案件类型 刑事案件
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  accessScope: 2,
                  fieldValue: ['xs'],
                  options: [{ label: '刑事案件', value: 'xs' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                // 案件身份 被告
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
                  fieldValue: ['defendant'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 近5年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                // 数据范围 不限
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '公安通告涉嫌非法吸收公众存款或非法集资的企业', '目标企业存在涉嫌非法吸收公众存款或诈骗相关通告', 2, [
        {
          order: 0,
          maxScore: 80,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 公安通告 数据范围不限， 统计周期不限',
              dimKey: DimensionTypeEnums.SecurityNotice,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '法定代表人失信风险', '法定代表人失信风险', 3, [
        // 当前法人 + (失信被执行人/限制高消费/限制出境)
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.High,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '失信被执行人',
              comment: '命中  当前法人 + 失信被执行人',
              dimKey: DimensionTypeEnums.PersonCreditCurrent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '限制高消费',
              comment: '命中  当前法人 + 限制高消费',
              dimKey: DimensionTypeEnums.RestrictedConsumptionCurrent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '限制出境',
              comment: '命中  当前法人 + 限制出境',
              dimKey: DimensionTypeEnums.MainMembersRestrictedOutbound,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        // 历史法人 + (失信被执行人/限制高消费/限制出境)
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '失信被执行人',
              comment: '命中  历史法人 + 失信被执行人',
              dimKey: DimensionTypeEnums.PersonCreditCurrent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '限制高消费',
              comment: '命中  历史法人 + 限制高消费',
              dimKey: DimensionTypeEnums.RestrictedConsumptionCurrent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '限制出境',
              comment: '命中  历史法人 + 限制出境',
              dimKey: DimensionTypeEnums.MainMembersRestrictedOutbound,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        // 当前法人 + (失信被执行人/限制高消费/限制出境) + 近5年 + 失效
        {
          order: 2,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '历史失信被执行人',
              comment: '命中  当前法人 + 历史失信被执行人 + 近3年',
              dimKey: DimensionTypeEnums.PersonCreditHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '历史限制高消费',
              comment: '命中  当前法人 + 历史限制高消费 + 近3年',
              dimKey: DimensionTypeEnums.RestrictedConsumptionHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '历史限制出境',
              comment: '命中  当前法人 + 限制出境 + 近3年 + 失效',
              dimKey: DimensionTypeEnums.MainMembersRestrictedOutbound,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Legal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  accessScope: 1,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        // 历史法人 + (失信被执行人/限制高消费/限制出境) + 近5年 + 失效
        {
          order: 3,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '历史失信被执行人',
              comment: '命中  历史法人 + 历史失信被执行人 + 近5年',
              dimKey: DimensionTypeEnums.PersonCreditHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '历史限制高消费',
              comment: '命中  历史法人 + 历史限制高消费 + 近3年',
              dimKey: DimensionTypeEnums.RestrictedConsumptionHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '历史限制出境',
              comment: '命中  历史法人 + 限制出境 + 近3年 + 失效',
              dimKey: DimensionTypeEnums.MainMembersRestrictedOutbound,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisLegal],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  accessScope: 1,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishdate', order: 'DESC', fieldSnapshot: 'PublishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '被列入失信被执行人', '被列入失信被执行人', 4, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              dimStrategyName: '失信被执行人',
              comment: '企业本身 被列入失信被执行人 PersonCreditCurrent',
              dimKey: DimensionTypeEnums.PersonCreditCurrent,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Self],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '失信被执行人（历史）',
              comment: '命中 近三年历史失信被执行人 PersonCreditHistory',
              dimKey: DimensionTypeEnums.PersonCreditHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.Self],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 近5年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'pubdate', order: 'DESC', fieldSnapshot: 'PublicDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '税收违法', '税收违法', 5, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              comment: '命中 税收违法 统计周期不限, 数据范围不限',
              dimKey: DimensionTypeEnums.TaxationOffences,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              comment: '命中 税务处罚, 统计周期不限, 数据范围不限',
              dimKey: DimensionTypeEnums.TaxPenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '案件风险', '案件风险', 6, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 裁判文书 统计周期：近3年, 数据范围：不限',
              dimKey: DimensionTypeEnums.Judgement,
              fields: [
                // 统计周期 近3年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                // 数据范围 不限
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  accessScope: 2,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '案件风险（历史）', '案件风险', 7, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 裁判文书 统计周期：3年前, 数据范围：不限',
              dimKey: DimensionTypeEnums.Judgement,
              fields: [
                // 统计周期 3年前
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                // 数据范围 不限
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  accessScope: 2,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '开庭公告', '判断目标企业自身开庭公告', 8, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '排除企业状态 停业、撤销、注销、吊销、歇业、责令关闭 ',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['70', '80', '99', '90', '75', '85'],
                  accessScope: 2,
                  options: [
                    { name: '停业', value: '70' },
                    { name: '撤销', value: '80' },
                    { name: '注销', value: '99' },
                    { name: '吊销', value: '90' },
                    { name: '歇业', value: '75' },
                    { name: '责令关闭', value: '85' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
            {
              comment: '命中 开庭公告  近3年 数据范围有效 ',
              dimKey: DimensionTypeEnums.CourtSessionAnnouncement,
              fields: [
                // 统计周期 近3年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                // 数据范围 有效
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'liandate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '开庭公告（补充）', '判断目标企业自身开庭公告', 9, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '排除企业状态 停业、撤销、注销、吊销、歇业、责令关闭 ',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['70', '80', '99', '90', '75', '85'],
                  accessScope: 2,
                  options: [
                    { name: '停业', value: '70' },
                    { name: '撤销', value: '80' },
                    { name: '注销', value: '99' },
                    { name: '吊销', value: '90' },
                    { name: '歇业', value: '75' },
                    { name: '责令关闭', value: '85' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
            {
              dimStrategyName: '开庭公告（补充）',
              comment: '命中 开庭公告 3年前 数据范围无效 ',
              dimKey: DimensionTypeEnums.CourtSessionAnnouncement,
              fields: [
                // 统计周期 近3年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                // 数据范围 无效
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'liandate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '审查调查', '该企业主要人员有执纪审查或党纪政务处分信息', 10, [
        {
          order: 0,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '该企业主要人员有执纪审查或党纪政务处分信息',
              comment: '命中 审查调查 ',
              dimKey: DimensionTypeEnums.ReviewAndInvestigation,
              fields: [
                {
                  // 此次增加的维度属性，仅仅为了页面能展示dimStrategyName, 无数据处理
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  accessScope: 3,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g1.groupId, '被列入被执行人', '被列入被执行人', 11, [
        {
          order: 0,
          maxScore: 30,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被执行人',
              comment: '命中 PersonExecution 被执行人信息 > 2 次 数据有效',
              dimKey: DimensionTypeEnums.PersonExecution,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                /* {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },*/
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被执行人',
              comment: '命中 PersonExecution 被执行人信息 <= 2 次 数据有效',
              dimKey: DimensionTypeEnums.PersonExecution,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                /*{
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },*/
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被执行人（历史）',
              comment: '命中 被执行人（历史） > 2 次 数据无效',
              dimKey: DimensionTypeEnums.PersonExecution,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                /*{
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },*/
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被执行人（历史）',
              comment: '命中 被执行人（历史） <= 2 次 数据无效',
              dimKey: DimensionTypeEnums.PersonExecution,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                /*{
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },*/
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LiAnDate' }],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '被列入严重违法失信企业名录', '被列入严重违法失信企业名录', 12, [
        {
          order: 0,
          maxScore: 80,
          riskLevel: DimensionRiskLevelEnum.High,
          operation: 'should',
          dimsFields: [
            {
              comment: '被列入严重违法失信企业名录 CompanyCredit ',
              dimKey: DimensionTypeEnums.CompanyCredit,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'AddDate', order: 'DESC', fieldSnapshot: 'AddDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              comment: '近3年 被列入严重违法失信企业名录（历史） CompanyCreditHistory  ',
              dimKey: DimensionTypeEnums.CompanyCreditHistory,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'AddDate', order: 'DESC', fieldSnapshot: 'AddDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '被列入经营异常名录', '被列入经营异常名录(未移出)', 13, [
        {
          order: 0,
          maxScore: 20,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 被列入经营异常名录',
              dimKey: DimensionTypeEnums.BusinessAbnormal3,
              fields: [
                {
                  // 经营异常类型
                  fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
                  fieldValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 周期不限
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  // 排序
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [
                    {
                      field: 'occurrencedate',
                      order: 'DESC',
                      fieldSnapshot: 'CurrenceDate',
                    },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '被列入经营异常名录（历史）', '多次被列入经营异常名录(当下未列入)', 14, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 被列入经营异常名录（历史） 近5年  >0次',
              dimKey: DimensionTypeEnums.OperationAbnormal,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  // 周期
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'occurrencedate', order: 'DESC', fieldSnapshot: 'CurrenceDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '被列入税务非正常户', '企业被列入税收非正常户', 18, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '被列入税务非正常户',
              comment: '命中 被列入非正常户',
              dimKey: DimensionTypeEnums.TaxUnnormals,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '监管处罚', '监管处罚', 19, [
        {
          order: 0,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '排除企业状态 停业、撤销、注销、吊销、歇业、责令关闭 ',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['70', '80', '99', '90', '75', '85'],
                  accessScope: 2,
                  options: [
                    { name: '停业', value: '70' },
                    { name: '撤销', value: '80' },
                    { name: '注销', value: '99' },
                    { name: '吊销', value: '90' },
                    { name: '歇业', value: '75' },
                    { name: '责令关闭', value: '85' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
            {
              dimStrategyName: '监管处罚',
              comment: '命中  行政处罚 有效+无效 近3年 数量>3次 + 处罚单位',
              dimKey: DimensionTypeEnums.AdministrativePenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 周期 近3年
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  // 处罚单位
                  fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
                  accessScope: 2,
                  fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
                  options: [
                    { label: '人民银行', value: '1' },
                    { label: '证监会', value: '2' },
                    { label: '金融监管局', value: '3' },
                    { label: '交易所', value: '4' },
                    { label: '发改委', value: '7' },
                    { label: '工信部', value: '8' },
                    { label: '财政部', value: '9' },
                    { label: '网信办', value: '10' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '排除企业状态 停业、撤销、注销、吊销、歇业、责令关闭 ',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['70', '80', '99', '90', '75', '85'],
                  accessScope: 2,
                  options: [
                    { name: '停业', value: '70' },
                    { name: '撤销', value: '80' },
                    { name: '注销', value: '99' },
                    { name: '吊销', value: '90' },
                    { name: '歇业', value: '75' },
                    { name: '责令关闭', value: '85' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
            {
              dimStrategyName: '监管处罚',
              comment: '命中  行政处罚 有效+无效 近3年 数量 ＜= 3次 + 处罚单位',
              dimKey: DimensionTypeEnums.AdministrativePenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 周期 近3年
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  // 处罚单位
                  fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
                  accessScope: 2,
                  fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
                  options: [
                    { label: '人民银行', value: '1' },
                    { label: '证监会', value: '2' },
                    { label: '金融监管局', value: '3' },
                    { label: '交易所', value: '4' },
                    { label: '发改委', value: '7' },
                    { label: '工信部', value: '8' },
                    { label: '财政部', value: '9' },
                    { label: '网信办', value: '10' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '监管处罚（历史）', '监管处罚', 20, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '排除企业状态 停业、撤销、注销、吊销、歇业、责令关闭 ',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['70', '80', '99', '90', '75', '85'],
                  accessScope: 2,
                  options: [
                    { name: '停业', value: '70' },
                    { name: '撤销', value: '80' },
                    { name: '注销', value: '99' },
                    { name: '吊销', value: '90' },
                    { name: '歇业', value: '75' },
                    { name: '责令关闭', value: '85' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
            {
              dimStrategyName: '监管处罚（历史）',
              comment: '命中  行政处罚 有效+无效 + 三年前 + 处罚单位',
              dimKey: DimensionTypeEnums.AdministrativePenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 周期 3年前
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                {
                  // 处罚单位
                  fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
                  accessScope: 2,
                  fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
                  options: [
                    { label: '人民银行', value: '1' },
                    { label: '证监会', value: '2' },
                    { label: '金融监管局', value: '3' },
                    { label: '交易所', value: '4' },
                    { label: '发改委', value: '7' },
                    { label: '工信部', value: '8' },
                    { label: '财政部', value: '9' },
                    { label: '网信办', value: '10' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '行政处罚', '行政处罚记录显示暂扣或吊销许可证/执照、疑似停产/停业整顿等', 21, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '行政处罚',
              comment: '命中 行政处罚  近3年， 停业、被责令停产、停产、暂扣、吊销许可证、吊销营业执照等信息即命中（复核）',
              dimKey: DimensionTypeEnums.AdministrativePenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.penaltiesType,
                  fieldValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909', '0999'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 处罚单位
                  fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
                  accessScope: 1,
                  fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
                  options: [
                    { label: '人民银行', value: '1' },
                    { label: '证监会', value: '2' },
                    { label: '金融监管局', value: '3' },
                    { label: '交易所', value: '4' },
                    { label: '发改委', value: '7' },
                    { label: '工信部', value: '8' },
                    { label: '财政部', value: '9' },
                    { label: '网信办', value: '10' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '行政处罚（历史）', '行政处罚记录显示暂扣或吊销许可证/执照、疑似停产/停业整顿等', 22, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '行政处罚（历史）',
              comment: '命中 行政处罚  3年前， 停业、被责令停产、停产、暂扣、吊销许可证、吊销营业执照等信息即命中（复核）',
              dimKey: DimensionTypeEnums.AdministrativePenalties,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                {
                  // 处罚类型
                  fieldKey: DimensionFieldKeyEnums.penaltiesType,
                  fieldValue: ['0908', '0911', '0915', '0910', '0914', '0913', '0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909', '0999'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  // 处罚单位
                  fieldKey: DimensionFieldKeyEnums.ProcessingAgencyLevelOne,
                  accessScope: 1,
                  fieldValue: ['1', '2', '3', '4', '7', '8', '9', '10'],
                  options: [
                    { label: '人民银行', value: '1' },
                    { label: '证监会', value: '2' },
                    { label: '金融监管局', value: '3' },
                    { label: '交易所', value: '4' },
                    { label: '发改委', value: '7' },
                    { label: '工信部', value: '8' },
                    { label: '财政部', value: '9' },
                    { label: '网信办', value: '10' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '工商抽查检查不合格', '工商抽查检查不合格', 26, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '工商抽查检查不合格',
              comment: '命中 抽查检查不合格 近3年 ',
              dimKey: DimensionTypeEnums.SpotCheck,
              fields: [
                {
                  // 周期
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [
                    {
                      field: 'publishdate',
                      order: 'DESC',
                      fieldSnapshot: 'PublishDate',
                    },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g2.groupId, '环保处罚', '环保处罚', 27, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 环保处罚 近5年 ',
              dimKey: DimensionTypeEnums.EnvironmentalPenalties,
              fields: [
                {
                  // 周期
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'punishdate', order: 'DESC', fieldSnapshot: 'PunishDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      // 经营合规性风险
      createMetric(manager, user, g3.groupId, '经营状态非存续', '经营状态非存续', 29, [
        {
          order: 0,
          maxScore: 80,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 经营状态非存续 BusinessAbnormal1',
              dimKey: DimensionTypeEnums.BusinessAbnormal1,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '简易注销', '简易注销', 30, [
        {
          order: 0,
          maxScore: 70,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 简易注销 BusinessAbnormal2',
              dimKey: DimensionTypeEnums.BusinessAbnormal2,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '注销备案', '注销备案', 31, [
        {
          order: 0,
          maxScore: 70,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 注销备案',
              dimKey: DimensionTypeEnums.CancellationOfFiling,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '经营期限已过有效期', '经营期限已过有效期', 32, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 经营期限已过有效期 BusinessAbnormal6',
              dimKey: DimensionTypeEnums.BusinessAbnormal6,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '企业存在涉诈风险', '该企业与相关高风险客户名单存在一定关联，请注意核实企业信息', 33, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              dimStrategyName: '企业存在涉诈风险',
              comment: '命中 专业版风险 code 1310',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['1310'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '支付/融资担保业务被中止或注销', '支付或者融资担保业务许可被中止续展或已到期', 35, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              dimStrategyName: '支付/融资担保业务被中止或注销',
              comment: '命中 专业版风险 code 1312',
              dimKey: DimensionTypeEnums.QfkRisk1312,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '假冒国企', '企业因假冒国企被公示', 36, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 假冒国企 FakeSOES',
              dimKey: DimensionTypeEnums.FakeSOES,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '疑似空壳企业', '疑似空壳企业', 37, [
        {
          order: 0,
          maxScore: 30,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 疑似空壳企业 CompanyShell',
              dimKey: DimensionTypeEnums.CompanyShell,
              fields: [],
            },
          ],
        },
      ]),
      // 分值调整到这里--------------------
      createMetric(manager, user, g3.groupId, '人员存在刑事案件', '人员存在刑事案件', 40, [
        {
          order: 0,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '人员存在刑事案件',
              comment: '命中 人员(董监高法、实控人、收益自然人)+裁判文书（刑事案件，不是交通肇事罪）',
              dimKey: DimensionTypeEnums.Judgement,
              fields: [
                // 排查对象 主要人员(在职)
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [
                    TargetInvestigationEnums.MainStaff,
                    TargetInvestigationEnums.Legal,
                    TargetInvestigationEnums.ActualController,
                    TargetInvestigationEnums.Benefit,
                  ],
                  options: [
                    { label: '董监高', value: TargetInvestigationEnums.MainStaff },
                    { label: '法定代表人', value: TargetInvestigationEnums.Legal },
                    { label: '实际控制人', value: TargetInvestigationEnums.ActualController },
                    { label: '受益自然人', value: TargetInvestigationEnums.Benefit },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案件类型 刑事案件
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  accessScope: 2,
                  fieldValue: ['xs'],
                  options: [{ label: '刑事案件', value: 'xs' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                // 案件身份 被告
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
                  fieldValue: ['defendant'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 不限
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                // 数据范围 不限
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '历史人员存在刑事案件',
              comment: '命中 历史董监高法 +裁判文书（刑事案件非交通肇事罪）+ 近3年',
              dimKey: DimensionTypeEnums.Judgement,
              fields: [
                // 排查对象
                {
                  fieldKey: DimensionFieldKeyEnums.targetInvestigation,
                  accessScope: 2,
                  fieldValue: [TargetInvestigationEnums.HisMainStaff, TargetInvestigationEnums.HisLegal],
                  options: [
                    { label: '历史董监高', value: TargetInvestigationEnums.HisMainStaff },
                    { label: '历史法定代表人', value: TargetInvestigationEnums.HisLegal },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案由类别 刑事案件
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  accessScope: 2,
                  fieldValue: ['xs'],
                  options: [{ label: '刑事案件', value: 'xs' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                // 案件身份 被告
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRoleInclude,
                  fieldValue: ['defendant'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 近三年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                // 数据范围 不限
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'courtdate', order: 'DESC', fieldSnapshot: 'courtdate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '人员存在违规处理事项', '人员存在违规处理事项', 40, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '人员存在违规处理',
              comment: '命中 人员(董监高法、股东、实控人、受益自然人)+ 违规处理 + 3年内',
              dimKey: DimensionTypeEnums.ViolationProcessings,
              fields: [
                // 与处罚主体关系类型
                {
                  fieldKey: DimensionFieldKeyEnums.relationShips,
                  fieldValue: ['1', '3', '5', '7', '9'],
                  options: [
                    { value: '1', label: '法定代表人' },
                    { value: '3', label: '董监高' },
                    { value: '5', label: '股东' },
                    { value: '7', label: '实际控制人' },
                    { value: '9', label: '受益自然人' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 近三年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publicdate', order: 'DESC', fieldSnapshot: 'publicdate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '历史人员存在违规处理',
              comment: '命中 人员(历史董监高法、股东)+ 违规处理 + 近3年',
              dimKey: DimensionTypeEnums.ViolationProcessings,
              fields: [
                // 与处罚主体关系类型
                {
                  fieldKey: DimensionFieldKeyEnums.relationShips,
                  fieldValue: ['2', '4', '6'],
                  options: [
                    { value: '2', label: '历史法定代表人' },
                    { value: '4', label: '历史董监高' },
                    { value: '6', label: '历史股东' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 统计周期 近三年
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publicdate', order: 'DESC', fieldSnapshot: 'publicdate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '分支机构中企业存在异常', '分支机构中企业存在异常', 41, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '分支机构企业异常',
              comment: '分支机构 + 吊销 ',
              dimKey: DimensionTypeEnums.RelatedCompanies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
                  accessScope: 1,
                  fieldValue: [RelatedTypeEnums.Branch],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  options: [{ name: '吊销', value: '90' }],
                  accessScope: 2,
                  fieldValue: ['90'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '分支机构企业异常',
              comment: '分支机构 + (经营异常/严重违法/失信被执行人/税收违法) + 周期不限 + 数据有效',
              dimKey: DimensionTypeEnums.SeriousViolation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
                  accessScope: 1,
                  fieldValue: [RelatedTypeEnums.Branch],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '经营异常', value: 'Exception' },
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '分支机构企业异常（历史）',
              comment: '分支机构 + (经营异常/严重违法/失信被执行人/税收违法) + 近3年 + 数据无效',
              dimKey: DimensionTypeEnums.SeriousViolation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  options: [{ label: '分支机构', value: RelatedTypeEnums.Branch }],
                  accessScope: 1,
                  fieldValue: [RelatedTypeEnums.Branch],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '经营异常', value: 'Exception' },
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // createMetric(manager, user, g3.groupId, '实缴异常', '实缴异常', 42, [
      //   {
      //     order: 0,
      //     maxScore: 2,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         comment: '命中 成立时长 >= 60个月 且  实缴比例 < 100%',
      //         dimKey: DimensionTypeEnums.RealCapitalException,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.duration,
      //             fieldValue: [60],
      //             options: [{ unit: '月', min: 0, max: 9999 }],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.registrationRatio,
      //             fieldValue: [100],
      //             options: [{ unit: '%', min: 0, max: 100 }],
      //             compareType: DimensionFieldCompareTypeEnums.LessThan,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // ]),
      createMetric(manager, user, g3.groupId, '负面新闻', '负面新闻', 43, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 负面新闻 近一年',
              dimKey: DimensionTypeEnums.NegativeNews,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  // 新闻主体类型： 停工停产，生产事故，偷税漏税
                  fieldKey: DimensionFieldKeyEnums.topics,
                  fieldValue: ['all'],
                  options: AllTopicTypes,
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'publishtime', order: 'DESC', fieldSnapshot: 'publishtime' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '无统一社会信用代码', '企业的基本信息中统一社会信用代码为空的', 47, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 无统一社会信用代码',
              dimKey: DimensionTypeEnums.BusinessAbnormal7,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '临近经营期限', '临近经营期限', 48, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 临近经营期限',
              dimKey: DimensionTypeEnums.BusinessAbnormal8,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '受益所有人无法识别或穿透边界以外', '受益所有人无法识别或穿透边界以外', 49, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6601',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6601'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '法定代表人控制企业域名被列入涉赌涉诈黑名单', '法定代表人控制企业域名被列入涉赌涉诈黑名单', 50, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6708',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6708'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '分支机构域名被列入涉赌涉诈黑名单', '分支机构域名被列入涉赌涉诈黑名单', 51, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6808',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6808'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '法定代表人控制企业涉及高风险行业', '法定代表人控制企业涉及高风险行业', 52, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6710',
              dimKey: DimensionTypeEnums.QfkRisk6710,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g3.groupId, '涉及高风险行业', '涉及高风险行业', 53, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '涉及到废品收购、旧货、艺术品收藏、博彩、典当、拍卖、贵金属等高风险行业',
              comment: '命中 专业版风险 code 6313',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6313'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g7.groupId, '关联方企业经营异常', '关联方成员企业存在异常', 54, [
        {
          order: 0,
          maxScore: 2,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业经营异常',
              comment: '关联方 + 经营异常 + 近5年 + 数据有效',
              dimKey: DimensionTypeEnums.BusinessAnomalies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [{ label: '经营异常', value: 'Exception' }],
                  accessScope: 2,
                  fieldValue: ['Exception'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业经营异常（历史）',
              comment: '关联方 + 经营异常 + 近3年 + 数据失效',
              dimKey: DimensionTypeEnums.BusinessAnomalies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [{ label: '经营异常', value: 'Exception' }],
                  accessScope: 2,
                  fieldValue: ['Exception'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // createMetric(manager, user, g7.groupId, '关联方企业开庭公告', '关联方企业开庭公告', 55, [
      //   // 关联方成员企业有开庭公告信息 5
      //   {
      //     order: 0,
      //     maxScore: 2,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方企业开庭公告',
      //         comment: '关联方 + 开庭公告(被告) + 时间不限 + 数据有效',
      //         dimKey: DimensionTypeEnums.RelatedAnnouncement,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '开庭公告', value: 'Announcement' }],
      //             accessScope: 1,
      //             fieldValue: ['Announcement'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.courtType,
      //             options: [{ label: '被告', value: 1 }],
      //             accessScope: 2,
      //             fieldValue: [1],
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [-1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [1],
      //             accessScope: 1,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 关联方成员企业有开庭公告信息 3
      //   {
      //     order: 1,
      //     maxScore: 1,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方企业开庭公告（历史）',
      //         comment: '关联方 + 开庭公告(被告) + 近3年 + 数据失效',
      //         dimKey: DimensionTypeEnums.RelatedAnnouncement,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '开庭公告', value: 'Announcement' }],
      //             accessScope: 1,
      //             fieldValue: ['Announcement'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.courtType,
      //             options: [{ label: '被告', value: 1 }],
      //             accessScope: 2,
      //             fieldValue: [1],
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [0],
      //             accessScope: 1,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // ]),
      // 关联企业风险
      createMetric(manager, user, g7.groupId, '关联方企业违法事项', '关联方企业违法事项', 56, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '关联方企业被吊销',
              comment: '关联方 + 企业(已吊销) ',
              dimKey: DimensionTypeEnums.RelatedCompanies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  options: [{ name: '吊销', value: '90' }],
                  accessScope: 2,
                  fieldValue: ['90'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
            {
              dimStrategyName: '关联方企业存在违法事项',
              comment: '关联方 + (严重违法/失信被执行人/税收违法) + 实际不限 + 数据有效',
              dimKey: DimensionTypeEnums.SeriousViolation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业存在违法事项（历史）',
              comment: '关联方 + (严重违法/失信被执行人/税收违法) + 近3年 + 数据失效',
              dimKey: DimensionTypeEnums.SeriousViolation,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [3],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // 关联企业风险
      createMetric(manager, user, g7.groupId, '关联方企业刑事案件', '关联方企业刑事案件', 57, [
        {
          order: 0,
          maxScore: 6,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业发生过刑事案件',
              comment: '关联方 + (裁判文书[刑事案件非交通肇事罪]) + 时间近5年 + 数据不限',
              dimKey: DimensionTypeEnums.MoneyLaundering,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: [
                    'PrincipalControl',
                    'LegalRepresentativeControl',
                    'ActualControllerControl',
                    'BeneficiaryControl',
                    'MotherCompanyMajorityShareholder',
                    'MotherCompanyControl',
                    'MajorityInvestment',
                  ],
                  accessScope: 1,
                  options: [],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [{ label: '裁判文书', value: 'Judgement' }],
                  accessScope: 2,
                  fieldValue: ['Judgement'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案件身份 被告
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRole,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [
                    { label: '原告', value: 0 },
                    { label: '被告', value: 1 },
                    { label: '第三人', value: 2 },
                    { label: '其他', value: 3 },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  options: [{ label: '刑事案件', value: 'xs' }],
                  accessScope: 2,
                  fieldValue: ['xs'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [-1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // 关联企业风险
      createMetric(manager, user, g7.groupId, '关联方企业集中注册且均无实缴资本', '关联方企业集中注册且均无实缴资本', 58, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '关联方企业集中注册且均无实缴资本',
              comment: '关联方企业集中注册且均无实缴资本 专业版 7099',
              dimKey: DimensionTypeEnums.QfkRisk7099,
              fields: [],
            },
          ],
        },
      ]),
      // createMetric(manager, user, g7.groupId, '关联方成员企业存在异常', '关联方成员企业存在异常', 54, [
      //   // 关联方成员企业存在异常 15
      //   {
      //     maxScore: 15,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     operation: 'should',
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员存在异常',
      //         comment: '关联方 + 经营异常 + 近3年 + 数据有效',
      //         dimKey: DimensionTypeEnums.BusinessAnomalies,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '经营异常', value: 'Exception' }],
      //             accessScope: 2,
      //             fieldValue: ['Exception'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 关联方成员企业存在异常 5
      //   {
      //     maxScore: 5,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员存在异常',
      //         comment: '关联方 + 经营异常 + 近3年 + 数据失效',
      //         dimKey: DimensionTypeEnums.BusinessAnomalies,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '经营异常', value: 'Exception' }],
      //             accessScope: 2,
      //             fieldValue: ['Exception'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [0],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 企业多个关联方成员存在违法事项 10
      //   {
      //     maxScore: 10,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     operation: 'should',
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员被吊销',
      //         comment: '关联方 + 企业(已吊销) ',
      //         dimKey: DimensionTypeEnums.RelatedCompanies,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.companyStatus,
      //             options: [{ name: '吊销', value: '90' }],
      //             accessScope: 2,
      //             fieldValue: ['90'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //         ],
      //       },
      //       {
      //         dimStrategyName: '关联方成员存在违法事项',
      //         comment: '关联方 + (严重违法/失信被执行人/税收违法) + 近3年 + 数据有效',
      //         dimKey: DimensionTypeEnums.SeriousViolation,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [
      //               { label: '严重违法', value: 'SeriousViolation' },
      //               { label: '失信被执行人', value: 'BadCreditExecuted' },
      //               { label: '税收违法', value: 'TaxIllegal' },
      //             ],
      //             accessScope: 2,
      //             fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 企业多个关联方成员存在违法事项 5
      //   {
      //     maxScore: 5,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员存在违法事项',
      //         comment: '关联方 + (严重违法/失信被执行人/税收违法) + 近3年 + 数据失效',
      //         dimKey: DimensionTypeEnums.SeriousViolation,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [
      //               { label: '严重违法', value: 'SeriousViolation' },
      //               { label: '失信被执行人', value: 'BadCreditExecuted' },
      //               { label: '税收违法', value: 'TaxIllegal' },
      //             ],
      //             accessScope: 2,
      //             fieldValue: ['SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [0],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 企业关联方成员曾发生过洗钱类刑事案件 10
      //   {
      //     maxScore: 10,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员发生过刑事案件',
      //         comment: '关联方 + (裁判文书[刑事案件]) + 时间不限 + 数据不限',
      //         dimKey: DimensionTypeEnums.MoneyLaundering,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '裁判文书', value: 'Judgement' }],
      //             accessScope: 2,
      //             fieldValue: ['Judgement'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.CaseType,
      //             options: [{ label: '刑事案件', value: 'xs' }],
      //             accessScope: 2,
      //             fieldValue: ['xs'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [-1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [-1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 关联方成员企业有开庭公告信息 5
      //   {
      //     maxScore: 5,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员有开庭公告',
      //         comment: '关联方 + 开庭公告(被告) + 时间不限 + 数据有效',
      //         dimKey: DimensionTypeEnums.RelatedAnnouncement,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '开庭公告', value: 'Announcement' }],
      //             accessScope: 2,
      //             fieldValue: ['Announcement'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.courtType,
      //             options: [{ label: '被告', value: 1 }],
      //             accessScope: 2,
      //             fieldValue: [1],
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [-1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [1],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 关联方成员企业有开庭公告信息 3
      //   {
      //     maxScore: 3,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员有开庭公告',
      //         comment: '关联方 + 开庭公告(被告) + 近3年 + 数据失效',
      //         dimKey: DimensionTypeEnums.RelatedAnnouncement,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRoleType,
      //             fieldValue: [
      //               'PrincipalControl',
      //               'LegalRepresentativeControl',
      //               'ActualControllerControl',
      //               'BeneficiaryControl',
      //               'MotherCompanyMajorityShareholder',
      //               'MotherCompanyControl',
      //               'MajorityInvestment',
      //             ],
      //             accessScope: 1,
      //             options: [],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.relatedRiskType,
      //             options: [{ label: '开庭公告', value: 'Announcement' }],
      //             accessScope: 2,
      //             fieldValue: ['Announcement'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.courtType,
      //             options: [{ label: '被告', value: 1 }],
      //             accessScope: 2,
      //             fieldValue: [1],
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.cycle,
      //             fieldValue: [5],
      //             options: [-1, 1, 3, 5],
      //             compareType: DimensionFieldCompareTypeEnums.GreaterThan,
      //           },
      //           {
      //             fieldKey: DimensionFieldKeyEnums.isValid,
      //             fieldValue: [0],
      //             accessScope: 2,
      //             compareType: DimensionFieldCompareTypeEnums.Equal,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      //   // 关联方企业集中注册且均无实缴资本 5
      //   {
      //     maxScore: 5,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         dimStrategyName: '关联方成员集中注册且均无实缴资本',
      //         comment: '关联方企业集中注册且均无实缴资本 专业版 7099',
      //         dimKey: DimensionTypeEnums.QfkRisk7099,
      //         fields: [],
      //       },
      //     ],
      //   },
      // ]),
      createMetric(manager, user, g7.groupId, '同联系方式企业存在异常', '同联系方式企业存在异常', 59, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '同联系方式关联方异常',
              comment: '相同电话 + (裁判文书[刑事案件(被告+非交通肇事罪)]/经营异常/严重违法/失信被执行人/税收违法) + 近五年 +  数据有效 ',
              dimKey: DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  options: [{ label: '相同电话', value: 'HasPhone' }],
                  accessScope: 1,
                  fieldValue: ['HasPhone'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '裁判文书', value: 'Judgement' },
                    { label: '经营异常', value: 'Exception' },
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['Judgement', 'Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  options: [{ label: '刑事案件', value: 'xs' }],
                  accessScope: 2,
                  fieldValue: ['xs'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRole,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [
                    { label: '原告', value: 0 },
                    { label: '被告', value: 1 },
                    { label: '第三人', value: 2 },
                    { label: '其他', value: 3 },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  options: [-1, 1, 2, 3, 4, 5], // 前端有近5年的选项
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g7.groupId, '同注册地址企业存在异常', '同注册地址企业存在异常', 60, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '同注册地址关联方异常',
              comment: '相同地址 + (裁判文书[刑事案件(被告+非交通肇事罪)]/经营异常/严重违法/失信被执行人/税收违法) + 近5年 +  数据有效',
              dimKey: DimensionTypeEnums.BusinessAnomaliesWithSamePhoneAndAddress,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  options: [{ label: '相同地址', value: 'HasAddress' }],
                  accessScope: 1,
                  fieldValue: ['HasAddress'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRiskType,
                  options: [
                    { label: '裁判文书', value: 'Judgement' },
                    { label: '经营异常', value: 'Exception' },
                    { label: '严重违法', value: 'SeriousViolation' },
                    { label: '失信被执行人', value: 'BadCreditExecuted' },
                    { label: '税收违法', value: 'TaxIllegal' },
                  ],
                  accessScope: 2,
                  fieldValue: ['Judgement', 'Exception', 'SeriousViolation', 'BadCreditExecuted', 'TaxIllegal'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.CaseType,
                  options: [{ label: '刑事案件', value: 'xs' }],
                  accessScope: 2,
                  fieldValue: ['xs'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                // 案件身份 被告
                {
                  fieldKey: DimensionFieldKeyEnums.judgementRole,
                  fieldValue: [1],
                  accessScope: 2,
                  options: [
                    { label: '原告', value: 0 },
                    { label: '被告', value: 1 },
                    { label: '第三人', value: 2 },
                    { label: '其他', value: 3 },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                // 案由 不是 交通肇事罪
                {
                  fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                  accessScope: 2,
                  fieldValue: [['A0248'], ['A0249']],
                  options: [
                    { label: '交通肇事罪', value: ['A0248'] },
                    { label: '危险驾驶罪', value: ['A0249'] },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [5],
                  accessScope: 2,
                  options: [-1, 1, 2, 3, 4, 5], // 前端有近5年的选项
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // 经营稳定性风险
      createMetric(manager, user, g4.groupId, '破产重整', '破产重整', 69, [
        {
          order: 0,
          maxScore: 80,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              dimStrategyName: '破产重整',
              comment: '命中 破产重整 时间不限 有效',
              dimKey: DimensionTypeEnums.Bankruptcy,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'riskdate', order: 'DESC', fieldSnapshot: 'RiskDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '破产重整（历史）', '破产重整（历史）', 70, [
        {
          order: 0,
          maxScore: 30,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              dimStrategyName: '破产重整（历史）',
              comment: '命中 破产重整 时间不限 失效',
              dimKey: DimensionTypeEnums.Bankruptcy,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  options: [-1, 1, 3, 5],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [0],
                  accessScope: 1,
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'riskdate', order: 'DESC', fieldSnapshot: 'RiskDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '股权冻结', '股权冻结', 72, [
        {
          order: 0,
          maxScore: 8,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 有股权被冻结  股权数额 > 0',
              dimKey: DimensionTypeEnums.FreezeEquity,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.equityAmount,
                  fieldValue: [0],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [-1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.isValid,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'LianDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '联系方式异常', '联系电话无效、停机、空号、疑似代理记账公司电话', 73, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 1810',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6401', '6402', '6404'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '员工数据不明', '公开披露的参保人数=0', 74, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6302',
              dimKey: DimensionTypeEnums.QfkRisk6302,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '股权出质', '有股权出质数据', 75, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 股权出质',
              dimKey: DimensionTypeEnums.EquityPledge,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'liandate', order: 'DESC', fieldSnapshot: 'RegDate' }],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      // 企业担保金额过大 暂无数据
      createMetric(manager, user, g4.groupId, '法定代表人变更', '法定代表人变更', 86, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期法定代表人变更',
              comment: '命中 近期变更法定代表人 近1年 >= 1 次',
              dimKey: DimensionTypeEnums.MainInfoUpdateLegalPerson,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期法定代表人频繁变更',
              comment: '命中 近期变更法定代表人 近2年 > 3 次',
              dimKey: DimensionTypeEnums.MainInfoUpdateLegalPerson,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '法定代表人持股比例低', '法定代表人持股比例低', 87, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 法人代表持股比例 < 5 %  且 非金融持牌机构或者国有企业 且 非分支机构',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.legalRepresentHoldingRatio,
                  fieldValue: [5],
                  compareType: DimensionFieldCompareTypeEnums.LessThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyFlag,
                  fieldValue: ['FNC_JR', 'FNC_BX', 'FNC_ZJ'],
                  options: [
                    { label: '金融机构', value: 'FNC_JR' },
                    { label: '保险机构', value: 'FNC_BX' },
                    { label: '保险中介机构', value: 'FNC_ZJ' },
                  ],
                  accessScope: 4,
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyIsBranch,
                  fieldValue: [1],
                  options: [{ label: '是分支机构', value: 1 }],
                  accessScope: 4,
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyEconType,
                  fieldValue: ['*********', '001003', '001004', '001005', '001011', '001016', '001015', '001009', '001006', '001007'],
                  options: [
                    { label: '国有企业', value: '*********' },
                    { label: '机关单位', value: '001003' },
                    { label: '事业单位', value: '001004' },
                    { label: '社会组织', value: '001005' },
                    { label: '律师事务所', value: '001011' },
                    { label: '学校', value: '001016' },
                    { label: '医疗机构', value: '001015' },
                    { label: '个体工商户', value: '001009' },
                    { label: '个人独资企业', value: '001006' },
                    { label: '合伙企业', value: '001007' },
                  ],
                  accessScope: 4,
                  compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '法定代表人控制企业集中注册且均无实缴资本', '法定代表人控制企业集中注册且均无实缴资本', 88, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6709',
              dimKey: DimensionTypeEnums.QfkRisk6709,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '同实际控制人企业众多增加不确定风险', '同实际控制人企业众多增加不确定风险', 89, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 2310',
              dimKey: DimensionTypeEnums.QfkRisk2310,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'percentTotal', order: 'DESC', fieldSnapshot: 'percentTotalNumber' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '同法定代表人企业众多且地区分散', '同法定代表人企业众多且地区分散', 90, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 2210',
              dimKey: DimensionTypeEnums.QfkRisk2210,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [
                    { field: 'stockPercent', order: 'DESC', fieldSnapshot: 'stockPercentNumber' },
                    { field: 'regCap', order: 'DESC', fieldSnapshot: 'regCapNumber' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '受益所有人控制企业众多', '受益所有人控制企业众多', 91, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 受益所有人控制企业众多',
              dimKey: DimensionTypeEnums.BeneficialOwnersControlNumerousEnterprises,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'startDate', order: 'DESC', fieldSnapshot: 'startDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人变更', '实际控制人变更', 92, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期实际控制人变更',
              comment: '命中 近期变更实际控制人 近1年 >= 1 次',
              dimKey: DimensionTypeEnums.MainInfoUpdatePerson,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期实际控制人频繁变更',
              comment: '命中 近期变更实际控制人 近2年 > 3 次',
              dimKey: DimensionTypeEnums.MainInfoUpdatePerson,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '联系方式或注册地址重复', '联系方式或注册地址重复', 93, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'should',
          dimsFields: [
            {
              dimStrategyName: '同联系方式企业',
              comment: '命中 相同电话 企业  >= 20',
              dimKey: DimensionTypeEnums.RelatedCompanies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['HasPhone'],
                  accessScope: 1,
                  options: [{ label: '相同电话', value: NebulaRelatedEdgeEnums.HasPhone }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['10', '20'],
                  accessScope: 2,
                  options: [
                    { name: '在业', value: '10' },
                    { name: '存续', value: '20' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [20],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
              ],
            },
            {
              dimStrategyName: '同注册地址企业',
              comment: '命中 相同地址 企业 >= 20',
              dimKey: DimensionTypeEnums.RelatedCompanies,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                  fieldValue: ['HasAddress'],
                  accessScope: 1,
                  options: [{ label: '相同地址', value: NebulaRelatedEdgeEnums.HasAddress }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.companyStatus,
                  fieldValue: ['10', '20'],
                  accessScope: 2,
                  options: [
                    { name: '在业', value: '10' },
                    { name: '存续', value: '20' },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [20],
                  accessScope: 2,
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '异地经营', '异地经营', 94, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 异地经营 专业版风险 code 2410',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['2410'],
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '大股东变更', '非上市公司大股东变更', 95, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '命中 非上市公司',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyListed,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '近期大股东变更',
              comment: '命中 大股东变更 近1年 >= 1 次',
              dimKey: DimensionTypeEnums.MainInfoUpdateHolder,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          operation: 'must',
          dimsFields: [
            {
              strategyRole: StrategyRoleEnums.OnlyFilter,
              comment: '命中 非上市公司',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyListed,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
            {
              dimStrategyName: '近期大股东频繁变更',
              comment: '命中 大股东变更 近2年 > 3 次',
              dimKey: DimensionTypeEnums.MainInfoUpdateHolder,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '股权结构复杂', '股权结构复杂', 96, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6801',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6801'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '控制权分散', '控制权分散', 97, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6803',
              dimKey: DimensionTypeEnums.QfkRisk6803,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '所有权与经营权分离', '所有权与经营权分离', 98, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6802',
              dimKey: DimensionTypeEnums.QfkRisk6802,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '股权结构疑似隐藏控制方', '股权结构疑似隐藏控制方', 99, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6809',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6809'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      // createMetric(manager, user, g4.groupId, '注册地址属于自贸区', '注册地址属于自贸区', 100, [
      //   {
      //     order: 0,
      //     maxScore: 1,
      //     riskLevel: DimensionRiskLevelEnum.Medium,
      //     dimsFields: [
      //       {
      //         comment: '命中 专业版风险 code 6502',
      //         dimKey: DimensionTypeEnums.QfkRisk,
      //         fields: [
      //           {
      //             fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
      //             options: [],
      //             accessScope: 1,
      //             fieldValue: ['6502'],
      //             compareType: DimensionFieldCompareTypeEnums.ContainsAny,
      //           },
      //         ],
      //       },
      //     ],
      //   },
      // ]),
      createMetric(manager, user, g4.groupId, '采用托管、代办、秘书公司注册', '采用托管、代办、秘书公司注册', 111, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6504',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6504'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '采用自主申报、住所申报方式注册', '采用自主申报、住所申报方式注册', 112, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6505',
              dimKey: DimensionTypeEnums.QfkRisk,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.QfkRiskItem,
                  options: [],
                  accessScope: 1,
                  fieldValue: ['6505'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人无法识别或穿透边界以外', '实际控制人无法识别或穿透边界以外', 113, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6615',
              dimKey: DimensionTypeEnums.QfkRisk6615,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人控制企业集中注册且均无实缴资本', '实际控制人控制企业集中注册且均无实缴资本', 117, [
        {
          order: 0,
          maxScore: 3,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6609',
              dimKey: DimensionTypeEnums.QfkRisk6609,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人控制企业涉及高风险行业', '实际控制人控制企业涉及高风险行业', 118, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6610',
              dimKey: DimensionTypeEnums.QfkRisk6610,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [
                    {
                      field: 'startDate',
                      order: 'DESC',
                      fieldSnapshot: 'startDate',
                    },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人控制企业位于边境贸易区', '实际控制人控制企业位于边境贸易区', 119, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6611',
              dimKey: DimensionTypeEnums.QfkRisk6611,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '实际控制人控制企业边境贸易区占比较高', '实际控制人控制企业边境贸易区占比较高', 130, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6612',
              dimKey: DimensionTypeEnums.QfkRisk6612,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [
                    {
                      field: 'startDate',
                      order: 'DESC',
                      fieldSnapshot: 'startDate',
                    },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '经营范围变更', '经营范围变更', 131, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期经营范围变更',
              comment: '命中 经营范围变更 近一年 >= 1次',
              dimKey: DimensionTypeEnums.MainInfoUpdateScope,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期经营范围频繁变更',
              comment: '命中 经营范围变更 近2年 > 3次',
              dimKey: DimensionTypeEnums.MainInfoUpdateScope,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '注册地址变更', '注册地址变更', 132, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期注册地址变更',
              comment: '命中 注册地址变更 近1年 >= 1次 ',
              dimKey: DimensionTypeEnums.MainInfoUpdateAddress,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期注册地址频繁变更',
              comment: '命中 注册地址变更 近2年 > 3次 ',
              dimKey: DimensionTypeEnums.MainInfoUpdateAddress,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '企业名称变更', '企业名称变更', 133, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期企业名称变更',
              comment: '命中 变更企业名称 近1年 >= 1次  ',
              dimKey: DimensionTypeEnums.MainInfoUpdateName,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [1],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: 10,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              dimStrategyName: '近期企业名称频繁变更',
              comment: '命中 变更企业名称 近2年 > 3次  ',
              dimKey: DimensionTypeEnums.MainInfoUpdateName,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.cycle,
                  fieldValue: [2],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.hitCount,
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                },
                {
                  fieldKey: DimensionFieldKeyEnums.sortField,
                  accessScope: 1,
                  fieldValue: [{ field: 'ChangeDate', order: 'DESC', fieldSnapshot: 'ChangeDate' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g4.groupId, '注册资本降幅过大', '注册资本降幅过大', 134, [
        {
          order: 0,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 6907',
              dimKey: DimensionTypeEnums.QfkRisk6907,
              fields: [],
            },
          ],
        },
      ]),
      // 国别风险
      createMetric(manager, user, g5.groupId, '来自高风险I类国家或地区', '来自高风险I类国家或地区', 135, [
        {
          order: 0,
          maxScore: 60,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 1410',
              dimKey: DimensionTypeEnums.QfkRisk1410,
              fields: [],
            },
          ],
        },
      ]),
      createMetric(manager, user, g5.groupId, '来自高风险II类、III类国家或地区', '来自高风险II类、III类国家或地区', 136, [
        {
          order: 0,
          maxScore: 5,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '命中 专业版风险 code 1411',
              dimKey: DimensionTypeEnums.QfkRisk1411,
              fields: [],
            },
          ],
        },
      ]),
      // 企业资质
      createMetric(manager, user, g6.groupId, '央企', '央企', 137, [
        {
          order: 0,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 工商企业性质 央企',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyEconType,
                  accessScope: 2,
                  options: [
                    { label: '央企', value: '*********001' },
                    { label: '央企子公司', value: '*********002' },
                  ],
                  fieldValue: ['*********001', '*********002'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '国企', '国企', 140, [
        {
          order: 0,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 工商企业性质 国企',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyEconType,
                  accessScope: 2,
                  options: [
                    { label: '省管国企', value: '*********003' },
                    { label: '市管国企', value: '*********004' },
                    { label: '国有全资企业', value: '*********005' },
                    { label: '国有独资企业', value: '*********006' },
                    { label: '国有控股企业', value: '*********007' },
                    { label: '国有实际控制企业', value: '*********008' },
                  ],
                  fieldValue: ['*********003', '*********004', '*********005', '*********006', '*********007', '*********008'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '实缴资本', '实缴资本', 148, [
        {
          order: 0,
          maxScore: -2,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 实缴资本 1亿-5亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
                  accessScope: 2,
                  options: [{ label: '1亿-5亿', value: [10000, 50000] }],
                  fieldValue: [[10000, 50000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 实缴资本 5亿-20亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
                  accessScope: 2,
                  options: [{ label: '5亿-20亿', value: [50000, 200000] }],
                  fieldValue: [[50000, 200000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: -4,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 实缴资本 20亿-50亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
                  accessScope: 2,
                  options: [{ label: '20亿-50亿', value: [200000, 500000] }],
                  fieldValue: [[200000, 500000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 3,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 实缴资本 50亿-100亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
                  accessScope: 2,
                  options: [{ label: '50亿-100亿', value: [500000, 1000000] }],
                  fieldValue: [[500000, 1000000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 4,
          maxScore: -6,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 实缴资本 100亿以上',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.realCapitalSelect,
                  accessScope: 2,
                  options: [{ label: '100亿以上', value: [1000000] }],
                  fieldValue: [[1000000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '净利润', '净利润', 149, [
        {
          order: 0,
          maxScore: -1,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 净利润 20亿-50亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.netProfit,
                  accessScope: 2,
                  options: [{ label: '20亿-50亿', value: [200000, 500000] }],
                  fieldValue: [[200000, 500000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -2,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 净利润 50亿-100亿',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.netProfit,
                  accessScope: 2,
                  options: [{ label: '50亿-100亿', value: [500000, 1000000] }],
                  fieldValue: [[500000, 1000000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 2,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 净利润 100亿以上',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.netProfit,
                  accessScope: 2,
                  options: [{ label: '100亿以上', value: [1000000] }],
                  fieldValue: [[1000000]],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '重点金融机构', '重点金融机构', 160, [
        {
          order: 0,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              dimStrategyName: '重点金融机构',
              comment: '命中 数据频道-金融机构',
              dimKey: DimensionTypeEnums.FinancialInstitution,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.financialInstitutionType,
                  accessScope: 2,
                  options: [
                    { value: '1', label: '银行' },
                    { value: '3', label: '保险机构' },
                    { value: '4', label: '证券公司' },
                    { value: '6', label: '基金公司' },
                    { value: '2', label: '信托公司' },
                    { value: '5', label: '期货公司' },
                    { value: '9', label: '银行理财子公司' },
                    { value: '14', label: '金融资管公司' },
                  ],
                  fieldValue: ['1', '3', '4', '6', '2', '5', '9', '14'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '融资上市', '融资上市', 161, [
        {
          order: 0,
          maxScore: -3,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 融资上市 上市',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.companyListed,
                  accessScope: 2,
                  options: [{ label: '上市公司(非ST/*ST)', value: 3 }],
                  fieldValue: [3],
                  compareType: DimensionFieldCompareTypeEnums.Equal,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g6.groupId, '财富榜单', '财富榜单', 162, [
        {
          order: 0,
          maxScore: -2,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 中国500强',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.wealthRank,
                  accessScope: 2,
                  options: [{ label: '中国500强', value: '140002' }],
                  fieldValue: ['140002'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
        {
          order: 1,
          maxScore: -5,
          riskLevel: DimensionRiskLevelEnum.Alert,
          dimsFields: [
            {
              comment: '命中 世界500强',
              dimKey: DimensionTypeEnums.CompanyDetail,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.wealthRank,
                  accessScope: 2,
                  options: [{ label: '世界500强', value: '140003' }],
                  fieldValue: ['140003'],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      // 黑名单
      createMetric(manager, user, g8.groupId, '政府采购严重违法失信行为记录名单', '政府采购严重违法失信行为记录名单', 163, [
        {
          order: 0,
          maxScore: 80,
          riskLevel: DimensionRiskLevelEnum.High,
          dimsFields: [
            {
              comment: '被列入政府采购严重违法失信行为记录名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['GovernmentPurchaseIllegal'],
                  options: [{ value: 'GovernmentPurchaseIllegal', label: '政府采购严重违法失信行为记录名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '国央企采购黑名单', '国央企采购黑名单', 164, [
        {
          order: 0,
          maxScore: 15,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入国央企采购黑名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['GovProcurementIllegal'],
                  options: [{ value: 'GovProcurementIllegal', label: '国央企采购黑名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '安全生产领域失信生产经营单位', '安全生产领域失信生产经营单位', 165, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入安全生产领域失信生产经营单位',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['SafetyProductionEnterprise'],
                  options: [{ value: 'SafetyProductionEnterprise', label: '安全生产领域失信生产经营单位' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '环保失信黑名单', '环保失信黑名单', 166, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入环保失信黑名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['EnvironmentalProtection'],
                  options: [{ value: 'EnvironmentalProtection', label: '环保失信黑名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '海关失信企业名单', '海关失信企业名单', 167, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入海关失信企业名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['CustomsList'],
                  options: [{ value: 'CustomsList', label: '海关失信企业名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '违法失信上市公司', '违法失信上市公司', 168, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入违法失信上市公司',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['ListedCompanyIllegal'],
                  options: [{ value: 'ListedCompanyIllegal', label: '违法失信上市公司' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '劳动保障违法', '劳动保障违法', 169, [
        {
          order: 0,
          maxScore: 20,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入劳动保障违法',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['LaborGuarantee'],
                  options: [{ value: 'LaborGuarantee', label: '劳动保障违法' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '重点行业领域监管黑名单', '重点行业领域监管黑名单', 180, [
        {
          order: 0,
          maxScore: 20,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入重点行业领域监管黑名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['SupervisionOfKeyIndustry'],
                  options: [{ value: 'SupervisionOfKeyIndustry', label: '重点行业领域监管黑名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '知识产权（专利）领域严重失信联合戒对象名单', '知识产权（专利）领域严重失信联合戒对象名单', 181, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入知识产权（专利）领域严重失信联合戒对象名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['IntellectualPropertyIllegal'],
                  options: [
                    {
                      value: 'IntellectualPropertyIllegal',
                      label: '知识产权（专利）领域严重失信联合戒对象名单',
                    },
                  ],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '发改委黑名单', '发改委黑名单', 182, [
        {
          order: 0,
          maxScore: 50,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入发改委黑名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['FgwBlackList'],
                  options: [{ value: 'FgwBlackList', label: '发改委黑名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '出口管制合规风险企业清单', '出口管制合规风险企业清单', 183, [
        {
          order: 0,
          maxScore: 1,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入出口管制合规风险企业清单',
              dimKey: DimensionTypeEnums.OvsSanction,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.sanctionListCodes,
                  accessScope: 2,
                  fieldValue: ['ForeignExportControlList'],
                  options: [{ value: 'ForeignExportControlList', label: '出口管制合规风险企业清单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
      createMetric(manager, user, g8.groupId, '军队采购失信名单', '军队采购失信名单', 184, [
        {
          order: 0,
          maxScore: 20,
          riskLevel: DimensionRiskLevelEnum.Medium,
          dimsFields: [
            {
              comment: '被列入军队采购失信名单',
              dimKey: DimensionTypeEnums.HitOuterBlackList,
              fields: [
                {
                  fieldKey: DimensionFieldKeyEnums.blackType,
                  accessScope: 2,
                  fieldValue: ['ArmyProcurementIllegal'],
                  options: [{ value: 'ArmyProcurementIllegal', label: '军队采购失信名单' }],
                  compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                },
              ],
            },
          ],
        },
      ]),
    ]);
    // 5. 分发模型给租户
    if (toOrgId) {
      await this.riskModelService.distributeRiskModel(
        {
          riskModelId: riskModel.modelId,
          orgId: toOrgId,
        },
        user,
      );
    }
    return riskModel;
  }

  // /**
  //  * 情况 模型相关所有数据，慎重
  //  * @param user
  //  */
  // async delModelData(user: PlatformUser) {
  //   const manager = this.riskModelRepo.manager;
  //   await Bluebird.all([
  //     manager.delete(RiskModelEntity, {}),
  //     manager.delete(GroupEntity, {}),
  //     manager.delete(MetricsEntity, {}),
  //     manager.delete(MetricDimensionRelationEntity, {}),
  //     manager.delete(GroupMetricRelationEntity, {}),
  //     manager.delete(DimensionDefinitionEntity, {}),
  //     manager.delete(DimensionFieldsEntity, {}),
  //     manager.delete(DimensionHitStrategyEntity, {}),
  //     manager.delete(DimensionHitStrategyFieldsEntity, {}),
  //     manager.delete(DistributedSystemResourceEntity, {}),
  //     manager.delete(DiligenceHistoryEntity, {}),
  //   ]);
  // }

  /**
   * 删除指定createHTFModel 创建的数 模型的数据
   *
   * 该方法默认所有资源都是新创建的，所以可以全部删掉
   * 迁移到 test_utils_module
   * @param modelId
   */
  // async clearSpecificRiskModelData(modelId: number) {
  //   this.logger.log(`开始删除模型 ${modelId} 相关数据`);
  //   try {
  //     const riskEntity = await getFullRiskModel(modelId, this.riskModelRepo.manager);
  //     const manager = this.riskModelRepo.manager;
  //     const riskModelId = riskEntity.modelId;
  //     const metricsIds = flattenDeep(riskEntity.groups?.map((g) => g.groupMetrics?.map((gm) => gm.metricsId)));
  //     const dimStrategyIds = (await manager.find(MetricDimensionRelationEntity, { metricsId: In(metricsIds) })).map((ds) => ds.dimensionStrategyId);
  //
  //     await Bluebird.all([
  //       this.riskModelRepo.delete(riskModelId),
  //       manager.delete(GroupEntity, { modelId: riskModelId }),
  //       manager.delete(GroupMetricRelationEntity, { metricsId: In(metricsIds) }),
  //       manager.delete(MetricsEntity, { metricsId: In(metricsIds) }),
  //       manager.delete(MetricDimensionRelationEntity, { metricsId: In(metricsIds) }),
  //       manager.delete(DimensionHitStrategyFieldsEntity, { strategyId: In(dimStrategyIds) }),
  //       manager.delete(DistributedSystemResourceEntity, {
  //         resourceId: riskModelId,
  //         resourceType: In([DistributedResourceTypeEnums.RiskModel, DistributedResourceTypeEnums.MonitorRiskModel]),
  //       }),
  //     ]);
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }

  async initDefaultDimensionDefs(user: PlatformUser) {
    await Bluebird.map(Object.keys(BaseDimensionDefinitionForGenerating), async (key: DimensionTypeEnums) => {
      const dimDef = BaseDimensionDefinitionForGenerating[key];
      try {
        if (!dimDef) {
          return null;
        }
        const dimension = await this.dimensionService.addDimension(
          Object.assign(
            new CreateDimensionPO(),
            pick(dimDef, ['name', 'description', 'key', 'source', 'template', 'sourcePath', 'detailSource', 'detailSourcePath', 'typeCode']),
            { createBy: 1 },
          ),
        );
        console.log(`save dimension ${dimension.name}`);
        if (!dimDef?.['dimensionFields']) {
          return dimension;
        }
        await Bluebird.map(dimDef.dimensionFields, async (field) => {
          const dimField = Object.assign(
            new CreateDimensionFieldPO(),
            { dimensionId: dimension.dimensionId },
            pick(field, [
              'fieldName',
              'fieldKey',
              'inputType',
              'dataType',
              'isArray',
              'fieldOrder',
              'comment',
              'options',
              'defaultValue',
              'defaultCompareType',
            ]),
          );
          try {
            const dimensionField = await this.dimensionFieldsService.addField(dimField);
            console.log(`save dimension ${dimension.name} - field ${dimensionField.fieldName}`);
            return dimensionField;
          } catch (error) {
            console.log(`error save  ${dimDef.name} - ${dimField.fieldName} - ${dimField.fieldKey}`);
            // console.log(error);
          }
        });
        return dimension;
      } catch (error) {
        console.log(`error save dimension ${dimDef.name}`);
        // console.log(error);
      }
    });
  }
}
