import { Injectable } from '@nestjs/common';
import { RiskModelService } from '../risk_model.service';
import { InjectRepository } from '@nestjs/typeorm';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { Repository } from 'typeorm';
import { PlatformUser } from '../../../libs/model/common';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import * as Bluebird from 'bluebird';
import { createDimensionFields, createGroup, createMetric } from '../../test_utils_module/dimension.test.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../../libs/enums/dimension/DimensionFieldCompareTypeEnums';
import {
  AnnouncementReportType,
  BaseLineDateSelect,
  ChangeStatusMap,
  CurrencyChangeMap,
  IsBPMap,
  isFilterRelatedCompanyMap,
  keyCauseActionMap,
  LayTypeMap,
  RegisCapitalTrendMap,
  ShareChangeStatusMap,
} from '../../../libs/constants/risk.change.constants';
import { RelatedTypeEnums } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { RelatedTypeMap } from '../../../libs/constants/related.constants';
import { MetricTypeEnums } from '../../../libs/enums/metric/MetricTypeEnums';
import { MetricDynamicDataSourceTypeEnum } from '../../../libs/model/metric/MetricDynamicStrategy';
import { ScoreStrategyEnums } from '../../../libs/enums/ScoreStrategyEnums';

/**
 * 默认监控模型初始化
 */
@Injectable()
export class ModelInitMonitorService {
  constructor(
    private readonly riskModelService: RiskModelService,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
  ) {}

  async createMonitorModel(user: PlatformUser, modelName: string, toOrgId: number, modelType: RiskModelTypeEnums = RiskModelTypeEnums.MonitorModel) {
    // 1. 创建风险模型
    const riskModel = await this.riskModelService.addRiskModel(
      {
        modelName,
        product: user.currentProduct,
        comment: modelName,
        status: DataStatusEnums.Developing,
        modelType,
      },
      user,
    );

    const manager = this.riskModelRepo.manager;

    // 2. 初始化维度，维度属性
    await createDimensionFields(manager, user);

    // 3. 创建分组
    const [g1, g2, g3, g4] = await Bluebird.all([
      createGroup(manager, user, riskModel.modelId, '工商风险', 1),
      createGroup(manager, user, riskModel.modelId, '司法风险', 2),
      createGroup(manager, user, riskModel.modelId, '经营风险', 3),
      createGroup(manager, user, riskModel.modelId, '关联方设置', 4),
    ]);

    // 4. 给分组内添加指标
    await Bluebird.all([
      createMetric(
        manager,
        user,
        g4.groupId,
        '关联方',
        '关联方',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '关联方变化',
                comment: '企业关联方增加或者减少',
                dimKey: DimensionTypeEnums.RelatedCompanyChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
                    fieldValue: [
                      // RelatedTypeEnums.PrincipalControl,
                      // RelatedTypeEnums.LegalRepresentativeControl,
                      // RelatedTypeEnums.ActualControllerControl,
                      // RelatedTypeEnums.BeneficiaryControl,
                      // RelatedTypeEnums.Branch,
                      RelatedTypeEnums.MotherCompanyMajorityShareholder,
                      // RelatedTypeEnums.MotherCompanyControl,
                      RelatedTypeEnums.MajorityInvestment,
                    ],
                    options: RelatedTypeMap,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isFilterRelatedCompany,
                    fieldValue: [1],
                    options: isFilterRelatedCompanyMap,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                    accessScope: 1,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Persistent,
          allowMultipleDynamics: false,
          allowRepeatedHits: false,
          isSameMetricStrategy: 1,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      // 工商变更
      createMetric(
        manager,
        user,
        g1.groupId,
        '主要人员变更',
        '主要人员变更',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业负责人变更',
                comment:
                  '命中：【1】riskCategories=39， 【2】企业负责人layTypes(ChangeExtend.C)=[法定代表人,执行事务合伙人,负责人,经营者,投资人,董事长,理事长,代表人]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [39],
                    options: [{ value: 39, label: '企业法人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.layTypes,
                    fieldValue: [1, 2, 3, 4, 5, 6, 7, 9],
                    options: LayTypeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '主要人员变更分析',
        '主要人员变更分析',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '主要人员变更',
                comment: '命中：【1】riskCategories=46',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '董事长变动',
                comment: '命中：【1】riskCategories=46 【2】变更角色：董事长 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.compChangeRole,
                    fieldValue: [1],
                    options: [{ value: 1, label: '董事长' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '总经理变动',
                comment: '命中：【1】riskCategories=46 【2】变更角色：总经理 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.compChangeRole,
                    fieldValue: [2],
                    options: [{ value: 2, label: '总经理' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '1个自然年度内发生三分之一以上董事变动',
                comment: '命中：【1】riskCategories=46 【2】变更角色：董事 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.compChangeRole,
                    fieldValue: [3],
                    options: [{ value: 3, label: '董事' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.baselineDate,
                    fieldValue: [1],
                    options: BaseLineDateSelect,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    // 变更占比 > 30%
                    fieldKey: DimensionFieldKeyEnums.changeThreshold,
                    fieldValue: [33],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '1个自然年度内发生三分之二以上监事变动',
                comment: '命中：【1】riskCategories=46 【2】变更角色：监事 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [46],
                    options: [{ value: 46, label: '主要成员' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.compChangeRole,
                    fieldValue: [4],
                    options: [{ value: 4, label: '监事' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.baselineDate,
                    fieldValue: [1],
                    options: BaseLineDateSelect,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    // 变更占比 > 66%
                    fieldKey: DimensionFieldKeyEnums.changeThreshold,
                    fieldValue: [66],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '股东变更',
        '股东变更',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '企业大股东变更',
                comment: '命中: 【1】企业大股东变更riskCategories=24',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [24],
                    options: [{ value: 24, label: '大股东变动' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '企业股东股份变更',
                comment: '命中: 【1】股东股份变更riskCategories=44',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [44],
                    options: [{ value: 44, label: '股东股份变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '控股股东变更分析',
        '控股股东变更分析',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '控股股东及前后持股比例变更',
                comment:
                  '命中：【1】riskCategories=68，【2】股份增加：shareChangeStatus=1, 【3】持股比例BeforeContent(ChangeExtend.B)< 50%, 【4】持股比例AfterContent(ChangeExtend.C) > 50%, 【5】变更后还是控股股东IsBP=1(ChangeExtend.IsBP)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '持股比例变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [1],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  /*{
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },*/
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [1],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '股权结构发生重大变化',
                comment:
                  '命中：【1】控股股东持股比例变更riskCategories=68，【2】股份减少：shareChangeStatus=0, 【3】持股比例BeforeContent(ChangeExtend.B)>50% 【4】AfterContent(ChangeExtend.C)<50%, 【5】变更后不再是股东IsBP(ChangeExtend.IsBP)=2',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '持股比例变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [2],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '实际控制人变更',
        '实际控制人变更',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '实际控制人变更',
                comment: '命中: 【1】riskCategories=25',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [25],
                    options: [{ value: 25, label: '实际控制人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      /*createMetric(
        manager,
        user,
        g1.groupId,
        '最终受益人变更',
        '最终受益人变更',
        6,
        [
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '最终受益人变更',
                comment: '命中: 【1】riskCategories=21, 【2】受益人类型BeneficiaryType最终受益人(changeInfo.T=2)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [21],
                    options: [{ value: 21, label: '最终受益人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beneficiaryType,
                    fieldValue: [2],
                    options: BeneficiaryTypeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
      ),*/
      createMetric(
        manager,
        user,
        g1.groupId,
        '受益所有人变更',
        '受益所有人变更',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '受益所有人变更',
                comment: '命中: 【1】riskCategories=114',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [114],
                    options: [{ value: 114, label: '受益所有人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '对外投资变更',
        '对外投资变更',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '新增对外投资',
                comment: '命中: 【1】riskCategories=17,【2】变更类型增加 ChangeStatus=1, 【3】BeforeContent="" ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17],
                    options: [{ value: 17, label: '对外投资' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '撤出对外投资',
                comment: '命中: 【1】riskCategories=17 【2】变更类型减少(撤销)：ChangeStatus=2, 【3】变更后持股 AfterContent(AfterContent="")',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17],
                    options: [{ value: 17, label: '对外投资' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [2],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '因对外投资成为大股东',
                comment: '命中【1】riskCategories=17, 【2】变更类型增加ChangeStatus=1, 【3】持股比例AfterContent<50%, 【4】成为股东IsBP (ChangeExtend.IsBP)=1',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17],
                    options: [{ value: 17, label: '对外投资' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [1],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '因对外投资不再是大股东',
                comment:
                  '不再是大股东: 【1】命中riskCategories=17, 【2】变更类型减少ChangeStatus=2, 【3】持股比例BeforeContent(BeforeContent)<50%, 【4】不再成为股东IsBP(ChangeExtend.IsBP)=2',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17],
                    options: [{ value: 17, label: '对外投资' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [2],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [2],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '所持股份上升',
                comment: '命中 【1】riskCategories=68,【2】变更类型增加：shareChangeStatus=1',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '股比变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [1],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '所持股份减少',
                comment: '命中: 【1】riskCategories=68,【2】变更类型减少：shareChangeStatus=0',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '股比变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '因股比变更成为大股东',
                comment:
                  '命中【1】riskCategories=68, 【2】变更类型增加：shareChangeStatus=1, 【3】持股比例AfterContent<50%, 【4】成为股东IsBP (ChangeExtend.IsBP)=1',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '股比变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [1],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.afterContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [1],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '因股比变更不再是大股东',
                comment:
                  '不再是大股东: 【1】命中riskCategories=68, 【2】变更类型减少：shareChangeStatus=0, 【3】持股比例BeforeContent(BeforeContent)<50%, 【4】不再成为股东IsBP(ChangeExtend.IsBP)=2',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [68],
                    options: [{ value: 68, label: '股比变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
                    fieldValue: [0],
                    options: ShareChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isBP,
                    fieldValue: [2],
                    options: IsBPMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '注册资本变更',
        '注册资本变更',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '注册资本减少且减幅超50%',
                comment:
                  '命中【1】riskCategories=37, 【2】是否是币种变更(Extend1.T)currencyChange=0, 【3】注册资本变更趋势减少(ChangeExtend.T=1),  【4】注册资本变更比例regisCapitalChangeRatio(ChangeExtend.A-ChangeExtend.B/ChangeExtend.A >50%)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [37],
                    options: [{ value: 37, label: '注册资本' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.currencyChange,
                    fieldValue: [0],
                    options: CurrencyChangeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
                    fieldValue: [1],
                    options: RegisCapitalTrendMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.regisCapitalChangeRatio,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '1个自然年度内拟减少注册资本超过其原注册资本5%',
                comment: '命中:【1】riskCategories=37, 【2】变更阀值: 5% 时间基准：去年期末 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [37],
                    options: [{ value: 37, label: '注册资本' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                    fieldValue: [
                      {
                        valuePeriodTrend: 1,
                        valuePeriodThreShold: 5,
                        valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
                        valuePeriodBaseLine: 1,
                      },
                    ],
                    options: [
                      {
                        valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                        valuePeriodThreShold: {
                          label: '占比',
                          value: { unit: '%', min: 0, max: 100 },
                        },
                        valuePeriodThreSholdCompareType: {
                          label: '占比比较(大于/小于)',
                          value: DimensionFieldCompareTypeEnums.GreaterThan,
                        },
                        valuePeriodBaseLine: {
                          label: '时间基准',
                          value: BaseLineDateSelect,
                        },
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '注册资本减少且减幅在50%内',
                comment:
                  '命中【1】riskCategories=37, 【2】是否是币种变更(Extend1.T)currencyChange=0, 【3】注册资本变更趋势减少(ChangeExtend.T=1),  【4】注册资本变更比例regisCapitalChangeRatio(ChangeExtend.A-ChangeExtend.B/ChangeExtend.A <50%)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [37],
                    options: [{ value: 37, label: '注册资本' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.currencyChange,
                    fieldValue: [0],
                    options: CurrencyChangeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
                    fieldValue: [1],
                    options: RegisCapitalTrendMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.regisCapitalChangeRatio,
                    fieldValue: [50],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
              {
                dimStrategyName: '注册资本币种变更',
                comment: '命中：【1】riskCategories=37，【2】是否是币种变更(Extend1.T)currencyChange=1',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [37],
                    options: [{ value: 37, label: '注册资本' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.currencyChange,
                    fieldValue: [1],
                    options: CurrencyChangeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '经营范围变更',
        '经营范围变更',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '经营范围变更',
                comment: '命中【1】riskCategories=41',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [41],
                    options: [{ value: 41, label: '经营范围' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '经营状态变更',
        '经营状态变更',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '经营状态为清算，吊销，责令关闭',
                comment: '命中：【1】riskCategories=38, 【2】businessStatus[ChangeExtend.B]= [清算,吊销,责令关闭,停业,注销,撤销,歇业,迁入,迁出]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [38],
                    options: [{ value: 38, label: '经营状态' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessStatus,
                    fieldValue: [40, 90, 85],
                    options: [
                      { label: '清算', value: 40 },
                      { label: '吊销', value: 90 },
                      { label: '责令关闭', value: 85 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '经营状态为停业，注销，撤销，歇业',
                comment: '命中：【1】命中riskCategories=38, 【2】businessStatus[ChangeExtend.B]= [清算,吊销,责令关闭,停业,注销,撤销,歇业,迁入,迁出]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [38],
                    options: [{ value: 38, label: '经营状态' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessStatus,
                    fieldValue: [70, 99, 80, 75],
                    options: [
                      { label: '停业', value: 70 },
                      { label: '注销', value: 99 },
                      { label: '撤销', value: 80 },
                      { label: '歇业', value: 75 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '经营状态为迁入，迁出',
                comment: '命中： 【1】riskCategories=38, 【2】businessStatus[ChangeExtend.B]= [清算,吊销,责令关闭,停业,注销,撤销,歇业,迁入,迁出]',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [38],
                    options: [{ value: 38, label: '经营状态' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessStatus,
                    fieldValue: [50, 60],
                    options: [
                      { label: '迁入', value: 50 },
                      { label: '迁出', value: 60 },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '企业名称变更',
        '企业名称变更',
        11,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业名称变更',
                comment: '命中riskCategories=60',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [60],
                    options: [{ value: 60, label: '企业名称变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '企业注册地址变更',
        '企业注册地址变更',
        12,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业注册地址变更',
                comment: '命中 【1】riskCategories=40',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [40],
                    options: [{ value: 40, label: '企业地址' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '企业经营地址变更',
        '企业经营地址变更',
        13,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业经营地址变更',
                comment: '命中 【1】riskCategories=139',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [139],
                    options: [{ value: 139, label: '企业经营地址变更' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '企业类型变更',
        '企业类型变更',
        14,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业类型变更',
                comment: '命中 【1】riskCategories=42',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [42],
                    options: [{ value: 42, label: '企业类型' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g1.groupId,
        '新增控股子公司',
        '新增控股子公司',
        15,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '近3个月内新增控股子公司超过1个',
                comment:
                  '命中: 【1】riskCategories=17,【2】变更类型增加 ChangeStatus=1, 【3】BeforeContent="" , 【4】变更周期：近XX个月，【5】变更数量：超过XX家',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.timePeriod,
                    fieldValue: [3],
                    options: [{ unit: '月', min: 1, max: 12 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.thresholdCount,
                    fieldValue: [1],
                    options: [{ unit: '个', min: 1, max: 50 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [17],
                    options: [{ value: 17, label: '对外投资' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.changeStatus,
                    fieldValue: [1],
                    options: ChangeStatusMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.beforeContent,
                    fieldValue: [0],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      // --------司法风险
      createMetric(
        manager,
        user,
        g2.groupId,
        '失信被执行人',
        '失信被执行人',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业成为失信被执行人',
                comment: '命中riskCategories=2',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [2],
                    options: [{ value: 2, label: '失信被执行人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '被执行人',
        '被执行人',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业成为被执行人',
                comment: '命中riskCategories=3',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [3],
                    options: [{ value: 3, label: '被执行人' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '限制高消费',
        '自身未履行法定义务；法定代表人未履行法定义务',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '自身未履行法定义务',
                comment: '命中 【1】riskCategories=55, 【2】Restricter (ChangeExtend.C = 企业本身，ChangeExtend.E=法人代表)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [55],
                    options: [{ value: 55, label: '限制高消费' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.restricterType,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '企业本身' },
                      //{ value: 2, label: '法人代表' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '终本案件',
        '企业终本案件',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '产生终本案件',
                comment: '命中 【1】riskCategories=56',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [56],
                    options: [{ value: 56, label: '终本案件' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '裁判文书',
        '裁判文书',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=4, 【2】被告人/被告/被上诉人/被申请人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=4, 【2】公诉人/原告/上诉人/申请人 /第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '案件案由为：「关键案由」，触发高风险',
                comment: '命中 【1】riskCategories=4',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [4],
                    options: [{ value: 4, label: '裁判文书' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: [],
                    options: keyCauseActionMap,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '立案信息',
        '立案信息',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=49, 【2】被告人/被告/被上诉人/被申请人 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '49' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=49, 【2】公诉人/原告/上诉人/申请人/第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '49' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '案件案由为：「关键案由」，触发高风险',
                comment: '命中 【1】riskCategories=49',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [49],
                    options: [{ value: 49, label: '立案信息' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: [],
                    options: keyCauseActionMap,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '开庭公告',
        '开庭公告',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=18, 【2】被告人/被告/被上诉人/被申请人 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=18, 【2】公诉人/原告/上诉人/申请人/第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '案件案由为：「关键案由」，触发高风险',
                comment: '命中 【1】riskCategories=18',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [18],
                    options: [{ value: 18, label: '开庭公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: [],
                    options: keyCauseActionMap,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '法院公告',
        '法院公告',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=7, 【2】被告人/被告/被上诉人/被申请人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=7, 【2】 公诉人/原告/上诉人/申请人/第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '案件案由为：「关键案由」，触发高风险',
                comment: '命中 【1】riskCategories=7',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [7],
                    options: [{ value: 7, label: '法院公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: [],
                    options: keyCauseActionMap,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '送达公告',
        '送达公告',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=27, 【2】被告人/被告/被上诉人/被申请人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27],
                    options: [{ value: 27, label: '送达公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=27, 【2】公诉人/原告/上诉人/申请人/第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [27],
                    options: [{ value: 27, label: '送达公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '诉前调解',
        '诉前调解',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：被告，被上诉人，被申请人',
                comment: '命中 【1】riskCategories=90, 【2】被告人/被告/被上诉人/被申请人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90],
                    options: [{ value: 90, label: '诉前调解' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['21', '23', '24'],
                    options: [
                      { label: '被告', value: '21' },
                      { label: '被上诉人', value: '23' },
                      { label: '被申请人', value: '24' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '案件角色为：原告，上诉人，申请人，第三人，其他当事人',
                comment: '命中 【1】riskCategories=90, 【2】公诉人/原告/上诉人/申请人/第三人/其他当事人',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90],
                    options: [{ value: 90, label: '诉前调解' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.judicialRole,
                    fieldValue: ['11', '13', '14', '91', '99'],
                    options: [
                      { label: '原告', value: '11' },
                      { label: '上诉人', value: '13' },
                      { label: '申请人', value: '14' },
                      { label: '第三人', value: '91' },
                      { label: '其他', value: '99' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '案件案由为：「关键案由」，触发高风险',
                comment: '命中 【1】riskCategories=90',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [90],
                    options: [{ value: 90, label: '诉前调解' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
                    fieldValue: [],
                    options: keyCauseActionMap,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '破产重整',
        '企业被申请破产重整',
        11,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '被申请破产重整',
                comment: '命中 【1】riskCategories=58',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [58],
                    options: [{ value: 58, label: '破产重整' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '股权冻结',
        '股东的股权；在其他企业持有的股权',
        12,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '股权冻结',
                comment:
                  '命中 【1】riskCategories=26， 【2】equityFreezeScope 股权冻结范围：changeInfo.T2 === 1  不等1 （失效，无效，解除） changeInfo?.T === 1 企业股权被冻结  , changeInfo?.T === 2 持有股权被冻结',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityFreezeScope,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '企业股权被冻结' },
                      { value: 2, label: '持有股权被冻结' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '司法拍卖',
        '企业涉及司法拍卖',
        13,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业涉及司法拍卖',
                comment: "命中 【1】riskCategories=57， 【2】拍卖类型：新增${changeInfo.Q === 1 ? '破产' : '司法'}拍卖",
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [57],
                    options: [{ value: 57, label: '司法拍卖' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.auctionType,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '破产拍卖' },
                      { value: 2, label: '司法拍卖' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.listingPrice,
                    fieldValue: [],
                    options: [{ unit: '元', min: 0, max: 99999999, label: '起拍价（元）' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '询价评估',
        '企业/人员有资产被询价评估；企业/人员有资产选定询价评估机构',
        14,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '资产被询价评估',
                comment: '命中riskCategories=59',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [59],
                    options: [{ value: 59, label: '询价评估' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.evaluationPrice,
                    fieldValue: [],
                    options: [{ unit: '元', min: 0, max: 99999999, label: '询价结果（元）' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '资产选定询价评估机构',
                comment: '命中riskCategories=76',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [76],
                    options: [{ value: 76, label: '询价评估-机构' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '限制出境',
        '企业被限制出境',
        15,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业被限制出境',
                comment: '命中riskCategories=91',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [91],
                    options: [{ value: 91, label: '限制出境' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g2.groupId,
        '公安通告',
        '企业存在非法集资的相关公告；企业存在非法吸收公众存款的相关公告',
        16,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业存在非法集资的相关公告；企业存在非法吸收公众存款的相关公告',
                comment: '命中riskCategories=109',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [109],
                    options: [{ value: 109, label: '公安通告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      // --------经营风险
      createMetric(
        manager,
        user,
        g3.groupId,
        '行政处罚',
        '行政处罚',
        1,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '处罚类别为：吊销许可证/执照，责令关闭，移送司法机关',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [1],
                    options: [{ label: 1, value: '税务局' }],
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0908', '0911', '0915'],
                    options: [
                      { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
                      { label: '责令关闭', value: '0911', esCode: 'A011' },
                      { label: '移送司法机关', value: '0915', esCode: 'A015' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '处罚类别为：责令停产停业，行政拘留，限制从业',
                comment: '命中 【1】riskCategories=107， 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [1],
                    options: [{ label: 1, value: '税务局' }],
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0910', '0914', '0913'],
                    options: [
                      { label: '责令停产停业', value: '0910', esCode: 'A010' },
                      { label: '行政拘留', value: '0914', esCode: 'A014' },
                      { label: '限制从业', value: '0913', esCode: 'A013' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '处罚类别为：罚款，没收违法所得，警告，通报批评，没收非法财物，暂扣许可证件，降低资质等级，限制开展生产经营活动',
                comment: '命中【1】riskCategories=107， 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [1],
                    options: [{ label: 1, value: '税务局' }],
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
                    options: [
                      { label: '罚款', value: '0903', esCode: 'A003' },
                      { label: '没收违法所得', value: '0904', esCode: 'A004' },
                      { label: '警告', value: '0901', esCode: 'A001' },
                      { label: '通报批评', value: '0902', esCode: 'A002' },
                      { label: '没收非法财物', value: '0905', esCode: 'A005' },
                      { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
                      { label: '降低资质等级', value: '0907', esCode: 'A007' },
                      { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '处罚类别为：其他行政处罚，不予处罚',
                comment: '命中 【1】riskCategories=107, 【2】命中行政处罚种类punishType：ChangeExtend.C, 【3】处罚单位：ChangeExtend.A 剔除税务局',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [1],
                    options: [{ label: 1, value: '税务局' }],
                    compareType: DimensionFieldCompareTypeEnums.ExceptAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0916', '0999'],
                    options: [
                      { label: '其他行政处罚', value: '0999', esCode: 'A099' },
                      { label: '不予处罚', value: '0916', esCode: 'A016' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '经营异常',
        '经营异常',
        2,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '公示信息隐瞒真实情况/弄虚作假',
                comment: '命中 【1】riskCategories=11',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [11],
                    options: [{ value: 11, label: '经营异常' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
                    fieldValue: ['0803'],
                    options: [{ value: '0803', esCode: '3', label: '公示信息隐瞒真实情况/弄虚作假' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName:
                  '登记的住所/经营场所无法联系企业； 未在规定期限公示年度报告；未按规定公示企业信息； 未在登记所从事经营活动；商事主体名称不适宜；其他原因',
                comment: '命中 【1】riskCategories=11',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [11],
                    options: [{ value: 11, label: '经营异常' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
                    fieldValue: ['0801', '0805', '0802', '0804', '0806', '0807'],
                    options: [
                      { value: '0801', esCode: '1', label: '登记的住所/经营场所无法联系企业' },
                      { value: '0805', esCode: '5', label: '未在规定期限公示年度报告' },
                      { value: '0802', esCode: '2', label: '未按规定公示企业信息' },
                      { value: '0804', esCode: '4', label: '未在登记所从事经营活动' },
                      { value: '0806', esCode: '6', label: '商事主体名称不适宜' },
                      { value: '0807', esCode: '7', label: '其他原因' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '严重违法',
        '严重违法',
        3,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业被列入严重违法',
                comment: '命中 【1】riskCategories=11',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [20],
                    options: [{ value: 20, label: '严重违法' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '环保处罚',
        '环保处罚',
        4,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '吊销许可证/执照，责令关闭，移送司法机关',
                comment: '命中 【1】riskCategories=22, 【2】命中环保处罚种类punishType：ChangeExtend.B',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0908', '0911', '0915'],
                    options: [
                      { label: '吊销许可证/执照', value: '0908', esCode: 'A008' },
                      { label: '责令关闭', value: '0911', esCode: 'A011' },
                      { label: '移送司法机关', value: '0915', esCode: 'A015' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '责令停产停业，行政拘留，限制从业',
                comment: '命中 【1】riskCategories=22，【2】命中环保处罚种类punishType：ChangeExtend.B',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0910', '0914', '0913'],
                    options: [
                      { label: '责令停产停业', value: '0910', esCode: 'A010' },
                      { label: '行政拘留', value: '0914', esCode: 'A014' },
                      { label: '限制从业', value: '0913', esCode: 'A013' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '罚款，没收违法所得，警告，通报批评，没收非法财物，暂扣许可证件，降低资质等级，限制开展生产经营活动',
                comment: '命中【1】riskCategories=22, 【2】命中环保处罚种类punishType：ChangeExtend.B',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0903', '0904', '0901', '0902', '0905', '0906', '0907', '0909'],
                    options: [
                      { label: '罚款', value: '0903', esCode: 'A003' },
                      { label: '没收违法所得', value: '0904', esCode: 'A004' },
                      { label: '警告', value: '0901', esCode: 'A001' },
                      { label: '通报批评', value: '0902', esCode: 'A002' },
                      { label: '没收非法财物', value: '0905', esCode: 'A005' },
                      { label: '暂扣许可证件', value: '0906', esCode: 'A006' },
                      { label: '降低资质等级', value: '0907', esCode: 'A007' },
                      { label: '限制开展生产经营活动', value: '0909', esCode: 'A009' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '其他行政处罚，不予处罚',
                comment: '命中 【1】riskCategories=22，【2】命中环保处罚种类punishType：ChangeExtend.B',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [22],
                    options: [{ value: 22, label: '环保处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.punishType,
                    fieldValue: ['0916', '0999'],
                    options: [
                      { label: '不予处罚', value: '0916', esCode: 'A016' },
                      { label: '其他行政处罚', value: '0999', esCode: 'A099' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '欠税公告',
        '欠税公告',
        5,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '欠税金额在500万元以上',
                comment: '命中 【1】riskCategories=30, 【2】taxOwedAmount （ChangeExtend.B） ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [31],
                    options: [{ value: 31, label: '欠税公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
                    fieldValue: [[500]],
                    options: [{ label: '500万元以上', value: [500] }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '欠税金额在200万元至500万元之间',
                comment: '命中 【1】riskCategories=30， 【2】taxOwedAmount （ChangeExtend.B）',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [31],
                    options: [{ value: 31, label: '欠税公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
                    fieldValue: [[200, 500]],
                    options: [{ label: '200万元-500万元', value: [200, 500] }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '欠税金额在200万元以下',
                comment: '命中【1】riskCategories=30【2】taxOwedAmount （ChangeExtend.B）',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [31],
                    options: [{ value: 31, label: '欠税公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
                    fieldValue: [[0, 200]],
                    options: [{ label: '200万元以下', value: [0, 200] }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '金融监管',
        '金融监管',
        6,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '处理措施为：暂停相关业务，暂停或者限制交易权限，加入黑名单',
                comment: '命中 【1】riskCategories=121, 【2】financialPenaltyCause = （ChangeExtend.I）',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [121],
                    options: [{ value: 121, label: '金融监管' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
                    fieldValue: ['112', '111', '118'],
                    options: [
                      { label: '暂停相关业务', value: '112', esCode: 'A112' },
                      { label: '暂停或者限制交易权限', value: '111', esCode: 'A111' },
                      { label: '加入黑名单', value: '118', esCode: 'A118' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '处理措施为：严重警告，注销基金管理人登记',
                comment: '命中 【1】riskCategories=121, 【2】financialPenaltyCause = （ChangeExtend.I',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [121],
                    options: [{ value: 121, label: '金融监管' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
                    fieldValue: ['110', '119'],
                    options: [
                      { label: '严重警告', value: '110', esCode: 'A110' },
                      { label: '注销基金管理人登记', value: '119', esCode: 'A119' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName:
                  '处理措施为：公开谴责，监管关注，监管警示，监管函，诫勉谈话，警告，警示函，内部批评，书面警示，通报批评，认定不适当人选，责令致歉，自律管理，其他处理措施',
                comment: '命中 【1】riskCategories=121, 【2】financialPenaltyCause = （ChangeExtend.I',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [121],
                    options: [{ value: 121, label: '金融监管' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
                    fieldValue: ['101', '102', '104', '103', '105', '117', '106', '107', '109', '116', '108', '114', '115', '199'],
                    options: [
                      { label: '公开谴责', value: '101', esCode: 'A101' },
                      { label: '监管关注', value: '102', esCode: 'A102' },
                      { label: '监管警示', value: '104', esCode: 'A104' },
                      { label: '监管函', value: '103', esCode: 'A103' },
                      { label: '诫勉谈话', value: '105', esCode: 'A105' },
                      { label: '警告', value: '117', esCode: 'A117' },
                      { label: '警示函', value: '106', esCode: 'A106' },
                      { label: '内部批评', value: '107', esCode: 'A107' },
                      { label: '书面警示', value: '109', esCode: 'A109' },
                      { label: '通报批评', value: '116', esCode: 'A116' },
                      { label: '认定不适当人选', value: '108', esCode: 'A108' },
                      { label: '责令致歉', value: '114', esCode: 'A114' },
                      { label: '自律管理', value: '115', esCode: 'A115' },
                      { label: '其他处理措施', value: '199', esCode: 'A199' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '税收违法',
        '税收违法',
        7,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '税收违法',
                comment: '命中【1】riskCategories=29',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [29],
                    options: [{ value: 29, label: '税收违法' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '税务处罚',
                comment: '命中【1】riskCategories=107',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
                    accessScope: 1,
                    fieldValue: [1],
                    options: [{ label: 1, value: '税务局' }],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [107],
                    options: [{ value: 107, label: '行政处罚' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '抽查检查',
        '抽查检查',
        8,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业被抽查检查结果为不合格',
                comment: '命中 【1】riskCategories=14, 【2】检查结果inspectionResultType  = 不合格（ChangeExtend.A）',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [14],
                    options: [{ value: 14, label: '抽查检查' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.inspectionResultType,
                    fieldValue: [0],
                    options: [{ value: 0, label: '不合格' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '双随机抽查',
        '双随机抽查',
        9,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业被双随机抽查',
                comment: '命中riskCategories=63',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [63],
                    options: [{ value: 63, label: '双随机抽查' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '惩戒名单',
        '惩戒名单',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业被列入惩戒名单',
                comment: '命中riskCategories=77',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [77],
                    options: [{ value: 77, label: '惩戒名单' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '未准入境',
        '未准入境',
        10,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业相关产品被禁止入境',
                comment: '命中riskCategories=98',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [98],
                    options: [{ value: 98, label: '未准入境' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '产品召回',
        '产品召回',
        11,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业产品被召回',
                comment: '命中riskCategories=78',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [78],
                    options: [{ value: 78, label: '产品召回' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '食品安全',
        '食品安全',
        12,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '销售/生产的产品被抽检不合格',
                comment: '命中riskCategories=79',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [79],
                    options: [{ value: 79, label: '食品安全' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.productSource,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '销售的产品' },
                      { value: 2, label: '生产的产品' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '注销备案',
        '注销备案',
        13,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '企业被注销备案',
                comment: '命中riskCategories=61',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [61],
                    options: [{ value: 61, label: '注销备案' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '简易注销',
        '简易注销',
        14,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '正在进行，准予注销，准许注销',
                comment: '命中riskCategories=23',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [23],
                    options: [{ value: 23, label: '简易注销' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.simpleCancelType,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '正在进行' },
                      { value: 2, label: '准予注销' },
                      { value: 3, label: '准许注销' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '不予受理，已撤销',
                comment: '命中riskCategories=23',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [23],
                    options: [{ value: 23, label: '简易注销' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.simpleCancelType,
                    fieldValue: [4, 5],
                    options: [
                      { value: 4, label: '不予受理' },
                      { value: 5, label: '已撤销' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorSupervisionMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '票据违约',
        '票据违约',
        15,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业存在票据违约',
                comment: '命中riskCategories=108',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [108],
                    options: [{ value: 108, label: '票据违约' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '融资动态',
        '融资动态',
        16,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业获得融资',
                comment: '命中riskCategories=28',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [28],
                    options: [{ value: 28, label: '融资动态' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '企业公告',
        '企业公告',
        17,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '产生企业公告',
                comment: '命中riskCategories=65，113',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [65, 113],
                    options: [
                      { value: 65, label: '企业公告' },
                      { value: 113, label: '企业公告' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.announcementReportType,
                    fieldValue: [],
                    options: AnnouncementReportType,
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '股权出质',
        '股权出质',
        18,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '总股本被出质比例在95%以上',
                comment: '命中riskCategories=12',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgedRatioOrHolding,
                    fieldValue: [95],
                    options: [
                      {
                        unit: '%',
                        min: 0,
                        max: 100,
                      },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '总股本被出质比例在95%以下',
                comment: '命中riskCategories=12',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgedRatioOrHolding,
                    fieldValue: [95],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '股权质押',
        '股权质押',
        19,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '总股本被质押比例在95%以上',
                comment: '命中 riskCategories=50',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.pledgedRatioOrHolding,
                    fieldValue: [95],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '总股本被质押比例在95%以下',
                comment: '命中riskCategories=50',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.pledgedRatioOrHolding,
                    fieldValue: [95],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '动产抵押',
        '动产抵押',
        20,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '被担保主债权数额在0万元以上',
                comment: '命中riskCategories=15',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [15],
                    options: [{ value: 15, label: '动产抵押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.guaranteedPrincipal,
                    fieldValue: [0],
                    options: [{ unit: '万元', min: 0, max: 99999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      // 数据支持不足，先下线
      /* createMetric(
         manager,
         user,
         g3.groupId,
         '土地抵押',
         '土地抵押',
         21,
         [
           {
             order: 0,
             maxScore: 10,
             riskLevel: DimensionRiskLevelEnum.Alert,
             dimsFields: [
               {
                 dimStrategyName: '抵押金额在0万元以上',
                 comment: '命中riskCategories=29',
                 dimKey: DimensionTypeEnums.RiskChange,
                 fields: [
                   {
                     fieldKey: DimensionFieldKeyEnums.isValid,
                     fieldValue: [1],
                     accessScope: 1,
                     compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                   },
                   {
                     fieldKey: DimensionFieldKeyEnums.riskCategories,
                     fieldValue: [30],
                     options: [{ value: 30, label: '土地抵押' }],
                     accessScope: 1,
                     compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                   },
                   {
                     fieldKey: DimensionFieldKeyEnums.landMortgageAmount,
                     fieldValue: [0],
                     options: [{ unit: '万元', min: 0, max: 99999999 }],
                     accessScope: 2,
                     compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                   },
                 ],
               },
             ],
           },
         ],
         MetricTypeEnums.MonitorSupervisionMetric,
         {
           mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
           allowMultipleDynamics: false,
           allowRepeatedHits: true,
         },
       ),*/
      createMetric(
        manager,
        user,
        g3.groupId,
        '担保信息',
        '担保信息',
        22,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '担保金额在50000万元以上',
                comment: '命中riskCategories=53,101',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [53, 101],
                    options: [
                      { value: 53, label: '担保信息' },
                      { value: 101, label: '担保信息' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.guaranteeAmount,
                    fieldValue: [50000],
                    options: [{ unit: '万元', min: 0, max: 99999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '担保金额在50000万元以下',
                comment: '命中riskCategories=53,101',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [53, 101],
                    options: [
                      { value: 53, label: '担保信息' },
                      { value: 101, label: '担保信息' },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.guaranteeAmount,
                    fieldValue: [50000],
                    options: [{ unit: '万元', min: 0, max: 99999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '公示催告',
        '公示催告',
        23,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业公示催告',
                comment: '命中riskCategories=51',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [51],
                    options: [{ value: 51, label: '公示催告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '资产拍卖',
        '资产拍卖',
        24,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业涉及资产拍卖',
                comment: '命中riskCategories=75',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [75],
                    options: [{ value: 75, label: '资产拍卖' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.quoteResultPrice,
                    fieldValue: [],
                    options: [{ unit: '元', min: 0, max: 99999999, label: '起拍价（元）' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '知识产权出质',
        '知识产权出质',
        25,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '出质人商标',
                comment: '命中riskCategories=86, 出质人，商标',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [86],
                    options: [{ value: 86, label: '知识产权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.intellectualRole,
                    fieldValue: [1],
                    options: [{ value: 1, label: '出质人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.intellectualType,
                    fieldValue: [2],
                    options: [{ value: 2, label: '商标' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '质权人专利',
                comment: '命中riskCategories=86，质权人，专利',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [86],
                    options: [{ value: 86, label: '知识产权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.intellectualRole,
                    fieldValue: [2],
                    options: [{ value: 2, label: '质权人' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.intellectualType,
                    fieldValue: [1],
                    options: [{ value: 1, label: '专利' }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '债券违约',
        '债券违约',
        26,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '企业涉及债券违约',
                comment: '命中riskCategories=110',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [110],
                    options: [{ value: 110, label: '债券违约' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '税务催报',
        '税务催报',
        27,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '企业被税务催报',
                comment: '命中riskCategories=130',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [130],
                    options: [{ value: 130, label: '税务催报' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '税务催缴',
        '税务催缴',
        28,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '欠缴金额在500万元以上',
                comment: '命中riskCategories=131',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [131],
                    options: [{ value: 131, label: '税务催缴' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.AmountOwed,
                    fieldValue: [500],
                    options: [{ unit: '万元', min: 0, max: 99999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            dimsFields: [
              {
                dimStrategyName: '欠缴金额在500万元以下',
                comment: '命中riskCategories=131',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [131],
                    options: [{ value: 131, label: '税务催缴' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.AmountOwed,
                    fieldValue: [500],
                    options: [{ unit: '万元', min: 0, max: 99999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '减资公告',
        '减资公告',
        29,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '一个自然年企业注册资本变更趋势大于50%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 123, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                    fieldValue: [
                      {
                        valuePeriodTrend: 1,
                        valuePeriodThreShold: [[50]],
                        valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                        valuePeriodBaseLine: 1,
                      },
                    ],
                    options: [
                      {
                        valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                        valuePeriodThreShold: {
                          label: '占比',
                          value: { unit: '%', min: 0, max: 100 },
                        },
                        valuePeriodThreSholdCompareType: {
                          label: '占比比较',
                          value: DimensionFieldCompareTypeEnums.ContainsAny,
                        },
                        valuePeriodBaseLine: {
                          label: '时间基准',
                          value: BaseLineDateSelect,
                        },
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '企业减资幅度大于50%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 123, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
                    accessScope: 2,
                    options: [{ label: '50%以上', value: [50] }],
                    fieldValue: [[50]],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '一个自然年企业注册资本变更趋势20-50%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 37, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                    fieldValue: [
                      {
                        valuePeriodTrend: 1,
                        valuePeriodThreShold: [[20, 50]],
                        valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                        valuePeriodBaseLine: 1,
                      },
                    ],
                    options: [
                      {
                        valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                        valuePeriodThreShold: {
                          label: '占比',
                          value: { unit: '%', min: 0, max: 100 },
                        },
                        valuePeriodThreSholdCompareType: {
                          label: '占比比较',
                          value: DimensionFieldCompareTypeEnums.ContainsAny,
                        },
                        valuePeriodBaseLine: {
                          label: '时间基准',
                          value: BaseLineDateSelect,
                        },
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '企业减资幅度小于20%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 123, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
                    accessScope: 2,
                    options: [{ label: '20%以下', value: [0, 20] }],
                    fieldValue: [[0, 20]],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '企业减资幅度20-50%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 123, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
                    accessScope: 2,
                    options: [{ label: '20%-50%', value: [20, 50] }],
                    fieldValue: [[20, 50]],
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
              {
                dimStrategyName: '一个自然年企业注册资本变更趋势小于20%',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 37, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
                    fieldValue: [
                      {
                        valuePeriodTrend: 1,
                        valuePeriodThreShold: [[0, 20]],
                        valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
                        valuePeriodBaseLine: 1,
                      },
                    ],
                    options: [
                      {
                        valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
                        valuePeriodThreShold: {
                          label: '占比',
                          value: { unit: '%', min: 0, max: 100 },
                        },
                        valuePeriodThreSholdCompareType: {
                          label: '占比比较',
                          value: DimensionFieldCompareTypeEnums.ContainsAny,
                        },
                        valuePeriodBaseLine: {
                          label: '时间基准',
                          value: BaseLineDateSelect,
                        },
                      },
                    ],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
              {
                dimStrategyName: '涉及币种变更',
                comment: '命中riskCategories=123',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [123],
                    options: [{ value: 123, label: '减资公告' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.currencyChange,
                    fieldValue: [1],
                    options: CurrencyChangeMap,
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Equal,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '大股东及实控人新增股权出质',
        '大股东及实控人新增股权出质',
        30,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '出质比例>5%',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业 , 出质比例>5%',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
                    fieldValue: [5],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '出质股权数额大于5000万元',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业 , 股权金额',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeAmount,
                    fieldValue: [50000000],
                    options: [{ unit: '元', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '出质股份数量大于5000万股',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业 , 股权金额',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeQuantity,
                    fieldValue: [50000000],
                    options: [{ unit: '股', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '出质比例1%-5%',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业, 出质比例1%-5% ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
                    fieldValue: [1, 5],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Between,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '出质比例＜1%',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业; 出质比例＜1% ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
                    fieldValue: [1],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
              {
                dimStrategyName: '新增对外出质',
                comment: '命中riskCategories=12； 命中角色（大股东，实际控制人）, "Status":"有效" ；KeyNo = 为人员 CompanyId= 当前企业 ',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [12],
                    options: [{ value: 12, label: '股权出质' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
                    fieldValue: [1],
                    options: [
                      { value: 1, label: '有效' },
                      { value: 2, label: '无效' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '大股东及实控人新增股权质押',
        '大股东及实控人新增股权质押',
        31,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '占总股本比例＞10%',
                comment:
                  '命中riskCategories=50； 命中人员角色（大股东，实际控制人）staffRole=1，2； HolderArray[0].KeyNo = 人，Compada.K = 当前企业，质押状态 T = 未达预警线,已解除质押，已达预警线未达平仓线; 占总股本比例＞10%',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '未达预警线' },
                      { value: 2, label: '已解除质押' },
                      { value: 3, label: '已达预警线未达平仓线' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
                    fieldValue: [10],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
              {
                dimStrategyName: '质押股份数量大于5000万(股)',
                comment:
                  '命中riskCategories=50； 命中人员角色（大股东，实际控制人）staffRole=1，2； HolderArray[0].KeyNo = 人，Compada.K = 当前企业，质押状态 T = 未达预警线,已解除质押，已达预警线未达平仓线; 质押股份数量大于5000万(股)',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '未达预警线' },
                      { value: 2, label: '已解除质押' },
                      { value: 3, label: '已达预警线未达平仓线' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.stockPledgeQuantity,
                    fieldValue: [50000000],
                    options: [{ unit: '股', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '占总股本比例 5%-10%',
                comment:
                  '命中riskCategories=50； 命中人员角色（大股东，实际控制人）staffRole=1，2； HolderArray[0].KeyNo = 人，Compada.K = 当前企业，质押状态 T = 未达预警线,已解除质押，已达预警线未达平仓线; 占总股本比例 5%-10%',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '未达预警线' },
                      { value: 2, label: '已解除质押' },
                      { value: 3, label: '已达预警线未达平仓线' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
                    fieldValue: [5, 10],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Between,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '占总股本比例＜5%',
                comment:
                  '命中riskCategories=50； 命中人员角色（大股东，实际控制人）staffRole=1，2； HolderArray[0].KeyNo = 人，Compada.K = 当前企业，质押状态 T = 未达预警线,已解除质押，已达预警线未达平仓线; 占总股本比例＜5%',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '未达预警线' },
                      { value: 2, label: '已解除质押' },
                      { value: 3, label: '已达预警线未达平仓线' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
                    fieldValue: [5],
                    options: [{ unit: '%', min: 0, max: 100 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
              {
                dimStrategyName: '新增对外质押',
                comment:
                  '命中riskCategories=50； 命中人员角色（大股东，实际控制人）staffRole=1，2； HolderArray[0].KeyNo = 人，Compada.K = 当前企业，质押状态 T = 未达预警线,已解除质押，已达预警线未达平仓线;',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [50],
                    options: [{ value: 50, label: '股权质押' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
                    fieldValue: [1, 2, 3],
                    options: [
                      { value: 1, label: '未达预警线' },
                      { value: 2, label: '已解除质押' },
                      { value: 3, label: '已达预警线未达平仓线' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
      createMetric(
        manager,
        user,
        g3.groupId,
        '大股东及实控人新增股权冻结',
        '大股东及实控人新增股权冻结',
        32,
        [
          {
            order: 0,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.High,
            dimsFields: [
              {
                dimStrategyName: '冻结股权数额在5000万元以上',
                comment:
                  '命中riskCategories=26； 命中人员角色（大股东，实际控制人）staffRole=1，2； 类型状态（暂无），KeyNo = 为人员， CompanyId = 当前企业; 冻结数额在5000万元以上',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
                    fieldValue: [50000000],
                    options: [{ unit: '元', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.GreaterThan,
                  },
                ],
              },
            ],
          },
          {
            order: 1,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Medium,
            dimsFields: [
              {
                dimStrategyName: '冻结股权数额在500到5000万元之间',
                comment:
                  '命中riskCategories=26； 命中人员角色（大股东，实际控制人）staffRole=1，2； 类型状态（暂无），KeyNo = 为人员， CompanyId = 当前企业; 冻结数额在500到5000万元之间',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
                    fieldValue: [5000000, 50000000],
                    options: [{ unit: '元', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.Between,
                  },
                ],
              },
            ],
          },
          {
            order: 2,
            maxScore: 10,
            riskLevel: DimensionRiskLevelEnum.Alert,
            operation: 'should',
            dimsFields: [
              {
                dimStrategyName: '冻结股权数额在500万元以下',
                comment:
                  '命中riskCategories=26； 命中人员角色（大股东，实际控制人）staffRole=1，2； 类型状态（暂无），KeyNo = 为人员， CompanyId = 当前企业; 冻结数额在500万元以下',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
                    fieldValue: [5000000],
                    options: [{ unit: '元', min: 0, max: 99999999999 }],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.LessThan,
                  },
                ],
              },
              {
                dimStrategyName: '新增对外冻结',
                comment: '命中riskCategories=26； 命中人员角色（大股东，实际控制人）staffRole=1，2； 类型状态（暂无），KeyNo = 为人员， CompanyId = 当前企业;',
                dimKey: DimensionTypeEnums.RiskChange,
                fields: [
                  {
                    fieldKey: DimensionFieldKeyEnums.isValid,
                    fieldValue: [1],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.riskCategories,
                    fieldValue: [26],
                    options: [{ value: 26, label: '股权冻结' }],
                    accessScope: 1,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                  {
                    fieldKey: DimensionFieldKeyEnums.holderRole,
                    fieldValue: [1, 2],
                    options: [
                      { value: 1, label: '大股东' },
                      { value: 2, label: '实际控制人' },
                    ],
                    accessScope: 2,
                    compareType: DimensionFieldCompareTypeEnums.ContainsAny,
                  },
                ],
              },
            ],
          },
        ],
        MetricTypeEnums.MonitorBusinessMetric,
        {
          mainDataSourceType: MetricDynamicDataSourceTypeEnum.Dynamic,
          allowMultipleDynamics: false,
          allowRepeatedHits: true,
        },
        ScoreStrategyEnums.MaxLevel,
      ),
    ]);

    // 4. 分发模型给租户
    await this.riskModelService.distributeRiskModel(
      {
        riskModelId: riskModel.modelId,
        orgId: toOrgId,
      },
      user,
    );
    return riskModel;
  }
}
