import { Test, TestingModule } from '@nestjs/testing';
// import { MonitorService } from '../monitor.service';
// import { MonitorController } from './monitor.controller';
// import { MonitorJobService } from '../monitor.job.service';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { PlatformUser } from '../../../libs/model/common';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import {
  AddMonitorGroupRequest,
  AddMonitorPushRule,
  BatchDeleteMonitorCompanyRequest,
  DeleteMonitorCompanyRequest,
  SearchMonitorCompanyRelatedPartyRequest,
  SearchMonitorCompanyRequest,
  TransferMonitorCompanyRequest,
} from '../../../libs/model/monitor';
import { AddMonitorRelatedCompanyItemPO, AddMonitorRelatedCompanyRequest } from '../../../libs/model/monitor';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { clearMonitorTestData } from '../../test_utils_module/monitor.test.tools';
import { EntityManager, getConnection, getRepository, In, Repository } from 'typeorm';
import { AppTestModule } from '../../app/app.test.module';
import { ModelInitMonitorService } from '../../risk_model/init_mode/model.init.monitor.service';
import { MonitorCompanyPrimaryObjectEnum, MonitorCompanyRelatedStatusEnum } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import * as _ from 'lodash';
import { MonitorGroupService } from '../group/monitor.group.service';
import { MonitorCompanyService } from './monitor.company.service';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { ResourceOperationException } from 'libs/exceptions/ResourceOperationException';
import { CompanyRelatedListParams, CompanyRelatedMonitorAllRequest } from '../../../libs/model/data/request';
import { RelatedCompanySource } from '../../data/source/related-company.source';
import { clearAllTestRiskModelTestData } from '../../test_utils_module/riskmodel.test.utils';
import { DistributedResourceTypeEnums } from '../../../libs/enums/DistributedResourceTypeEnums';
import { CompanySearchService } from '../../company/company-search.service';
import { KysCompanyResponseDetails } from '@kezhaozhao/company-search-api';
import { MonitorModule } from '../monitor.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { BadRequestException } from '@nestjs/common';
import { InternalServerErrorException } from '@nestjs/common';
import { PushEnableEnum } from '../../../libs/enums/push/PushEnableEnum';
import { PushRuleJsonPo } from '../../../libs/model/push/PushRuleJsonPo';
import { PushScopePo } from '../../../libs/model/push/PushScopePo';
import { PushTimePo } from '../../../libs/model/push/PushTimePo';
import { PushFrequencyTypeEnum } from '../../../libs/enums/push/PushFrequencyTypeEnum';
import { MonitorStatusEnums } from '../../../libs/enums/monitor/MonitorStatusEnums';

jest.setTimeout(600000);
describe('MonitorCompanyService  test', () => {
  let monitorCompanyService: MonitorCompanyService;
  let monitorGroupService: MonitorGroupService;
  let modelInitMonitorService: ModelInitMonitorService;
  let companySearchService: CompanySearchService;
  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('monitor.company.related.service.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let entityManager: EntityManager;
  let monitorCompanyRepo: Repository<MonitorCompanyEntity>;
  let companyRelatedRepo: Repository<MonitorCompanyRelatedPartyEntity>;
  let monitorGroupRepo: Repository<MonitorGroupEntity>;
  let nebulaGraphService: RelatedCompanySource;
  let testMonitorModelEntity: RiskModelEntity;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      // controllers: [MonitorController],
      // providers: [MonitorCompanyService, MonitorGroupService, MonitorJobService, MonitorDynamicService, MonitorDynamicEsService],
      imports: [MonitorModule, AppTestModule],
    }).compile();
    monitorCompanyService = module.get<MonitorCompanyService>(MonitorCompanyService);
    monitorGroupService = module.get<MonitorGroupService>(MonitorGroupService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    monitorCompanyRepo = getRepository(MonitorCompanyEntity);
    monitorGroupRepo = getRepository(MonitorGroupEntity);
    companyRelatedRepo = getRepository(MonitorCompanyRelatedPartyEntity);
    entityManager = monitorCompanyRepo.manager;
    modelInitMonitorService = module.get<ModelInitMonitorService>(ModelInitMonitorService);
    nebulaGraphService = module.get<RelatedCompanySource>(RelatedCompanySource);
    testMonitorModelEntity = await modelInitMonitorService.createMonitorModel(testUser, '自动创建监控模型001' + new Date().getTime(), testUser.currentOrg);
  });

  let defaultGroupEntity: MonitorGroupEntity;
  beforeEach(async () => {
    await clearMonitorTestData(entityManager, testUser);
    testMonitorModelEntity = await modelInitMonitorService.createMonitorModel(testUser, '自动创建监控模型001' + new Date().getTime(), testUser.currentOrg);
    await monitorGroupService.initMonitor(testUser);
    defaultGroupEntity = await entityManager.findOne(MonitorGroupEntity, {
      orgId: testUser.currentOrg,
      product: ProductCodeEnums.Pro,
    });
    expect(defaultGroupEntity).not.toBeNull();

    // Spy on checkCompanyStandardCode method
    jest.spyOn(companySearchService, 'checkCompanyStandardCode').mockImplementation(async (companyIds: string[]) => {
      const supportedCompanies: KysCompanyResponseDetails[] = [];
      const unsupportedCompanies: KysCompanyResponseDetails[] = [];

      // 为每个公司ID创建一个响应对象
      companyIds.forEach((id) => {
        const companyInfo = new KysCompanyResponseDetails();
        companyInfo.id = id;
        companyInfo.name = `测试公司${id.substring(0, 5)}`;
        // 设置标准代码，这里假设所有公司都是支持的
        companyInfo['standard_code'] = ['100000']; // 假设这是一个有效的标准代码

        supportedCompanies.push(companyInfo);
      });

      // 使用 any 类型来绕过类型检查
      const companyInfos: any = {
        Result: supportedCompanies,
        GroupItems: [],
        Paging: {
          PageIndex: 1,
          PageSize: companyIds.length,
          TotalRecords: companyIds.length,
        },
        Status: 200,
        scrollId: '',
      };

      // 打印日志，帮助调试
      // console.log(`checkCompanyStandardCode 被调用，参数：${JSON.stringify(companyIds)}`);
      // console.log(`返回结果：supportedCompanies.length = ${supportedCompanies.length}`);

      return {
        supportedCompanies,
        unsupportedCompanies,
        companyInfos,
      };
    });

    // 直接 spy MonitorCompanyService 的 checkCompanyStandardCode 方法
    jest.spyOn(monitorCompanyService, 'checkCompanyStandardCode' as any).mockImplementation(async (companyIds: string[]) => {
      const supportedCompanies: KysCompanyResponseDetails[] = [];
      const unsupportedCompanies: KysCompanyResponseDetails[] = [];

      // 为每个公司ID创建一个响应对象
      companyIds.forEach((id) => {
        const companyInfo = new KysCompanyResponseDetails();
        companyInfo.id = id;
        companyInfo.name = `测试公司${id.substring(0, 5)}`;
        // 设置标准代码，这里假设所有公司都是支持的
        companyInfo['standard_code'] = ['100000']; // 假设这是一个有效的标准代码

        if (id.indexOf('unsupported') >= 0) {
          unsupportedCompanies.push(companyInfo);
        } else {
          supportedCompanies.push(companyInfo);
        }
      });

      // 使用 any 类型来绕过类型检查
      const companyInfos: any = {
        Result: supportedCompanies,
        GroupItems: [],
        Paging: {
          PageIndex: 1,
          PageSize: companyIds.length,
          TotalRecords: companyIds.length,
        },
        Status: 200,
        scrollId: '',
      };

      // 打印日志，帮助调试
      // console.log(`MonitorCompanyService.checkCompanyStandardCode 被调用，参数：${JSON.stringify(companyIds)}`);
      // console.log(`返回结果：supportedCompanies.length = ${supportedCompanies.length}`);

      // 确保 supportedCompanies 不为空
      if (supportedCompanies.length === 0) {
        console.error('警告：supportedCompanies 为空，这可能导致测试失败');
      }

      return {
        supportedCompanies,
        unsupportedCompanies,
        companyInfos,
      };
    });
  });
  afterAll(async () => {
    await clearMonitorTestData(entityManager, testUser);
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.MonitorRiskModel, [testMonitorModelEntity.modelId]);
    const connection = getConnection();
    await connection.close();
  });
  describe('saveRelatedCompany() test', () => {
    // 添加测试前的准备工作
    beforeEach(async () => {
      // 添加一个主体公司用于测试
      const companyItem = {
        companyId: '8d3d1836c775514f8fc92c95b608b018',
        companyName: '北京斯尔科技有限公司',
      };

      const companyItem2 = {
        companyId: '440e0dd77abb3c3434a1f7310d48c667',
        companyName: '上海嘉阡点网络科技有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyItem, companyItem2],
      });
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('应该处理关联方1作为同分组下公司A以及公司B的共同关联方，且公司A已关联关联方1的情况下，公司B关联关联方1应当只新增关联关系且额度不扣除', async () => {
      const companyAID = '8d3d1836c775514f8fc92c95b608b018';
      const companyBID = '440e0dd77abb3c3434a1f7310d48c667';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };
      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyAID, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result.updatedList).toHaveLength(0);

      // 执行测试
      const result2 = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyBID, [relatedItem]);

      // 验证结果
      expect(result2).toBeDefined();
      expect(result2.successList).toHaveLength(0);
      expect(result2.updatedList).toHaveLength(1); // 监控列表已存在，只添加新的关联关系，额度不会扣除
    });

    it('应该处理公司1作为同分组下公司A以及公司B的共同关联方，且公司A以及公司B均关联公司1，删除公司A的关联方公司1的情况下，公司B与公司1的关联关系应当不存在且公司1不在监控列表中，', async () => {
      //删除关联方企业是删除企业并非删除关联关系
      const companyAID = '8d3d1836c775514f8fc92c95b608b018';
      const companyBID = '440e0dd77abb3c3434a1f7310d48c667';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };
      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyAID, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result.updatedList).toHaveLength(0);

      // 执行测试
      const result2 = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyBID, [relatedItem]);

      // 验证结果
      expect(result2).toBeDefined();
      expect(result2.successList).toHaveLength(0);
      expect(result2.updatedList).toHaveLength(1); // 监控列表已存在，只添加新的关联关系，额度不会扣除

      // 执行删除关联方操作
      const deleteRelatedCompanyRequest = new DeleteMonitorCompanyRequest();
      deleteRelatedCompanyRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      deleteRelatedCompanyRequest.companyId = existingCompany.companyId;

      const batchDeleteReq = new BatchDeleteMonitorCompanyRequest();
      batchDeleteReq.deleteMonitors = [deleteRelatedCompanyRequest];

      const deleteResult = await monitorCompanyService.batchCompanyDelete(testUser, batchDeleteReq);

      expect(deleteResult).toBeDefined();
      expect(deleteResult.affected).toBe(1);
      // 验证关联关系是否仍然存在
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: companyBID,
          companyIdRelated: existingCompany.companyId,
        },
      });

      expect(relatedParties?.length).toBe(0);

      // 验证关联公司是否仍然在监控列表中
      const relatedCompanyEntity = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: existingCompany.companyId,
      });

      expect(relatedCompanyEntity).toBeUndefined();

      // 验证主体公司的关联方计数是否未改变
      const primaryCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: companyBID,
      });
      expect(primaryCompany).toBeDefined();
      expect(primaryCompany.relatedPartyCount).toBe(0);

      // 验证主体公司的关联方计数是否改变
      const primaryCompany2 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: companyAID,
      });
      expect(primaryCompany2).toBeDefined();
      expect(primaryCompany2.relatedPartyCount).toBe(0);
    });

    it('应该处理公司1作为同分组下公司A以及公司B的共同关联方，且公司A以及公司B均关联公司1，公司A被删除时，公司B以及公司1的关联关系仍然存在，且公司1仍存在于监控列表中', async () => {
      const companyAID = '8d3d1836c775514f8fc92c95b608b018';
      const companyBID = '440e0dd77abb3c3434a1f7310d48c667';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };
      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyAID, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result.updatedList).toHaveLength(0);

      // 执行测试
      const result2 = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyBID, [relatedItem]);

      // 验证结果
      expect(result2).toBeDefined();
      expect(result2.successList).toHaveLength(0);
      expect(result2.updatedList).toHaveLength(1); // 监控列表已存在，只添加新的关联关系，额度不会扣除

      //执行删除公司A的操作
      const deleteRelatedCompanyRequest = new DeleteMonitorCompanyRequest();
      deleteRelatedCompanyRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      deleteRelatedCompanyRequest.companyId = companyAID;
      const batchDeleteReq = new BatchDeleteMonitorCompanyRequest();
      batchDeleteReq.deleteMonitors = [deleteRelatedCompanyRequest];

      const deleteResult = await monitorCompanyService.batchCompanyDelete(testUser, batchDeleteReq);

      expect(deleteResult).toBeDefined();
      expect(deleteResult.affected).toBe(1);
      expect(deleteResult.affectedInfos[0]).toBe(companyAID);

      const companyEntity = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: existingCompany.companyId,
      });

      expect(companyEntity).toBeDefined();

      //查询主体公司B的关联方
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: companyBID,
          companyIdRelated: existingCompany.companyId,
        },
      });

      expect(relatedParties?.length).toBe(1);
    });

    it('应该处理公司1作为同分组下公司A的关联方，且除公司A外未被其他监控主体添加为关联方，公司A被删除时，公司A以及公司1的关联关系不存在，且公司1不存在于监控列表中', async () => {
      const companyAID = '8d3d1836c775514f8fc92c95b608b018';
      const companyBID = '440e0dd77abb3c3434a1f7310d48c667';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };
      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyAID, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result.updatedList).toHaveLength(0);

      //执行删除公司A的操作
      const deleteRelatedCompanyRequest = new DeleteMonitorCompanyRequest();
      deleteRelatedCompanyRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      deleteRelatedCompanyRequest.companyId = companyAID;
      const batchDeleteReq = new BatchDeleteMonitorCompanyRequest();
      batchDeleteReq.deleteMonitors = [deleteRelatedCompanyRequest];

      const deleteResult = await monitorCompanyService.batchCompanyDelete(testUser, batchDeleteReq);

      expect(deleteResult).toBeDefined();
      expect(deleteResult.affected).toBe(2);

      const companyEntity = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: existingCompany.companyId,
      });

      expect(companyEntity).toBeUndefined();

      //查询主体公司A的关联方
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: companyAID,
          companyIdRelated: existingCompany.companyId,
        },
      });

      expect(relatedParties?.length).toBe(0);
    });

    it('应该处理同分组下公司A以及公司B互为关联方的情况下，可以互相添加对方为关联方,且仅扣费AB两次', async () => {
      const companyItem = {
        companyId: '8d3d1836c775514f8fc92c95b608b018',
        companyName: '北京斯尔科技有限公司',
      };

      const companyItem2 = {
        companyId: '440e0dd77abb3c3434a1f7310d48c667',
        companyName: '上海嘉阡点网络科技有限公司',
      };

      // 准备添加关联关系
      const relatedItem1 = new AddMonitorRelatedCompanyItemPO();
      relatedItem1.companyId = companyItem2.companyId;
      relatedItem1.companyName = companyItem2.companyName;
      relatedItem1.relatedType = 'Legal';

      const relatedItem2 = new AddMonitorRelatedCompanyItemPO();
      relatedItem2.companyId = companyItem.companyId;
      relatedItem2.companyName = companyItem.companyName;
      relatedItem2.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyItem.companyId, [relatedItem1]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(0);
      expect(result.updatedList).toHaveLength(1); // 监控列表已存在，只添加新的关联关系，额度不会扣除

      // 执行测试
      const result2 = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyItem2.companyId, [relatedItem2]);

      // 验证结果
      expect(result2).toBeDefined();
      expect(result2.successList).toHaveLength(0);
      expect(result2.updatedList).toHaveLength(1); // 监控列表已存在，只添加新的关联关系，额度不会扣除

      //执行查询关联关系表
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: companyItem.companyId,
          companyIdRelated: companyItem2.companyId,
        },
      });

      expect(relatedParties?.length).toBe(1);

      const relatedParties2 = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: companyItem2.companyId,
          companyIdRelated: companyItem.companyId,
        },
      });

      expect(relatedParties2?.length).toBe(1);
    });

    it('应该处理不同分组下，公司A以及公司B均添加公司1作为关联方，如果删除公司A的关联方公司1，那么公司B的关联关系应当不受影响', async () => {
      const companyAID = '8d3d1836c775514f8fc92c95b608b018';
      const companyBID = '440e0dd77abb3c3434a1f7310d48c667';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };
      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, companyAID, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result.updatedList).toHaveLength(0);

      const req = new AddMonitorGroupRequest();
      req.groupName = 'test';
      req.pushEnable = PushEnableEnum.Disable;
      req.monitorStatus = MonitorStatusEnums.Disabled;

      const monitorGroupEntity = await monitorGroupService.addMonitorGroup(testUser, req);
      expect(monitorGroupEntity).not.toBeNull();

      // 执行测试
      const result1 = await saveRelatedCompanyMethod(testUser, monitorGroupEntity.monitorGroupId, companyBID, [relatedItem]);

      // 验证结果
      expect(result1).toBeDefined();
      expect(result1.successList).toHaveLength(1); // 添加新监控公司且添加新的关联关系
      expect(result1.updatedList).toHaveLength(0);

      //执行删除公司1的操作
      const deleteRelatedCompanyRequest = new DeleteMonitorCompanyRequest();
      deleteRelatedCompanyRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      deleteRelatedCompanyRequest.companyId = existingCompany.companyId;
      const batchDeleteReq = new BatchDeleteMonitorCompanyRequest();
      batchDeleteReq.deleteMonitors = [deleteRelatedCompanyRequest];

      const deleteResult = await monitorCompanyService.batchCompanyDelete(testUser, batchDeleteReq);

      expect(deleteResult).toBeDefined();
      expect(deleteResult.affected).toBe(1);

      const companyEntity = await monitorCompanyRepo.findOne({
        monitorGroupId: monitorGroupEntity.monitorGroupId,
        companyId: existingCompany.companyId,
      });

      expect(companyEntity).toBeDefined();

      //查询主体公司B的关联方
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: monitorGroupEntity.monitorGroupId,
          companyIdPrimary: companyBID,
          companyIdRelated: existingCompany.companyId,
        },
      });

      expect(relatedParties?.length).toBe(1);
    });
  });
});
