关联方逻辑：
1，模型设置：
（1）模型设置中，配置关联方信息，关联方数据来源为 Nebra 数据中心， nebra 中返回的关联方数据过滤（不支持监控的港澳台）企业。

2，添加企业入口添加关联方监控：
（1）查询主企业的关联方：查询某个企业的关联方，通过模型 Id 获取 dimension 信息，通过 dimension 信息查询关联方。
查询如果已经是监控企业的（MonitorCompanyRelatedPartyEntity），则打上已经监控的标签。
(2) 添加关联方监控：
判断关联方公司是否已经被添加到监控列表中, 如果不存在，添加到监控列表 ；关联方关系表中已经存在的关联方，判断是否要修改关联关系 ；关联方关系表中不存存在的关联方，添加到关联方表中

3，监控企业列表中的标签点击添加关联方的监控：
（1）页面关联方新增的标签出现的逻辑：
（1.1）每次模型跑关联方监控动态，当前一次尽调和前一次尽调做比较，新增的 count，或者减少的 count，作为当前监控的关联的 count 标签，生成一个 companyMetricsHashkey 绑定到关联方动态。
（1.2）关联方变化预览：companyMetricsHashkey 找到 MonitorMetricsDynamicEntity ，从 ES 获取在当前 bathId 不在前一次 batchId 中的动态数据。
（1.3）关联方变化列表页添加监控：查询两次 differ 返回的企业。

4，监控动态：
（1）当前尽调和前一次尽调做比较，生成新的增量动态。

sf 控制企业的定义是一层，应该是主体对外投资企业中持股比例超过 50%的企业，控制企业列表有穿透持股的逻辑客户只要一层，不能用
需要调整的部分：
主体控制的企业（大于 50%，穿透一层） 主体对外投资企业中持股比例超过过 50%;
实控人控制的企业（大于 50%，穿透一层） 主体实控人对外投资企业中持股比例超过过 50
