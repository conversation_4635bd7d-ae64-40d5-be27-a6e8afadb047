import { Test, TestingModule } from '@nestjs/testing';
// import { MonitorService } from '../monitor.service';
// import { MonitorController } from './monitor.controller';
// import { MonitorJobService } from '../monitor.job.service';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { PlatformUser } from '../../../libs/model/common';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import {
  BatchDeleteMonitorCompanyRequest,
  DeleteMonitorCompanyRequest,
  SearchMonitorCompanyRelatedPartyRequest,
  SearchMonitorCompanyRequest,
  TransferMonitorCompanyRequest,
} from '../../../libs/model/monitor';
import { AddMonitorRelatedCompanyItemPO, AddMonitorRelatedCompanyRequest } from '../../../libs/model/monitor';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { clearMonitorTestData } from '../../test_utils_module/monitor.test.tools';
import { EntityManager, getConnection, getRepository, In, Repository } from 'typeorm';
import { AppTestModule } from '../../app/app.test.module';
import { ModelInitMonitorService } from '../../risk_model/init_mode/model.init.monitor.service';
import { MonitorCompanyPrimaryObjectEnum, MonitorCompanyRelatedStatusEnum } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import * as _ from 'lodash';
import { MonitorGroupService } from '../group/monitor.group.service';
import { MonitorCompanyService } from './monitor.company.service';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { ResourceOperationException } from 'libs/exceptions/ResourceOperationException';
import { CompanyRelatedListParams, CompanyRelatedMonitorAllRequest } from '../../../libs/model/data/request';
import { RelatedCompanySource } from '../../data/source/related-company.source';
import { clearAllTestRiskModelTestData } from '../../test_utils_module/riskmodel.test.utils';
import { DistributedResourceTypeEnums } from '../../../libs/enums/DistributedResourceTypeEnums';
import { CompanySearchService } from '../../company/company-search.service';
import { KysCompanyResponseDetails } from '@kezhaozhao/company-search-api';
import { MonitorModule } from '../monitor.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { BadRequestException } from '@nestjs/common';
import { InternalServerErrorException } from '@nestjs/common';

jest.setTimeout(600000);
describe('MonitorCompanyService  test', () => {
  let monitorCompanyService: MonitorCompanyService;
  let monitorGroupService: MonitorGroupService;
  let modelInitMonitorService: ModelInitMonitorService;
  let companySearchService: CompanySearchService;
  // 测试数据
  const [testOrgId, testUserId] = generateUniqueTestIds('monitor.company.integration.spec.ts');
  const testUser = getTestUser(testOrgId, testUserId);
  let entityManager: EntityManager;
  let monitorCompanyRepo: Repository<MonitorCompanyEntity>;
  let companyRelatedRepo: Repository<MonitorCompanyRelatedPartyEntity>;
  let monitorGroupRepo: Repository<MonitorGroupEntity>;
  let nebulaGraphService: RelatedCompanySource;
  let testMonitorModelEntity: RiskModelEntity;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      // controllers: [MonitorController],
      // providers: [MonitorCompanyService, MonitorGroupService, MonitorJobService, MonitorDynamicService, MonitorDynamicEsService],
      imports: [MonitorModule, AppTestModule],
    }).compile();
    monitorCompanyService = module.get<MonitorCompanyService>(MonitorCompanyService);
    monitorGroupService = module.get<MonitorGroupService>(MonitorGroupService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    monitorCompanyRepo = getRepository(MonitorCompanyEntity);
    monitorGroupRepo = getRepository(MonitorGroupEntity);
    companyRelatedRepo = getRepository(MonitorCompanyRelatedPartyEntity);
    entityManager = monitorCompanyRepo.manager;
    modelInitMonitorService = module.get<ModelInitMonitorService>(ModelInitMonitorService);
    nebulaGraphService = module.get<RelatedCompanySource>(RelatedCompanySource);
    testMonitorModelEntity = await modelInitMonitorService.createMonitorModel(testUser, '自动创建监控模型001' + new Date().getTime(), testUser.currentOrg);
  });

  let defaultGroupEntity: MonitorGroupEntity;
  beforeEach(async () => {
    await clearMonitorTestData(entityManager, testUser);
    testMonitorModelEntity = await modelInitMonitorService.createMonitorModel(testUser, '自动创建监控模型001' + new Date().getTime(), testUser.currentOrg);
    await monitorGroupService.initMonitor(testUser);
    defaultGroupEntity = await entityManager.findOne(MonitorGroupEntity, {
      orgId: testUser.currentOrg,
      product: ProductCodeEnums.Pro,
    });
    expect(defaultGroupEntity).not.toBeNull();

    // Spy on checkCompanyStandardCode method
    jest.spyOn(companySearchService, 'checkCompanyStandardCode').mockImplementation(async (companyIds: string[]) => {
      const supportedCompanies: KysCompanyResponseDetails[] = [];
      const unsupportedCompanies: KysCompanyResponseDetails[] = [];

      // 为每个公司ID创建一个响应对象
      companyIds.forEach((id) => {
        const companyInfo = new KysCompanyResponseDetails();
        companyInfo.id = id;
        companyInfo.name = `测试公司${id.substring(0, 5)}`;
        // 设置标准代码，这里假设所有公司都是支持的
        companyInfo['standard_code'] = ['100000']; // 假设这是一个有效的标准代码

        supportedCompanies.push(companyInfo);
      });

      // 使用 any 类型来绕过类型检查
      const companyInfos: any = {
        Result: supportedCompanies,
        GroupItems: [],
        Paging: {
          PageIndex: 1,
          PageSize: companyIds.length,
          TotalRecords: companyIds.length,
        },
        Status: 200,
        scrollId: '',
      };

      // 打印日志，帮助调试
      // console.log(`checkCompanyStandardCode 被调用，参数：${JSON.stringify(companyIds)}`);
      // console.log(`返回结果：supportedCompanies.length = ${supportedCompanies.length}`);

      return {
        supportedCompanies,
        unsupportedCompanies,
        companyInfos,
      };
    });

    // 直接 spy MonitorCompanyService 的 checkCompanyStandardCode 方法
    jest.spyOn(monitorCompanyService, 'checkCompanyStandardCode' as any).mockImplementation(async (companyIds: string[]) => {
      const supportedCompanies: KysCompanyResponseDetails[] = [];
      const unsupportedCompanies: KysCompanyResponseDetails[] = [];

      // 为每个公司ID创建一个响应对象
      companyIds.forEach((id) => {
        const companyInfo = new KysCompanyResponseDetails();
        companyInfo.id = id;
        companyInfo.name = `测试公司${id.substring(0, 5)}`;
        // 设置标准代码，这里假设所有公司都是支持的
        companyInfo['standard_code'] = ['100000']; // 假设这是一个有效的标准代码

        if (id.indexOf('unsupported') >= 0) {
          unsupportedCompanies.push(companyInfo);
        } else {
          supportedCompanies.push(companyInfo);
        }
      });

      // 使用 any 类型来绕过类型检查
      const companyInfos: any = {
        Result: supportedCompanies,
        GroupItems: [],
        Paging: {
          PageIndex: 1,
          PageSize: companyIds.length,
          TotalRecords: companyIds.length,
        },
        Status: 200,
        scrollId: '',
      };

      // 打印日志，帮助调试
      // console.log(`MonitorCompanyService.checkCompanyStandardCode 被调用，参数：${JSON.stringify(companyIds)}`);
      // console.log(`返回结果：supportedCompanies.length = ${supportedCompanies.length}`);

      // 确保 supportedCompanies 不为空
      if (supportedCompanies.length === 0) {
        console.error('警告：supportedCompanies 为空，这可能导致测试失败');
      }

      return {
        supportedCompanies,
        unsupportedCompanies,
        companyInfos,
      };
    });
  });
  afterAll(async () => {
    await clearMonitorTestData(entityManager, testUser);
    await clearAllTestRiskModelTestData(entityManager, testUser, DistributedResourceTypeEnums.MonitorRiskModel, [testMonitorModelEntity.modelId]);
    const connection = getConnection();
    await connection.close();
  });

  describe('searchMonitorCompanyRelated() test', () => {
    let primaryCompany;
    let relatedCompanies;

    beforeEach(async () => {
      // 准备测试数据 - 添加一个主体公司
      primaryCompany = {
        companyId: '8d3d1836c775514f8fc92c95b608b018',
        companyName: '北京斯尔科技有限公司',
      };

      // 确保使用已初始化的defaultGroupEntity
      expect(defaultGroupEntity).not.toBeNull();
      // console.log(`使用监控组ID: ${defaultGroupEntity.monitorGroupId}`);

      // 添加主体公司
      const addResult = await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [primaryCompany],
      });
      // console.log(`添加主体公司结果: 成功${addResult.successList.length}个, 失败${addResult.failList.length}个`);

      // 添加多个关联方
      relatedCompanies = [
        {
          companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
          companyName: '苏州知彼信息科技中心（有限合伙）',
          relatedType: 'Legal',
        },
        {
          companyId: '8d464621f8fd32be4d667c9ac75c7593',
          companyName: '信阳南信建设发展有限公司',
          relatedType: 'Investment',
        },
      ];

      // 使用AddMonitorRelatedCompanyItemPO数组
      const items: AddMonitorRelatedCompanyItemPO[] = relatedCompanies.map((company) => {
        const item = new AddMonitorRelatedCompanyItemPO();
        item.companyId = company.companyId;
        item.companyName = company.companyName;
        item.relatedType = company.relatedType;
        return item;
      });

      const addRelatedResult = await monitorCompanyService.addRelatedCompany(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompany.companyId,
        items: items,
      });

      // 验证关联方是否成功添加
      const relatedCount = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: primaryCompany.companyId,
      });
      // console.log(`添加关联方后关联关系数量: ${relatedCount}`);

      // 验证关联方公司是否添加成功
      const relatedCompanies1 = await monitorCompanyRepo.find({
        where: {
          companyId: In(relatedCompanies.map((c) => c.companyId)),
          monitorGroupId: defaultGroupEntity.monitorGroupId,
        },
      });
      // console.log(`添加的关联方公司数量: ${relatedCompanies1.length}`);
    });

    it('搜索特定公司的关联方', async () => {
      const searchRequest = new SearchMonitorCompanyRelatedPartyRequest();
      searchRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      searchRequest.companyId = primaryCompany.companyId;

      // 验证主体公司存在
      const primaryExists = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompany.companyId,
      });
      // console.log(`主体公司存在: ${!!primaryExists}, ID: ${primaryCompany.companyId}`);

      const result = await monitorCompanyService.searchMonitorCompanyRelated(searchRequest, testUser);
      // console.log(`搜索结果总数: ${result.total}, 数据项数: ${result.data?.length || 0}`);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
      expect(result.data).toHaveLength(2);
    });

    it('按风险等级倒序搜索关联方', async () => {
      const searchRequest = new SearchMonitorCompanyRelatedPartyRequest();
      searchRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      searchRequest.companyId = primaryCompany.companyId;
      searchRequest.sortField = 'riskLevel';
      searchRequest.isSortAsc = false;

      const updateResult = await monitorCompanyRepo.update(
        {
          companyId: In(['8d464621f8fd32be4d667c9ac75c7593']),
          monitorGroupId: defaultGroupEntity.monitorGroupId,
        },
        {
          riskLevel: 2,
        },
      );
      // console.log(`更新风险等级结果: 影响了${updateResult.affected}行`);
      expect(updateResult).toBeDefined();
      expect(updateResult.affected).toBe(1);

      const result = await monitorCompanyService.searchMonitorCompanyRelated(searchRequest, testUser);
      // console.log(`按风险等级搜索结果: ${result.total}, 数据项数: ${result.data?.length || 0}`);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
      expect(result.data).toHaveLength(2);
      expect(result.data[0].monitorCompanyEntity.companyName).toContain('信阳南信建设发展有限公司');
    });

    it('分页搜索关联方', async () => {
      const searchRequest = new SearchMonitorCompanyRelatedPartyRequest();
      searchRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      searchRequest.companyId = primaryCompany.companyId;
      searchRequest.pageSize = 1;
      searchRequest.pageIndex = 1;

      const result = await monitorCompanyService.searchMonitorCompanyRelated(searchRequest, testUser);
      // console.log(`分页搜索结果: 总数${result.total}, 当前页数据项数: ${result.data?.length || 0}`);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
      expect(result.data).toHaveLength(1);
    });

    it('不分页搜索关联方', async () => {
      const searchRequest = new SearchMonitorCompanyRelatedPartyRequest();
      searchRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      searchRequest.companyId = primaryCompany.companyId;

      const result = await monitorCompanyService.searchMonitorCompanyRelated(searchRequest, testUser, true);
      // console.log(`不分页搜索结果: 总数${result.total}`);

      expect(result).toBeDefined();
      expect(result.total).toBe(2);
    });

    it('搜索不存在主体公司的关联方', async () => {
      const searchRequest = new SearchMonitorCompanyRelatedPartyRequest();
      searchRequest.monitorGroupId = defaultGroupEntity.monitorGroupId;
      searchRequest.companyId = 'non_existent_company_id';

      const result = await monitorCompanyService.searchMonitorCompanyRelated(searchRequest, testUser);
      // console.log(`搜索不存在公司关联方结果: 总数${result.total}`);

      expect(result).toBeDefined();
      expect(result.total).toBe(0);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('addCompanies() test', () => {
    it('正常添加', async () => {
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];
      // step1 增加3个公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });
      const totalMonitorCompanyCount1 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const count12 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
        status: MonitorCompanyRelatedStatusEnum.Valid,
      });
      expect(totalMonitorCompanyCount1).toBe(companyItems.length);
      expect(count12).toBe(0);

      //step2 给companyItems 第一个公司增加一个关联方
      const items: AddMonitorRelatedCompanyItemPO[] = [];
      const item = new AddMonitorRelatedCompanyItemPO();
      item.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      item.companyName = '苏州知彼信息科技中心（有限合伙）';
      item.relatedType = 'Legal';
      items.push(item);
      const req1 = new AddMonitorRelatedCompanyRequest();
      req1.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req1.companyId = companyItems[0].companyId;
      req1.items = items;

      await monitorCompanyService.addRelatedCompany(testUser, req1);
      const totalMonitorCompanyCount2 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const relatedCompanyCount1 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      const primaryCompany1 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });
      expect(totalMonitorCompanyCount2).toBe(totalMonitorCompanyCount1 + 1);
      expect(relatedCompanyCount1).toBe(1);
      // 给企业增加一个关联方之后，企业自身冗余的  relatedPartyCount 应该 +1
      expect(primaryCompany1.relatedPartyCount).toEqual(1);

      //step3 给step2被添加为关联方的 苏州知彼信息科技中心（有限合伙）  增加新的关联方
      const req2 = new AddMonitorRelatedCompanyRequest();
      req2.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req2.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      const item1 = new AddMonitorRelatedCompanyItemPO();
      item1.companyId = '8d464621f8fd32be4d667c9ac75c7593';
      item1.companyName = '信阳南信建设发展有限公司';
      item1.relatedType = 'Legal';
      req2.items = [item1];
      await monitorCompanyService.addRelatedCompany(testUser, req2);

      const relatedCompanyCount2 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: req2.companyId,
      });
      const primaryCompany2 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req2.companyId,
      });
      expect(relatedCompanyCount2).toBe(1);
      expect(primaryCompany2.relatedPartyCount).toEqual(1);

      //search company
      const postData = Object.assign(new SearchMonitorCompanyRequest(), {
        pageSize: 10,
        pageIndex: 1,
        needAggs: 1,
      });
      const metricsDynamic = await monitorCompanyService.searchMonitorCompany(postData, testUser);
      expect(metricsDynamic).not.toBeNull();
      expect(metricsDynamic.total).toBeGreaterThan(0);

      // search related company
      const postData1 = Object.assign(new SearchMonitorCompanyRelatedPartyRequest(), {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: '000bedbd1b43b81c050c2083083b602a',
      });
      const res = await monitorCompanyService.searchMonitorCompanyRelated(postData1, Object.assign(new PlatformUser(), testUser, {}));
      expect(res).not.toBeNull();
    });
    it('应该成功添加新公司到监控列表', async () => {
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
      ];

      const result = await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      expect(result.successList.length).toBe(2);
      expect(result.failList.length).toBe(0);
      expect(result.addedCount).toBe(2);
      expect(result.updatedCount).toBe(0);

      const totalCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(totalCount).toBe(2);
    });

    it('添加已存在的主体公司应该失败', async () => {
      // 先添加一个公司作为主体
      const existingCompany = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [existingCompany],
      });

      // 再次添加同一个公司
      const result = await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [existingCompany],
      });

      expect(result.failList).toContain(existingCompany.companyId);
      expect(result.failCount).toBe(1);
      expect(result.addedCount).toBe(0);
    });

    it('添加已存在的关联方公司应该更新为主体', async () => {
      const companyItems = [
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];
      // step1 增加1个公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // step2 先添加一个公司作为关联方
      const companyAsRelated = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      const items: AddMonitorRelatedCompanyItemPO[] = [];
      const item = new AddMonitorRelatedCompanyItemPO();
      item.companyId = companyAsRelated.companyId;
      item.companyName = companyAsRelated.companyName;
      item.relatedType = 'Legal';
      items.push(item);
      const req1 = new AddMonitorRelatedCompanyRequest();
      req1.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req1.companyId = companyItems[0].companyId;
      req1.items = items;
      await monitorCompanyService.addRelatedCompany(testUser, req1);

      // step3 把step2再将其添加为主体
      const result: any = await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyAsRelated],
      });

      expect(result.updatedList).toContain(companyAsRelated.companyId);
      expect(result.updatedCount).toBe(1);
      expect(result.addedCount).toBe(0);

      const updatedCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: companyAsRelated.companyId,
      });
      expect(updatedCompany.primaryObject).toBe(1);
    });

    it('添加不支持的公司类型应该抛出异常', async () => {
      const unsupportedCompany = {
        companyId: 'unsupported_id',
        companyName: '香港公司',
      };

      await expect(
        monitorCompanyService.addCompanies(testUser, {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          items: [unsupportedCompany],
        }),
      ).rejects.toThrow(BadParamsException);
    });

    it('添加到不存在的监控组应该抛出异常', async () => {
      const companyItem = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await expect(
        monitorCompanyService.addCompanies(testUser, {
          monitorGroupId: 999999, // 不存在的组ID
          items: [companyItem],
        }),
      ).rejects.toThrow(ResourceOperationException);
    });

    it('批量添加公司时存在不支持的公司失败应该抛异常', async () => {
      const companyItems = [
        {
          companyId: '********************************',
          companyName: '北京四零二零文化娱乐有限公司',
        },
        {
          companyId: 'unsupported_id',
          companyName: '香港公司',
        },
        {
          companyId: 'f66d9d4f0e4639325637011eda1a125c',
          companyName: '安徽明讯新材料科技股份有限公司',
        },
      ];

      await expect(
        monitorCompanyService.addCompanies(testUser, {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          items: companyItems,
        }),
      ).rejects.toThrow(BadParamsException);
    });

    it('添加空公司列表应该返回空结果', async () => {
      const result = await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [],
      });

      expect(result.successList).toHaveLength(0);
      expect(result.failList).toHaveLength(0);
      expect(result.addedCount).toBe(0);
      expect(result.updatedCount).toBe(0);
      expect(result.failCount).toBe(0);
    });
  });

  describe('batchCompanyDelete() test', () => {
    it.skip('正常删除', async () => {
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];
      // step1 增加3个公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });
      const totalMonitorCompanyCount1 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const count12 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      expect(totalMonitorCompanyCount1).toBe(companyItems.length);
      expect(count12).toBe(0);

      //step2 给companyItems 第一个公司增加一个关联方
      const items: AddMonitorRelatedCompanyItemPO[] = [];
      const item = new AddMonitorRelatedCompanyItemPO();
      item.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      item.companyName = '苏州知彼信息科技中心（有限合伙）';
      item.relatedType = 'Legal';
      items.push(item);
      const req1 = new AddMonitorRelatedCompanyRequest();
      req1.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req1.companyId = companyItems[0].companyId;
      req1.items = items;
      await monitorCompanyService.addRelatedCompany(testUser, req1);
      const totalMonitorCompanyCount2 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const relatedCompanyCount1 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      const primaryCompany1 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });

      expect(totalMonitorCompanyCount2).toBe(totalMonitorCompanyCount1 + 1);
      expect(relatedCompanyCount1).toBe(1);
      // 给企业增加一个关联方之后，企业自身冗余的  relatedPartyCount 应该 +1
      expect(primaryCompany1.relatedPartyCount).toEqual(1);
      //  搜索关联方， 应该找到1条
      const searchedRelatedParty1 = await monitorCompanyService.searchMonitorCompanyRelated(
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: req1.companyId,
        },
        testUser,
      );
      expect(searchedRelatedParty1?.total).toBe(1);
      //step3 把 step2 中添加的关联方从监控列表中移除 ， 这个时候 step2 中添加的企业是仅作为关联方存在，并不是监控主体
      const addedCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: item.companyId,
      });

      // 移除该公司之后， 监控列表总数应该 -1 ， 原来的主体公司的  relatedPartyCount 应该 -1
      // 移除的时候作为关联方
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];
      deleteMonitors.push(
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: addedCompany.companyId,
        }),
      );
      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );
      const totalMonitorCompanyCount3 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const relatedCompanyCount2 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      const primaryCompany2 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });
      expect(totalMonitorCompanyCount3).toBe(totalMonitorCompanyCount2 - 1);
      expect(relatedCompanyCount2).toBe(relatedCompanyCount1 - 1);
      expect(primaryCompany2.relatedPartyCount).toEqual(primaryCompany1.relatedPartyCount - 1);

      // step4 搜索关联方， 应该找不到
      const searchedRelatedParty2 = await monitorCompanyService.searchMonitorCompanyRelated(
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: req1.companyId,
        },
        testUser,
      );
      expect(searchedRelatedParty2?.total).toBe(0);
    });
    it('删除不存在的公司应该正常返回', async () => {
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];
      deleteMonitors.push(
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'non_existent_company_id',
        }),
      );

      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );

      // 验证数据库状态没有变化
      const totalCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(totalCount).toBe(0);
    });

    it('批量删除多个公司', async () => {
      // 准备测试数据
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
      ];

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // 执行批量删除
      const deleteMonitors = companyItems.map((item) =>
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: item.companyId,
        }),
      );

      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );

      // 验证所有公司都被删除
      const remainingCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(remainingCount).toBe(0);
    });

    it('删除主体公司时应该同时删除其关联方关系以及关联方', async () => {
      // 准备测试数据 - 添加主体公司
      const primaryCompany = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [primaryCompany],
      });

      // 添加关联方
      const relatedCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
        relatedType: 'Legal',
      };

      await monitorCompanyService.addRelatedCompany(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompany.companyId,
        items: [relatedCompany],
      });

      // 删除主体公司
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];
      deleteMonitors.push(
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: primaryCompany.companyId,
        }),
      );

      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );

      // 验证关联关系被删除
      const relatedCount = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: primaryCompany.companyId,
      });
      expect(relatedCount).toBe(0);

      // 验证关联方公司也被删除
      const relatedCompanyExists = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: relatedCompany.companyId,
      });
      expect(relatedCompanyExists).toBeUndefined();
    });

    it('删除时monitorGroupId不匹配应该正常处理', async () => {
      // 1. 准备测试数据 - 添加一个主体公司
      const companyItem = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyItem],
      });

      // 2. 使用错误的 monitorGroupId 尝试删除
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];
      deleteMonitors.push(
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: 999999, // 使用不存在的 monitorGroupId
          companyId: companyItem.companyId,
        }),
      );

      // 3. 验证删除操作抛出资源操作异常
      await expect(
        monitorCompanyService.batchCompanyDelete(
          testUser,
          Object.assign(new BatchDeleteMonitorCompanyRequest(), {
            deleteMonitors,
          }),
        ),
      ).rejects.toThrow(ResourceOperationException);

      // 4. 验证公司没有被删除
      const company = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: companyItem.companyId,
      });
      expect(company).toBeDefined();
    });

    it('删除空列表应该正常返回', async () => {
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];

      const item = new DeleteMonitorCompanyRequest();
      item.monitorGroupId = defaultGroupEntity.monitorGroupId;
      deleteMonitors.push(item);

      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );

      // 验证数据库状态没有变化
      const totalCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(totalCount).toBe(0);
    });

    it('删除时部分公司不存在应该继续处理其他公司', async () => {
      // 1. 准备测试数据 - 添加一个存在的公司
      const existingCompany = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [existingCompany],
      });

      // 2. 准备删除请求 - 包含存在和不存在的公司
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: existingCompany.companyId,
        },
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'non_existent_company_id',
        },
      ];

      // 3. 执行删除操作
      await monitorCompanyService.batchCompanyDelete(
        testUser,
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          deleteMonitors,
        }),
      );

      // 4. 验证存在的公司被删除
      const remainingCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: existingCompany.companyId,
      });
      expect(remainingCompany).toBeUndefined();
    });
  });

  describe('companyTransfer() test', () => {
    it.skip('正常转移', async () => {
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];
      // step1 增加3个公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });
      const totalMonitorCompanyCount1 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const count12 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      expect(totalMonitorCompanyCount1).toBe(companyItems.length);
      expect(count12).toBe(0);

      //step2 给companyItems 第一个公司增加一个关联方
      const items: AddMonitorRelatedCompanyItemPO[] = [];
      const item = new AddMonitorRelatedCompanyItemPO();
      item.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      item.companyName = '苏州知彼信息科技中心（有限合伙）';
      item.relatedType = 'Legal';
      items.push(item);
      const req1 = new AddMonitorRelatedCompanyRequest();
      req1.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req1.companyId = companyItems[0].companyId;
      req1.items = items;
      await monitorCompanyService.addRelatedCompany(testUser, req1);
      const totalMonitorCompanyCount2 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const relatedCompanyCount1 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      const primaryCompany1 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });

      expect(totalMonitorCompanyCount2).toBe(totalMonitorCompanyCount1 + 1);
      expect(relatedCompanyCount1).toBe(1);
      // 给企业增加一个关联方之后，企业自身冗余的  relatedPartyCount 应该 +1
      expect(primaryCompany1.relatedPartyCount).toEqual(1);
      //  搜索关联方， 应该找到1条
      const searchedRelatedParty1 = await monitorCompanyService.searchMonitorCompanyRelated(
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: req1.companyId,
        },
        testUser,
      );
      expect(searchedRelatedParty1?.total).toBe(1);
      //step3 把 step2 中添加的关联方从监控列表中移除 ， 这个时候 step2 中添加的企业是仅作为关联方存在，并不是监控主体
      const addedCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: item.companyId,
      });

      // 移除该公司之后， 监控列表总数应该 -1 ， 原来的主体公司的  relatedPartyCount 应该 -1
      // 移除的时候作为关联方
      const deleteMonitors: DeleteMonitorCompanyRequest[] = [];
      deleteMonitors.push(
        Object.assign(new BatchDeleteMonitorCompanyRequest(), {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: addedCompany.companyId,
        }),
      );

      // 再次创建一个分组
      const newGroupEntity = _.omit(defaultGroupEntity, 'monitorGroupId');
      newGroupEntity.name = defaultGroupEntity.name + 'V1';
      const newMonitorGroup = await monitorGroupRepo.save(newGroupEntity);

      await monitorCompanyService.batchCompanyTransfer(
        testUser,
        Object.assign(new TransferMonitorCompanyRequest(), {
          deleteMonitors,
          toMonitorGroupId: newMonitorGroup.monitorGroupId,
        }),
      );
      const newMonitorCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: newMonitorGroup.monitorGroupId,
        companyId: addedCompany.companyId,
      });
      expect(newMonitorCompany).not.toBeNull();
    });

    it('正常转移监控企业', async () => {
      // 1. 准备初始数据 - 添加主体企业
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
      ];

      // 添加企业到默认分组
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // 2. 创建目标分组
      const targetGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        name: defaultGroupEntity.name + '_target',
      });

      // 3. 执行转移操作
      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = targetGroup.monitorGroupId;
      transferRequest.deleteMonitors = companyItems.map((item) => ({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: item.companyId,
      }));

      const result = await monitorCompanyService.batchCompanyTransfer(testUser, transferRequest);

      // 4. 验证结果
      // 验证源分组企业数量
      const sourceCompanyCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(sourceCompanyCount).toBe(0);

      // 验证目标分组企业数量
      const targetCompanyCount = await monitorCompanyRepo.count({
        monitorGroupId: targetGroup.monitorGroupId,
      });
      expect(targetCompanyCount).toBe(companyItems.length);

      // 验证 affected 值（应为0，因为删除和添加的数量相同）
      expect(result.affected).toBe(0);
    });

    it('转移不存在的企业应该返回affected为0', async () => {
      // 创建目标分组
      const targetGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        name: defaultGroupEntity.name + '_target',
      });

      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = targetGroup.monitorGroupId;
      transferRequest.deleteMonitors = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'non_existent_company_id',
        },
      ];

      const result = await monitorCompanyService.batchCompanyTransfer(testUser, transferRequest);
      expect(result.affected).toBe(0);
    });

    it('转移到不存在的分组应该抛出异常', async () => {
      // 1. 准备初始数据
      const companyItem = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyItem],
      });

      // 2. 尝试转移到不存在的分组
      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = 999999; // 不存在的分组ID
      transferRequest.deleteMonitors = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: companyItem.companyId,
        },
      ];

      await expect(monitorCompanyService.batchCompanyTransfer(testUser, transferRequest)).rejects.toThrow(ResourceOperationException);
    });

    it('转移到其他组织的分组应该抛出异常', async () => {
      // 1. 准备初始数据
      const companyItem = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyItem],
      });

      // 2. 创建一个属于其他组织的分组
      const otherOrgGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        orgId: 999, // 其他组织ID
        name: '其他组织的分组',
      });

      // 3. 尝试转移到其他组织的分组
      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = otherOrgGroup.monitorGroupId;
      transferRequest.deleteMonitors = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: companyItem.companyId,
        },
      ];

      await expect(monitorCompanyService.batchCompanyTransfer(testUser, transferRequest)).rejects.toThrow(ResourceOperationException);
    });

    it('转移带有关联方的企业', async () => {
      // 1. 准备初始数据 - 添加主体企业
      const primaryCompany = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [primaryCompany],
      });

      // 2. 添加关联方
      const relatedCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
        relatedType: 'Legal',
      };

      await monitorCompanyService.addRelatedCompany(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompany.companyId,
        items: [relatedCompany],
      });

      // 验证初始关联关系
      const initialRelatedCount = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: primaryCompany.companyId,
        companyIdRelated: relatedCompany.companyId,
      });
      expect(initialRelatedCount).toBe(1);

      // 3. 创建目标分组
      const targetGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        name: defaultGroupEntity.name + '_target',
      });

      // 4. 执行转移操作 - 转移分组时候所有待转移的企业都会变成主体，不会自动携带原有的关联方关系
      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = targetGroup.monitorGroupId;
      transferRequest.deleteMonitors = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: primaryCompany.companyId,
        },
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: relatedCompany.companyId,
        },
      ];

      const result = await monitorCompanyService.batchCompanyTransfer(testUser, transferRequest);

      // 5. 验证结果
      // 验证源分组企业数量
      const sourceCompanyCount = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(sourceCompanyCount).toBe(0);

      // 验证目标分组企业数量（主体 + 关联方）
      const targetCompanyCount = await monitorCompanyRepo.count({
        monitorGroupId: targetGroup.monitorGroupId,
      });
      expect(targetCompanyCount).toBe(2);

      // 验证关联关系是否被正确转移
      const relatedCount = await companyRelatedRepo.count({
        monitorGroupId: targetGroup.monitorGroupId,
        companyIdPrimary: primaryCompany.companyId,
        companyIdRelated: relatedCompany.companyId,
      });
      expect(relatedCount).toBe(0); // 关联方关系不会自动跟随

      // 验证主体企业的关联方数量是否正确
      const primaryCompanyInTarget = await monitorCompanyRepo.findOne({
        monitorGroupId: targetGroup.monitorGroupId,
        companyId: primaryCompany.companyId,
      });
      expect(primaryCompanyInTarget.relatedPartyCount).toBe(0);

      // 验证关联方企业是否正确转移
      const relatedCompanyInTarget = await monitorCompanyRepo.findOne({
        monitorGroupId: targetGroup.monitorGroupId,
        companyId: relatedCompany.companyId,
      });
      expect(relatedCompanyInTarget).toBeDefined();
      expect(relatedCompanyInTarget.primaryObject).toBe(MonitorCompanyPrimaryObjectEnum.Primary);
    });

    it('转移空列表应该抛异常', async () => {
      // 创建目标分组
      const targetGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        name: defaultGroupEntity.name + '_target',
      });

      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = targetGroup.monitorGroupId;
      transferRequest.deleteMonitors = [];

      await expect(monitorCompanyService.batchCompanyTransfer(testUser, transferRequest)).rejects.toThrow(BadParamsException);
    });

    it('转移时部分公司不存在应该继续处理其他公司', async () => {
      // 1. 准备测试数据 - 添加一个存在的公司
      const existingCompany = {
        companyId: '84c17a005a759a5e0d875c1ebb6c9846',
        companyName: '乐视网信息技术（北京）股份有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [existingCompany],
      });

      // 2. 创建目标分组
      const targetGroup = await monitorGroupRepo.save({
        ..._.omit(defaultGroupEntity, 'monitorGroupId'),
        name: defaultGroupEntity.name + '_target',
      });

      // 3. 准备转移请求 - 包含存在和不存在的公司
      const transferRequest = new TransferMonitorCompanyRequest();
      transferRequest.toMonitorGroupId = targetGroup.monitorGroupId;
      transferRequest.deleteMonitors = [
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: existingCompany.companyId,
        },
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'non_existent_company_id',
        },
      ];

      // 4. 执行转移操作
      const result = await monitorCompanyService.batchCompanyTransfer(testUser, transferRequest);

      // 5. 验证结果
      expect(result.affected).toBe(0);

      // 验证存在的公司被转移到目标分组
      const transferredCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: targetGroup.monitorGroupId,
        companyId: existingCompany.companyId,
      });
      expect(transferredCompany).toBeDefined();
    });
  });

  describe('临时单元测试', () => {
    it('companyRelatedMonitorAll() test', async () => {
      // Mock nebulaGraphService.getCompanyRelatedList
      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue({
        Result: [
          {
            companyKeynoRelated: 'test-company-1',
            companyNameRelated: '测试关联公司1',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'test-company-2',
            companyNameRelated: '测试关联公司2',
            isMonitor: false,
            relatedTypes: ['Investment'],
          },
        ],
        Paging: {
          PageSize: 10,
          PageIndex: 1,
          TotalRecords: 2,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
      });

      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      // step1 增加公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      const postData = Object.assign(new CompanyRelatedListParams(), {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: '4356627ba90f2931af278024fc939ab0',
      });
      const res = await nebulaGraphService.getCompanyRelatedList(postData, testUser.currentOrg, testUser.currentProduct);
      expect(res).not.toBeNull();
      expect(res.Result.length).toBe(2);

      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;
      const resp = await monitorCompanyService.companyRelatedMonitorAll(req, testUser);
      expect(resp).not.toBeNull();
      expect(resp.addedCount).toBe(2);

      // 验证 mock 函数被调用
      expect(nebulaGraphService.getCompanyRelatedList).toHaveBeenCalledWith(
        expect.objectContaining({
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: '4356627ba90f2931af278024fc939ab0',
        }),
        expect.any(Object),
      );
    });

    it('removeAllCompanyByRelatedSearch() test', async () => {
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      // step1 增加公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      //step2 给companyItems 第一个公司增加一个关联方
      const items: AddMonitorRelatedCompanyItemPO[] = [];
      const item = new AddMonitorRelatedCompanyItemPO();
      item.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      item.companyName = '苏州知彼信息科技中心（有限合伙）';
      item.relatedType = 'Legal';
      items.push(item);
      const req1 = new AddMonitorRelatedCompanyRequest();
      req1.monitorGroupId = defaultGroupEntity.monitorGroupId;
      req1.companyId = companyItems[0].companyId;
      req1.items = items;
      await monitorCompanyService.addRelatedCompany(testUser, req1);
      const totalMonitorCompanyCount2 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      const relatedCompanyCount1 = await companyRelatedRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyIdPrimary: companyItems[0].companyId,
      });
      const primaryCompany1 = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });

      expect(relatedCompanyCount1).toBeGreaterThanOrEqual(1);
      //  搜索关联方， 应该找到1条
      const searchedRelatedParty1 = await monitorCompanyService.searchMonitorCompanyRelated(
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: req1.companyId,
        },
        testUser,
      );
      expect(searchedRelatedParty1?.total).toBeGreaterThanOrEqual(1);
      const affectedResponse = await monitorCompanyService.removeAllCompanyByRelatedSearch(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: req1.companyId,
      });

      expect(affectedResponse).not.toBeNull();
      expect(affectedResponse?.affected).toBeGreaterThanOrEqual(1);
    });

    it('removeAllCompanyBySearch() test', async () => {
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];
      // step1 增加3个公司到监控列表
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      const totalMonitorCompanyCount1 = await monitorCompanyRepo.count({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(totalMonitorCompanyCount1).toBeGreaterThanOrEqual(companyItems.length);

      const postData = Object.assign(new SearchMonitorCompanyRequest(), {
        groupIds: [defaultGroupEntity.monitorGroupId],
        pageSize: 10,
        pageIndex: 1,
        needAggs: 0,
      });
      await monitorCompanyService.removeAllCompanyBySearch(testUser, postData);
    });
  });

  describe('companyRelatedMonitorAll() test', () => {
    // Mock nebulaGraphService.getCompanyRelatedList在每个测试前重置
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('应该成功添加所有关联企业到监控列表', async () => {
      // 准备测试数据 - 先添加一个主体公司
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // Mock nebulaGraphService返回一些未监控的关联企业
      const mockRelatedList = {
        Result: [
          {
            companyKeynoRelated: 'related-company-id-1',
            companyNameRelated: '关联测试公司1',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'related-company-id-2',
            companyNameRelated: '关联测试公司2',
            isMonitor: false,
            relatedTypes: ['Investment'],
          },
        ],
        Paging: {
          PageSize: 10,
          PageIndex: 1,
          TotalRecords: 2,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
        message: 'success',
      };

      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue(mockRelatedList);

      // 执行测试
      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;

      const resp = await monitorCompanyService.companyRelatedMonitorAll(req, testUser);

      // 验证结果
      expect(resp).not.toBeNull();
      expect(resp.addedCount).toBe(2); // 应该添加2个关联企业
      expect(resp.failCount).toBe(0);
      expect(nebulaGraphService.getCompanyRelatedList).toHaveBeenCalledWith(expect.any(Object), expect.any(Object), false);
    });

    it('当未监控公司列表为空时应抛出异常', async () => {
      // 准备测试数据 - 先添加一个主体公司
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // Mock nebulaGraphService只返回已经被监控的关联企业
      const mockEmptyRelatedList = {
        Result: [
          {
            companyKeynoRelated: 'already-monitored-id-1',
            companyNameRelated: '已监控公司1',
            isMonitor: true, // 已被监控
            relatedTypes: ['Legal'],
          },
        ],
        Paging: {
          PageSize: 10,
          PageIndex: 1,
          TotalRecords: 1,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
        message: 'success',
      };

      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue(mockEmptyRelatedList);

      // 执行测试
      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;

      // 应该抛出异常
      await expect(monitorCompanyService.companyRelatedMonitorAll(req, testUser)).rejects.toThrow(BadParamsException);
    });

    it('当关联企业超过6000家时应抛出异常', async () => {
      // 准备测试数据 - 先添加一个主体公司
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // 创建一个包含6001个未监控关联企业的列表
      const largeCompanyList = new Array(6001).fill(null).map((_, index) => ({
        companyKeynoRelated: `large-related-id-${index}`,
        companyNameRelated: `大量关联公司${index}`,
        isMonitor: false,
        relatedTypes: ['Legal'],
      }));

      // Mock nebulaGraphService返回大量未监控的关联企业
      const mockLargeRelatedList = {
        Result: largeCompanyList,
        Paging: {
          PageSize: 10000,
          PageIndex: 1,
          TotalRecords: 6001,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
        message: 'success',
      };

      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue(mockLargeRelatedList);

      // 执行测试
      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;

      // 应该抛出异常
      await expect(monitorCompanyService.companyRelatedMonitorAll(req, testUser)).rejects.toThrow(BadParamsException);
    });

    it('应该过滤掉不支持监控的企业', async () => {
      // 准备测试数据 - 先添加一个主体公司
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // Mock nebulaGraphService返回包含一些不支持监控的企业的列表
      const mockMixedRelatedList = {
        Result: [
          {
            companyKeynoRelated: 'supported-company-id-1',
            companyNameRelated: '支持监控公司1',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'supported-company-id-2',
            companyNameRelated: '支持监控公司2',
            isMonitor: false,
            relatedTypes: ['Investment'],
          },
          {
            companyKeynoRelated: 'unsupported-company-id-1',
            companyNameRelated: '香港公司',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
        ],
        Paging: {
          PageSize: 10,
          PageIndex: 1,
          TotalRecords: 3,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
        message: 'success',
      };

      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue(mockMixedRelatedList);

      // Mock checkCompanyStandardCode方法以返回一些不支持的公司
      jest.spyOn(monitorCompanyService as any, 'filterUnMonitorCompany').mockImplementation(async () => {
        return {
          unMonitorCompanyCount: 3,
          filterMonitorCompanyCount: 2,
          unMonitorCompanyList: mockMixedRelatedList.Result,
          unsupportedCompanies: [
            {
              id: 'unsupported-company-id-1',
              name: '香港公司',
            },
          ],
        };
      });

      // 执行测试
      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;

      const resp = await monitorCompanyService.companyRelatedMonitorAll(req, testUser);

      // 验证结果
      expect(resp).not.toBeNull();
      expect(resp.addedCount).toBe(2); // 应该只添加2个支持的公司
      expect(resp.failCount).toBe(1); // 1个不支持的公司计入失败
    });

    it('处理批量添加中的部分失败情况', async () => {
      // 准备测试数据 - 先添加一个主体公司
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });

      // 创建一个包含600个关联企业的列表（超过单批次500的限制）
      const largeCompanyList = new Array(600).fill(null).map((_, index) => ({
        companyKeynoRelated: `batch-related-id-${index}`,
        companyNameRelated: `批次关联公司${index}`,
        isMonitor: false,
        relatedTypes: ['Legal'],
      }));

      // Mock nebulaGraphService返回大量关联企业
      const mockBatchRelatedList = {
        Result: largeCompanyList,
        Paging: {
          PageSize: 1000,
          PageIndex: 1,
          TotalRecords: 600,
        },
        GroupItems: [],
        status: ApiResponseStatusEnum.OK,
        message: 'success',
      };

      jest.spyOn(nebulaGraphService, 'getCompanyRelatedList').mockResolvedValue(mockBatchRelatedList);

      // Mock filterUnMonitorCompany以返回所有企业都未监控
      jest.spyOn(monitorCompanyService as any, 'filterUnMonitorCompany').mockImplementation(async () => {
        return {
          unMonitorCompanyCount: 600,
          filterMonitorCompanyCount: 600,
          unMonitorCompanyList: mockBatchRelatedList.Result,
          unsupportedCompanies: [],
        };
      });

      // Mock saveRelatedCompany方法，让第一批成功，第二批失败
      let callCount = 0;
      jest.spyOn(monitorCompanyService as any, 'saveRelatedCompany').mockImplementation(async () => {
        callCount++;
        if (callCount === 1) {
          // 第一批成功
          return {
            successList: new Array(500).fill(null).map((_, i) => `batch-related-id-${i}`),
            updatedList: [],
          };
        } else {
          // 第二批失败
          throw new Error('测试批次处理失败');
        }
      });

      // 执行测试
      const req = new CompanyRelatedMonitorAllRequest();
      req.companyId = '4356627ba90f2931af278024fc939ab0';
      req.monitorGroupId = defaultGroupEntity.monitorGroupId;

      const resp = await monitorCompanyService.companyRelatedMonitorAll(req, testUser);

      // 验证结果
      expect(resp).not.toBeNull();
      expect(resp.addedCount).toBe(500); // 第一批500个添加成功
      expect(resp.failCount).toBe(100); // 第二批100个添加失败
    });
  });

  describe('relatedDynamicCompanyRelatedMonitorAll test', () => {
    let monitorDynamicMessageListener;

    beforeEach(() => {
      jest.clearAllMocks();

      // 获取monitorDynamicMessageListener依赖
      monitorDynamicMessageListener = monitorCompanyService['monitorDynamicMessageListener'];

      // Mock filterUnMonitorCompany方法
      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 3,
        filterMonitorCompanyCount: 3,
        unMonitorCompanyList: [
          {
            companyKeynoRelated: 'test-company-id-1',
            companyNameRelated: '测试关联公司1',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'test-company-id-2',
            companyNameRelated: '测试关联公司2',
            isMonitor: false,
            relatedTypes: ['Legal', 'Investment'],
          },
          {
            companyKeynoRelated: 'test-company-id-3',
            companyNameRelated: '测试关联公司3',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
        ],
        unsupportedCompanies: [],
      });

      // Mock saveRelatedCompany方法
      jest.spyOn(monitorCompanyService as any, 'saveRelatedCompany').mockResolvedValue({
        successList: ['test-company-id-1', 'test-company-id-2', 'test-company-id-3'],
        updatedList: [],
      });
    });

    it('应该成功监控所有未监控的关联企业', async () => {
      // 准备测试数据
      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal, // 使用正确的枚举值
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 执行测试
      const result = await monitorCompanyService.relatedDynamicCompanyRelatedMonitorAll(request, testUser);

      // 验证结果
      expect(monitorDynamicMessageListener.filterUnMonitorCompany).toHaveBeenCalledWith(
        expect.objectContaining({
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'test-primary-company-id',
          orgId: testUser.currentOrg,
        }),
      );

      expect(result).toHaveProperty('successList');
      expect(result).toHaveProperty('failList');
      expect(result).toHaveProperty('addedCount');
      expect(result).toHaveProperty('updatedCount');
      expect(result).toHaveProperty('failCount');

      expect(result.addedCount).toBe(3);
      expect(result.updatedCount).toBe(0);
      expect(result.failCount).toBe(0);
    });

    it('当未监控公司列表为空时应抛出异常', async () => {
      // 模拟无未监控公司的情况
      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 0,
        filterMonitorCompanyCount: 0,
        unMonitorCompanyList: [],
        unsupportedCompanies: [],
      });

      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal,
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 执行测试并期望抛出异常
      await expect(monitorCompanyService.relatedDynamicCompanyRelatedMonitorAll(request, testUser)).rejects.toThrow(BadParamsException);
    });

    it('当超过6000家公司时应抛出异常', async () => {
      // 模拟超过6000家未监控公司的情况
      const largeCompanyList = new Array(6001).fill(null).map((_, index) => ({
        companyKeynoRelated: `test-company-id-${index}`,
        companyNameRelated: `测试关联公司${index}`,
        isMonitor: false,
        relatedTypes: ['Legal'],
      }));

      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 6001,
        filterMonitorCompanyCount: 6001,
        unMonitorCompanyList: largeCompanyList,
        unsupportedCompanies: [],
      });

      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal,
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 执行测试并期望抛出异常
      await expect(monitorCompanyService.relatedDynamicCompanyRelatedMonitorAll(request, testUser)).rejects.toThrow(BadParamsException);
    });

    it.skip('应处理部分公司保存失败的情况', async () => {
      // 准备测试数据
      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal,
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 第一批次成功，第二批次失败的情况
      let callCount = 0;
      jest.spyOn(monitorCompanyService as any, 'saveRelatedCompany').mockImplementation(async () => {
        callCount++;
        if (callCount === 1) {
          return {
            successList: ['test-company-id-1'],
            updatedList: [],
          };
        } else {
          throw new Error('测试保存失败');
        }
      });

      // 模拟超过一个批次的数据
      const largeCompanyList = new Array(501).fill(null).map((_, index) => ({
        companyKeynoRelated: `test-company-id-${index}`,
        companyNameRelated: `测试关联公司${index}`,
        isMonitor: false,
        relatedTypes: ['Legal'],
      }));

      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 501,
        filterMonitorCompanyCount: 501,
        unMonitorCompanyList: largeCompanyList,
        unsupportedCompanies: [],
      });

      // 执行测试
      const result = await monitorCompanyService.relatedDynamicCompanyRelatedMonitorAll(request, testUser);

      // 验证结果
      expect(result.addedCount).toBe(1); // 第一批次成功添加1个
      expect(result.failCount).toBeGreaterThan(0); // 第二批次失败
    });
  });

  describe('relatedDynamicGetUnMonitorCompany test', () => {
    let monitorDynamicMessageListener;

    beforeEach(() => {
      jest.clearAllMocks();

      // 获取monitorDynamicMessageListener依赖
      monitorDynamicMessageListener = monitorCompanyService['monitorDynamicMessageListener'];

      // Mock filterUnMonitorCompany方法
      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 3,
        filterMonitorCompanyCount: 3,
        unMonitorCompanyList: [
          {
            companyKeynoRelated: 'test-company-id-1',
            companyNameRelated: '测试关联公司1',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'test-company-id-2',
            companyNameRelated: '测试关联公司2',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
          {
            companyKeynoRelated: 'test-company-id-3',
            companyNameRelated: '测试关联公司3',
            isMonitor: false,
            relatedTypes: ['Legal'],
          },
        ],
        unsupportedCompanies: [],
      });
    });

    it('应返回未监控的公司数量和过滤后的监控公司数量', async () => {
      // 准备测试数据
      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal,
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 执行测试
      const result = await monitorCompanyService.relatedDynamicGetUnMonitorCompany(request);

      // 验证结果
      expect(monitorDynamicMessageListener.filterUnMonitorCompany).toHaveBeenCalledWith(request);
      expect(result).toHaveProperty('unMonitorCompanyCount');
      expect(result).toHaveProperty('filterMonitorCompanyCount');
      expect(result.unMonitorCompanyCount).toBe(3); // 三个未监控的公司
      expect(result.filterMonitorCompanyCount).toBe(3); // 无不支持的公司，所以与未监控数量相同
    });

    it('应处理无数据的情况', async () => {
      // 模拟无未监控公司的情况
      jest.spyOn(monitorDynamicMessageListener, 'filterUnMonitorCompany').mockResolvedValue({
        unMonitorCompanyCount: 0,
        filterMonitorCompanyCount: 0,
        unMonitorCompanyList: [],
        unsupportedCompanies: [],
      });

      const request = {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: 'test-primary-company-id',
        orgId: testUser.currentOrg,
        dimensionKey: DimensionTypeEnums.Risk_Legal,
        strategyId: 1,
        diligenceId: 1,
        batchId: 1,
        preBatchId: 1,
      };

      // 执行测试
      const result = await monitorCompanyService.relatedDynamicGetUnMonitorCompany(request);

      // 验证结果
      expect(result.unMonitorCompanyCount).toBe(0);
      expect(result.filterMonitorCompanyCount).toBe(0);
    });
  });

  describe('debug专用, 正常应该skip掉', () => {
    it('debug test', async () => {});
  });

  describe('searchMonitorCompany() test', () => {
    beforeEach(async () => {
      // 准备测试数据
      const companyItems = [
        {
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '乐视网信息技术（北京）股份有限公司',
        },
        {
          companyId: 'ecbb4bb2cd15c3feb554581874ba558c',
          companyName: '深圳市众鑫创展科技有限公司',
        },
        {
          companyId: 'e3e5619f80d34bd97f53222bbb39f84e',
          companyName: '云南热尔科技有限公司',
        },
      ];

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: companyItems,
      });
    });

    it('基本搜索功能', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;
      searchRequest.needAggs = 1;

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(3);
      expect(result.data).toHaveLength(3);
      expect(result.aggs).not.toBeNull();
    });

    it('按分组ID搜索', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;
      searchRequest.groupIds = [defaultGroupEntity.monitorGroupId];

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(3);
      expect(result.data).toHaveLength(3);
    });

    it('按公司名称模糊搜索', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;
      searchRequest.keywords = '乐视网';

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBeGreaterThanOrEqual(0);
    });

    it('分页搜索 - 第一页', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 2;
      searchRequest.pageIndex = 1;

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(3);
      expect(result.data).toHaveLength(2);
    });

    it('分页搜索 - 第二页', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 2;
      searchRequest.pageIndex = 2;

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(3);
      expect(result.data).toHaveLength(1);
    });

    it('不进行聚合搜索', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;
      searchRequest.needAggs = 0;

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(3);
      expect(result.aggs).toBeUndefined();
    });

    it('搜索不存在的数据', async () => {
      const searchRequest = new SearchMonitorCompanyRequest();
      searchRequest.pageSize = 10;
      searchRequest.pageIndex = 1;
      searchRequest.keywords = '不存在的公司名称';

      const result = await monitorCompanyService.searchMonitorCompany(searchRequest, testUser);

      expect(result).toBeDefined();
      expect(result.total).toBe(0);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('saveMonitorCompany() test', () => {
    it('应该处理空列表的情况', async () => {
      // 准备空数据
      const emptyItems = [];

      // 调用私有方法
      const saveMonitorCompanyMethod = monitorCompanyService['saveMonitorCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveMonitorCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, MonitorCompanyPrimaryObjectEnum.Primary, emptyItems);

      // 验证结果
      expect(result).toBeDefined();
      expect(result).toHaveLength(0);

      // 验证监控组计数没有变化
      const updatedGroup = await monitorGroupRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
      });
      expect(updatedGroup.companyCount).toBe(0);
    });

    it('应该处理锁超时错误并进行重试', async () => {
      // 准备测试数据
      const companyItems = [
        {
          companyId: '4356627ba90f2931af278024fc939ab0',
          companyName: '上海长途汽车客运总站有限公司',
        },
      ];

      // 调用私有方法
      const saveMonitorCompanyMethod = monitorCompanyService['saveMonitorCompany'].bind(monitorCompanyService);

      // Mock数据库操作抛出锁超时错误，然后成功
      let callCount = 0;
      const originalTransaction = monitorCompanyRepo.manager.transaction;
      jest.spyOn(monitorCompanyRepo.manager, 'transaction').mockImplementation(async (callback) => {
        callCount++;
        if (callCount === 1) {
          // 第一次调用抛出锁超时错误
          const error = new Error('Lock wait timeout') as any;
          error.code = 'ER_LOCK_WAIT_TIMEOUT';
          throw error;
        } else {
          // 后续调用正常执行
          return originalTransaction.call(monitorCompanyRepo.manager, callback);
        }
      });

      // 禁用实际的超时等待，加速测试
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        callback();
        return 0 as any;
      });

      // 执行测试
      const result = await saveMonitorCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, MonitorCompanyPrimaryObjectEnum.Primary, companyItems);

      // 验证结果
      expect(result).toBeDefined();
      expect(result).toHaveLength(1);
      expect(result).toContain(companyItems[0].companyId);
      expect(callCount).toBe(2); // 确认发生了一次重试

      // 验证数据库状态
      const savedCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: companyItems[0].companyId,
      });
      expect(savedCompany).toBeDefined();
    });

    it('应该在超过最大重试次数后抛出错误', async () => {
      // 准备测试数据
      const companyItems = [
        {
          companyId: '********************************',
          companyName: '北京四零二零文化娱乐有限公司',
        },
      ];

      // 调用私有方法
      const saveMonitorCompanyMethod = monitorCompanyService['saveMonitorCompany'].bind(monitorCompanyService);

      // Mock数据库操作总是抛出锁超时错误
      jest.spyOn(monitorCompanyRepo.manager, 'transaction').mockImplementation(async () => {
        const error = new Error('Lock wait timeout') as any;
        error.code = 'ER_LOCK_WAIT_TIMEOUT';
        throw error;
      });

      // 禁用实际的超时等待，加速测试
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        callback();
        return 0 as any;
      });

      // 执行测试并期望抛出错误
      await expect(
        saveMonitorCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, MonitorCompanyPrimaryObjectEnum.Primary, companyItems),
      ).rejects.toThrow('Lock wait timeout');
    });

    it('应该处理其他类型的数据库错误', async () => {
      // 准备测试数据
      const companyItems = [
        {
          companyId: 'f66d9d4f0e4639325637011eda1a125c',
          companyName: '安徽明讯新材料科技股份有限公司',
        },
      ];

      // 调用私有方法
      const saveMonitorCompanyMethod = monitorCompanyService['saveMonitorCompany'].bind(monitorCompanyService);

      // Mock数据库操作抛出普通错误
      jest.spyOn(monitorCompanyRepo.manager, 'transaction').mockImplementation(async () => {
        throw new Error('数据库操作失败');
      });

      // 执行测试并期望抛出错误
      await expect(
        saveMonitorCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, MonitorCompanyPrimaryObjectEnum.Primary, companyItems),
      ).rejects.toThrow('数据库操作失败');
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });
  });

  describe('saveRelatedCompany() test', () => {
    // 添加测试前的准备工作
    beforeEach(async () => {
      // 添加一个主体公司用于测试
      const companyItem = {
        companyId: '8d3d1836c775514f8fc92c95b608b018',
        companyName: '北京斯尔科技有限公司',
      };

      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [companyItem],
      });
    });

    it('应该成功添加新的关联方', async () => {
      // 准备测试数据
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const relatedCompanies = [
        {
          companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
          companyName: '苏州知彼信息科技中心（有限合伙）',
          relatedType: 'Legal',
        },
        {
          companyId: '8d464621f8fd32be4d667c9ac75c7593',
          companyName: '信阳南信建设发展有限公司',
          relatedType: 'Investment',
        },
      ];

      // 转换为AddMonitorRelatedCompanyItemPO对象
      const relatedItems = relatedCompanies.map((company) => {
        const item = new AddMonitorRelatedCompanyItemPO();
        item.companyId = company.companyId;
        item.companyName = company.companyName;
        item.relatedType = company.relatedType;
        return item;
      });

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, relatedItems);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(2);
      expect(result.updatedList).toHaveLength(0);

      // 验证关联方公司是否被添加到监控列表
      const addedCompanies = await monitorCompanyRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: In(relatedCompanies.map((c) => c.companyId)),
        },
      });
      expect(addedCompanies).toHaveLength(2);

      // 验证每个添加的公司都是关联方
      addedCompanies.forEach((company) => {
        expect(company.primaryObject).toBe(MonitorCompanyPrimaryObjectEnum.Related);
      });

      // 验证关联关系是否建立
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
        },
      });
      expect(relatedParties).toHaveLength(2);

      // 验证主体公司的关联方计数是否更新
      const primaryCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompanyId,
      });
      expect(primaryCompany.relatedPartyCount).toBe(2);
      expect(primaryCompany.primaryObject).toBe(MonitorCompanyPrimaryObjectEnum.Primary);
    });

    it('应该处理已存在公司的情况（不添加监控公司但添加关联关系）', async () => {
      // 先添加一个公司作为关联方
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const existingCompany = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
      };

      // 先添加到监控列表但不建立关联关系
      await monitorCompanyService.addCompanies(testUser, {
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        items: [existingCompany],
      });

      // 准备添加关联关系
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = existingCompany.companyId;
      relatedItem.companyName = existingCompany.companyName;
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(0); // 不添加新监控公司
      expect(result.updatedList).toHaveLength(1); // 更新了关联关系

      // 验证关联关系是否建立
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
          companyIdRelated: existingCompany.companyId,
        },
      });
      expect(relatedParties).toHaveLength(1);
      expect(relatedParties[0].relatedTypeStr).toBe('Legal');

      // 验证主体公司的关联方计数是否更新
      const primaryCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompanyId,
      });
      expect(primaryCompany.relatedPartyCount).toBe(1);
    });

    it('应该更新已存在关联方的关联类型', async () => {
      // 先添加一个公司作为关联方
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const existingRelated = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
        relatedType: 'Legal',
      };

      // 添加关联方
      const initialItem = new AddMonitorRelatedCompanyItemPO();
      initialItem.companyId = existingRelated.companyId;
      initialItem.companyName = existingRelated.companyName;
      initialItem.relatedType = existingRelated.relatedType;

      // 调用私有方法添加初始关联关系
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);
      await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [initialItem]);

      // 验证初始状态
      const initialRelated = await companyRelatedRepo.findOne({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
          companyIdRelated: existingRelated.companyId,
        },
      });
      expect(initialRelated.relatedTypeStr).toBe('Legal');

      // 准备修改关联类型
      const updatedItem = new AddMonitorRelatedCompanyItemPO();
      updatedItem.companyId = existingRelated.companyId;
      updatedItem.companyName = existingRelated.companyName;
      updatedItem.relatedType = 'Investment'; // 修改类型

      // 执行测试 - 更新关联类型
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [updatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(0);
      expect(result.updatedList).toHaveLength(1);

      // 验证关联类型是否更新
      const updatedRelated = await companyRelatedRepo.findOne({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
          companyIdRelated: existingRelated.companyId,
        },
      });
      expect(updatedRelated.relatedTypeStr).toBe('Investment');
    });

    it('应该同时处理新增和更新的关联方', async () => {
      // 先添加一个公司作为关联方
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const existingRelated = {
        companyId: 'fc9ac2c207a8f102d0c1e6d372a501df',
        companyName: '苏州知彼信息科技中心（有限合伙）',
        relatedType: 'Legal',
      };

      // 添加初始关联方
      const initialItem = new AddMonitorRelatedCompanyItemPO();
      initialItem.companyId = existingRelated.companyId;
      initialItem.companyName = existingRelated.companyName;
      initialItem.relatedType = existingRelated.relatedType;

      // 调用私有方法添加初始关联关系
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);
      await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [initialItem]);

      // 准备混合操作 - 一个更新关联类型，一个新增关联方
      const updatedItem = new AddMonitorRelatedCompanyItemPO();
      updatedItem.companyId = existingRelated.companyId;
      updatedItem.companyName = existingRelated.companyName;
      updatedItem.relatedType = 'Investment,Legal'; // 修改类型

      const newItem = new AddMonitorRelatedCompanyItemPO();
      newItem.companyId = '8d464621f8fd32be4d667c9ac75c7593';
      newItem.companyName = '信阳南信建设发展有限公司';
      newItem.relatedType = 'Investment';

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [updatedItem, newItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(1); // 新增了一个监控公司
      expect(result.updatedList).toHaveLength(1); // 更新了一个关联类型

      // 验证关联关系
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
        },
      });
      expect(relatedParties).toHaveLength(2);

      // 验证更新的关联类型
      const updatedRelated = relatedParties.find((r) => r.companyIdRelated === existingRelated.companyId);
      expect(updatedRelated.relatedTypeStr).toBe('Investment,Legal');

      // 验证主体公司的关联方计数
      const primaryCompany = await monitorCompanyRepo.findOne({
        monitorGroupId: defaultGroupEntity.monitorGroupId,
        companyId: primaryCompanyId,
      });
      expect(primaryCompany.relatedPartyCount).toBe(2);
    });

    it('应该处理锁超时错误并进行重试', async () => {
      // 准备测试数据
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      relatedItem.companyName = '苏州知彼信息科技中心（有限合伙）';
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // Mock数据库事务操作抛出锁超时错误，然后成功
      let callCount = 0;
      const originalTransaction = monitorCompanyService['monitorGroupRepo'].manager.transaction;
      jest.spyOn(monitorCompanyService['monitorGroupRepo'].manager, 'transaction').mockImplementation(async (callback) => {
        callCount++;
        if (callCount === 1) {
          // 第一次调用抛出锁超时错误
          const error = new Error('Lock wait timeout') as any;
          error.code = 'ER_LOCK_WAIT_TIMEOUT';
          throw error;
        } else {
          // 后续调用正常执行
          return originalTransaction.call(monitorCompanyService['monitorGroupRepo'].manager, callback);
        }
      });

      // 禁用实际的超时等待，加速测试
      jest.spyOn(global, 'setTimeout').mockImplementation((callback: any) => {
        callback();
        return 0 as any;
      });

      // 执行测试
      const result = await saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [relatedItem]);

      // 验证结果
      expect(result).toBeDefined();
      expect(callCount).toBe(2); // 确认发生了一次重试
      expect(result.successList).toHaveLength(1);

      // 验证关联关系是否建立
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
        },
      });
      expect(relatedParties).toHaveLength(1);
    });

    it('应该处理字段默认值错误并抛出BadParamsException', async () => {
      // 准备测试数据
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      relatedItem.companyName = '苏州知彼信息科技中心（有限合伙）';
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // Mock数据库事务操作抛出字段默认值错误
      jest.spyOn(monitorCompanyService['monitorGroupRepo'].manager, 'transaction').mockImplementation(async () => {
        const error = new Error("Field doesn't have a default value") as any;
        error.code = 'ER_NO_DEFAULT_FOR_FIELD';
        throw error;
      });

      // 执行测试并期望抛出BadParamsException
      await expect(saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [relatedItem])).rejects.toThrow(BadParamsException);
    });

    it('应该处理其他数据库错误并抛出InternalServerErrorException', async () => {
      // 准备测试数据
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';
      const relatedItem = new AddMonitorRelatedCompanyItemPO();
      relatedItem.companyId = 'fc9ac2c207a8f102d0c1e6d372a501df';
      relatedItem.companyName = '苏州知彼信息科技中心（有限合伙）';
      relatedItem.relatedType = 'Legal';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // Mock数据库事务操作抛出一般错误
      jest.spyOn(monitorCompanyService['monitorGroupRepo'].manager, 'transaction').mockImplementation(async () => {
        throw new Error('一般数据库错误');
      });

      // 执行测试并期望抛出InternalServerErrorException
      await expect(saveRelatedCompanyMethod(testUser, defaultGroupEntity.monitorGroupId, primaryCompanyId, [relatedItem])).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('处理空关联方列表的情况', async () => {
      // 准备测试数据
      const primaryCompanyId = '8d3d1836c775514f8fc92c95b608b018';

      // 调用私有方法
      const saveRelatedCompanyMethod = monitorCompanyService['saveRelatedCompany'].bind(monitorCompanyService);

      // 执行测试
      const result = await saveRelatedCompanyMethod(
        testUser,
        defaultGroupEntity.monitorGroupId,
        primaryCompanyId,
        [], // 空列表
      );

      // 验证结果
      expect(result).toBeDefined();
      expect(result.successList).toHaveLength(0);
      expect(result.updatedList).toHaveLength(0);

      // 验证没有关联关系被创建
      const relatedParties = await companyRelatedRepo.find({
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyIdPrimary: primaryCompanyId,
        },
      });
      expect(relatedParties).toHaveLength(0);
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });
  });
});
