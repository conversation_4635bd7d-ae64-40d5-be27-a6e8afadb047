import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { AffectedResponse, PlatformUser } from '../../../libs/model/common';
import { Brackets, EntityManager, In, Not, Repository } from 'typeorm';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanySearchService } from '../../company/company-search.service';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import * as Bluebird from 'bluebird';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { QueueService } from '../../../libs/config/queue.service';
import { KafkaQueue, RabbitMQ } from '@kezhaozhao/message-queue';
import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
import * as _ from 'lodash';
import { find, groupBy } from 'lodash';
import { ResourceOperationException } from '../../../libs/exceptions/ResourceOperationException';
import {
  AddMonitorCompanyItemPO,
  AddMonitorCompanyRequest,
  AddMonitorRelatedCompanyItemPO,
  BatchAddCompanyByNameRequest,
  BatchDeleteMonitorCompanyRequest,
  CreateCompanyResponse,
  DeleteMonitorCompanyRequest,
  QueryMonitorDynamicDetialsRequest,
  SearchMonitorCompanyRequest,
  SearchMonitorCompanyResponse,
  StrategicCustomerPushMessageRequest,
} from '../../../libs/model/monitor';
import { Message } from 'kafkajs';
import * as crypto from 'crypto';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { QueryBuilderHelper } from '../../../libs/common/sql.helper';
import { AddMonitorRelatedCompanyRequest } from '../../../libs/model/monitor/AddMonitorRelatedCompanyRequest';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import {
  MonitorCompanyPrimaryObjectEnum,
  MonitorCompanyRelatedStatusEnum,
  MonitorCompanyStatusEnums,
} from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { ESResponse } from '@kezhaozhao/search-utils';
import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { SearchMonitorCompanyRelatedPartyResponse } from '../../../libs/model/monitor/SearchMonitorCompanyRelatedPartyResponse';
import { TransferMonitorCompanyRequest } from '../../../libs/model/monitor/RemoveMonitorCompanyRequest';
import { SearchMonitorCompanyRelatedPartyRequest } from '../../../libs/model/monitor/SearchMonitorCompanyRelatedPartyRequest';
import { MetricDynamicStatusEnums } from '../../../libs/enums/metric/MetricDynamicStatusEnums';
import { MonitorDynamicEsService } from '../dynamic/monitor.dynamic.es.service';
import { relatedTypeAnnotations } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { MetricUpdateDynamicStatusPO } from '../po/MetricUpdateDynamicStatusPO';
import { CompanyRelatedListParams, CompanyRelatedMonitorAllRequest } from '../../../libs/model/data/request';
import { RelatedCompanySource } from '../../data/source/related-company.source';
// import { MessageEntity } from '../../../libs/entities/MessageEntity';
import { Cacheable } from 'type-cacheable';
// import { MonitorDynamicMessageListener } from '../dynamic/monitor.dynamic.message.listener';
import { StatusCode } from 'libs/constants/company.constants';
import { MonitorGroupService } from '../group/monitor.group.service';
import { MonitorDynamicDetailsService } from '../dynamic/process/services/monitor-dynamic-details.service';

@Injectable()
export class MonitorCompanyService {
  private readonly logger: Logger = QccLogger.getLogger(MonitorCompanyService.name);
  private readonly strategicCustomerKafkaQueue: KafkaQueue;
  private readonly overLimitTimeoutSeconds: number = 3600 * 1;
  public batchResultBusinessQueue: RabbitMQ;

  constructor(
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedPartyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly monitorMetricsDynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    // @InjectRepository(MessageEntity) private readonly msgRepository: Repository<MessageEntity>,
    private readonly redisService: RedisService,
    private readonly companySearchService: CompanySearchService,
    private readonly queueService: QueueService,
    private readonly dynamicEsService: MonitorDynamicEsService,
    private readonly relatedCompanyService: RelatedCompanySource,
    // private readonly monitorDynamicMessageListener: MonitorDynamicMessageListener,
    private readonly dynamicDetailsService: MonitorDynamicDetailsService,
    private readonly monitorGroupService: MonitorGroupService,
  ) {
    // kafka
    this.strategicCustomerKafkaQueue = this.queueService.strategicCustomerKafkaQueue;
    this.batchResultBusinessQueue = this.queueService.batchResultBusinessQueue;
  }

  /**
   * B端头部客户反馈其在监控列表中跟踪的企业动态数据延迟月余仍未有动态推送，
   * 严重影响使用体验与工作效率（特别是金融客户，对于工商信息有变更有严格的监管要求【T+2】）。
   * 经排查发现，数据侧采集效率目前在国家企业信用公示系统上受到阻滞，所有数据都产生了延迟，
   * 需手动触发更新。现针对该问题明确解决方案，对B端客户监控的企业数据单开监控队列，
   * 第一优先级进行数据采集和清洗、推送。高优先级采集维度为工商信息。
   *
   * kafka topic:  kezz_strategic_customer_monitor
   * 消息体结构：
   * {
   *  "priority": 1,
   *   "keynos": [
   *     "f625a5b661058ba5082ca508f99ffe1b",
   *     "d1b84f508d958bdd374cba6f903686b3"
   *   ]
   * }
   * @param currentUser
   * @param postData
   */
  async strategicCustomerPushMessage(postData: StrategicCustomerPushMessageRequest) {
    try {
      const { keynos, isValid } = postData;
      const uniqueKey = this.generateUniqueKey(keynos);
      const key = `StrategicCustomerPush:${uniqueKey}`;
      const redisClient = this.redisService.getClient();
      const exists = await redisClient.get(key);
      if (exists) {
        throw new BadParamsException(RoverExceptions.Monitor.Message.Duplicated);
      }
      const body = JSON.stringify({ priority: 1, isValid, keynos });
      const message: Message = { value: body };
      await this.strategicCustomerKafkaQueue.sendMessage([message]);
      await redisClient.set(key, body);
      await redisClient.expire(key, this.overLimitTimeoutSeconds);
    } catch (e) {
      this.logger.error('send strategic customer message error', e);
    }
  }

  /**
   * 查找指定企业的关联方
   * @param postData
   * @param currentUser
   * @param notPage
   */
  async searchMonitorCompanyRelated(
    postData: SearchMonitorCompanyRelatedPartyRequest,
    currentUser: PlatformUser,
    notPage = false,
  ): Promise<SearchMonitorCompanyRelatedPartyResponse> {
    postData.pageSize = postData.pageSize || 10;
    postData.pageIndex = postData.pageIndex || 1;
    const { monitorGroupId, companyId, pageIndex, pageSize } = postData;
    const paginationResponse = new SearchMonitorCompanyRelatedPartyResponse();
    paginationResponse.pageIndex = pageIndex;
    paginationResponse.pageSize = pageSize;
    const qb = this.relatedPartyRepo
      .createQueryBuilder('relatedParty')
      .where('relatedParty.orgId = :orgId', { orgId: currentUser.currentOrg })
      .andWhere('relatedParty.product = :product', { product: currentUser.currentProduct })
      .andWhere('relatedParty.monitorGroupId = :monitorGroupId', { monitorGroupId })
      .andWhere('relatedParty.companyIdPrimary = :companyId', { companyId });
    if (!notPage) {
      qb.skip((pageIndex - 1) * pageSize).take(pageSize);
    }
    const [items, total] = await qb.getManyAndCount();
    if (!items?.length) {
      paginationResponse.total = 0;
      paginationResponse.data = [];
      return paginationResponse;
    }
    if (notPage) {
      paginationResponse.total = total;
      paginationResponse.data = items;
      return paginationResponse;
    }
    const companyIdList = _.uniq(items.map((t) => t.companyIdRelated));
    const qbCompany = this.monitorCompanyRepo.createQueryBuilder('monitorCompany').where('monitorCompany.monitorGroupId = :monitorGroupId', { monitorGroupId });
    if (companyIdList?.length) {
      qbCompany.andWhere('monitorCompany.companyId IN (:...companyIds)', { companyIds: companyIdList });
    }
    if (postData.sortField) {
      qbCompany.orderBy(`monitorCompany.${postData.sortField}`, postData.isSortAsc ? 'ASC' : 'DESC');
    } else {
      qbCompany.orderBy('monitorCompany.createDate', 'DESC');
    }
    const monitorCompanyEntities = await qbCompany.getMany();
    // const monitorCompanyEntities = await this.monitorCompanyRepo.find({
    //   monitorGroupId,
    //   companyId: In(items.map((i) => i.companyIdRelated)),
    // });
    const companyDetaiRes: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        pageIndex: 1,
        pageSize: companyIdList.length,
        includeFields: ['id', 'name', 'statusCode'],
        filter: { ids: companyIdList },
      }),
    );
    paginationResponse.total = total;
    paginationResponse.data = items.map((i) => {
      const kysCompany = companyDetaiRes?.Result?.find((t) => t.id === i.companyIdRelated);
      if (kysCompany) {
        i['companyStatus'] = StatusCode[kysCompany?.statuscode] || '-';
      }
      i.monitorCompanyEntity = monitorCompanyEntities.find((j) => j.companyId === i.companyIdRelated);
      const relatedType = i.relatedType;
      i['relatedTypeDescList'] = relatedType.map((x) => {
        return relatedTypeAnnotations[x];
      });
      return i;
    });
    if (postData.sortField === 'riskLevel') {
      paginationResponse.data.sort((a, b) => {
        // 检查 a 的 monitorCompanyEntity 是否存在且有 riskLevel 属性
        const aRiskLevel = a.monitorCompanyEntity && a.monitorCompanyEntity.riskLevel !== null ? a.monitorCompanyEntity.riskLevel : Infinity;
        // 检查 b 的 monitorCompanyEntity 是否存在且有 riskLevel 属性
        const bRiskLevel = b.monitorCompanyEntity && b.monitorCompanyEntity.riskLevel !== null ? b.monitorCompanyEntity.riskLevel : Infinity;
        if (aRiskLevel === Infinity && bRiskLevel === Infinity) {
          return 0; // 两者都是 Infinity，顺序不变
        } else if (aRiskLevel === Infinity) {
          return 1; // a 是 Infinity，b 排在前面
        } else if (bRiskLevel === Infinity) {
          return -1; // b 是 Infinity，a 排在前面
        }
        if (postData.isSortAsc) {
          return aRiskLevel - bRiskLevel;
        }
        return bRiskLevel - aRiskLevel;
      });
    }
    return paginationResponse;
  }

  async searchMonitorCompany(postData: SearchMonitorCompanyRequest, currentUser: PlatformUser, notUsePage = false): Promise<SearchMonitorCompanyResponse> {
    const res: SearchMonitorCompanyResponse = new SearchMonitorCompanyResponse();
    const { groupIds, operatorIds, riskLevels, keywords, pageIndex, pageSize, sortField, isSortAsc, companyIds, searchType, needAggs, isRelatedCompanyExist } =
      postData;
    Object.assign(res, {
      pageIndex: pageIndex || 1,
      pageSize: pageSize || 10,
    });
    const { currentProduct: product, currentOrg: orgId } = currentUser;

    // 可见分组权限验证
    const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(currentUser);
    if (!authGroupIds?.length) {
      return res;
    }
    if (groupIds?.length) {
      const diffGroupIds = _.difference(groupIds, authGroupIds);
      if (diffGroupIds?.length) {
        throw new BadRequestException(RoverExceptions.Monitor.Group.NotGroupAuth);
      }
    }

    const qb = this.monitorCompanyRepo
      .createQueryBuilder('monitorCompany')
      .where('monitorCompany.orgId = :orgId', { orgId })
      .andWhere('monitorCompany.product = :product', { product })
      .andWhere('monitorCompany.status = :status', { status: MonitorCompanyStatusEnums.Done });
    if (searchType) {
      if (searchType === 1) {
        qb.andWhere('monitorCompany.primaryObject = :searchType', { searchType });
        if (companyIds?.length) {
          qb.andWhere('monitorCompany.companyId in (:...companyIds)', { companyIds });
        }
      }
      // else if (searchType === 2) {
      //   if (!groupIds.length && !companyIds?.length) {
      //     throw new BadRequestException('查找关联方监控企业必须指定groupId或者companyId');
      //   }
      //   //返回指定公司的关联方
      //   const subQb = qb.subQuery().from(MonitorCompanyRelatedPartyEntity, 'relatedParty').select('relatedParty.companyIdRelated');
      //   if (groupIds.length) {
      //     subQb.andWhere('relatedParty.monitorGroupId in (:...groupIds)', { groupIds });
      //   }
      //   if (companyIds.length) {
      //     subQb.where('relatedParty.companyIdPrimary in (:...companyIds)', { companyIds });
      //   }
      //   //
      //   qb.andWhere(`monitorCompany.companyId in (${subQb.getQuery()})`);
      // }
    } else {
      if (companyIds?.length) {
        qb.andWhere('monitorCompany.companyId in (:...companyIds)', { companyIds });
      }
    }
    if (groupIds) {
      qb.andWhere('monitorCompany.monitorGroupId in (:...groupIds)', { groupIds });
    } else {
      qb.andWhere('monitorCompany.monitorGroupId in (:...authGroupIds)', { authGroupIds });
    }
    if (operatorIds) {
      qb.andWhere('monitorCompany.createBy in (:...operatorIds)', { operatorIds });
    }
    if (keywords) {
      qb.andWhere('monitorCompany.companyName like :name', { name: `%${keywords}%` });
    }

    if (riskLevels) {
      qb.andWhere('monitorCompany.riskLevel in (:...riskLevels)', { riskLevels });
    }
    if (isRelatedCompanyExist && isRelatedCompanyExist.length === 1) {
      qb.andWhere(isRelatedCompanyExist.includes(1) ? 'monitorCompany.related_party_count != 0' : 'monitorCompany.related_party_count = 0');
    }
    // 创建时间
    QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');

    const promiseArray: Promise<any>[] = [];
    const qb1 = qb.clone();
    if (sortField) {
      qb1.orderBy(`monitorCompany.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
    } else {
      qb1.orderBy('monitorCompany.createDate', 'DESC');
    }

    if (!notUsePage) {
      qb1.skip(res.pageSize * (res.pageIndex - 1)).take(res.pageSize);
    }
    qb1.leftJoinAndSelect('monitorCompany.monitorGroupEntity', 'monitorGroupEntity');
    promiseArray.push(qb1.getManyAndCount());
    if (needAggs) {
      const qb2 = qb.clone();
      const qb3 = qb.clone();
      const qb4 = qb.clone();
      const qb5 = qb.clone();
      qb2
        .addGroupBy('monitorCompany.monitorGroupId')
        .select('monitorCompany.monitorGroupId', 'monitorGroupId')
        .addSelect('COUNT(1)', 'count')
        .orderBy('count', 'DESC');
      qb3.addGroupBy('monitorCompany.createBy').select('monitorCompany.createBy', 'creator').addSelect('COUNT(1)', 'count').orderBy('count', 'DESC');
      qb4.addGroupBy('monitorCompany.riskLevel').select('monitorCompany.riskLevel', 'riskLevel').addSelect('COUNT(1)', 'count').orderBy('count', 'DESC');
      qb5
        .select('COUNT(CASE WHEN related_party_count != 0 THEN 1 END)', 'existCount')
        .addSelect('COUNT(CASE WHEN related_party_count = 0 THEN 1 END)', 'noEnterPriesCount');
      promiseArray.push(qb2.getRawMany());
      promiseArray.push(qb3.getRawMany());
      promiseArray.push(qb4.getRawMany());
      promiseArray.push(qb5.getRawMany());
    }
    const [result1, result2, result3, result4, result5] = await Bluebird.all(promiseArray);
    res.data = result1[0];
    res.total = result1[1];
    if (res.data?.length && !notUsePage) {
      const companyIdList = _.uniq(res.data.map((t) => t.companyId));
      const companyDetaiRes: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
        Object.assign(new KysCompanySearchRequest(), {
          pageIndex: 1,
          pageSize: companyIdList.length,
          includeFields: ['id', 'name', 'statusCode'],
          filter: { ids: companyIdList },
        }),
      );
      res.data = res.data.map((x) => {
        const kysCompany = companyDetaiRes?.Result?.find((t) => t.id === x.companyId);
        if (kysCompany) {
          x['companyStatus'] = StatusCode[kysCompany?.statuscode] || '-';
        }
        return x;
      });
    }
    if (needAggs) {
      let modelList = result2;
      // 补充分组名称名称，分组的状态
      if (result2?.length) {
        const monitorGroupIds = result2.map((t) => t.monitorGroupId);
        const monitorGroupList = await this.monitorGroupRepo.findByIds(monitorGroupIds);
        modelList = result2.map((s) => {
          const monitorGroup = monitorGroupList.find((r) => r.monitorGroupId === s.monitorGroupId);
          s.name = monitorGroup?.name || '';
          s.monitorStatus = monitorGroup?.monitorStatus || '';
          return s;
        });
      }
      res.aggsRes = {
        monitorGroup: modelList,
        creator: result3,
        riskLevel: result4,
        relatedExist: result5,
      };
    }
    return res;
  }

  private async checkCompanyStandardCode(companyIds: string[]) {
    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控
    return await this.companySearchService.checkCompanyStandardCode(companyIds);
  }

  /**
   * 根据企业名称批量添加企业到分组中
   * @param currentUser
   * @param request
   */
  async batchAddMonitorByName(currentUser: PlatformUser, request: BatchAddCompanyByNameRequest) {
    const { monitorGroupId, companyNames } = request;
    // 如果传入的是企业名称，用于批量添加企业
    const postData: AddMonitorCompanyRequest = new AddMonitorCompanyRequest();
    postData.monitorGroupId = monitorGroupId;
    if (companyNames?.length) {
      const { matchedCompanyInfos } = await this.companySearchService.matchCompanyInfo(companyNames);
      if (matchedCompanyInfos?.length) {
        const items: AddMonitorCompanyItemPO[] = matchedCompanyInfos.map((x) => {
          return Object.assign(new AddMonitorCompanyItemPO(), { companyId: x.id, companyName: x.name });
        });
        postData.items = items;
      }
    }
    return this.addCompanies(currentUser, postData);
  }

  /**
   * 批量添加关联公司到监控列表
   * @param currentUser
   * @param postData
   */
  async addCompanies(currentUser: PlatformUser, postData: AddMonitorCompanyRequest) {
    const response: CreateCompanyResponse & { updatedList?: string[] } = {
      successList: [],
      failList: [],
      addedCount: 0,
      updatedCount: 0,
      failCount: 0,
      updatedList: [],
    };
    const { monitorGroupId, items: requests } = postData;
    if (!requests || requests.length == 0) {
      return response;
    }

    // 入参公司去重
    const uniqueCompanies = this.removeDuplicateCompanies(requests);
    const { currentOrg: orgId } = currentUser;

    // 校验 groupId
    const groupEntity: MonitorGroupEntity = await this.monitorGroupRepo.findOne({ where: { orgId, monitorGroupId } });
    if (!groupEntity) {
      //不能操作不是自己所在组织的group
      throw new ResourceOperationException();
    }

    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控, 返回可以监控的公司列表
    const { unsupportedCompanies, supportedCompanies, companyInfos } = await this.checkCompanyStandardCode(uniqueCompanies.map((x) => x.companyId));
    if (unsupportedCompanies.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
    }

    if (supportedCompanies.length < 1) {
      throw new BadParamsException(RoverExceptions.Monitor.Company.MonitorCompanyNotFound);
    }

    const companyIds = supportedCompanies.map((x) => x.id);
    let retryCount = 0;
    const maxRetries = 3;

    const executeTransaction = async (): Promise<CreateCompanyResponse> => {
      // 在事务外部定义，以便在 catch 块中可以访问
      let newGroupCompanyInfos: AddMonitorCompanyItemPO[] = [];

      try {
        // 校验公司是否已经被添加到监控列表中
        const existingGroupCompanies = await this.monitorCompanyRepo.find({
          where: {
            orgId,
            companyId: In(companyIds),
            monitorGroupId: monitorGroupId,
          },
        });
        const existingRelatedCompany: MonitorCompanyEntity[] = [];
        if (existingGroupCompanies.length) {
          existingGroupCompanies.forEach((e) => {
            if (e.primaryObject == 1) {
              // 是监控主体，无法重复主体
              response.failList.push(e.companyId);
            } else {
              // 是关联方，记录下来
              existingRelatedCompany.push(e);
            }
          });
          response.failCount = response.failList.length;
        }

        newGroupCompanyInfos = supportedCompanies
          .filter((s) => !existingGroupCompanies?.some((e) => e.companyId == s.id) && !existingRelatedCompany?.some((e) => e.companyId == s.id))
          .map((s) => Object.assign(new AddMonitorCompanyItemPO(), { companyId: s.id, companyName: s.name }));

        newGroupCompanyInfos.forEach((x) => {
          const kysCompany = companyInfos.Result.find((m) => x.companyId === m.id);
          x.companyName = kysCompany?.name || x.companyName;
        });

        await this.monitorCompanyRepo.manager.transaction(async (manager) => {
          // 使用临时变量存储事务内操作结果
          const transactionSuccessList: string[] = [];
          const transactionUpdateList: string[] = [];
          let transactionAddedCount = 0;
          let transactionUpdatedCount = 0;

          if (newGroupCompanyInfos.length) {
            // 新增监控企业到MonitorCompanyEntity表
            const addCompanyIds = await this.saveMonitorCompany(
              currentUser,
              monitorGroupId,
              MonitorCompanyPrimaryObjectEnum.Primary,
              newGroupCompanyInfos,
              manager,
            );
            transactionSuccessList.push(...addCompanyIds);
            transactionAddedCount = addCompanyIds.length;
          }

          if (existingRelatedCompany.length) {
            // 如果有公司已经被作为关联方加入到监控列表中并且是作为primaryObject加入到监控列表的，则把 primaryObject update 为1
            await manager.update(
              MonitorCompanyEntity,
              {
                monitorCompanyId: In(existingRelatedCompany.map((e) => e.monitorCompanyId)),
              },
              { primaryObject: 1 },
            );
            transactionUpdateList.push(...existingRelatedCompany.map((e) => e.companyId));
            transactionUpdatedCount = existingRelatedCompany.length;
          }
          // 只有事务成功提交后才更新外部 response
          response.successList.push(...transactionSuccessList);
          response.updatedList.push(...transactionUpdateList);
          response.addedCount = transactionAddedCount;
          response.updatedCount = transactionUpdatedCount;
        });

        return response;
      } catch (e) {
        // 检查是否是锁超时错误，如果是且未超过最大重试次数，则重试
        if (e.code === 'ER_LOCK_WAIT_TIMEOUT' && retryCount < maxRetries) {
          retryCount++;
          this.logger.warn(`addCompanies 锁超时错误，正在进行第 ${retryCount} 次重试...`);
          // 添加随机延迟，避免多个事务同时重试导致再次冲突
          await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));
          return executeTransaction();
        }

        this.logger.error('addCompanies error', e);
        response.failList = _.uniq([...newGroupCompanyInfos.map((x) => x.companyId), ...response.failList]);
        response.failCount = response.failList.length;
        return response;
      }
    };

    return await executeTransaction();
  }

  /**
   * 批量添加关联公司到监控列表
   * @param currentUser
   * @param postData
   */
  async addRelatedCompany(currentUser: PlatformUser, postData: AddMonitorRelatedCompanyRequest) {
    const { monitorGroupId, items: requests, companyId } = postData;
    // 校验 监控主体 是否存在
    const monitorCompany = await this.monitorCompanyRepo.findOne({
      where: {
        orgId: currentUser.currentOrg,
        monitorGroupId: monitorGroupId,
        companyId: companyId,
      },
    });
    if (!monitorCompany) {
      throw new BadRequestException(RoverExceptions.Monitor.Company.MonitorCompanyNotFound);
    }
    const response: CreateCompanyResponse & { updatedList?: string[] } = {
      successList: [],
      failList: [],
      addedCount: 0,
      updatedCount: 0,
      failCount: 0,
      updatedList: [],
    };

    const companyIds = requests.map((x) => x.companyId);

    let toAddRelated = requests;
    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控, 返回可以监控的公司列表
    const { supportedCompanies, companyInfos } = await this.checkCompanyStandardCode(companyIds);
    if (supportedCompanies.length < 1) {
      throw new BadParamsException(RoverExceptions.Monitor.Company.MonitorCompanyNotFound);
    }
    toAddRelated = requests.filter((x) => supportedCompanies.some((e) => e.id == x.companyId));

    let retryCount = 0;
    const maxRetries = 3;

    const executeTransaction = async (): Promise<CreateCompanyResponse & { updatedList?: string[] }> => {
      try {
        const result = await this.saveRelatedCompany(currentUser, monitorGroupId, companyId, toAddRelated);
        response.successList = result.successList;
        response.addedCount = result.successList.length;
        response.updatedList = result.updatedList;
        response.updatedCount = result.updatedList.length;
        return response;
      } catch (e) {
        // 检查是否是锁超时错误，如果是且未超过最大重试次数，则重试
        if (e.code === 'ER_LOCK_WAIT_TIMEOUT' && retryCount < maxRetries) {
          retryCount++;
          this.logger.warn(`addRelatedCompany 锁超时错误，正在进行第 ${retryCount} 次重试...`);
          // 添加随机延迟，避免多个事务同时重试导致再次冲突
          await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));
          return executeTransaction();
        }

        this.logger.error('addRelatedCompany error', e);
        throw e;
      }
    };

    return await executeTransaction();
  }

  /**
   * 添加全部关联方
   * @param request
   * @param currentUser
   * @returns
   */
  public async companyRelatedMonitorAll(request: CompanyRelatedMonitorAllRequest, currentUser: PlatformUser) {
    const user = new PlatformUser();
    user.currentOrg = currentUser.currentOrg;
    user.currentProduct = currentUser.currentProduct;
    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控
    const resp = await this.filterUnMonitorCompany(request, user);
    const { unMonitorCompanyList, unsupportedCompanies } = resp;
    // 如果最终未监控公司列表为空，抛出异常
    if (!unMonitorCompanyList.length) {
      throw new BadParamsException(RoverExceptions.Setting.monitorRelatedCompaniesNull);
    }
    // 监控公司不可超过六千
    if (unMonitorCompanyList.length > 6000) {
      throw new BadParamsException(RoverExceptions.Setting.monitorRelatedCompanyLengthMax);
    }

    const response: CreateCompanyResponse = {
      successList: [],
      failList: [],
      addedCount: 0,
      updatedCount: 0,
      failCount: 0,
    };

    //组装请求参数， 忽略不支持的企业
    const toAddRelated: AddMonitorRelatedCompanyItemPO[] = [];
    unMonitorCompanyList.forEach((x) => {
      if (!unsupportedCompanies.some((company) => company.id == x.companyKeynoRelated)) {
        const item = new AddMonitorRelatedCompanyItemPO();
        item.companyId = x.companyKeynoRelated;
        item.companyName = x.companyNameRelated;
        item.relatedType = x.relatedTypes.join(',');
        toAddRelated.push(item);
      }
    });

    const { monitorGroupId, companyId } = request;

    const batchSize = 500; // 每批次查询的最大条数
    let offset = 0; // 当前查询的起始位置

    do {
      const batchToAddRelated = toAddRelated.slice(offset, offset + batchSize);

      try {
        if (batchToAddRelated.length > 0) {
          const { successList: batchSuccessList, updatedList: batchUpdatedList } = await this.saveRelatedCompany(
            currentUser,
            monitorGroupId,
            companyId,
            batchToAddRelated,
          );
          response.addedCount += batchSuccessList.length;
          response.updatedCount += batchUpdatedList.length;
        }
      } catch (e) {
        const companyIds = batchToAddRelated.map((item) => item.companyId);
        // 构造包含描述信息的对象
        const logInfo = {
          error: e,
          companyId: companyId,
          companyIds,
        };
        this.logger.error('关联方变化监控全部分页异常', logInfo);
        response.failCount += companyIds.length;
      }
      offset += batchSize;
    } while (offset < toAddRelated.length);
    response.failCount += unsupportedCompanies.length;
    return response;
    // TODO: 暂时使用同步批量添加, 不创建消息

    // //消息落库
    // const messageBody = Object.assign(new MessageEntity(), {
    //   title: BatchBusinessMessageTitle[BatchBusinessTypeEnums.monitor_Related_Company],
    //   content: `异步任务添加关联方已经完成，添加成功 ${response?.successList?.length || 0} 家企业，添加失败 ${response?.failList?.length || 0} 家企业`,
    //   userId: currentUser.userId,
    //   orgId: currentUser.currentOrg,
    //   product: currentUser.currentProduct,
    //   msgType: MsgType.SystemMsg,
    // });
    // this.logger.info(`batchResultBusinessQueue sendMessage: ${JSON.stringify(messageBody)}`);
    // await this.msgRepository.save(messageBody);
    // await this.batchResultBusinessQueue.sendMessageV2(messageBody, { ttl: 1, retries: 1 });
    // return response;
  }

  /**
   * 从当前分组中删除
   *
   * delCompany = select * from monitor_company mc where mc.company_id = 'delCompanyId' and mc.monitor_group_id = 1324;
   *
   * monitorCompanyDelIds = []
   * monitorCompanyDelIds.push(delCompanyId);
   *
   * if(company.primary_object == 1){
   * -- 如果delCompany 主体公司， 查询出 delCompany 的所有关联方在监控列表中的状态 ，
   * relatedCompanyIds = select * from monitor_company_realted_party where company_id_primary = 'delCompanyId' and
   * mc.monitor_group_id = 1324;
   *
   * otherRelatedCompanyIds = select * from monitor_company_realted_party where company_id_primary != 'delCompanyId' and company_id_related in(relatedCompanyIds)
   * mc.monitor_group_id = 1324;
   *
   * deleleCompanyIds = relatedCompanyIds- otherRelatedCompanyIds
   *
   * monitorCompanyPrimary0 = select * from monitor_company mc where mc.company_id = 'deleleCompanyIds' and primary_object =
   * 0 and mc.monitor_group_id = 1324;
   *
   *  if(monitorCompanyPrimary0){
   *    -- delCompany 的关联方是0 的部分， 删除这些关联方关系以及监控列表记录
   *    monitorCompanyDelIds.push(monitorCompanyPrimary0)
   *  }
   *
   * }
   *
   * -- 删除 delCompany 作为关联方的 所有关系记录
   * delete from monitor_company_realted_party where  (company_id_related = 'delCompanyId' or company_id_primary = '
   * delCompanyId' ) and mc.monitor_group_id = 1324;
   * delete from monitor_company where mc.company_id in (monitorCompanyDelIds) and mc.monitor_group_id = 1324;
   * @param currentUser
   * @param param
   */
  public async singleGroupCompanysDelete(currentUser: PlatformUser, monitorGroupId: number, companyIds: string[], entityManger?: EntityManager) {
    let hasDeleted = false;
    const { currentOrg: orgId, currentProduct } = currentUser;
    const monitorCompanyEntityList = await this.monitorCompanyRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        product: currentUser.currentProduct,
        monitorGroupId,
        companyId: In(companyIds),
      },
    });
    if (!monitorCompanyEntityList.length) {
      return { hasDeleted };
    }
    if (!entityManger) {
      entityManger = this.monitorCompanyRepo.manager;
    }

    let deleteMonitorCompanyIds: string[] = [];
    // 1,被删除企业ID中过滤出是主体企业的，找到主体企业的关联方企业，
    // 如果关联方企业还是作为别的企业的关联方不删，如果关联方企业只是
    // 作为当前被删除企业的关联方，则关联方企业也删除。
    const primaryCompanyIds = monitorCompanyEntityList.filter((x) => x.primaryObject == 1)?.map((r) => r.companyId) || [];
    if (primaryCompanyIds.length) {
      const relatedPartyList = await this.relatedPartyRepo.find({
        where: {
          orgId,
          product: currentProduct,
          companyIdPrimary: In(primaryCompanyIds),
          monitorGroupId: monitorGroupId,
        },
      });
      const relateCompanyIds = relatedPartyList?.map((t) => t.companyIdRelated) || [];
      //如果还作为别的企业的关联方不删除
      const otherRelatedPartyList = await this.relatedPartyRepo.find({
        where: {
          orgId,
          product: currentProduct,
          monitorGroupId: monitorGroupId,
          companyIdPrimary: Not(In(primaryCompanyIds)),
          companyIdRelated: In(relateCompanyIds),
        },
      });
      const otherRelateCompanyIds = otherRelatedPartyList?.map((t) => t.companyIdRelated) || [];

      const deleteRelateCompanyIds = _.difference(relateCompanyIds, otherRelateCompanyIds);
      if (deleteRelateCompanyIds?.length) {
        // 删除关联方只是作为关联方的数据
        const monitorCompanyPrimary0List = await this.monitorCompanyRepo.find({
          where: {
            orgId: currentUser.currentOrg,
            product: currentUser.currentProduct,
            monitorGroupId,
            primaryObject: 0,
            companyId: In(deleteRelateCompanyIds),
          },
        });
        if (monitorCompanyPrimary0List.length) {
          deleteMonitorCompanyIds.push(...monitorCompanyPrimary0List.map((t) => t.companyId));
        }
      }
    }
    deleteMonitorCompanyIds.push(...monitorCompanyEntityList.map((t) => t.companyId));
    deleteMonitorCompanyIds = _.uniq(deleteMonitorCompanyIds);

    // 2, MonitorCompanyRelatedPartyEntity表统计删除的关联方Id，(companyIdPrimary
    const relatedPartyItems = await this.relatedPartyRepo
      .createQueryBuilder('relatedParty')
      .where('relatedParty.monitorGroupId = :monitorGroupId', { monitorGroupId })
      .andWhere('relatedParty.product = :product', { product: currentProduct })
      .andWhere('relatedParty.orgId = :orgId', { orgId })
      .andWhere(
        new Brackets((qb1) => {
          qb1
            .where('relatedParty.companyIdPrimary in (:...companyIds)', { companyIds })
            .orWhere('relatedParty.companyIdRelated in (:...companyIds)', { companyIds });
        }),
      )
      .getMany();
    const deleteRelatedIds = relatedPartyItems?.map((t) => t.id) || [];

    // 3，统计需要更新count数量的主体企业Id
    const needUpdateCompanyIds = _.uniq([
      ...(relatedPartyItems?.map((t) => t.companyIdPrimary) || []),
      ...(relatedPartyItems?.map((t) => t.companyIdRelated) || []),
    ]);
    const diffCompanyIds = _.difference(needUpdateCompanyIds, deleteMonitorCompanyIds) || [];
    const needUpdateRelatedCompanys = await this.monitorCompanyRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        product: currentUser.currentProduct,
        primaryObject: 1,
        monitorGroupId,
        companyId: In(diffCompanyIds),
      },
    });

    // 4，统计需要删除动态Id
    const metricDynamicList = await this.monitorMetricsDynamicRepo.find({
      orgId,
      companyId: In(deleteMonitorCompanyIds),
      monitorGroupId,
    });
    const monitorUniqueHashkeys = metricDynamicList.map((t) => t.uniqueHashkey);
    // 5，删除事务
    try {
      await entityManger.transaction(async (transactionalEntityManager) => {
        // 删除监控企业
        if (deleteMonitorCompanyIds?.length) {
          await transactionalEntityManager.delete(MonitorCompanyEntity, {
            companyId: In(deleteMonitorCompanyIds),
            orgId,
            product: currentProduct,
            monitorGroupId,
          });
          await transactionalEntityManager.update(
            MonitorGroupEntity,
            { monitorGroupId },
            { companyCount: () => `company_count - ${deleteMonitorCompanyIds.length}` },
          );
        }
        // 删除关联方
        if (deleteRelatedIds?.length) {
          await transactionalEntityManager.delete(MonitorCompanyRelatedPartyEntity, {
            id: In(deleteRelatedIds),
          });
        }
        // 更新主企业的关联方数量
        if (needUpdateCompanyIds?.length) {
          const dbUpdateRelateds = needUpdateRelatedCompanys.map((t) => {
            t.relatedPartyCount = t.relatedPartyCount - relatedPartyItems?.filter((r) => r.companyIdPrimary == t.companyId)?.length || 0;
            return t;
          });
          await transactionalEntityManager.save(dbUpdateRelateds);
        }
        // 废弃动态,逻辑删除
        if (monitorUniqueHashkeys?.length) {
          await transactionalEntityManager.update(
            MonitorMetricsDynamicEntity,
            {
              uniqueHashkey: In(monitorUniqueHashkeys),
            },
            { status: MetricDynamicStatusEnums.Deprecated },
          );
        }
      });
      hasDeleted = true;
    } catch (error) {
      console.error('删除企业异常:', error);
      return { hasDeleted };
    }
    return { hasDeleted, monitorUniqueHashkeys, deleteMonitorCompanyIds };
  }

  public async batchCompanyDelete(currentUser: PlatformUser, param: BatchDeleteMonitorCompanyRequest, entityManger?: EntityManager): Promise<AffectedResponse> {
    const { currentOrg: orgId, currentProduct } = currentUser;
    const affectedResponse = new AffectedResponse();
    const { deleteMonitors } = param;
    const affectedInfos: any[] = [];
    const esUpdateIds: string[] = [];
    const deleteMonitorMap = groupBy(deleteMonitors, (t) => t.monitorGroupId);
    const monitorGroupIds = Object.keys(deleteMonitorMap);
    const groupEntitys = await this.monitorGroupRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        monitorGroupId: In(monitorGroupIds),
      },
    });
    if (!groupEntitys.length || groupEntitys.length !== monitorGroupIds.length) {
      //不能操作不是自己所在组织的group
      throw new ResourceOperationException();
    }
    await Bluebird.map(monitorGroupIds, async (monitorGroupId) => {
      const companyIds = deleteMonitorMap[monitorGroupId].map((t) => t.companyId);
      const { hasDeleted, monitorUniqueHashkeys, deleteMonitorCompanyIds } = await this.singleGroupCompanysDelete(
        currentUser,
        Number(monitorGroupId),
        companyIds,
        entityManger,
      );
      if (hasDeleted) {
        affectedInfos.push(...deleteMonitorCompanyIds);
        if (monitorUniqueHashkeys?.length) {
          esUpdateIds.push(...monitorUniqueHashkeys);
        }
      }
    });
    if (esUpdateIds.length) {
      // ES 中动态更新为废弃
      await this.dynamicEsService.updateDynamicStatus(
        orgId,
        currentProduct,
        Object.assign(new MetricUpdateDynamicStatusPO(), { uniqueHashkey: esUpdateIds }),
        MetricDynamicStatusEnums.Deprecated,
      );
    }
    affectedResponse.affectedInfos = affectedInfos;
    affectedResponse.affected = affectedInfos.length;
    return affectedResponse;
  }

  public async removeAllCompanyBySearch(currentUser: PlatformUser, param: SearchMonitorCompanyRequest) {
    const resp = await this.searchMonitorCompany(param, currentUser, true);
    const data = resp.data;
    if (data.length) {
      const deleteCompanyItems: DeleteMonitorCompanyRequest[] = data.map((x) =>
        Object.assign(new DeleteMonitorCompanyRequest(), { monitorGroupId: x.monitorGroupId, companyId: x.companyId }),
      );
      const request = new BatchDeleteMonitorCompanyRequest();
      request.deleteMonitors = deleteCompanyItems;
      return await this.batchCompanyDelete(currentUser, request);
    }
    const affectedResponse = new AffectedResponse();
    affectedResponse.affected = 0;
    affectedResponse.affectedInfos = [];
    return affectedResponse;
  }

  public async removeAllCompanyByRelatedSearch(currentUser: PlatformUser, param: SearchMonitorCompanyRelatedPartyRequest) {
    const resp = await this.searchMonitorCompanyRelated(param, currentUser, true);
    const data = resp.data;
    if (data.length) {
      const deleteCompanyItems: DeleteMonitorCompanyRequest[] = data.map((x) =>
        Object.assign(new DeleteMonitorCompanyRequest(), {
          monitorGroupId: x.monitorGroupId,
          companyId: x.companyIdRelated,
        }),
      );
      const request = new BatchDeleteMonitorCompanyRequest();
      request.deleteMonitors = deleteCompanyItems;
      return await this.batchCompanyDelete(currentUser, request);
    }
    const affectedResponse = new AffectedResponse();
    affectedResponse.affected = 0;
    affectedResponse.affectedInfos = [];
    return affectedResponse;
  }

  /**
   * 从当前分组中移动到另一个分组中
   * 1，从当前分组中删除，
   * 2，添加到另外一个分组。
   *
   * @param currentUser
   * @param param
   */
  public async batchCompanyTransfer(currentUser: PlatformUser, param: TransferMonitorCompanyRequest): Promise<AffectedResponse> {
    const affectedResponse = new AffectedResponse();
    const { toMonitorGroupId, deleteMonitors } = param;

    const monitorGroupIds = [...new Set([...deleteMonitors.map((x) => x.monitorGroupId), toMonitorGroupId])];
    // 校验 groupId
    const groupEntitys = await this.monitorGroupRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        monitorGroupId: In(monitorGroupIds),
      },
    });

    if (!groupEntitys.length || groupEntitys.length !== monitorGroupIds.length) {
      //不能操作不是自己所在组织的group
      throw new ResourceOperationException();
    }
    const existingGroupCompanies = await this.monitorCompanyRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        monitorGroupId: toMonitorGroupId,
        product: currentUser.currentProduct,
        companyId: In(deleteMonitors.map((x) => x.companyId)),
      },
    });
    //过滤已存在于目标分组中的企业，并去重
    const newGroupCompanyInfos = _.uniq(deleteMonitors.filter((s) => !existingGroupCompanies?.some((e) => e.companyId == s.companyId)));

    if (newGroupCompanyInfos.length === 0) {
      //无有效数据
      throw new BadParamsException(RoverExceptions.Setting.transferDuplicateError);
    }
    //补充 companyName
    const companyIds = _.uniq(newGroupCompanyInfos?.map((t) => t.companyId)) || [];
    const esRes: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        pageIndex: 1,
        pageSize: companyIds.length,
        includeFields: ['id', 'name'],
        filter: { ids: companyIds },
      }),
    );
    const toAddCompany = esRes?.Result?.map((t) => {
      return Object.assign(new AddMonitorCompanyItemPO(), { companyId: t.id, companyName: t.name });
    });
    try {
      await this.monitorCompanyRepo.manager.transaction(async (entityManager) => {
        const batchReq = new BatchDeleteMonitorCompanyRequest();
        batchReq.deleteMonitors = newGroupCompanyInfos;
        // 1,删除企业
        const response = await this.batchCompanyDelete(currentUser, batchReq, entityManager);
        const deletedCount = response?.affected || 0;
        affectedResponse.affected = deletedCount; // 删除后退回的额度
        // 2，企业添加到分组
        const addResult = toAddCompany?.length
          ? await this.saveMonitorCompany(currentUser, toMonitorGroupId, MonitorCompanyPrimaryObjectEnum.Primary, toAddCompany, entityManager)
          : [];
        const addCount = addResult.length;
        // 这里 affected 标识的是删除的数量-重新新增的数量，结果值是需要退回的额度
        affectedResponse.affected = deletedCount - addCount;
      });
    } catch (e) {
      this.logger.error('companyTransfer error', e);
      throw new BadParamsException(RoverExceptions.Setting.transferError);
    }

    return affectedResponse;
  }

  private generateUniqueKey(list: string[]) {
    const sortedList = list.sort((a, b) => a.localeCompare(b));
    const serialized = JSON.stringify(sortedList);
    return crypto.createHash('sha256').update(serialized).digest('hex');
  }

  public async getUnMonitorCompany(request: CompanyRelatedListParams, currentUser: PlatformUser) {
    const user = new PlatformUser();
    user.currentOrg = currentUser.currentOrg;
    user.currentProduct = currentUser.currentProduct;
    const resp = await this.filterUnMonitorCompany(request, user);
    return {
      unMonitorCompanyCount: resp.unMonitorCompanyCount,
      filterMonitorCompanyCount: resp.filterMonitorCompanyCount,
    };
  }

  @Cacheable({ ttlSeconds: 60 })
  public async filterUnMonitorCompany(request: CompanyRelatedListParams, currentUser: PlatformUser) {
    const { currentOrg: orgId, currentProduct: product } = currentUser;
    //获取所有关联方企业
    const companyRelatedList = await this.relatedCompanyService.getCompanyRelatedList(request, orgId, product, false);
    // 过滤未监控的公司
    const unMonitorCompanyList = companyRelatedList.Result.filter((x) => x['isMonitor'] === false);

    if (!companyRelatedList) {
      return {
        unMonitorCompanyCount: 0,
        filterMonitorCompanyCount: 0,
        unMonitorCompanyList: [],
        unsupportedCompanies: [],
      };
    }
    const unsupportedCompanies: any[] = [];
    //关联方查询中已过滤港澳台等海外企业
    // if (unMonitorCompanyList.length > 0) {
    //   unsupportedCompanies = await this.checkCompanyStandardCode(unMonitorCompanyList.map(x => x.companyKeynoRelated));
    // }
    return {
      unMonitorCompanyCount: unMonitorCompanyList?.length,
      filterMonitorCompanyCount: unMonitorCompanyList?.length - unsupportedCompanies?.length,
      unMonitorCompanyList: unMonitorCompanyList,
      unsupportedCompanies: unsupportedCompanies,
    };
  }

  /**
   * 记录当前分组所有主体企业的关联方日志
   * @param orgId
   * @param monitorGroupId
   * @param companyIdPrimary
   */
  public async saveCompanyRelatedDailyFroGroup(orgId: number, monitorGroupId: number) {
    try {
      const companyIdPrimarys = await this.monitorCompanyRepo.find({
        where: {
          orgId,
          monitorGroupId,
          primaryObject: MonitorCompanyPrimaryObjectEnum.Primary,
          status: MonitorCompanyStatusEnums.Done,
        },
        select: ['companyId'],
      });
      await Bluebird.map(
        companyIdPrimarys,
        async (companyIdPrimary) => {
          await this.relatedCompanyService.saveCompanyRelatedDaily(orgId, monitorGroupId, companyIdPrimary.companyId);
        },
        { concurrency: 10 },
      );
    } catch (error) {
      this.logger.error(`saveCompanyRelatedDailyFroGroup orgId:${orgId}, monitorGroupId:${monitorGroupId}, error:${error.message}`);
      this.logger.error(error);
    }
  }

  /**
   * 新增监控企业到MonitorCompanyEntity表
   * @param currentUser
   * @param monitorGroupId
   * @param primaryObject 1-监控主体； 0-关联方
   * @param newGroupCompanyInfos
   * @param entityManger
   * @returns
   */
  private async saveMonitorCompany(
    currentUser: PlatformUser,
    monitorGroupId: number,
    primaryObject: MonitorCompanyPrimaryObjectEnum,
    newGroupCompanyInfos: AddMonitorCompanyItemPO[],
    entityManger?: EntityManager,
  ): Promise<string[]> {
    const { userId, currentOrg: orgId, currentProduct } = currentUser;
    const addCompanyIds: string[] = [];
    const toSaveGroupCompanyInfos: MonitorCompanyEntity[] = newGroupCompanyInfos.map((r: AddMonitorCompanyItemPO) => {
      const groupCompanyItem: MonitorCompanyEntity = Object.assign(new MonitorCompanyEntity(), {
        companyId: r.companyId,
        companyName: r.companyName,
        createBy: userId,
        orgId: orgId,
        product: currentProduct,
        monitorGroupId,
        primaryObject,
      });
      return groupCompanyItem;
    });

    if (!entityManger) {
      entityManger = this.monitorCompanyRepo.manager;
    }

    if (toSaveGroupCompanyInfos.length) {
      let retryCount = 0;
      const maxRetries = 3;

      const executeOperation = async (): Promise<string[]> => {
        try {
          // 如果已经在事务中，直接执行操作，不要创建新的事务
          if (entityManger?.queryRunner?.isTransactionActive) {
            await entityManger.insert(MonitorCompanyEntity, toSaveGroupCompanyInfos);
            await entityManger.update(MonitorGroupEntity, monitorGroupId, {
              companyCount: () => `company_count + ${toSaveGroupCompanyInfos.length}`,
            });
            addCompanyIds.push(...toSaveGroupCompanyInfos.map((x) => x.companyId));
            return addCompanyIds;
          } else {
            // 如果不在事务中，创建新的事务
            await entityManger.transaction(async (manager) => {
              await manager.insert(MonitorCompanyEntity, toSaveGroupCompanyInfos);
              await manager.update(MonitorGroupEntity, monitorGroupId, {
                companyCount: () => `company_count + ${toSaveGroupCompanyInfos.length}`,
              });
              addCompanyIds.push(...toSaveGroupCompanyInfos.map((x) => x.companyId));
            });
            return addCompanyIds;
          }
        } catch (e) {
          // 检查是否是锁超时错误，如果是且未超过最大重试次数，则重试
          if (e.code === 'ER_LOCK_WAIT_TIMEOUT' && retryCount < maxRetries) {
            retryCount++;
            this.logger.warn(`saveMonitorCompany 锁超时错误，正在进行第 ${retryCount} 次重试...`);
            // 添加随机延迟，避免多个事务同时重试导致再次冲突
            await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));
            return executeOperation();
          }
          throw e;
        }
      };

      return await executeOperation();
    }
    return addCompanyIds;
  }

  private async saveRelatedCompany(
    currentUser: PlatformUser,
    monitorGroupId: number,
    companyIdPrimary: string,
    toAddRelated: AddMonitorRelatedCompanyItemPO[],
  ) {
    const { currentOrg: orgId, currentProduct } = currentUser;
    let successList = [];
    let updatedList = [];
    const companyIds = toAddRelated.map((r) => r.companyId);
    let retryCount = 0;
    const maxRetries = 3;

    // 1 判断关联方公司是否已经被添加到监控列表中, 如果不存在，添加到监控列表，并且把 primaryObject 置为0
    const existingGroupCompanies = await this.monitorCompanyRepo.find({
      where: {
        orgId,
        companyId: In(companyIds),
        monitorGroupId: monitorGroupId,
      },
    });

    // 2、关联方关系表中已经存在的关联方， 判断是否要修改关联关系
    const existingRelatedPartyInfos = await this.relatedPartyRepo.find({
      where: {
        monitorGroupId: monitorGroupId,
        companyIdPrimary,
        companyIdRelated: In(companyIds),
      },
    });
    const toUpdateRelatedParties = existingRelatedPartyInfos?.filter((e) => {
      const n = find(toAddRelated, (e1) => e1.companyId == e.companyIdRelated);
      // 二次判断已经存在的关联方类型和即将添加的同一个企业的关联方类型是否一致
      return n && n.relatedType != e.relatedTypeStr;
    });

    const executeTransaction = async (): Promise<{ successList: string[]; updatedList: string[] }> => {
      try {
        return await this.monitorGroupRepo.manager.transaction(async (manager) => {
          if (existingGroupCompanies?.length !== companyIds.length) {
            const diffCompanyIds = companyIds.filter((x) => !existingGroupCompanies.some((y) => y.companyId === x));
            const toAddRelatedDiff = toAddRelated.filter((x) => diffCompanyIds.some((y) => x.companyId === y));
            // 新增监控企业到MonitorCompanyEntity表
            const addSucCompanyIds = await this.saveMonitorCompany(
              currentUser,
              monitorGroupId,
              MonitorCompanyPrimaryObjectEnum.Related,
              toAddRelatedDiff,
              manager,
            );
            successList = successList.concat(addSucCompanyIds);
          }
          if (toUpdateRelatedParties.length) {
            //找到新的 relatedType 更新到已经存在的关联方表中的 relatedTypeStr 字段
            await Bluebird.map(toUpdateRelatedParties, (to) => {
              const n = find(toAddRelated, (e1) => e1.companyId == to.companyIdRelated);
              return manager.update(
                MonitorCompanyRelatedPartyEntity,
                {
                  id: to.id,
                },
                { relatedTypeStr: n.relatedType },
              );
            });
            updatedList = toUpdateRelatedParties.map((r) => r.companyIdRelated);
          }
          // 3、关联方关系表中已经存在的关联方，添加到关联方表中
          const newRelatedParties = toAddRelated
            .filter((r) => !existingRelatedPartyInfos.some((e) => e.companyIdRelated == r.companyId))
            .map((r) => {
              const relatedPartyItem: MonitorCompanyRelatedPartyEntity = Object.assign(new MonitorCompanyRelatedPartyEntity(), {
                monitorGroupId: monitorGroupId,
                companyIdPrimary,
                companyIdRelated: r.companyId,
                orgId,
                product: currentProduct,
                status: MonitorCompanyRelatedStatusEnum.Valid,
                relatedTypeStr: r.relatedType,
              });
              return relatedPartyItem;
            });
          if (newRelatedParties.length) {
            await manager.insert(MonitorCompanyRelatedPartyEntity, newRelatedParties);
            // 更新监控主体的关联方数量，并且把 primaryObject 置为监控主体
            // 使用传入的事务管理器进行更新，避免创建新的事务
            await manager.update(
              MonitorCompanyEntity,
              {
                monitorGroupId: monitorGroupId,
                companyId: companyIdPrimary,
              },
              {
                relatedPartyCount: () => `related_party_count + ${newRelatedParties.length}`,
                primaryObject: MonitorCompanyPrimaryObjectEnum.Primary,
              },
            );
            // successList = newRelatedParties.map((r) => r.companyIdRelated);
            updatedList = updatedList.concat(newRelatedParties.map((r) => r.companyIdRelated));
          }
          //updateList只保留非新添加到监控列表的数据
          updatedList = _.uniq(updatedList.filter((x) => !successList.includes(x)));
          return { successList, updatedList };
        });
      } catch (e) {
        // 检查是否是锁超时错误，如果是且未超过最大重试次数，则重试
        if (e.code === 'ER_LOCK_WAIT_TIMEOUT' && retryCount < maxRetries) {
          retryCount++;
          this.logger.warn(`锁超时错误，正在进行第 ${retryCount} 次重试...`);
          // 添加随机延迟，避免多个事务同时重试导致再次冲突
          await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));
          return executeTransaction();
        } else if (e.code === 'ER_NO_DEFAULT_FOR_FIELD') {
          throw new BadParamsException(RoverExceptions.Setting.addRelatedError);
        }
        this.logger.error('addRelatedCompany error', e);
        this.logger.error(e);
        throw new InternalServerErrorException(RoverExceptions.Setting.addRelatedError);
      }
    };

    return await executeTransaction();
  }

  /**
   * 按照companyId去重, 重复数据忽略
   * @param requestCompanies
   */
  private removeDuplicateCompanies(requestCompanies: AddMonitorCompanyItemPO[]) {
    // const companyIds: string[] = requestCompanies?.map((x) => x.companyId);
    // if (new Set(companyIds).size !== requestCompanies.length) {
    //   throw new BadRequestException(RoverExceptions.Monitor.Company.DuplicatedMonitorCompanies);
    // }
    // return companyIds;

    return requestCompanies.filter((company, index, self) => index === self.findIndex((c) => c.companyId === company.companyId));
  }

  /**
   * 添加全部关联方
   * @param request
   * @param currentUser
   * @returns
   */
  public async relatedDynamicCompanyRelatedMonitorAll(request: QueryMonitorDynamicDetialsRequest, currentUser: PlatformUser) {
    request.orgId = currentUser.currentOrg;
    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控
    const resp = await this.dynamicDetailsService.filterUnMonitorCompany(request);
    const { unMonitorCompanyList, unsupportedCompanies } = resp;
    // 如果最终未监控公司列表为空，抛出异常
    if (!unMonitorCompanyList.length) {
      throw new BadParamsException(RoverExceptions.Setting.monitorRelatedCompaniesNull);
    }
    // 监控公司不可超过六千
    if (unMonitorCompanyList.length > 6000) {
      throw new BadParamsException(RoverExceptions.Setting.monitorRelatedCompanyLengthMax);
    }

    const response: CreateCompanyResponse = {
      successList: [],
      failList: [],
      addedCount: 0,
      updatedCount: 0,
      failCount: 0,
    };

    //组装请求参数， 忽略不支持的企业
    const toAddRelated: AddMonitorRelatedCompanyItemPO[] = [];
    unMonitorCompanyList.forEach((x) => {
      if (!unsupportedCompanies.some((company) => company.id == x.companyKeynoRelated)) {
        const item = new AddMonitorRelatedCompanyItemPO();
        item.companyId = x.companyKeynoRelated;
        item.companyName = x.companyNameRelated;
        item.relatedType = x.relatedTypes.join(',');
        toAddRelated.push(item);
      }
    });

    const { monitorGroupId, companyId } = request;

    const batchSize = 500; // 每批次查询的最大条数
    let offset = 0; // 当前查询的起始位置

    do {
      const batchToAddRelated = toAddRelated.slice(offset, offset + batchSize);

      try {
        if (batchToAddRelated.length > 0) {
          const { successList: batchSuccessList, updatedList: batchUpdatedList } = await this.saveRelatedCompany(
            currentUser,
            monitorGroupId,
            companyId,
            batchToAddRelated,
          );
          response.addedCount += batchSuccessList.length;
          response.updatedCount += batchUpdatedList.length;
        }
      } catch (e) {
        const companyIds = batchToAddRelated.map((item) => item.companyId);
        // 构造包含描述信息的对象
        const logInfo = {
          error: e,
          companyId: companyId,
          companyIds,
        };
        this.logger.error('关联方变化监控全部分页异常', logInfo);
        response.failCount += companyIds.length;
      }
      offset += batchSize;
    } while (offset < toAddRelated.length);
    response.failCount += unsupportedCompanies.length;
    return response;
  }

  public async relatedDynamicGetUnMonitorCompany(request: QueryMonitorDynamicDetialsRequest) {
    const resp = await this.dynamicDetailsService.filterUnMonitorCompany(request);
    return {
      unMonitorCompanyCount: resp.unMonitorCompanyCount,
      filterMonitorCompanyCount: resp.filterMonitorCompanyCount,
    };
  }
}
