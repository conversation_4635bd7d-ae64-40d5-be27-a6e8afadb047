import { EntityManager, getConnection, getRepository, Repository } from 'typeorm';
import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
import { PlatformUser } from '../../../libs/model/common';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { Test, TestingModule } from '@nestjs/testing';
import { AnalyzeMonitorDynamicMessagePO } from '../po/AnalyzeMonitorDynamicMessagePO';
import { EvaluationService } from '../../diligence/evaluation/evaluation.service';
import { MonitorDynamicMessageListener } from './monitor.dynamic.message.listener';
import { getMetricDimensionStrategyField } from '../../../libs/db_helpers/resource.publish.helper';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from '../../../libs/entities/DimensionHitStrategyFieldsEntity';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { deleteRiskModelCache } from '../../risk_model/risk_model.utils';
import { MonitorModule } from '../monitor.module';
import { QueryMonitorDynamicDetialsRequest } from '../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { MonitorDynamicEsService } from './monitor.dynamic.es.service';
import { SearchDynamicEsPO } from '../../../libs/model/monitor/SearchDynamicEsPO';
import { MonitorMetricDynamicEsDoc } from '../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { AppTestModule } from '../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';

// 测试数据
const [testOrgId, testUserId] = generateUniqueTestIds('monitor.dynamic.message.debug.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(600000);
describe.skip('debug-生成动态', () => {
  process.env.MOCK_MESSAGE_QUEUE_NOT = 'true';
  process.env.STAGE = 'local';
  let monitorDynamicService: MonitorDynamicMessageListener;
  let monitorDynamicRepo: Repository<MonitorMetricsDynamicEntity>;
  let entityManager: EntityManager;
  let evaluationService: EvaluationService;
  let redisService: RedisService;
  let dynamicEsService: MonitorDynamicEsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MonitorModule],
    }).compile();
    monitorDynamicService = module.get(MonitorDynamicMessageListener);
    monitorDynamicRepo = getRepository(MonitorMetricsDynamicEntity);
    entityManager = monitorDynamicRepo.manager;
    evaluationService = module.get(EvaluationService);
    redisService = module.get(RedisService);
    dynamicEsService = module.get(MonitorDynamicEsService);

    // await clearMonitorTestData(entityManager, testUser);
  });

  afterAll(async () => {
    const connection = getConnection();
    await connection.close();
  });

  it.skip('debug-修改模型的指定配置 潜在关联方的数据范围', async () => {
    const modelId = 2136;
    const strategyField = await getMetricDimensionStrategyField(
      entityManager,
      modelId,
      '潜在关联方',
      DimensionTypeEnums.RelatedCompanies,
      DimensionFieldKeyEnums.relatedRoleType,
    );
    // console.log(strategyField.id);
    // console.log(strategyField.fieldValue);
    // [
    //   "MotherCompanyMajorityShareholder",
    //   "MotherCompanyControl",
    //   "MajorityInvestment",
    // ]

    const newFieldValue = ['MotherCompanyMajorityShareholder', 'MotherCompanyControl', 'MajorityInvestment', 'Branch'] as any[];

    await entityManager.update(DimensionHitStrategyFieldsEntity, { id: strategyField.id }, { fieldValue: newFieldValue });

    // 删除模型缓存
    await deleteRiskModelCache(modelId, redisService.getClient());

    const strategy = await entityManager.findOne(DimensionHitStrategyFieldsEntity, { id: strategyField.id });
    expect(strategy.fieldValue).toEqual(newFieldValue);
  });

  it.skip('debug-查询指标指定策略id的动态内容列表', async () => {
    const param: QueryMonitorDynamicDetialsRequest = {
      // companyId: '917720a16d08bdde171681983391aed0',
      // // companyName: '恒大地产集团有限公司',
      // dimensionKey: DimensionTypeEnums.RiskChange,
      // orgId: 208,
      // strategyId: 136118,
      // diligenceId: 50215176,
      // batchId: 50003076,
      // preBatchId: 0,
      // monitorGroupId: 1522,
      // pageIndex: 1,
      // pageSize: 10,
      // needAggs: 1,
      // ---------- preBatchId 无值
      orgId: 1003263,
      companyId: '4ba8d3610f81fe61f24ead328c600270',
      dimensionKey: DimensionTypeEnums.RelatedCompanies,
      strategyId: 180959,
      diligenceId: 50397455,
      batchId: 50006609,
      preBatchId: 0,
      monitorGroupId: 2910,
      // sort: { field: 'publishTime', order: 'DESC' },
      pageIndex: 1,
      pageSize: 10,
      // needAggs: 1,
      // ---------- preBatchId 有值
      // orgId: 1003263,
      // dimensionKey: DimensionTypeEnums.RelatedCompanies,
      // strategyId: 175387,
      // companyId: '84c17a005a759a5e0d875c1ebb6c9846',
      // diligenceId: 50325826,
      // batchId: 50004761,
      // preBatchId: 50004543,
      // monitorGroupId: 1323,
      // // sort: { field: 'publishTime', order: 'DESC' },
      // pageIndex: 1,
      // pageSize: 10,
      // // needAggs: 1,
    };

    const result = await monitorDynamicService.fatchDimensionHitDetails(param);
    expect(result.data.length).toBeLessThanOrEqual(result.total);

    // const param1: QueryMonitorDynamicDetialsRequest = {
    //   companyId: '917720a16d08bdde171681983391aed0',
    //   batchId: 50002431,
    //   preBatchId: 50002421,
    //   orgId: 208,
    //   diligenceId: 50083629,
    //   dimensionKey: DimensionTypeEnums.RelatedCompanies,
    //   strategyId: 81888,
    //   pageIndex: 1,
    //   pageSize: 100,
    //   monitorGroupId: 1324,
    // };

    // const result2 = await monitorDynamicService.fatchDimensionHitDetails(param1);

    // expect(result2.data.length).toEqual(result2.total);
  });

  it.skip('debug-handleMetricsAnalyze', async () => {
    const preMsgPO: AnalyzeMonitorDynamicMessagePO = {
      orgId: 1003542,
      product: ProductCodeEnums.Pro,
      monitorGroupId: 2858,
      diligenceIds: [51860032],
      batchId: 50039182,
      retryCount: 0,
    };
    // 第一次批量尽调生成占位动态
    const preBatchInsertDynamic = await monitorDynamicService.handleMetricsAnalyze(preMsgPO);
    expect(preBatchInsertDynamic.totalCount).toBeGreaterThan(0);
  });

  it.skip('debug-getRiskList test', async () => {
    const user: PlatformUser = Object.assign(new PlatformUser(), {
      currentOrg: 1003263,
      currentProduct: ProductCodeEnums.Pro,
      userId: 102704,
    });

    // const modelEntity = await icbcSZMonitorService.createMonitorModel(testUser, '自动创建监控模型002', testUser.currentOrg);

    const company = {
      companyId: 'c1fd3ec67b90c9aa12ec73a1a1949d9b',
      companyName: '上海埃迪卡拉科技有限公司',
    };
    // const modelId = 2932;
    // const batchId = 50004164;

    const diligence = await evaluationService.getRiskList(user, {
      companyId: company.companyId,
      companyName: company.companyName,
      orgModelIds: [3567],
    });

    expect(diligence).toBeDefined();
  });

  it.skip('debug-searchDynamics()', async () => {
    const user: PlatformUser = Object.assign(new PlatformUser(), {
      currentOrg: 1003542,
      currentProduct: ProductCodeEnums.Pro,
      userId: 103080,
    });

    const res = await dynamicEsService.searchDynamics(
      Object.assign(new SearchDynamicEsPO(), {
        orgId: user.currentOrg,
        product: user.currentProduct,
        ompanyIds: ['522eaea91e9023281737b0f71b4042c2'],
        groupId: [3006],
        includeRelatedParty: 0,
        pageIndex: 1,
        pageSize: 100, // 默认给到100个
        aggsField: [4],
      }),
    );
    expect(res.data.length).toBeGreaterThan(0);
  });

  it.skip('debug-compareDynamicContents', async () => {
    const batchId = 50009202;
    const preBatchId = 50009137;
    const res = await monitorDynamicService.compareDynamicContents(
      Object.assign(new MonitorMetricDynamicEsDoc(), {
        batchId,
        monitorGroupId: 3129,
        companyId: '44d5992e16ff513c91f86c5b0fdf2227',
        diligenceId: 50451609,
        orgId: 208,
        metricsContent: {
          displayContent: [],
        },
      }),
      [
        Object.assign(new DimensionHitResultPO(), {
          source: 'NebulaGraph',
          status: 2,
          totalHits: 263,
          strategyId: 196189,
          description: '匹配到目标主体 <em class=" - ">【新增的关联方】 257条记录</em>',
          dimensionKey: 'RelatedCompanies',
          strategyName: '新增的关联方',
          strategyRole: 1,
          dimensionName: '关联方企业',
          dimensionFilter: {
            endTime: 1739422323,
            startTime: 1739335923,
          },
        }),
      ],
      preBatchId,
    );
    expect(res.length).toBeGreaterThan(1);
  });
});
