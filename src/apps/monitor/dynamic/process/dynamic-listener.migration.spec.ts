import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Test, TestingModule } from '@nestjs/testing';
import * as Bluebird from 'bluebird';
import * as moment from 'moment';
import { EntityManager, getConnection, getRepository, In, Repository } from 'typeorm';
import { BatchEntity } from '../../../../libs/entities/BatchEntity';
import { MonitorCompanyRelatedDailyEntity } from '../../../../libs/entities/MonitorCompanyRelatedDailyEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorMetricsDynamicEntity } from '../../../../libs/entities/MonitorMetricsDynamicEntity';
import { BatchBusinessTypeEnums } from '../../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DimensionTypeEnums } from '../../../../libs/enums/diligence/DimensionTypeEnums';
import { NebulaRelatedEdgeEnums } from '../../../../libs/enums/dimension/NebulaRelatedEdgeEnums';
import { PlatformUser } from '../../../../libs/model/common';
import { AddMonitorRelatedCompanyRequest } from '../../../../libs/model/monitor/AddMonitorRelatedCompanyRequest';
import { QueryMonitorDynamicDetialsRequest } from '../../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { AppTestModule } from '../../../app/app.test.module';
import { BatchInfoPO } from '../../../batch/model/BatchInfoPO';
import { EvaluationService } from '../../../diligence/evaluation/evaluation.service';
import { DiligenceSnapshotService } from '../../../diligence/snapshot/diligence.snapshot.service';
import { SnapshotQueueTypeEnums } from '../../../diligence/snapshot/po/SnapshotQueueTypeEnums';
import { ModelInitICBCSZMonitorService } from '../../../risk_model/init_mode/model.init.monitor.icbc.sz.service';
import { ModelInitMonitorService } from '../../../risk_model/init_mode/model.init.monitor.service';
import { clearMonitorTestData } from '../../../test_utils_module/monitor.test.tools';
import { generateUniqueTestIds, getTestUser } from '../../../test_utils_module/test.user';
import { MonitorCompanyService } from '../../company/monitor.company.service';
import { MonitorGroupService } from '../../group/monitor.group.service';
import { MonitorModule } from '../../monitor.module';
import { AnalyzeMonitorDynamicMessagePO } from '../../po/AnalyzeMonitorDynamicMessagePO';
import { MonitorDynamicEsService } from '../monitor.dynamic.es.service';
import { MonitorDynamicMessageListener } from '../monitor.dynamic.message.listener';
import { MonitorDynamicProcessModule } from './monitor-dynamic-process.module';
import { MonitorDynamicMessageListenerV2 } from './monitor-dynamic-message-listener-v2';
import { PushEnableEnum } from 'libs/enums/push/PushEnableEnum';
import { createHash } from 'node:crypto';
import { intersection, pick } from 'lodash';
import * as stringify from 'json-stringify-safe';
import { MonitorCompanyRelatedStatusEnum } from '../../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { MonitorCompanyEntity } from '../../../../libs/entities/MonitorCompanyEntity';

// 测试数据
const [testOrgId, testUserId] = generateUniqueTestIds('monitor.dynamic.message.v2.spec.ts');
const testUser = getTestUser(testOrgId, testUserId);

jest.setTimeout(600000);
describe('集成测试-监控分组两次执行监控后对比生成动态', () => {
  process.env.MOCK_MESSAGE_QUEUE_NOT = 'true';
  process.env.STAGE = 'local';
  let monitorCompanyService: MonitorCompanyService;
  let monitorGroupService: MonitorGroupService;
  let monitorDynamicServiceV2: MonitorDynamicMessageListenerV2;
  let monitorDynamicService: MonitorDynamicMessageListener;
  let monitorDynamicRepo: Repository<MonitorMetricsDynamicEntity>;
  let entityManager: EntityManager;
  let modelInitICBCSZMonitorService: ModelInitICBCSZMonitorService;
  let evaluationService: EvaluationService;
  let snapshotService: DiligenceSnapshotService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppTestModule, MonitorModule, MonitorDynamicProcessModule],
    }).compile();
    monitorCompanyService = module.get<MonitorCompanyService>(MonitorCompanyService);
    monitorGroupService = module.get<MonitorGroupService>(MonitorGroupService);
    monitorDynamicService = module.get(MonitorDynamicMessageListener);
    monitorDynamicServiceV2 = module.get(MonitorDynamicMessageListenerV2);
    modelInitICBCSZMonitorService = module.get(ModelInitICBCSZMonitorService);
    monitorDynamicRepo = getRepository(MonitorMetricsDynamicEntity);
    entityManager = monitorDynamicRepo.manager;
    evaluationService = module.get(EvaluationService);
    snapshotService = module.get(DiligenceSnapshotService);

    // await clearMonitorTestData(entityManager, testUser);
  });

  afterAll(async () => {
    const connection = getConnection();
    await connection.close();
  });

  const doMonitor = async (
    user: PlatformUser,
    groupId: number,
    modelId: number,
    companyItems: { companyId: string; companyName: string }[],
    relatedCompanies: AddMonitorRelatedCompanyRequest,
    listener: MonitorDynamicMessageListener | MonitorDynamicMessageListenerV2,
  ) => {
    await monitorCompanyService.addCompanies(user, {
      monitorGroupId: groupId,
      items: companyItems,
    });

    await monitorCompanyService.addRelatedCompany(user, relatedCompanies);
    // mock 消费生成快照消息
    jest.spyOn(snapshotService.snapshotBatchQueue, 'sendMessageV2').mockImplementation((msg, options) => {
      // console.log(msg);
      return snapshotService.processSnapshotMessage(msg, SnapshotQueueTypeEnums.BatchDiligence);
    });

    // 第一次batch查询时间取消
    const preBatchInfo: BatchInfoPO = {
      asyncBatch: 1, // 标记为异步启动批次，需要后续手动启动
      orgModelIds: [modelId],
      cacheHours: 0,
      currentUser: user,
      monitorGroupId: groupId,
      batchStartTime: moment().subtract(3, 'month').unix(), // 设置动态时间区间范围
      batchEndTime: moment().subtract(1, 'month').unix(),
    };

    const preBatch = Object.assign(new BatchEntity(), {
      product: user.currentProduct,
      createDate: new Date(),
      orgId: user.currentOrg,
      createBy: user.userId,
      businessType: BatchBusinessTypeEnums.Diligence_Continuous,
      batchInfo: preBatchInfo,
      recordCount: 0,
      statisticsInfo: {
        totalCount: 0,
        placeholderCount: 0,
        nonRepeatableCount: 0,
      },
    });

    const preBatchEntity = await entityManager.save(preBatch);

    const { data: monitorCompanies } = await monitorCompanyService.searchMonitorCompany(
      {
        groupIds: [groupId],
        pageSize: 20,
      },
      user,
    );

    expect(monitorCompanies.length).toBeGreaterThan(1);

    // 第一次对监控分组内的企业执行尽调

    const relateds = await entityManager.find(MonitorCompanyRelatedPartyEntity, {
      where: { monitorGroupId: groupId, companyIdRelated: In(monitorCompanies.map((i) => i.companyId)) },
    });

    const preDiligenceList = await Bluebird.map(monitorCompanies, async (company) => {
      let isRelated = company.primaryObject === 0;
      if (!isRelated && relateds?.find((r) => r.companyIdRelated === company.companyId)) {
        isRelated = true;
      }

      const diligence = await evaluationService.runMonitorRisk(
        user.currentOrg,
        {
          companyId: company.companyId,
          companyName: company.companyName,
          orgModelIds: [modelId],
          batchId: preBatchEntity.batchId,
          isRelated,
        },
        'SAAS_PRO',
      );
      return diligence;
    });

    const preMsgPO: AnalyzeMonitorDynamicMessagePO = {
      orgId: user.currentOrg,
      product: user.currentProduct,
      monitorGroupId: groupId,
      diligenceIds: preDiligenceList.map((f) => f.id),
      batchId: preBatchEntity.batchId,
      retryCount: 0,
    };
    // 第一次批量尽调生成占位动态
    const preBatchInsertDynamic = await listener.handleMetricsAnalyze(preMsgPO);
    expect(preBatchInsertDynamic.totalCount).toBeGreaterThan(0);
    // 第一次批量尽调的动态全是占位动态
    expect(preBatchInsertDynamic.placeholderCount).toEqual(preBatchInsertDynamic.totalCount);

    const curBatchInfo: BatchInfoPO = {
      ...preBatchInfo,
      batchStartTime: moment().subtract(1, 'month').unix(), // 设置动态时间区间范围
      batchEndTime: moment().unix(),
    };
    const curBatch = Object.assign(new BatchEntity(), {
      product: user.currentProduct,
      createDate: new Date(),
      orgId: user.currentOrg,
      createBy: user.userId,
      businessType: BatchBusinessTypeEnums.Diligence_Continuous,
      batchInfo: curBatchInfo,
      recordCount: 0,
      statisticsInfo: {
        totalCount: 0,
        placeholderCount: 0,
        nonRepeatableCount: 0,
      },
    });

    // 第二次尽调的batch
    const curBatchEntity = await entityManager.save(curBatch);

    const companyIdPrimary = companyItems[0].companyId;

    // 添加关联方日志
    await entityManager.save(MonitorCompanyRelatedDailyEntity, {
      orgId: testUser.currentOrg,
      companyId: companyIdPrimary,
      monitorGroupId: groupId,
      relatedIds: relateds.map((item) => item.companyIdRelated).join(','),
      updateTime: new Date(),
      product: testUser.currentProduct,
    });

    // 第二次对监控分组内的企业执行尽调
    const curDiligenceList = await Bluebird.map(monitorCompanies, async (company) => {
      let isRelated = company.primaryObject === 0;
      if (!isRelated && relateds?.find((r) => r.companyIdRelated === company.companyId)) {
        isRelated = true;
      }
      const diligence = await evaluationService.runMonitorRisk(
        user.currentOrg,
        {
          companyId: company.companyId,
          companyName: company.companyName,
          orgModelIds: [modelId],
          batchId: curBatchEntity.batchId,
          isRelated,
        },
        'SAAS_PRO',
      );
      return diligence;
    });
    const curMesPO: AnalyzeMonitorDynamicMessagePO = {
      ...preMsgPO,
      diligenceIds: curDiligenceList.map((f) => f.id),
      batchId: curBatchEntity.batchId,
    };
    // 第二次批量尽调后生成动态
    const curBatchInsertDynamic = await listener.handleMetricsAnalyze(curMesPO);
    expect(curBatchInsertDynamic.totalCount).toBeGreaterThan(0);

    // 第二次批量尽调后生成动态 没有占位动态
    expect(curBatchInsertDynamic.placeholderCount).toBe(0);

    // 查找关联方变更的动态
    const dynamicRelated = await entityManager.findOne(MonitorMetricsDynamicEntity, {
      monitorGroupId: groupId,
      batchId: curBatchEntity.batchId,
      orgId: user.currentOrg,
      metricsName: '关联方',
    });

    expect(dynamicRelated.metricsContent).toBeDefined();

    const strategies = dynamicRelated.metricsContent.metricScorePO.hitDetails.should;
    const strategyIdadd = strategies.find((s) => s.strategyName == '关联方新增');

    // 查找关联方新增
    expect(strategyIdadd).toBeDefined();
    const param: QueryMonitorDynamicDetialsRequest = {
      orgId: user.currentOrg,
      companyId: dynamicRelated.companyId,
      dimensionKey: DimensionTypeEnums.RelatedCompanyChange,
      strategyId: strategyIdadd.strategyId,
      diligenceId: dynamicRelated.diligenceId,
      batchId: dynamicRelated.batchId,
      preBatchId: preBatchEntity.batchId,
      monitorGroupId: groupId,
      pageIndex: 1,
      pageSize: 20,
    };
    const result = await listener.fatchDimensionHitDetails(param);
    expect(result.data.length).toBeLessThanOrEqual(result.total);

    // 查找关联方减少
    const strategyIdremove = strategies.find((s) => s.strategyName == '关联方失效');
    expect(strategyIdremove).toBeDefined();
    const param2: QueryMonitorDynamicDetialsRequest = {
      orgId: user.currentOrg,
      companyId: dynamicRelated.companyId,
      dimensionKey: DimensionTypeEnums.RelatedCompanyChange,
      strategyId: strategyIdremove.strategyId,
      diligenceId: dynamicRelated.diligenceId,
      batchId: dynamicRelated.batchId,
      preBatchId: preBatchEntity.batchId,
      monitorGroupId: groupId,
      pageIndex: 1,
      pageSize: 20,
    };

    const resultRemove = await listener.fatchDimensionHitDetails(param2);

    expect(resultRemove.data[0].companyKeynoRelated).toEqual('f625a5b661058ba5082ca508f99ffe1b');

    // 关联方失效验证
    const relatedCompanyEntity = await entityManager.findOne(MonitorCompanyRelatedPartyEntity, {
      companyIdRelated: resultRemove.data[0].companyKeynoRelated,
      companyIdPrimary: companyItems[0].companyId,
      monitorGroupId: groupId,
    });

    expect(relatedCompanyEntity.status).toEqual(MonitorCompanyRelatedStatusEnum.Invalid);

    // 主体的关联方变更标签，hashkey验证
    expect(dynamicRelated.uniqueHashkey).toBeDefined();
    const primaryCompany = await entityManager.findOne(MonitorCompanyEntity, { monitorGroupId: groupId, companyId: companyItems[0].companyId });

    expect(dynamicRelated.uniqueHashkey).toBe(primaryCompany.relatedDynamicHashKey);

    // 删除刚才创建的测试batch  preBatchEntity  curBatchEntity
    await entityManager.delete(BatchEntity, [preBatchEntity.batchId, curBatchEntity.batchId]);
  };

  it('mock group & model 通过两次尽调生成快照结果对比成动态, 测试完成清空数据', async () => {
    await clearMonitorTestData(entityManager, testUser);
    // 重新创建监控模型
    // const modelEntity = await modelInitMonitorService.createMonitorModel(testUser, '自动创建监控模型001', testUser.currentOrg);
    const modelEntity = await modelInitICBCSZMonitorService.createMonitorModel(testUser, '自动创建icbcsf监控模型001', testUser.currentOrg);
    try {
      // 初始话创建默认监控分组
      const defaultGroupEntity = await monitorGroupService.initMonitor(testUser);

      const companyId = 'acfb204d6fbe49198a12c8279e7b2d20';
      const companyName = '永赢金融租赁有限公司';
      const relateds = [
        {
          companyId: '4e3587df894bcdea475124d0eeacb230',
          companyName: '上海永赢沪畅翔一号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        {
          companyId: '8ca43ae432dd1ecc64f9f47b2325833e',
          companyName: '上海永赢沪畅翔二号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        { companyId: 'f625a5b661058ba5082ca508f99ffe1b', companyName: '企查查科技股份有限公司', relatedType: NebulaRelatedEdgeEnums.ActualController },
      ];

      // 执行两次监控批次
      await doMonitor(
        testUser,
        defaultGroupEntity.monitorGroupId,
        modelEntity.modelId,
        [
          { companyId, companyName },
          // {
          //   companyId: '9538e01f5e93b435a067efefd797da09',
          //   companyName: '深圳国人通信有限公司',
          // },
        ],
        // 关联方参数
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'acfb204d6fbe49198a12c8279e7b2d20',
          items: relateds,
        },
        monitorDynamicServiceV2,
      );
    } finally {
      // 清理 testUser 监控数据
      await clearMonitorTestData(entityManager, testUser);
    }
  });

  it('migration test', async () => {
    await clearMonitorTestData(entityManager, testUser);
    const modelEntity = await modelInitICBCSZMonitorService.createMonitorModel(testUser, '自动创建icbcsf监控模型001', testUser.currentOrg);
    try {
      // 初始话创建默认监控分组
      const defaultGroupEntity = await monitorGroupService.initMonitor(testUser);
      const newMonitorGroup = await monitorGroupService.addMonitorGroup(testUser, {
        groupName: '自动创建监控分组001',
        monitorModelId: modelEntity.modelId,
        pushEnable: PushEnableEnum.Disable,
        monitorPushRules: [],
        userIds: [],
      });
      const companyId = 'acfb204d6fbe49198a12c8279e7b2d20';
      const companyName = 'z';
      const relateds = [
        {
          companyId: '4e3587df894bcdea475124d0eeacb230',
          companyName: '上海永赢沪畅翔一号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        {
          companyId: '8ca43ae432dd1ecc64f9f47b2325833e',
          companyName: '上海永赢沪畅翔二号飞机租赁有限公司',
          relatedType: NebulaRelatedEdgeEnums.ActualController,
        },
        { companyId: 'f625a5b661058ba5082ca508f99ffe1b', companyName: '企查查科技股份有限公司', relatedType: NebulaRelatedEdgeEnums.ActualController },
      ];

      // 老方法执行一次
      await doMonitor(
        testUser,
        defaultGroupEntity.monitorGroupId,
        modelEntity.modelId,
        [
          { companyId, companyName },
          // {
          //   companyId: '9538e01f5e93b435a067efefd797da09',
          //   companyName: '深圳国人通信有限公司',
          // },
        ],
        // 关联方参数
        {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          companyId: 'acfb204d6fbe49198a12c8279e7b2d20',
          items: relateds,
        },
        monitorDynamicService,
      );

      // 新方法执行一次
      await doMonitor(
        testUser,
        newMonitorGroup.monitorGroupId,
        modelEntity.modelId,
        [
          { companyId, companyName },
          // {
          //   companyId: '9538e01f5e93b435a067efefd797da09',
          //   companyName: '深圳国人通信有限公司',
          // },
        ],
        // 关联方参数
        {
          monitorGroupId: newMonitorGroup.monitorGroupId,
          companyId: 'acfb204d6fbe49198a12c8279e7b2d20',
          items: relateds,
        },
        monitorDynamicServiceV2,
      );

      console.log(`old group: ${defaultGroupEntity.monitorGroupId}`);
      console.log(`new group: ${newMonitorGroup.monitorGroupId}`);
      const dynamicList1 = await entityManager.find(MonitorMetricsDynamicEntity, {
        where: {
          monitorGroupId: defaultGroupEntity.monitorGroupId,
          orgId: testUser.currentOrg,
        },
      });
      const dynamicList2 = await entityManager.find(MonitorMetricsDynamicEntity, {
        where: {
          monitorGroupId: newMonitorGroup.monitorGroupId,
          orgId: testUser.currentOrg,
        },
      });

      // const key1 = dynamicList1.map((item) => createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}`).digest('hex'));
      // const key2 = dynamicList2.map((item) => createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}`).digest('hex'));

      const r1 = dynamicList1.map((item) => ({
        ...pick(item, ['companyId', 'metricsId', 'riskModelBranchCode', 'companyName', 'metricsName']),
        key: createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}-${item.status}`).digest('hex'),
      }));
      const r2 = dynamicList2.map((item) => ({
        ...pick(item, ['companyId', 'metricsId', 'riskModelBranchCode', 'companyName', 'metricsName']),
        key: createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}-${item.status}`).digest('hex'),
      }));
      const key1 = r1.map((item) => item.key);
      const key2 = r2.map((item) => item.key);

      // console.log(r1);
      // console.log(r2);
      expect(key1.length).toEqual(key2.length);
      const intersectionKey = intersection(key1, key2);
      // console.log(`intersectionKey: ${intersectionKey}`);
      expect(intersectionKey.length).toEqual(key1.length);

      // 比较两个 分组产生的动态是否完全一致
    } finally {
      // 清理 testUser 监控数据
      //await clearMonitorTestData(entityManager, testUser);
    }
  });

  it('migration test 2', async () => {
    const dynamicList1 = await entityManager.find(MonitorMetricsDynamicEntity, {
      where: {
        monitorGroupId: 3643,
        orgId: testUser.currentOrg,
      },
    });
    const dynamicList2 = await entityManager.find(MonitorMetricsDynamicEntity, {
      where: {
        monitorGroupId: 3644,
        orgId: testUser.currentOrg,
      },
    });

    const key1 = dynamicList1.map((item) =>
      createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}_${item.status}`).digest('hex'),
    );
    const key2 = dynamicList2.map((item) =>
      createHash('md5').update(`${item.companyId}_${item.metricsId}-${item.riskModelBranchCode}_${item.status}`).digest('hex'),
    );

    expect(key1.length).toEqual(key2.length);
    expect(intersection(key1, key2).length).toEqual(key1.length);
  });
});
