import { Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { In } from 'typeorm';
import { QueueService } from '../../../../libs/config/queue.service';
import { AnalyzeMonitorDynamicMessagePO } from '../../po/AnalyzeMonitorDynamicMessagePO';
import { MonitorCompanyEntity } from '../../../../libs/entities/MonitorCompanyEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MonitorDynamicDiligenceService } from './services/monitor-dynamic-diligence.service';
import { MonitorDynamicGenerationService } from './services/monitor-dynamic-generation.service';
import { MonitorDynamicComparisonService } from './services/monitor-dynamic-comparison.service';
import { MonitorDynamicPersistenceService } from './services/monitor-dynamic-persistence.service';
import { MonitorDynamicDetailsService } from './services/monitor-dynamic-details.service';
import { MonitorMetricsDynamicEntity } from '../../../../libs/entities/MonitorMetricsDynamicEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { QueryMonitorDynamicDetialsRequest } from '../../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { MonitorDynamicRelatedService } from './services/monitor-dynamic-related.service';

/**
 * 监控动态消息监听器
 * 重构版本，使用领域服务分离和依赖注入
 */
@Injectable()
export class MonitorDynamicMessageListenerV2 {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicMessageListenerV2.name);
  private readonly continuousDiligenceAnalyzeQueue: RabbitMQ;

  constructor(
    private readonly queueService: QueueService,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    private readonly diligenceService: MonitorDynamicDiligenceService,
    private readonly generationService: MonitorDynamicGenerationService,
    private readonly comparisonService: MonitorDynamicComparisonService,
    private readonly persistenceService: MonitorDynamicPersistenceService,
    private readonly relatedService: MonitorDynamicRelatedService,
    private readonly detailsService: MonitorDynamicDetailsService,
  ) {
    // 初始化消息队列
    this.continuousDiligenceAnalyzeQueue = this.queueService.continuousDiligenceAnalyzeQueue;
    this.continuousDiligenceAnalyzeQueue.consume(this.handleMetricsAnalyze.bind(this));
  }

  /**
   * 保存指标动态
   * 根据指标的动态生成规则，生成动态以及动态内容，保存到数据库和es
   * @param msgPO 消息对象
   */
  public async handleMetricsAnalyze(msgPO: AnalyzeMonitorDynamicMessagePO): Promise<{
    placeholderCount: number;
    repeatableCount: number;
    nonRepeatableCount: number;
    totalCount: number;
  }> {
    const { orgId, product, monitorGroupId, diligenceIds, batchId, retryCount } = msgPO;
    this.logger.info(`handleMetricsAnalyze msgPO: ${JSON.stringify(msgPO)}`);

    // 1. 查找和分类尽调记录
    const diligenceEntities = await this.diligenceService.findDiligence(diligenceIds);
    if (!diligenceEntities.length) {
      this.logger.warn('handleMetricsAnalyze(), there is no diligence to analyze metrics dynamic');
      return {
        placeholderCount: 0,
        repeatableCount: 0,
        nonRepeatableCount: 0,
        totalCount: 0,
      };
    }

    const { finshedDiligence, unFinshedDiligence } = this.diligenceService.classifyDiligence(diligenceEntities, batchId);

    // 2. 处理未完成的尽调记录
    await this.diligenceService.retryUnfinishedDiligence(unFinshedDiligence, msgPO, retryCount);

    // 3. 如果尽调快照都未完成，直接返回0
    if (finshedDiligence.length < 1) {
      return {
        placeholderCount: 0,
        repeatableCount: 0,
        nonRepeatableCount: 0,
        totalCount: 0,
      };
    }

    // 4. 获取基础数据
    const companyIds = finshedDiligence.map((d) => d.companyId);
    const [initDynamics, primaryCompanies] = await Promise.all([
      this.findInitDynamics(orgId, monitorGroupId, companyIds, finshedDiligence[0].orgModelId),
      this.findRelatedParties(monitorGroupId, companyIds),
    ]);

    // 5. 生成占位动态
    const placeholderDynamics = this.generationService.generatePlaceholderDynamics(
      finshedDiligence,
      initDynamics,
      primaryCompanies,
      monitorGroupId,
      batchId,
      orgId,
      product,
    );

    // 6. 分类指标动态
    const { repeatableDynamics, nonRepeatableDynamics } = await this.generationService.classifyMetricDynamics(
      finshedDiligence,
      initDynamics,
      primaryCompanies,
      monitorGroupId,
      batchId,
      orgId,
      product,
      placeholderDynamics,
    );

    // 7. 重置监控主体表上关联的最新关联方变更动态hashKey为空
    await this.relatedService.setRelatedDynamicHashKey(companyIds, monitorGroupId, '');

    // 8. 保存占位动态
    let placeholderCount = 0;
    if (placeholderDynamics.length) {
      placeholderCount = await this.persistenceService.saveDynamic(placeholderDynamics);
      if (placeholderCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), placeholderCount: ${placeholderCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no placeholder dynamic inserted`);
      }
    }

    // 9. 保存可重复动态
    let repeatableCount = 0;
    if (repeatableDynamics.length) {
      repeatableCount = await this.persistenceService.saveDynamic(repeatableDynamics);
      if (repeatableCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), repeatableCount: ${repeatableCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no repeatable dynamic inserted`);
      }
    }

    // 10. 处理和保存不可重复动态
    let nonRepeatableCount = 0;
    if (nonRepeatableDynamics.length) {
      // 需要对比两次快照内容是否有变化，计算出需要插入的动态
      const processedDynamics = await this.comparisonService.processNonRepeatableDynamics(nonRepeatableDynamics);
      nonRepeatableCount = await this.handleNonRepeatableDynamics(processedDynamics);

      if (nonRepeatableCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), nonRepeatableCount: ${nonRepeatableCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no nonRepeatable dynamic inserted`);
      }
    }

    // 11. 更新监控企业状态
    const inserted = repeatableCount + nonRepeatableCount;
    await this.persistenceService.updateMonitorCompanyStatus(finshedDiligence, monitorGroupId, batchId, inserted);

    return {
      placeholderCount,
      repeatableCount,
      nonRepeatableCount,
      totalCount: placeholderCount + repeatableCount + nonRepeatableCount,
    };
  }

  /**
   * 查找初始化动态
   * @param orgId 组织ID
   * @param monitorGroupId 监控分组ID
   * @param companyIds 公司ID列表
   * @param riskModelId 风险模型ID
   * @private
   */
  private async findInitDynamics(orgId: number, monitorGroupId: number, companyIds: string[], riskModelId: number) {
    // 找出来已经初始化跑过的企业的指标动态 status=-1 or -2,这些数据用作对比的基线
    return this.dynamicRepo.find({
      where: {
        orgId,
        monitorGroupId,
        status: In([-1, -2]),
        companyId: In(companyIds),
        riskModelId, // 同一个批次里面的尽调，用的riskModelId肯定一样
      },
    });
  }

  /**
   * 查找指定公司是谁的关联方
   * @param monitorGroupId 监控分组ID
   * @param companyIds 公司ID列表
   * @private
   */
  private async findRelatedParties(monitorGroupId: number, companyIds: string[]) {
    return this.relatedCompanyRepo.find({
      where: {
        monitorGroupId,
        companyIdRelated: In(companyIds),
      },
    });
  }

  /**
   * 处理不可重复动态
   * @param processedDynamics 处理后的动态列表
   * @private
   */
  private async handleNonRepeatableDynamics(processedDynamics: any[]): Promise<number> {
    if (!processedDynamics.length) {
      return 0;
    }
    return this.persistenceService.saveDynamic(processedDynamics);
  }

  /**
   * 获取维度命中详情
   * 委托给详情服务处理
   * @param param 请求参数
   */
  public async fatchDimensionHitDetails(param: QueryMonitorDynamicDetialsRequest) {
    return this.detailsService.fatchDimensionHitDetails(param);
  }

  /**
   * 过滤未监控的公司
   * 委托给详情服务处理
   * @param request 查询请求
   */
  public async filterUnMonitorCompany(request: QueryMonitorDynamicDetialsRequest) {
    return this.detailsService.filterUnMonitorCompany(request);
  }
}
