请帮我执行代码同步任务，具体如下：

1. 分析 src/apps/monitor/dynamic/monitor.dynamic.message.listener.ts 文件中的最新优化调整
2. 检查这些调整是否已经同步到 src/apps/monitor/dynamic/process/ 目录下的新架构中
3. 具体关注以下几个方面：

   - fatchDimensionHitDetails 方法的实现变化
   - filterUnMonitorCompany 方法的实现变化
   - 处理特殊维度类型的逻辑（如 RiskChange、RelatedCompanyChange、TaxpayerCertificationChange）
   - 动态内容比对和生成的逻辑优化
   - 新增的辅助方法或工具函数

4. 针对未同步的变更，请提供具体的代码实现建议，包括：

   - 应该更新哪些文件
   - 需要添加/修改的代码片段
   - 如何保持与新架构设计模式的一致性

5. 如有必要，请解释这些变更如何与 dynamic_workflow_v2.md 中描述的新架构设计原则保持一致

请确保所有同步的代码都符合 TypeScript 类型系统要求，并解决可能出现的类型错误。

## 注意事项

1. 不要修改任何业务逻辑，确保功能的完整性和正确性
2. 使用 TypeScript 类型，避免使用 any 类型
3. 代码风格要统一，与现有代码保持一致
4. 所有新增文件放在`src/apps/monitor/dynamic/process/`目录下
5. 遵循 SOLID 原则，特别是单一职责原则和依赖倒置原则
6. 阅读 dynamic_workflow.md 文件，了解当前的动态生成流程
7. 阅读 dynamic_optimization_plan.md 文件，了解当前的优化方案的整体步骤
