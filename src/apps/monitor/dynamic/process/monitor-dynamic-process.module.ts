import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonitorMetricsDynamicEntity } from '../../../../libs/entities/MonitorMetricsDynamicEntity';
import { DiligenceHistoryEntity } from '../../../../libs/entities/DiligenceHistoryEntity';
import { MonitorCompanyEntity } from '../../../../libs/entities/MonitorCompanyEntity';
import { MonitorCompanyRelatedPartyEntity } from '../../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorDynamicDiligenceService } from './services/monitor-dynamic-diligence.service';
import { MonitorDynamicGenerationService } from './services/monitor-dynamic-generation.service';
import { MonitorDynamicComparisonService } from './services/monitor-dynamic-comparison.service';
import { MonitorDynamicPersistenceService } from './services/monitor-dynamic-persistence.service';
import { MonitorDynamicMessageListenerV2 } from './monitor-dynamic-message-listener-v2';
import { RegularMetricStrategy } from './strategies/regular-metric.strategy';
// import { RelatedCompanyMetricStrategy } from './strategies/related-company-metric.strategy';
import { TaxpayerMetricStrategy } from './strategies/taxpayer-metric.strategy';
import { MetricStrategyFactory } from './strategies/metric-strategy.factory';
import { MonitorGroupEntity } from '../../../../libs/entities/MonitorGroupEntity';
import { MonitorDynamicDetailsService } from './services/monitor-dynamic-details.service';
import { DiligenceModule } from '../../../diligence/diligence.module';
import { MetricModule } from '../../../metric/metric.module';
import { MonitorDynamicEsService } from '../monitor.dynamic.es.service';
import { MonitorDynamicRelatedService } from './services/monitor-dynamic-related.service';

/**
 * 监控动态处理模块
 * 提供监控动态生成相关服务
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([MonitorCompanyEntity, MonitorMetricsDynamicEntity, MonitorGroupEntity, MonitorCompanyRelatedPartyEntity, DiligenceHistoryEntity]),
    DiligenceModule, // 导入DiligenceModule以获取DiligenceSnapshotEsService和DiligenceSnapshotEsCompareService
    MetricModule, // 导入MetricModule以获取MetricService
  ],
  providers: [
    // 领域服务
    MonitorDynamicDiligenceService,
    MonitorDynamicGenerationService,
    MonitorDynamicComparisonService,
    MonitorDynamicPersistenceService,
    MonitorDynamicDetailsService,
    MonitorDynamicRelatedService,
    // 策略类
    MetricStrategyFactory,
    RegularMetricStrategy,
    // RelatedCompanyMetricStrategy,
    TaxpayerMetricStrategy,
    // 监听器
    MonitorDynamicMessageListenerV2,
    // monitor dynamic 相关的provider
    MonitorDynamicEsService,
  ],
  exports: [MonitorDynamicDetailsService],
})
export class MonitorDynamicProcessModule {}
