import { Injectable } from '@nestjs/common';
import { CompanyEntity } from 'libs/entities/CompanyEntity';
import { Repository } from 'typeorm/repository/Repository';
import { InjectRepository } from '@nestjs/typeorm';
import {
  DynamicChartXmaindAggsResponse,
  DynamicChartXmindAggsRequest,
  HighRiskChartAggsResponse,
  MetricDynamicSummaryResponse,
  SearchMetricDynamicRequest,
  SearchMetricsDynamicResponse,
} from '../../../libs/model/monitor';
import { PlatformUser } from '../../../libs/model/common';
import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
import { In } from 'typeorm';
import { TodayHighRiskDynamicsAggsRequest } from '../../../libs/model/monitor/TodayHighRiskDynamicsAggsRequest';
import { SearchDynamicEsPO } from '../../../libs/model/monitor/SearchDynamicEsPO';
import { MonitorDynamicEsService } from './monitor.dynamic.es.service';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { flattenDeep, groupBy } from 'lodash';
import { MetricsEntity } from '../../../libs/entities/MetricsEntity';
import * as Bluebird from 'bluebird';
import { Cacheable } from 'type-cacheable';
import { QueryRelatedHashKeyRequest } from '../../../libs/model/monitor/QueryRelatedHashKeyRequest';
import { MonitorCompanyStatusEnums } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { RelatedCompanyChartRequest } from '../../../libs/model/monitor/RelatedCompanyChartRequest';
import {
  RelatedCompanyChartItemPO,
  RelatedCompanyChartResponse,
  RelatedCompanyItemPO,
  RelatedCompanyMetricItemPO,
} from '../../../libs/model/monitor/RelatedCompanyChartResponse';
import { relatedTypeAnnotations } from '../../../libs/enums/dimension/RelatedTypeEnums';
import { ESResponse } from '@kezhaozhao/search-utils';
import { KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { CompanySearchService } from '../../company/company-search.service';
import { MonitorGroupService } from '../group/monitor.group.service';

@Injectable()
export class MonitorDynamicService {
  constructor(
    @InjectRepository(CompanyEntity) private readonly companyRepo: Repository<CompanyEntity>,
    @InjectRepository(MetricsEntity) private readonly metricsRepo: Repository<MetricsEntity>,
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly monitorMetricsDynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    private readonly dynamicEsService: MonitorDynamicEsService,
    private readonly companySearchService: CompanySearchService,
    private readonly monitorGroupService: MonitorGroupService,
  ) {}

  // /**
  //  * @deprecated
  //  * @param postData
  //  * @param currentUser
  //  * @param notUsePage
  //  */
  // async searchMetricsDynamic(postData: SearchMetricDynamicRequest, currentUser: PlatformUser, notUsePage = false): Promise<SearchMetricsDynamicResponse> {
  //   const res: SearchMetricsDynamicResponse = new SearchMetricsDynamicResponse();
  //   res.pageSize = postData.pageSize || 10;
  //   res.pageIndex = postData.pageIndex || 1;
  //   const { currentProduct: product, currentOrg: orgId } = currentUser;
  //   postData.dataStatus = postData.dataStatus || [MetricDynamicStatusEnums.Unhandled];
  //   const { groupId, riskLevels, metricsIds, companyIds, sortField, isSortAsc, dataStatus } = postData;

  //   const qb = this.monitorMetricsDynamicRepo
  //     .createQueryBuilder('monitorMetricsDynamic')
  //     .where('monitorMetricsDynamic.orgId = :orgId', { orgId })
  //     .andWhere('monitorMetricsDynamic.product = :product', { product });
  //   // const query: any = {
  //   //   orgId: currentOrg,
  //   //   product: currentProduct,
  //   // };
  //   if (groupId) {
  //     qb.andWhere('monitorMetricsDynamic.monitorGroupId = :groupId', { groupId });
  //   }
  //   if (riskLevels) {
  //     qb.andWhere('monitorMetricsDynamic.riskLevel in (:...riskLevels)', { riskLevels });
  //   }

  //   if (metricsIds) {
  //     qb.andWhere('monitorMetricsDynamic.metricsId in (:...metricsIds)', { metricsIds });
  //   }

  //   if (companyIds) {
  //     qb.andWhere('monitorMetricsDynamic.companyId IN (:...companyIds)', { companyIds });
  //   }
  //   if (dataStatus) {
  //     qb.andWhere('monitorMetricsDynamic.status in (:...dataStatus)', { dataStatus });
  //   }

  //   // 创建时间
  //   QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');

  //   if (sortField) {
  //     qb.orderBy(`monitorMetricsDynamic.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
  //   } else {
  //     qb.orderBy('monitorMetricsDynamic.createDate', 'DESC');
  //   }

  //   if (!notUsePage) {
  //     qb.skip(res.pageSize * (res.pageIndex - 1)).take(res.pageSize);
  //   }
  //   //todo 监管类以及业务类动态 需要通过metricsId反查metrics表 会增加一个标签字段 根据标签字段判断是否为监管或者业务类
  //   const [items, total] = await qb.getManyAndCount();

  //   if (items.length === 0) {
  //     return res;
  //   }

  //   const companyIds1 = items.map((item) => item.companyId);
  //   const companyMap = await this.companyRepo.find({
  //     where: { companyId: In(companyIds1) },
  //   });
  //   res.data = items.map((item) => {
  //     const company = companyMap.find((c) => c.companyId === item.companyId);
  //     item['companyName'] = company?.name || '';
  //     return item;
  //   });
  //   res.total = total;
  //   return res;
  // }

  /**
   * 监控动态-列表
   * @param postData
   * @param currentUser
   */

  public async searchDynamics(postData: SearchMetricDynamicRequest, currentUser: PlatformUser) {
    // 可见分组权限验证
    const { groupId, favoriteGroupId } = postData;
    let res = new SearchMetricsDynamicResponse();
    const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(currentUser);
    if (!authGroupIds?.length) {
      return res;
    }
    postData.authGroupIds = authGroupIds;
    res = await this.dynamicEsService.searchDynamics(
      Object.assign(new SearchDynamicEsPO(), postData, {
        product: currentUser.currentProduct,
        orgId: currentUser.currentOrg,
        userId: currentUser.userId,
      }),
    );
    return res;
  }

  /**
   * 监控动态-关联方图表
   * @param postData
   * @param currentUser
   */
  public async searchRelatedChartDynamics(postData: RelatedCompanyChartRequest, currentUser: PlatformUser): Promise<RelatedCompanyChartResponse> {
    const relatedCompanyChartResponse = new RelatedCompanyChartResponse();
    const response = await this.dynamicEsService.searchDynamics(
      Object.assign(new SearchDynamicEsPO(), postData, {
        product: currentUser.currentProduct,
        orgId: currentUser.currentOrg,
        aggsField: [5],
        pageSize: 1,
        pageIndex: 1,
      }),
    );
    const aggsResponse = response.aggsResponse || {};
    const { relatedType } = aggsResponse['5_relatedCompany'] || {};
    const relatedTypeList = relatedType?.buckets || [];
    if (!relatedTypeList?.length) {
      return relatedCompanyChartResponse;
    }
    const relatedCompanyChartItems: RelatedCompanyChartItemPO[] = [];
    relatedTypeList.forEach((item) => {
      const relatedCompanys: RelatedCompanyItemPO[] = [];
      const companyInfos = (item?.reverseToRoot?.companyId?.buckets as any[]) || [];
      if (companyInfos?.length) {
        companyInfos.forEach((companyInfo) => {
          const companyMetrics: RelatedCompanyMetricItemPO[] = [];
          const metricInfos = (companyInfo?.metricsId?.buckets as any[]) || [];
          if (metricInfos?.length) {
            metricInfos.forEach((metricInfo) => {
              let maxRiskLevel = null;
              const riskLevels = (metricInfo.riskLevel?.buckets as any[]) || [];
              if (riskLevels?.length) {
                const riskLevelKeys = riskLevels.map((r) => Number(r.key));
                // 取最大
                maxRiskLevel = Math.max(...riskLevelKeys);
              }
              companyMetrics.push(
                Object.assign(new RelatedCompanyMetricItemPO(), {
                  metricsId: metricInfo.key,
                  metricsName: metricInfo.metricsName?.buckets[0]?.key,
                  metricsCount: metricInfo.metricsName?.buckets[0]?.doc_count || 0,
                  riskLevel: maxRiskLevel,
                }),
              );
            });
          }
          relatedCompanys.push(
            Object.assign(new RelatedCompanyItemPO(), {
              companyId: companyInfo.key,
              companyMetrics,
            }),
          );
        });
      }
      relatedCompanyChartItems.push(
        Object.assign(new RelatedCompanyChartItemPO(), {
          relatedType: item.key,
          relatedName: relatedTypeAnnotations[item.key],
          relatedCompanys,
        }),
      );
    });
    Object.assign(relatedCompanyChartResponse, {
      relatedCompanyChartItems,
    });
    // 补充企业名称
    const companyIds = relatedCompanyChartItems.flatMap((item) => item.relatedCompanys.map((company) => company.companyId));
    const companyDetaiRes: ESResponse<KysCompanyResponseDetails> = await this.companySearchService.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        pageIndex: 1,
        pageSize: companyIds.length,
        includeFields: ['id', 'name', 'statusCode'],
        filter: { ids: companyIds },
      }),
    );
    relatedCompanyChartResponse.relatedCompanyChartItems.forEach((item) => {
      item.relatedCompanys.forEach((company) => {
        const kysCompany = companyDetaiRes?.Result?.find((t) => t.id === company.companyId);
        if (kysCompany) {
          company.companyName = kysCompany.name || '-';
        }
      });
    });
    return relatedCompanyChartResponse;
  }

  /**
   * 监控动态-小卡片分析（废弃）
   * @param requestData
   * @param user
   */
  public async dynamicCardAggs(requestData: SearchMetricDynamicRequest, currentUser: PlatformUser) {
    //TODO 获取当前用户所能访问的监控分组ID
    //requestData.groupId = []
    // 可见分组权限验证
    const { groupId, favoriteGroupId } = requestData;
    const res = new SearchMetricsDynamicResponse();
    const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(currentUser);
    if (!authGroupIds?.length) {
      return res;
    }
    requestData.authGroupIds = authGroupIds;
    // 动态监控分析-头部小卡片聚合
    const [dynamicSearchResponse] = await Bluebird.all([
      this.dynamicEsService.searchDynamics(
        Object.assign(new SearchDynamicEsPO(), requestData, {
          product: currentUser.currentProduct,
          orgId: currentUser.currentOrg,
          aggsField: requestData.aggsField || [1, 2, 3],
          pageSize: 0,
        }),
      ),
    ]);
    const aggsResponse = dynamicSearchResponse.aggsResponse || {};
    if (requestData.aggsField?.includes(2)) {
      // 可见分组权限验证
      const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(currentUser);
      if (!authGroupIds?.length) {
        aggsResponse['2_monitorCompanyCardTotal'] = 0;
        return aggsResponse;
      }

      const dbQuery = {
        orgId: currentUser.currentOrg,
        product: currentUser.currentProduct,
        status: MonitorCompanyStatusEnums.Done,
        monitorGroupId: In(authGroupIds),
      };

      aggsResponse['2_monitorCompanyCardTotal'] = await this.monitorCompanyRepo.count({
        where: dbQuery,
      });
    }

    return aggsResponse;
  }

  /**
   * 今日高风险动态分析- 指标交叉
   * @param requestData
   * @param user
   */
  public async dynamicChartAnalyzeForHighRisk(requestData: TodayHighRiskDynamicsAggsRequest, user: PlatformUser) {
    const res = new HighRiskChartAggsResponse();
    const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(user);
    if (!authGroupIds?.length) {
      return res;
    }
    requestData.filter.authGroupIds = authGroupIds;
    return this.dynamicEsService.dynamicChartAnalyzeForHighRisk(requestData, user);
  }

  public async dynamicChartAnalyzeForXmind(requestData: DynamicChartXmindAggsRequest, user: PlatformUser): Promise<DynamicChartXmaindAggsResponse> {
    const res = await this.dynamicEsService.dynamicChartAnalyzeForXmind(requestData, user);
    const metricIds = flattenDeep(res.relatedTypeAggs.map((r) => r.metricIdAggs.map((m) => m.metricsId)));
    const metricEntities = await this.findMetrics(metricIds);
    res.relatedTypeAggs.forEach((r) => {
      r.metricIdAggs.forEach((m) => {
        m.metricsName = metricEntities.find((e) => e.metricsId === m.metricsId)?.name;
      });
    });
    return res;
  }

  @Cacheable({ ttlSeconds: 60 })
  private async findMetrics(metricsIds: number[]) {
    const metricsEntities = await this.metricsRepo.find({
      where: {
        metricsId: In(metricsIds),
      },
      select: ['metricsId', 'name'],
    });
    return metricsEntities;
  }

  public async analyzeSummary(filter: SearchMetricDynamicRequest, currentUser: PlatformUser) {
    const res = new MetricDynamicSummaryResponse();
    const authGroupIds = await this.monitorGroupService.getUserMonitorGroupList(currentUser);
    if (!authGroupIds?.length) {
      return res;
    }
    filter.authGroupIds = authGroupIds;
    const response = await this.dynamicEsService.analyzeSummary(filter, currentUser);
    response.metricEntities = await this.findMetrics(response.metricsIds);
    return response;
  }

  // async queryCompanyDynamic(currentUser: PlatformUser, postData: QueryMonitorCompanyDynamicRequest, notUsePage = false) {
  //   const res: QueryMonitorCompanyDynamicResponse = new QueryMonitorCompanyDynamicResponse();
  //   res.pageSize = postData.pageSize || 10;
  //   res.pageIndex = postData.pageIndex || 1;
  //   const { currentProduct: product, currentOrg: orgId } = currentUser;
  //   const { groupId, riskLevels, metricsIds, companyId, sortField, isSortAsc } = postData;
  //
  //   const qb = this.monitorMetricsDynamicRepo
  //     .createQueryBuilder('monitorMetricsDynamic')
  //     .where('monitorMetricsDynamic.orgId = :orgId', { orgId })
  //     .andWhere('monitorMetricsDynamic.product = :product', { product })
  //     .andWhere('monitorMetricsDynamic.status in (:...status)', { status: [0, 1] })
  //     .andWhere('monitorMetricsDynamic.companyId = :companyId', { companyId })
  //     .andWhere('monitorMetricsDynamic.monitorGroupId = :groupId', { groupId });
  //
  //   if (riskLevels) {
  //     qb.andWhere('monitorMetricsDynamic.riskLevel in (:...riskLevels)', { riskLevels });
  //   }
  //
  //   if (metricsIds) {
  //     qb.andWhere('monitorMetricsDynamic.metricsId in (:...metricsIds)', { metricsIds });
  //   }
  //
  //   if (sortField) {
  //     qb.orderBy(`monitorMetricsDynamic.${sortField}`, isSortAsc ? 'ASC' : 'DESC');
  //   } else {
  //     qb.orderBy('monitorMetricsDynamic.createDate', 'DESC');
  //   }
  //
  //   if (!notUsePage) {
  //     qb.skip(res.pageSize * (res.pageIndex - 1)).take(res.pageSize);
  //   }
  //
  //   // 创建时间
  //   QueryBuilderHelper.applyDateRangeQuery(qb, postData?.createDate, 'createDate');
  //
  //   const [items, total] = await qb.getManyAndCount();
  //
  //   if (items.length === 0) {
  //     return res;
  //   }
  //
  //   res.data = items;
  //   res.total = total;
  //   return res;
  // }

  // async queryCompanyDynamicAgg(currentUser: PlatformUser, postData: QueryMonitorCompanyDynamicRequest) {
  //   const res = await this.queryCompanyDynamic(currentUser, postData, true);
  //
  //   const data = res.data;
  //   if (!data) {
  //     return data;
  //   }
  //
  //   const companyEntities = await this.monitorCompanyRepo.find({
  //     where: {
  //       companyId: postData.companyId,
  //       orgId: currentUser.currentOrg,
  //       product: currentUser.currentProduct,
  //     },
  //     relations: ['monitorGroupEntity'],
  //   });
  //
  //   const groupList: MCDynamicAggGroupItem[] = [];
  //   companyEntities.forEach((x) => {
  //     const groupItem = new MCDynamicAggGroupItem();
  //     groupItem.groupId = x.monitorGroupEntity.monitorGroupId;
  //     groupItem.groupName = x.monitorGroupEntity.name;
  //     groupList.push(groupItem);
  //   });
  //   const riskLevelMap = groupBy(data, (item) => item.riskLevel);
  //   const metricsIdMap = groupBy(data, (item) => item.metricsId);
  //   const riskLevelList: MCDynamicAggRiskLevelItem[] = [];
  //   const metricsList: MCDynamicAggMetricsItem[] = [];
  //   Object.keys(riskLevelMap).forEach((key) => {
  //     const riskLevelItem = new MCDynamicAggRiskLevelItem();
  //     riskLevelItem.count = riskLevelMap[key].length;
  //     riskLevelItem.riskLevel = parseInt(key);
  //     riskLevelList.push(riskLevelItem);
  //   });
  //   Object.keys(metricsIdMap).forEach((key) => {
  //     const metricsItem = new MCDynamicAggMetricsItem();
  //     metricsItem.count = metricsIdMap[key].length;
  //     metricsItem.metricsId = parseInt(key);
  //     metricsItem.metricsName = metricsIdMap[key][0].metricsName;
  //     metricsList.push(metricsItem);
  //   });
  //   const resp = new QueryMonitorCompanyDynamicAggResponse();
  //   resp.metricsList = metricsList;
  //   resp.riskLevelList = riskLevelList;
  //   resp.groupList = groupList;
  //   return resp;
  // }

  async queryRelatedHashKey(currentUser: PlatformUser, postData: QueryRelatedHashKeyRequest) {
    const monitorMetricsDynamicEntities = await this.monitorMetricsDynamicRepo.find({
      where: {
        orgId: currentUser.currentOrg,
        uniqueHashkey: In(postData.hashKeys),
        product: currentUser.currentProduct,
      },
      order: {
        createDate: 'DESC',
      },
    });
    return groupBy(monitorMetricsDynamicEntities, (item) => item.uniqueHashkey);
  }
}
