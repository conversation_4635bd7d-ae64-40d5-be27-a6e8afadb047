import { Injectable } from '@nestjs/common';
import { QueueService } from '../../../libs/config/queue.service';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { findRelatedStreategyIds, getMetricsDynamicSnapshotId } from '../monitor.utils';
import { chunk, find, flattenDeep, toPlainObject } from 'lodash';
import { MetricScorePO } from '../../../libs/model/metric/MetricScorePO';
import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
import { In, MoreThan, Not, Repository } from 'typeorm';
import { AnalyzeMonitorDynamicMessagePO } from '../po/AnalyzeMonitorDynamicMessagePO';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import * as Bluebird from 'bluebird';
import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import * as moment from 'moment/moment';
import { MonitorDynamicEsService } from './monitor.dynamic.es.service';
import { DiligenceSnapshotEsCompareService } from '../../diligence/snapshot/diligence.snapshot.es.compare.service';
import { SnapshotChangesTypeEnum } from '../../diligence/snapshot/po/SearchDimensionDiffsFromSnapshotRequest';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { MonitorCompanyRelatedStatusEnum } from '../../../libs/enums/monitor/MonitorCompanyStatusEnums';
import { DynamicDisplayContent } from '../../../libs/model/metric/MetricDynamicContentPO';
import { CompanyRelatedPartyEsDoc, MonitorMetricDynamicEsDoc } from '../../../libs/model/monitor/MonitorMetricDynamicEsDoc';
import { v4 } from 'uuid';
import { DiligenceSnapshotEsService } from '../../diligence/snapshot/diligence.snapshot.es.service';
import { PaginationResponse } from '@kezhaozhao/qcc-model';
import { MonitorCompanyRelatedPartyEntity } from '../../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MetricService } from '../../metric/metric.service';
import { QueryMonitorDynamicDetialsRequest } from '../../../libs/model/monitor/QueryMonitorDynamicDetialsRequest';
import { SnapshotStatus } from '../../../libs/model/diligence/SnapshotDetail';
import { MetricHitDetailsPO } from '../../../libs/model/metric/MetricHitDetailsPO';
import { Cacheable } from 'type-cacheable';
import { RelatedPartyGroupPO } from '../../../libs/model/diligence/graph/RelatedPartyGroupPO';

// @Injectable()
export class MonitorDynamicMessageListener {
  private readonly logger: Logger = QccLogger.getLogger(MonitorDynamicMessageListener.name);
  private readonly continuousDiligenceAnalyzeQueue: RabbitMQ;

  constructor(
    private readonly queueService: QueueService,
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly dynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    @InjectRepository(MonitorCompanyRelatedPartyEntity) private readonly relatedCompanyRepo: Repository<MonitorCompanyRelatedPartyEntity>,
    private readonly dynamicEsService: MonitorDynamicEsService,
    private readonly snapshotEsCompareService: DiligenceSnapshotEsCompareService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    private readonly metricService: MetricService,
  ) {
    // Pulsar
    // this.continuousDiligenceAnalyzeQueue = this.queueService.continuousDiligenceAnalyzeQueue;
    // this.continuousDiligenceAnalyzeQueue.consume(this.handleMetricsAnalyze.bind(this));
  }

  /**
   * 指标指定策略id的动态内容列表
   * RiskChange 数据源直接取维度快照数据返回
   * 两次batch对比找到本次新增的记录
   */
  public async fatchDimensionHitDetails(param: QueryMonitorDynamicDetialsRequest) {
    const { monitorGroupId, companyId, batchId, orgId, diligenceId, preBatchId, dimensionKey, strategyId, pageIndex, pageSize, esFilter, sort } = param;

    // 监控动态，关联监控动态
    if (
      [
        DimensionTypeEnums.ControllerCompany,
        DimensionTypeEnums.AssetInvestigationAndFreezing,
        DimensionTypeEnums.PledgeMerger,
        DimensionTypeEnums.PatentInfo,
        DimensionTypeEnums.RiskChange,
        DimensionTypeEnums.RecentInvestCancellationsRiskChange,
        DimensionTypeEnums.ActualControllerRiskChange,
        DimensionTypeEnums.ListedEntityRiskChange,
      ].includes(dimensionKey)
    ) {
      if (sort?.field) {
        sort.field = 'dimensionContentSearch.publishTime';
      }
      // RiskChange 数据源直接取维度快照数据返回
      return this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          orgId,
          diligenceId: [diligenceId],
          dimensionKey: [dimensionKey],
          pageSize,
          pageIndex,
          strategyId,
          esFilter,
          sort,
        },
        true,
      );
    }

    let result;
    if (preBatchId) {
      // 两次batch对比找到本次新增的记录
      result = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
        dimensionKey: [dimensionKey],
        batchIds: [batchId, preBatchId],
        strategyId,
        recordIds: [],
        companyId: companyId,
        changesType: SnapshotChangesTypeEnum.Added,
        pageSize,
        pageIndex,
        onlyDimension: 1,
        esFilter,
      });
    } else {
      // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
      result = await this.snapshotEsService.searchSnapshotData(
        {
          companyId,
          orgId,
          diligenceId: [diligenceId],
          dimensionKey: [dimensionKey],
          pageSize,
          pageIndex,
          strategyId,
          esFilter,
          // 关联方列表不支持排序
          // sort,
        },
        true,
      );
    }

    // 关联方变更列表展示时，获取最新的监控状态
    if (DimensionTypeEnums.RelatedCompanyChange == dimensionKey) {
      if (result.data.length > 0) {
        // 标记是否已被监控
        // 关联方列表中已经被加入关联方的企业打上标签
        const companyKeynoRelatedList = result.data.map((x) => x?.companyKeynoRelated);
        const monitored = await this.relatedCompanyRepo.find({
          where: {
            monitorGroupId,
            companyIdRelated: In(companyKeynoRelatedList),
            companyIdPrimary: companyId,
          },
        });

        result.data.forEach((x) => {
          x['isMonitor'] = !!monitored.find((m) => m.companyIdRelated === x?.companyKeynoRelated);
        });
      }
    }
    //查询纳税人资质变更
    if (DimensionTypeEnums.TaxpayerCertificationChange === dimensionKey) {
      if (result.data.length > 0 && preBatchId) {
        const removeData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
          dimensionKey: [dimensionKey],
          batchIds: [batchId, preBatchId],
          strategyId,
          recordIds: [],
          companyId: companyId,
          changesType: SnapshotChangesTypeEnum.Removed,
          pageSize,
          pageIndex,
          onlyDimension: 1,
          esFilter,
        });
        const diligenceHistoryEntity = await this.diligenceRepo.findOne({
          where: {
            id: diligenceId,
          },
        });
        result.data = [
          {
            changeBefore: removeData?.data[0]?.description || '-',
            changeAfter: result?.data[0]?.description || '-',
            changeDate: moment(diligenceHistoryEntity?.createDate).format('YYYY-MM-DD') || '-',
            CreateDate: moment(diligenceHistoryEntity?.createDate).startOf('day').valueOf() / 1000,
          },
        ];
      }
    }
    return result;
  }

  /**
   * 保存指标动态
   * 根据指标的动态生成规则，生成动态以及动态内容，保存到数据库和es
   * @param msgPO
   */
  public async handleMetricsAnalyze(msgPO: AnalyzeMonitorDynamicMessagePO): Promise<{
    placeholderCount: number;
    repeatableCount: number;
    nonRepeatableCount: number;
    totalCount: number;
  }> {
    const { orgId, product, monitorGroupId, diligenceIds, batchId, retryCount } = msgPO;
    this.logger.info(`handleMetricsAnalyze msgPO: ${JSON.stringify(msgPO)}`);
    const diligenceEntities = await this.findDiligence(diligenceIds);
    if (!diligenceEntities.length) {
      this.logger.warn('handleMetricsAnalyze(), there is no diligence to analyze metrics dynamic');
      return {
        placeholderCount: 0,
        repeatableCount: 0,
        nonRepeatableCount: 0,
        totalCount: 0,
      };
    }
    const finshedDiligence: DiligenceHistoryEntity[] = [];
    const unFinshedDiligence: DiligenceHistoryEntity[] = [];

    diligenceEntities.forEach((diligence) => {
      if (SnapshotStatus.SUCCESS == diligence.snapshotDetails.status) {
        finshedDiligence.push(diligence);
      } else if (SnapshotStatus.PROCESSING == diligence.snapshotDetails.status) {
        unFinshedDiligence.push(diligence);
      } else {
        // 快照失败
        this.logger.error(`快照失败无法生成动态 batchId: ${batchId}, diligenceId: ${diligence.id}`);
      }
    });
    // 找到快照未完成的尽调记录，重新发送动态生成消息，延迟3分钟执行
    if (unFinshedDiligence.length > 0) {
      const diligenceIds = unFinshedDiligence.map((d) => d.id);
      if (retryCount > 20) {
        this.logger.error(`重试 #{retryCount} 次，快照依然未完成 batchId: ${batchId}, diligenceIds: ${diligenceIds}`);
      } else {
        const ttl = 60 * 1000;
        this.logger.info(`retry sendMessage to handleMetricsAnalyze retryCount: ${retryCount + 1} delay: ${ttl} `);
        await this.queueService.continuousDiligenceAnalyzeQueue.sendMessageV2(Object.assign({}, msgPO, { diligenceIds, retryCount: retryCount + 1 }), { ttl });
      }
    }
    // 如果尽调快照都未完成，直接返回0
    if (finshedDiligence.length < 1) {
      return {
        placeholderCount: 0,
        repeatableCount: 0,
        nonRepeatableCount: 0,
        totalCount: 0,
      };
    }

    const companyIds = finshedDiligence.map((d) => d.companyId);
    //找出来已经初始化跑过的企业的指标动态 status=-1 or -2,这些数据用作对比的基线
    const [initDynamics, primaryCompanies] = await Bluebird.all([
      this.dynamicRepo.find({
        where: {
          orgId,
          //product,
          monitorGroupId,
          status: In([-1, -2]),
          companyId: In(companyIds),
          riskModelId: finshedDiligence[0].orgModelId, // 同一个批次里面的尽调，用的riskModelId肯定一样
        },
      }),
      this.findRelatedParties(monitorGroupId, companyIds),
    ]);

    // 1. 预生成占位用的指标动态(尽调通过了，没有命中任何指标)
    const placeholderDynamics: MonitorMetricDynamicEsDoc[] = finshedDiligence
      .map((d) => {
        if (!d.details?.originalHits?.length) {
          const fakeMetricsId = -1;
          // const relatedCompany = monitorCompanyEntities.find((e) => e.relatedCompanyId == d.companyId);
          const companyMetricsHashkey = getMetricsDynamicSnapshotId({
            monitorGroupId,
            modelBranchCode: d.modelBranchCode,
            metricsId: fakeMetricsId,
            riskLevel: d.result,
            companyId: d.companyId,
          });
          return {
            metricsId: fakeMetricsId,
            metricsName: '公司加入监控分组第一次排查没有风险，用于占位',
            riskLevel: d.result,
            riskScore: d.score,
            riskModelId: d.orgModelId,
            riskModelBranchCode: d.modelBranchCode,
            relatedCompany: this.generateRelatedPartyEsDoc(primaryCompanies, d.companyId),
            monitorGroupId,
            companyId: d.companyId,
            companyName: d.name,
            uniqueHashkey: v4(),
            diligenceId: d.id,
            diligenceResult: d.result,
            diligenceScore: d.score,
            totalHits: 0,
            metricsContent: null,
            status: -2,
            batchId,
            preBatchId: 0,
            companyMetricsHashkey,
            orgId,
            product,
            createDate: new Date(),
          };
        }
      })
      .filter((t) => t && !initDynamics.some((e) => e.companyId == t.companyId));

    //2. 生成动态并按照  可重复生成 和 不可重复生成分组
    const repeatableDynamics: MonitorMetricDynamicEsDoc[] = [];
    const nonRepeatableDynamics: MonitorMetricDynamicEsDoc[] = [];
    const allMetricIds = flattenDeep(finshedDiligence.map((d) => d.details?.originalHits?.map((h) => h?.metricsId))).filter((t) => t);
    const metricFeatureKeys = await this.metricService.getMetricFeature(allMetricIds, 2);
    finshedDiligence.forEach((d) => {
      if (d.details?.originalHits?.length) {
        d.details.originalHits.forEach((metricScorePO) => {
          const companyMetricsHashkey = getMetricsDynamicSnapshotId({
            monitorGroupId,
            modelBranchCode: d.modelBranchCode,
            riskLevel: metricScorePO.riskLevel,
            metricsId: metricScorePO.metricsId,
            companyId: d.companyId,
          });
          const status = initDynamics.some((e) => e.companyId == d.companyId) ? 0 : -1;
          const metricFeatureObj = find(metricFeatureKeys, { metricsId: metricScorePO.metricsId });
          const dynamicPO: MonitorMetricDynamicEsDoc = {
            createDate: new Date(),
            uniqueHashkey: v4(),
            metricsId: metricScorePO.metricsId,
            metricsName: metricFeatureObj?.metricsName || metricScorePO.name,
            metricsFeatureKeyL1: metricFeatureObj?.featureKeyL1,
            metricsFeatureKeyL2: metricFeatureObj?.featureKeyL2,
            relatedStrategyIds: findRelatedStreategyIds(metricScorePO.hitDetails),
            relatedCompany: this.generateRelatedPartyEsDoc(primaryCompanies, d.companyId),
            metricsType: metricScorePO.metricType,
            riskLevel: metricScorePO.riskLevel,
            riskScore: metricScorePO.score,
            riskModelId: d.orgModelId,
            riskModelBranchCode: d.modelBranchCode,
            monitorGroupId,
            companyId: d.companyId,
            companyName: d.name,
            diligenceId: d.id,
            diligenceResult: d.result,
            diligenceScore: d.score,
            metricsContent: {
              metricScorePO: toPlainObject(metricScorePO),
              displayContent: [],
            },
            status,
            batchId,
            preBatchId: 0,
            companyMetricsHashkey,
            orgId,
            product,
          };

          if (dynamicPO.status == -1) {
            // 未初始化过基线动态的企业，需要先占位
            placeholderDynamics.push(dynamicPO);
          } else if (metricScorePO.detailsJson?.dynamicStrategy?.allowRepeatedHits) {
            repeatableDynamics.push(dynamicPO);
          } else {
            nonRepeatableDynamics.push(dynamicPO);
          }
        });
      }
    });
    // 提前将企业的关联方变更状态标签设置为空，如果有关联方变更，会在 processHashKeyByIsSameMetric 更新变更标签未对应动态的hashKey
    await this.monitorCompanyRepo.update({ companyId: In(companyIds), monitorGroupId }, { relatedDynamicHashKey: '' });

    let placeholderCount = 0;
    let repeatableCount = 0;
    if (placeholderDynamics.length) {
      placeholderCount = await this.saveDynamic(placeholderDynamics);
      if (placeholderCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), placeholderCount: ${placeholderCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no placeholder dynamic inserted`);
      }
    }
    if (repeatableDynamics.length) {
      repeatableCount = await this.saveDynamic(repeatableDynamics);
      if (repeatableCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), repeatableCount: ${repeatableCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no repeatable dynamic inserted`);
      }
    }

    let nonRepeatableCount = 0;
    if (nonRepeatableDynamics.length) {
      // 需要对比两次快照内容是否有变化，计算出需要插入的动态
      const nonRepeatableDynamics2 = await this.processHashKeyByIsSameMetric(nonRepeatableDynamics);

      // 不可重复的动态，需要先找出是否已经有存在的动态并忽略它们
      //后续可以增加针对 MetricDynamicStrategy.lifeCycle 的处理，确保一段时间内没有重复的动态即可
      const hasTimeRange = nonRepeatableDynamics2.filter((n) => n.metricsContent?.metricScorePO?.detailsJson?.dynamicStrategy?.lifeCycle);
      const noTimeRange = nonRepeatableDynamics2.filter((n) => !n.metricsContent?.metricScorePO?.detailsJson?.dynamicStrategy?.lifeCycle);
      // 针对有时间限制的动态，因为lifeCycle 可能不一样，所以需要单条处理，否则就可以批量处理
      const promiseArray: Promise<number[]>[] = [];
      if (noTimeRange.length) {
        const chunks = chunk(noTimeRange, 50);
        promiseArray.push(
          Bluebird.map<MonitorMetricDynamicEsDoc[], number>(
            chunks,
            async (chunk) => {
              const existingDynamics1 = await this.dynamicRepo.find({
                where: {
                  companyMetricsHashkey: In(chunk.map((c) => c.companyMetricsHashkey)),
                },
                select: ['id', 'companyMetricsHashkey'],
              });
              //去掉已经存在的动态
              const filterDynamics = chunk.filter((c) => !existingDynamics1.some((e) => e.companyMetricsHashkey == c.companyMetricsHashkey));
              if (filterDynamics.length) {
                return this.saveDynamic(filterDynamics);
              } else {
                return 0;
              }
            },
            { concurrency: 1 },
          ),
        );
      }
      if (hasTimeRange.length) {
        promiseArray.push(
          Bluebird.map<MonitorMetricDynamicEsDoc, number>(
            hasTimeRange,
            async (item) => {
              const dynamicStrategy = item.metricsContent?.metricScorePO?.detailsJson?.dynamicStrategy;
              const lifeCycle = dynamicStrategy.lifeCycle;
              const startDate = moment(new Date()).subtract(lifeCycle, 'days').startOf('date').toDate();
              const n1 = await this.dynamicRepo.count({
                where: {
                  companyMetricsHashkey: item.companyMetricsHashkey,
                  createDate: MoreThan(startDate),
                },
                select: ['id', 'companyMetricsHashkey'],
              });
              if (n1 == 0) {
                return this.saveDynamic([item]);
              } else {
                return 0;
              }
            },
            { concurrency: 10 },
          ),
        );
      }
      const results = await Bluebird.all(promiseArray);
      const total = results.reduce((acc, cur) => acc + cur.reduce((acc1, cur1) => acc1 + cur1, 0), 0);

      nonRepeatableCount += total;

      if (nonRepeatableCount > 0) {
        this.logger.info(`handleMetricsAnalyze(), nonRepeatableCount: ${nonRepeatableCount}`);
      } else {
        this.logger.warn(`handleMetricsAnalyze() no nonRepeatable dynamic inserted`);
      }
    }

    // 更新监控企业关联的最新最高动态等级
    await Bluebird.map(finshedDiligence, async (d) => {
      const highLeverDynamic = await this.dynamicRepo.findOne({
        where: { batchId, monitorGroupId, companyId: d.companyId, status: Not(In([-1, -2])) },
        order: { riskLevel: 'DESC' },
      });
      if (highLeverDynamic?.id) {
        await this.monitorCompanyRepo.update(
          {
            monitorGroupId,
            companyId: d.companyId,
          },
          { riskLevel: highLeverDynamic.riskLevel },
        );
      }
    });

    // 插入的有效的动态
    const inserted = repeatableCount + nonRepeatableCount;
    if (inserted > 0) {
      this.logger.info(`found ${inserted} metrics dynamic for monitorGroup=${monitorGroupId}`);
      await this.dynamicRepo.manager.update(MonitorGroupEntity, monitorGroupId, {
        changesCount: () => `changes_count + ${inserted}`,
      });
    } else {
      this.logger.info(`no metrics dynamic for monitorGroup=${monitorGroupId}`);
    }
    return {
      placeholderCount,
      repeatableCount,
      nonRepeatableCount,
      totalCount: placeholderCount + repeatableCount + nonRepeatableCount,
    };
  }

  private async findDiligence(diligenceIds: number[]) {
    return this.diligenceRepo.findByIds(diligenceIds);
  }

  /**
   * 查找指定公司是谁的关联方
   * @param monitorGroupId
   * @param companyIds
   */
  private async findRelatedParties(monitorGroupId: number, companyIds: string[]) {
    return this.relatedCompanyRepo.find({
      where: {
        monitorGroupId,
        companyIdRelated: In(companyIds),
      },
    });
  }

  /**
   * 根据指定companyId，找到他是谁的关联方并生成 es doc
   * @param relatedCompanies
   * @param companyId
   */
  private generateRelatedPartyEsDoc(relatedCompanies: MonitorCompanyRelatedPartyEntity[], companyId: string): CompanyRelatedPartyEsDoc[] {
    return relatedCompanies
      .filter((r) => r.companyIdRelated == companyId)
      .map((b) => {
        return {
          companyIdPrimary: b.companyIdPrimary,
          relatedType: b.relatedType,
        };
      });
  }

  /**
   * 关联方变化指标生成条件判断，
   * 如果是关联方变更指标, 指标中只使用到了RelatedCompanyChange 这个维度， 公司本身不是关联方企业
   * @param dimHitRes
   * @returns
   */
  private isRelatedMetric(dimHitRes: DimensionHitResultPO[]): boolean {
    return dimHitRes[0].dimensionKey == DimensionTypeEnums.RelatedCompanyChange;
  }

  // private isRelatedMetric(dimHitRes: DimensionHitResultPO[]): boolean {
  //   if (dimHitRes.length == 1 && dimHitRes[0].dimensionKey == DimensionTypeEnums.RelatedCompanyChange && dimHitRes[0].dimensionFilter.isRelated == false) {
  //     return true;
  //   }
  //   return false;
  // }

  /**
   * 指标是否纳税人资质变更
   * @returns
   * @param dimHit
   */
  private isTaxpayerCertificationChange(dimHit: DimensionHitResultPO): boolean {
    return dimHit.dimensionKey == DimensionTypeEnums.TaxpayerCertificationChange;
  }

  /**
   * 根据比对结果重新修改metricScorePO中每个策略的命中情况
   * @param hitDetails
   * @param contents
   * @returns
   */
  private resetHitDetails(hitDetails: MetricHitDetailsPO, contents: DynamicDisplayContent[]) {
    let hitDetailsHits = 0;
    const getCount = (contents: DynamicDisplayContent[], strategyId: number) => {
      return contents.filter((c) => c.strategyId == strategyId).reduce((acc, cur) => acc + cur.count, 0);
    };
    hitDetails?.must?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });
    hitDetails?.must_not?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });
    hitDetails?.should?.forEach((f) => {
      f.totalHits = getCount(contents, f.strategyId);
      hitDetailsHits += f.totalHits;
    });
    hitDetails['totalHits'] = hitDetailsHits;
    return hitDetails;
  }

  /**
   * 比对两次动态的具体内容判断动态是否变化
   * @param dynamic 动态内容
   * @param detailsField  比较的命中策略部分 "hitDetails" | "otherHitDetails" | "All",
   * @param preBatchId 上次动态的批次id
   * @returns
   */
  public async compareDynamicContents(
    dynamic: MonitorMetricDynamicEsDoc,
    dimHitRes: DimensionHitResultPO[],
    preBatchId: number,
  ): Promise<[number, DynamicDisplayContent[]]> {
    let totalHits = 0;
    const displayDontents: DynamicDisplayContent[] = [];
    // const dimHitRes = this.findMetricDimensions(dynamic.metricsContent?.metricScorePO);
    if (!dimHitRes.length) {
      return [totalHits, displayDontents];
    }
    const { batchId, companyId, diligenceId, orgId } = dynamic;
    await Bluebird.map(dimHitRes, async (dimHit) => {
      const { dimensionKey, strategyId } = dimHit;
      let dynamicAdd: DynamicDisplayContent;
      if (preBatchId) {
        // 该指标找到上次动态记录， 取出 preBatchId
        dynamic.preBatchId = preBatchId;
        dynamic.metricsContent.displayContent = [];

        // 旧版关联方变更动态的实现逻辑，现在已调整到尽调的时候计算关联方方变更内容快照， 动态的直接取快照内容展示
        // if (this.isRelatedMetric(dimHitRes)) {
        //   // 只对关联方指标减少记录 做处理
        //   const reducedData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
        //     dimensionKey: [dimensionKey],
        //     batchIds: [batchId, preBatchId],
        //     recordIds: [],
        //     companyId: dynamic.companyId,
        //     changesType: SnapshotChangesTypeEnum.Removed,
        //     pageSize: 1000,
        //     pageIndex: 1,
        //   });

        //   if (reducedData.total > 0) {
        //     // 分组内已监控的关联方，如果变成了在减少关联方reducedData中，需要将其 relatedStatus 标记为无效，并且生成一个动态
        //     const affectedCompnayList = await this.updateRelated(reducedData, monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Valid);
        //     if (affectedCompnayList.length > 0) {
        //       const invalidRelatedIds = affectedCompnayList.map((r) => r.companyIdRelated);
        //       // 将失效的已监控关联方记录到动态中
        //       const dynamicReduced: DynamicDisplayContent = {
        //         dimensionKey,
        //         strategyId,
        //         operate: 1,
        //         count: affectedCompnayList.length,
        //         dimensionContent: reducedData.data
        //           .filter((d) => invalidRelatedIds.includes(d.dimensionContent.companyKeynoRelated))
        //           .map((d) => d.dimensionContent),
        //       };
        //       totalHits += dynamicReduced.count;
        //       displayDontents.push(dynamicReduced);
        //     }
        //   }
        // }
        // 策略的每个维度，比对两次快照的新增数据，动态只存每个维度新增的第一条做示意，totalHits 统计全部的新增数据条数
        const addData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
          dimensionKey: [dimensionKey],
          batchIds: [batchId, preBatchId],
          strategyId,
          recordIds: [],
          companyId: companyId,
          changesType: SnapshotChangesTypeEnum.Added,
          pageSize: 500,
          pageIndex: 1,
        });
        if (addData.total > 0) {
          if (this.isTaxpayerCertificationChange(dimHit)) {
            const removeData = await this.snapshotEsCompareService.searchDimensionDiffsByBatch({
              dimensionKey: [dimensionKey],
              batchIds: [batchId, preBatchId],
              strategyId,
              recordIds: [],
              companyId: companyId,
              changesType: SnapshotChangesTypeEnum.Removed,
              pageSize: 500,
              pageIndex: 1,
            });
            dynamicAdd = {
              dimensionKey,
              strategyId,
              operate: 0,
              count: addData.total,
              dimensionContent: [
                {
                  changeBefore: removeData?.data[0]?.dimensionContent?.description || '-',
                  changeAfter: addData?.data[0]?.dimensionContent?.description || '-',
                  changeDate: moment().format('YYYY-MM-DD'),
                },
              ],
            };
          } else {
            // 添加动态内容
            dynamicAdd = {
              dimensionKey,
              strategyId,
              operate: 0,
              count: addData.total,
              dimensionContent: addData.data.map((d) => d.dimensionContent),
            };
          }
          totalHits += dynamicAdd.count;
          displayDontents.push(dynamicAdd);
          // // 关联方变更指标
          // if (this.isRelatedMetric(dimHitRes)) {
          //   // 分组内已监控失效的关联方，如果变成了在新增关联方addData中，需要将relatedStatus变更成有效
          //   await this.updateRelated(addData, monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Invalid);
          // }
        }
      } else {
        // 没有上次快照，直接取出本次快照的内容记录为新增动态内容
        // 动态只存每个维度新增的第一条做示意，totalHits 统计全部的新增数据条数
        const addData = await this.snapshotEsService.searchSnapshotData(
          {
            companyId,
            orgId,
            diligenceId: [diligenceId],
            dimensionKey: [dimensionKey],
            pageSize: 1,
            pageIndex: 1,
            strategyId,
          },
          true,
        );
        if (addData.total > 0) {
          // 添加动态内容
          dynamicAdd = {
            dimensionKey,
            strategyId,
            operate: 0,
            count: addData.total,
            dimensionContent: addData.data,
          };
          totalHits += dynamicAdd.count;
          displayDontents.push(dynamicAdd);
        }
      }
    });
    return [totalHits, displayDontents];
  }

  /**
   * 是否需要同步比对两次动态的具体内容判断动态是否创建
   * 根据 metricContent.metricScorePO.detailsJson.dynamicStrategy.isSameMetricStrategy 判断
   * 0 默认 只要有这个指标命中就创建动态
   * 1 则需要比对详细的命中内容来判断，内容有新增才创建动态
   * @private
   */
  private async processHashKeyByIsSameMetric(nonRepeatableDynamics: MonitorMetricDynamicEsDoc[]): Promise<MonitorMetricDynamicEsDoc[]> {
    const processedDynamics: MonitorMetricDynamicEsDoc[] = [];
    await Bluebird.map(
      nonRepeatableDynamics,
      async (dynamic) => {
        const metricScorePO = dynamic.metricsContent.metricScorePO;
        const isSameMetricStrategy = metricScorePO?.detailsJson?.dynamicStrategy?.isSameMetricStrategy;
        if (isSameMetricStrategy == 1) {
          const dimHitRes = this.findMetricDimensions(metricScorePO);
          // 这里来获取两次的快照，然后比对是否相同，如果不相同，则生成新的 companyMetricsHashkey，这样就会被当成一条新的动态
          // 本次维度有命中数据，然后判断上次快照是否有命中数据，如果有则进行比对，如果没有则把本次维度命中内容当做新的动态内容
          if (dimHitRes.length > 0) {
            const { batchId, companyId } = dynamic;

            // 新逻辑，在模型指标中设置了target 关联方变更只针对主体企业，关联方企业不会命中这个指标， 下面的逻辑可以废除
            // 如果是关联方变更指标, 指标中只使用到了 DimensionTypeEnums.RelatedCompanies 这个维度
            // if (this.isRelatedMetric(dimHitRes)) {
            //   // 如果公司不是监控主体,直接忽略 关联方变更指标
            //   const monitorCompany = await this.monitorCompanyRepo.findOne({ monitorGroupId, companyId });
            //   if (monitorCompany.primaryObject == 0) {
            //     return;
            //   }
            // }

            // 2 找到上次快照的动态记录
            const preMetricDynamic = await this.dynamicRepo.findOne({
              where: {
                companyId: companyId,
                orgId: dynamic.orgId,
                riskModelBranchCode: dynamic.riskModelBranchCode,
                metricsId: dynamic.metricsId,
                batchId: Not(batchId),
              },
              select: ['id', 'batchId', 'metricsContent'],
              order: { createDate: 'DESC' },
            });
            const preBatchId = preMetricDynamic?.batchId;
            const [totalHits, contents] = await this.compareDynamicContents(dynamic, dimHitRes, preBatchId);

            if (totalHits > 0) {
              dynamic.preBatchId = preBatchId;
              dynamic.metricsContent.displayContent.push(...contents);
              // // 重新生成 Hashkey, 同一指标相同对比批次去重
              // const companyMetricsHashkey = md5(`${dynamic.companyMetricsHashkey}_${batchId}_${preBatchId}`);
              // dynamic.companyMetricsHashkey = companyMetricsHashkey;
              // 重新计算命中策略的命中记录数
              dynamic.metricsContent.metricScorePO.totalHits = totalHits;

              if (dynamic.metricsContent.metricScorePO?.hitDetails) {
                dynamic.metricsContent.metricScorePO['hitDetails'] = this.resetHitDetails(dynamic.metricsContent.metricScorePO.hitDetails, contents);
              }
              if (dynamic.metricsContent.metricScorePO?.otherHitDetails?.length > 0) {
                dynamic.metricsContent.metricScorePO['otherHitDetails'] = dynamic.metricsContent.metricScorePO.otherHitDetails.map((hitDetails) => {
                  return this.resetHitDetails(hitDetails, contents);
                });
              }
              processedDynamics.push(dynamic);

              // if (this.isRelatedMetric(dimHitRes)) {
              //   // 给监控主体上 记录关联方变化的动态hashKey
              //   await this.monitorCompanyRepo.update({ companyId, monitorGroupId }, { relatedDynamicHashKey: companyMetricsHashkey });
              // }
            }
          }
        } else {
          processedDynamics.push(dynamic);
        }
      },
      { concurrency: 5 },
    );

    return processedDynamics;
  }

  /**
   * 修改监控列表关联方状态
   * @param relatedChangeCompany 发生变化的关联方
   * @param monitorGroupId 监控分组
   * @param companyIdPrimary 主体公司
   * @param relatedStatus 需要的修改的关联方，当前状态
   */
  private async updateRelated(
    relatedChangeCompany: RelatedPartyGroupPO[],
    monitorGroupId: number,
    companyIdPrimary: string,
    relatedStatus: MonitorCompanyRelatedStatusEnum,
  ) {
    const companyIdRelateds = relatedChangeCompany.map((d) => d.companyKeynoRelated);

    const [companyList, count] = await this.relatedCompanyRepo.findAndCount({
      monitorGroupId,
      companyIdPrimary,
      companyIdRelated: In(companyIdRelateds),
      status: relatedStatus,
    });

    if (count > 0) {
      if (MonitorCompanyRelatedStatusEnum.Valid == relatedStatus) {
        // 当前监控关联方是有效，需要变更成失效
        await this.relatedCompanyRepo.update(
          {
            monitorGroupId,
            companyIdPrimary,
            companyIdRelated: In(companyList.map((c) => c.companyIdRelated)),
            status: relatedStatus,
          },
          { status: MonitorCompanyRelatedStatusEnum.Invalid },
        );
      } else {
        // 当前监控关联方是无效，需要变更成有效，并更新relatedType
        await Bluebird.map(companyList, async (company) => {
          const relatedCompany = relatedChangeCompany.find((d) => d.companyKeynoRelated == company.companyIdRelated);
          const relatedTypeStr = relatedCompany.relatedTypes.join(',');
          await this.relatedCompanyRepo.update(
            { id: company.id, status: relatedStatus },
            {
              status: MonitorCompanyRelatedStatusEnum.Valid,
              relatedTypeStr,
            },
          );
        });
      }
    }
    return companyList;
  }

  /**
   * 给动态数据填充内容, 关联方变化指标对监控主体增加标记
   * @private
   */
  private async fulfillDynamicContent(dynamicList: MonitorMetricDynamicEsDoc[]) {
    return Bluebird.map(
      dynamicList,
      async (dynamic) => {
        // 对非初始化动态和没有内容的动态添加动态内容
        if (![-1, -2].includes(dynamic.status) && dynamic.metricsContent.displayContent.length < 1) {
          const metricScorePO = dynamic.metricsContent.metricScorePO;
          const dimHitRes = this.findMetricDimensions(metricScorePO);
          if (dimHitRes.length > 0) {
            const { companyId, orgId, diligenceId, monitorGroupId, uniqueHashkey } = dynamic;

            if (this.isRelatedMetric(dimHitRes)) {
              // 给监控主体上 记录关联方变化的动态hashKey
              await this.monitorCompanyRepo.update({ companyId, monitorGroupId }, { relatedDynamicHashKey: uniqueHashkey });
              await Bluebird.map(dimHitRes, async (dimHit: DimensionHitResultPO) => {
                const { dimensionKey, strategyId, strategyName, totalHits } = dimHit;
                const searchRes: PaginationResponse = await this.fatchDimensionHitDetails({
                  companyId,
                  orgId,
                  dimensionKey,
                  diligenceId,
                  strategyId,
                  pageSize: totalHits,
                  pageIndex: 1,
                  batchId: null,
                  preBatchId: null,
                  monitorGroupId,
                  sort: {
                    field: 'publishTime',
                    order: 'DESC',
                  },
                });

                if (searchRes?.total > 0) {
                  let operate = 0;
                  if (strategyName == '关联方新增') {
                    operate = 0;
                    // 分组内已监控失效的关联方，如果变成了在新增关联方addData中，需要将当前 无效的 relatedStatus 变更成有效
                    await this.updateRelated(searchRes.data as RelatedPartyGroupPO[], monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Invalid);
                  } else {
                    operate = 1;
                    await this.updateRelated(searchRes.data as RelatedPartyGroupPO[], monitorGroupId, companyId, MonitorCompanyRelatedStatusEnum.Valid);
                  }

                  // 添加动态内容
                  const dynamicAdd: DynamicDisplayContent = {
                    dimensionKey,
                    strategyId,
                    operate,
                    count: searchRes?.total,
                    dimensionContent: searchRes.data.slice(0, 1),
                  };
                  dynamic.metricsContent.displayContent.push(dynamicAdd);
                }
              });
            } else {
              const { dimensionKey, strategyId } = dimHitRes[0];
              // await Bluebird.map(dimHitRes, async (dimHit: DimensionHitResultPO) => {
              // 查询 指标中第一策略的 快照取出命中的具体内容
              const searchRes: PaginationResponse = await this.fatchDimensionHitDetails({
                companyId,
                orgId,
                dimensionKey,
                diligenceId,
                strategyId,
                pageSize: 1,
                pageIndex: 1,
                batchId: null,
                preBatchId: null,
                monitorGroupId,
                sort: {
                  field: 'publishTime',
                  order: 'DESC',
                },
              });

              if (searchRes?.total > 0) {
                // 添加动态内容
                const dynamicAdd: DynamicDisplayContent = {
                  dimensionKey,
                  strategyId,
                  operate: 0,
                  count: searchRes?.total,
                  dimensionContent: searchRes.data,
                };
                dynamic.metricsContent.displayContent.push(dynamicAdd);
              }
            }
          }
        }
        return dynamic;
      },
      { concurrency: 10 },
    );
  }

  /**
   * 报错动态到数据库和es
   * @param dynamics
   * @returns 保存成功数量
   */
  private async saveDynamic(dynamics: MonitorMetricDynamicEsDoc[]): Promise<number> {
    let insertCount = 0;

    const formtedDynamics = await this.fulfillDynamicContent(dynamics);

    const chunks = chunk(formtedDynamics, 50);

    await Bluebird.all([
      await Bluebird.map(
        chunks,
        async (chunk) => {
          const t = await this.dynamicRepo
            .createQueryBuilder()
            .insert()
            .into(MonitorMetricsDynamicEntity)
            .orIgnore(true)
            .values(chunk)
            .updateEntity(false)
            .execute();
          insertCount += t.raw.affectedRows;
        },
        { concurrency: 5 },
      ),
      await this.dynamicEsService.insertDynamicDoc(dynamics),
    ]);
    return insertCount;
  }

  private findMetricDimensions(metircScore: MetricScorePO): DimensionHitResultPO[] {
    const dimHitResults: DimensionHitResultPO[] = [];
    if (metircScore?.totalHits) {
      const array = [];
      array.push(metircScore.hitDetails);
      if (metircScore?.otherHitDetails?.length > 0) {
        array.push(...metircScore.otherHitDetails);
      }
      array.forEach((mhd) => {
        if (mhd?.totalHits) {
          if (mhd?.must?.length) {
            dimHitResults.push(...mhd.must);
          }
          if (mhd?.should?.length) {
            dimHitResults.push(...mhd.should);
          }
          if (mhd?.must_not?.length) {
            dimHitResults.push(...mhd.must_not);
          }
        }
      });
    }
    return dimHitResults.filter((t) => t.dimensionKey);
  }

  @Cacheable({ ttlSeconds: 60 })
  public async filterUnMonitorCompany(request: QueryMonitorDynamicDetialsRequest) {
    //分页取所有
    const pageSize = 2000; // 每批次查询的最大条数
    let pageIndex = 1; // 当前查询的起始位置
    let companyRelatedList = [];
    let total = 0;

    do {
      request.pageIndex = pageIndex;
      request.pageSize = pageSize;
      //获取所有关联方企业 快照中已剔除不支持的企业
      const resp: PaginationResponse = await this.fatchDimensionHitDetails(request);
      companyRelatedList = companyRelatedList.concat(resp.data);
      total = resp.total;
      // 增加页码，准备下一次查询
      pageIndex++;
    } while ((pageIndex - 1) * pageSize < total);

    // 过滤未监控的公司
    const unMonitorCompanyList = companyRelatedList.filter((x) => x['isMonitor'] === false);

    if (!companyRelatedList) {
      return {
        unMonitorCompanyCount: 0,
        filterMonitorCompanyCount: 0,
        unMonitorCompanyList: [],
        unsupportedCompanies: [],
      };
    }
    const unsupportedCompanies: any[] = [];
    return {
      unMonitorCompanyCount: unMonitorCompanyList?.length,
      filterMonitorCompanyCount: unMonitorCompanyList?.length - unsupportedCompanies?.length,
      unMonitorCompanyList: unMonitorCompanyList,
      unsupportedCompanies: unsupportedCompanies,
    };
  }
}
