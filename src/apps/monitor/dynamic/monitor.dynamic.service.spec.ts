import { Test, TestingModule } from '@nestjs/testing';
// import { MonitorController } from './monitor.controller';
import { MonitorMetricsDynamicEntity } from '../../../libs/entities/MonitorMetricsDynamicEntity';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { PlatformUser } from '../../../libs/model/common';
import { MonitorDynamicService } from './monitor.dynamic.service';
import { MonitorDynamicRemarkService } from './monitor.dynamic.remark.service';
import { GetMonitorDynamicRemarkRequest, MonitorDynamicHandleRequest } from '../../../libs/model/monitor/MonitorDynamicHandleRequest';
import MyOssService from '../../basic/my-oss.service';
import { MonitorDynamicEsService } from './monitor.dynamic.es.service';
import { MetricDynamicStatusEnums } from '../../../libs/enums/metric/MetricDynamicStatusEnums';
import { QueryRelatedHashKeyRequest } from '../../../libs/model/monitor/QueryRelatedHashKeyRequest';
import { AppTestModule } from '../../app/app.test.module';
import { generateUniqueTestIds, getTestUser } from '../../test_utils_module/test.user';
import { clearMonitorTestData } from '../../test_utils_module/monitor.test.tools';
import { EntityManager, getConnection, getRepository, In, Repository } from 'typeorm';
import { MetricTypeEnums } from '../../../libs/enums/metric/MetricTypeEnums';
import * as crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonitorGroupEntity } from '../../../libs/entities/MonitorGroupEntity';
import { MonitorDynamicsRemarkEntity } from '../../../libs/entities/MonitorDynamicsRemarkEntity';
import { CompanyEntity } from '../../../libs/entities/CompanyEntity';
import { MetricsEntity } from '../../../libs/entities/MetricsEntity';
import { MonitorCompanyEntity } from '../../../libs/entities/MonitorCompanyEntity';
import { CompanySearchService } from '../../company/company-search.service';
import { CompanyDetailService } from '../../company/company-detail.service';
import { DynamicChartXmindAggsRequest, SearchMetricDynamicRequest, TodayHighRiskDynamicsAggsRequest } from '../../../libs/model/monitor';
import { RelatedCompanyChartRequest } from '../../../libs/model/monitor';
import { HighRiskChartAggsResponse } from '../../../libs/model/monitor';
import { HighRiskChartItemPO } from '../po/HighRiskChartItemPO';
import { MonitorModule } from '../monitor.module';
const [testOrgId, testUserId] = generateUniqueTestIds('monitor.dynamic.service.spec.ts');

jest.setTimeout(600000);
describe('MonitorService searchMetricsDynamic test', () => {
  let monitorService: MonitorDynamicService;
  let remarkService: MonitorDynamicRemarkService;
  let testUser: PlatformUser;

  let entityManager: EntityManager;
  let monitorMetricsDynamicRepo: Repository<MonitorMetricsDynamicEntity>;
  let dynamicEsService: MonitorDynamicEsService;
  let companySearchService: CompanySearchService;

  beforeAll(async () => {
    jest.clearAllMocks();
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AppTestModule,
        TypeOrmModule.forFeature([
          MonitorMetricsDynamicEntity,
          MonitorGroupEntity,
          MonitorDynamicsRemarkEntity,
          CompanyEntity,
          MetricsEntity,
          MonitorCompanyEntity,
        ]),
        MonitorModule,
      ],
      // providers: [
      //   MonitorDynamicEsService,
      //   MonitorDynamicRemarkService,
      //   MonitorDynamicService,
      //   {
      //     provide: MyOssService,
      //     useValue: jest.fn(),
      //   },
      //   {
      //     provide: CompanySearchService,
      //     useValue: {
      //       companySearchForKys: jest.fn().mockResolvedValue({
      //         Result: [{ id: '84c17a005a759a5e0d875c1ebb6c9846', name: '测试公司1' }],
      //       }),
      //     },
      //   },
      //   {
      //     provide: CompanyDetailService,
      //     useValue: jest.fn(),
      //   },
      // ],
    }).compile();
    monitorService = module.get<MonitorDynamicService>(MonitorDynamicService);
    remarkService = module.get<MonitorDynamicRemarkService>(MonitorDynamicRemarkService);
    dynamicEsService = module.get<MonitorDynamicEsService>(MonitorDynamicEsService);
    companySearchService = module.get<CompanySearchService>(CompanySearchService);
    monitorMetricsDynamicRepo = getRepository(MonitorMetricsDynamicEntity);
    entityManager = monitorMetricsDynamicRepo.manager;
    testUser = getTestUser(testOrgId, testUserId);

    // Mock dynamicEsService methods
    jest.spyOn(dynamicEsService, 'searchDynamics').mockResolvedValue({
      data: [
        {
          metricsId: 1,
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '测试公司',
          metricsName: '测试指标',
          riskLevel: 2,
          riskScore: 80,
          riskModelId: 1,
          riskModelBranchCode: 'test',
          monitorGroupId: 1,
          status: 0,
          createDate: new Date(),
          orgId: testUser.currentOrg,
          product: ProductCodeEnums.Pro,
          uniqueHashkey: 'test-key',
          companyMetricsHashkey: 'test-metrics-key',
          metricsType: MetricTypeEnums.MonitorBusinessMetric,
          diligenceId: 1,
          diligenceScore: 75,
          diligenceResult: 1,
          batchId: 1,
          preBatchId: 0,
        },
      ],
      total: 1,
      pageSize: 10,
      pageIndex: 1,
      aggsResponse: {
        '1_riskLevel': {
          buckets: [
            { key: 1, doc_count: 5 },
            { key: 2, doc_count: 3 },
          ],
        },
        '2_metrics': {
          buckets: [
            { key: 1, doc_count: 4, metricsName: { buckets: [{ key: '测试指标1' }] } },
            { key: 2, doc_count: 4, metricsName: { buckets: [{ key: '测试指标2' }] } },
          ],
        },
        '3_company': {
          buckets: [{ key: '84c17a005a759a5e0d875c1ebb6c9846', doc_count: 8 }],
        },
        '5_relatedCompany': {
          relatedType: {
            buckets: [
              {
                key: 'RELATED_SHAREHOLDER',
                reverseToRoot: {
                  companyId: {
                    buckets: [
                      {
                        key: '84c17a005a759a5e0d875c1ebb6c9846',
                        metricsId: {
                          buckets: [
                            {
                              key: 1,
                              metricsName: {
                                buckets: [
                                  {
                                    key: '测试指标',
                                    doc_count: 1,
                                  },
                                ],
                              },
                              riskLevel: {
                                buckets: [
                                  {
                                    key: 2,
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
      },
    });

    jest.spyOn(dynamicEsService, 'dynamicChartAnalyzeForXmind').mockResolvedValue({
      relatedTypeAggs: [
        {
          relatedType: 'RELATED_SHAREHOLDER',
          count: 1,
          metricIdAggs: [
            {
              metricsId: 1,
              metricsName: null,
              count: 1,
            },
          ],
        },
      ],
    });

    jest.spyOn(dynamicEsService, 'analyzeSummary').mockResolvedValue({
      metricsIds: [1, 2],
      metricEntities: [],
      companyCount: 5,
    });

    // 创建符合 HighRiskChartAggsResponse 定义的模拟数据
    const mockHighRiskResponse = new HighRiskChartAggsResponse();
    mockHighRiskResponse.items = [
      Object.assign(new HighRiskChartItemPO(), {
        xAxis: 1,
        yAxis: 2,
        count: 5,
        list: ['测试指标1'],
        metricsId: 1, // 虽然接口中没有定义，但可能在实际使用中需要
        metricsName: '测试指标1', // 虽然接口中没有定义，但可能在实际使用中需要
      }),
      Object.assign(new HighRiskChartItemPO(), {
        xAxis: 2,
        yAxis: 3,
        count: 3,
        list: ['测试指标2'],
        metricsId: 2, // 虽然接口中没有定义，但可能在实际使用中需要
        metricsName: '测试指标2', // 虽然接口中没有定义，但可能在实际使用中需要
      }),
    ];
    jest.spyOn(dynamicEsService, 'dynamicChartAnalyzeForHighRisk').mockResolvedValue(mockHighRiskResponse);

    // 模拟 monitorCompanyRepo.count 方法
    jest.spyOn(monitorService['monitorCompanyRepo'], 'count').mockResolvedValue(10);
  });

  afterAll(async () => {
    // jest.clearAllMocks();
    // await clearMonitorTestData(entityManager, testUser);
    // const connection = getConnection();
    // await connection.close();
  });

  //测试代码整理至monitor.dynamic.remark.service.spec.ts
  it.skip('test dynamicHandle', async () => {
    const uuid = uuidv4();
    // console.log(uuid);
    //临时插入一条动态数据
    const entity = new MonitorMetricsDynamicEntity();
    entity.orgId = testUser.currentOrg;
    entity.uniqueHashkey = '14732';
    entity.metricsId = 1;
    entity.companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    entity.metricsName = 'test';
    entity.riskLevel = 0;
    entity.riskScore = 0;
    entity.monitorGroupId = 1;
    entity.status = MetricDynamicStatusEnums.Unhandled;
    entity.riskModelId = 1;
    entity.diligenceId = 1;
    entity.batchId = 1;
    entity.companyMetricsHashkey = crypto.createHash('sha256').update('test').digest('hex');
    entity.product = ProductCodeEnums.Pro;
    entity.diligenceScore = 0;
    entity.diligenceResult = 0;
    entity.preBatchId = 1;
    entity.uniqueHashkey = uuid;
    entity.metricsType = MetricTypeEnums.MonitorBusinessMetric;
    entity.companyName = '乐视网信息技术（北京）股份有限公司';

    const respEntity = await monitorMetricsDynamicRepo.save(entity);
    expect(respEntity).not.toBeNull();
    const param = new MonitorDynamicHandleRequest();
    param.status = MetricDynamicStatusEnums.InProgress;
    param.dynamicId = respEntity.uniqueHashkey;
    param.grade = 0;
    param.way = 1;
    param.comment = 'unit-test';
    const remark = await remarkService.dynamicHandle(testUser, param);
    expect(remark).not.toBeNull();
    expect(remark.status).toBe(MetricDynamicStatusEnums.InProgress);
    expect(remark.dynamicId).toBe(param.dynamicId);
    expect(remark.grade).toBe(param.grade);
    expect(remark.comment).toBe('unit-test');
    expect(remark.way).toBe(param.way);
    expect(remark.attachments).toBeNull();
  });

  //测试代码整理至monitor.dynamic.remark.service.spec.ts
  it.skip('test getDynamicRemark', async () => {
    const uuid = uuidv4();
    // console.log(uuid);
    //临时插入一条动态数据
    const entity = new MonitorMetricsDynamicEntity();
    entity.orgId = testUser.currentOrg;
    entity.uniqueHashkey = '14732';
    entity.metricsId = 1;
    entity.companyId = '84c17a005a759a5e0d875c1ebb6c9846';
    entity.metricsName = 'test';
    entity.riskLevel = 0;
    entity.riskScore = 0;
    entity.monitorGroupId = 1;
    entity.status = MetricDynamicStatusEnums.Unhandled;
    entity.riskModelId = 1;
    entity.diligenceId = 1;
    entity.batchId = 1;
    entity.companyMetricsHashkey = crypto.createHash('sha256').update('test').digest('hex');
    entity.product = ProductCodeEnums.Pro;
    entity.diligenceScore = 0;
    entity.diligenceResult = 0;
    entity.preBatchId = 1;
    entity.uniqueHashkey = uuid;
    entity.metricsType = MetricTypeEnums.MonitorBusinessMetric;
    entity.companyName = '乐视网信息技术（北京）股份有限公司';

    const respEntity = await monitorMetricsDynamicRepo.save(entity);
    expect(respEntity).not.toBeNull();
    const handleParam = new MonitorDynamicHandleRequest();
    handleParam.status = MetricDynamicStatusEnums.InProgress;
    handleParam.dynamicId = respEntity.uniqueHashkey;
    handleParam.grade = 0;
    handleParam.way = 1;
    handleParam.comment = 'unit-test';
    const handleRemark = await remarkService.dynamicHandle(testUser, handleParam);
    expect(handleRemark).not.toBeNull();

    const param = new GetMonitorDynamicRemarkRequest();
    param.dynamicId = respEntity.uniqueHashkey;
    param.pageIndex = 1;
    param.pageSize = 10;
    const remark = await remarkService.getDynamicRemark(testUser, param);
    expect(remark).not.toBeNull();
    expect(remark.data.length).toBe(1);
    expect(remark.data[0].dynamicId).toBe(respEntity.uniqueHashkey);
  });

  it.skip('debug queryRelatedHashKey', async () => {
    const user: PlatformUser = {
      bundleStartDate: null,
      loginUserId: 0,
      orgName: '',
      currentOrg: 1003542,
      currentProduct: ProductCodeEnums.Pro,
      userId: 103103,
    };
    const param = new QueryRelatedHashKeyRequest();
    // param.dynamicId = '14732';
    // param.pageIndex = 1;
    // param.pageSize = 10;
    param.hashKeys = ['eba462de-dd7e-4481-a89a-e929174856da'];
    const remark = await monitorService.queryRelatedHashKey(user, param);
    expect(remark).not.toBeNull();
  });

  it('测试 searchDynamics 方法', async () => {
    // 准备测试数据
    const postData = Object.assign(new SearchMetricDynamicRequest(), {
      pageSize: 10,
      pageIndex: 1,
      groupId: [1],
    });

    // 执行测试
    const result = await monitorService.searchDynamics(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result.data.length).toBe(1);
    expect(result.total).toBe(1);
    expect(dynamicEsService.searchDynamics).toHaveBeenCalledWith(
      expect.objectContaining({
        pageSize: 10,
        pageIndex: 1,
        groupId: [1],
        product: testUser.currentProduct,
        orgId: testUser.currentOrg,
      }),
    );
  });

  it('测试 searchRelatedChartDynamics 方法', async () => {
    // 准备测试数据
    const postData = Object.assign(new RelatedCompanyChartRequest(), {
      groupId: 1,
    });

    // 执行测试
    const result = await monitorService.searchRelatedChartDynamics(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result.relatedCompanyChartItems.length).toBe(1);
    expect(result.relatedCompanyChartItems[0].relatedCompanys[0].companyName).toBe('测试公司1');
    expect(dynamicEsService.searchDynamics).toHaveBeenCalledWith(
      expect.objectContaining({
        groupId: 1,
        product: testUser.currentProduct,
        orgId: testUser.currentOrg,
        aggsField: [5],
        pageSize: 1,
        pageIndex: 1,
      }),
    );
  });

  it('测试 searchRelatedChartDynamics 方法, 不存在的公司查询时应返回空', async () => {
    jest.spyOn(dynamicEsService, 'searchDynamics').mockResolvedValue({
      data: [
        {
          metricsId: 1,
          companyId: '84c17a005a759a5e0d875c1ebb6c9846',
          companyName: '测试公司',
          metricsName: '测试指标',
          riskLevel: 2,
          riskScore: 80,
          riskModelId: 1,
          riskModelBranchCode: 'test',
          monitorGroupId: 1,
          status: 0,
          createDate: new Date(),
          orgId: testUser.currentOrg,
          product: ProductCodeEnums.Pro,
          uniqueHashkey: 'test-key',
          companyMetricsHashkey: 'test-metrics-key',
          metricsType: MetricTypeEnums.MonitorBusinessMetric,
          diligenceId: 1,
          diligenceScore: 75,
          diligenceResult: 1,
          batchId: 1,
          preBatchId: 0,
        },
      ],
      total: 1,
      pageSize: 10,
      pageIndex: 1,
      aggsResponse: {
        '1_riskLevel': {
          buckets: [
            { key: 1, doc_count: 5 },
            { key: 2, doc_count: 3 },
          ],
        },
        '2_metrics': {
          buckets: [
            { key: 1, doc_count: 4, metricsName: { buckets: [{ key: '测试指标1' }] } },
            { key: 2, doc_count: 4, metricsName: { buckets: [{ key: '测试指标2' }] } },
          ],
        },
        '3_company': {
          buckets: [{ key: '84c17a005a759a5e0d875c1ebb6c9846', doc_count: 8 }],
        },
      },
    });

    // 准备测试数据
    const postData = Object.assign(new RelatedCompanyChartRequest(), {
      groupId: 1,
    });

    // 执行测试
    const result = await monitorService.searchRelatedChartDynamics(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
  });

  it('测试 dynamicChartAnalyzeForXmind 方法', async () => {
    // 准备测试数据
    const postData = Object.assign(new DynamicChartXmindAggsRequest(), {
      groupId: 1,
    });

    // 模拟 findMetrics 方法返回数据
    jest.spyOn(monitorService as any, 'findMetrics').mockResolvedValue([{ metricsId: 1, name: '测试指标1' }]);

    // 执行测试
    const result = await monitorService.dynamicChartAnalyzeForXmind(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result.relatedTypeAggs.length).toBe(1);
    expect(result.relatedTypeAggs[0].metricIdAggs[0].metricsName).toBe('测试指标1');
    expect(dynamicEsService.dynamicChartAnalyzeForXmind).toHaveBeenCalledWith(
      expect.objectContaining({
        groupId: 1,
      }),
      testUser,
    );
  });

  it('测试 analyzeSummary 方法', async () => {
    // 准备测试数据
    const postData = Object.assign(new SearchMetricDynamicRequest(), {
      groupId: [1],
    });

    // 模拟 findMetrics 方法返回数据
    jest.spyOn(monitorService as any, 'findMetrics').mockResolvedValue([
      { metricsId: 1, name: '测试指标1' },
      { metricsId: 2, name: '测试指标2' },
    ]);

    // 执行测试
    const result = await monitorService.analyzeSummary(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result.metricsIds).toEqual([1, 2]);
    expect(result.metricEntities.length).toBe(2);
    expect(dynamicEsService.analyzeSummary).toHaveBeenCalledWith(
      expect.objectContaining({
        groupId: [1],
      }),
      testUser,
    );
  });

  it('测试 searchDynamics 方法 - 带有过滤条件', async () => {
    // 准备更复杂的测试数据，包含过滤条件
    const postData = Object.assign(new SearchMetricDynamicRequest(), {
      pageSize: 10,
      pageIndex: 1,
      groupId: [1],
      riskLevels: [1, 2],
      metricsIds: [1],
      companyIds: ['84c17a005a759a5e0d875c1ebb6c9846'],
      createDate: {
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-12-31'),
      },
    });

    // 执行测试
    const result = await monitorService.searchDynamics(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(dynamicEsService.searchDynamics).toHaveBeenCalledWith(
      expect.objectContaining({
        pageSize: 10,
        pageIndex: 1,
        groupId: [1],
        riskLevels: [1, 2],
        metricsIds: [1],
        companyIds: ['84c17a005a759a5e0d875c1ebb6c9846'],
        createDate: expect.anything(),
        product: testUser.currentProduct,
        orgId: testUser.currentOrg,
      }),
    );
  });

  it('测试 dynamicCardAggs 方法', async () => {
    // 准备测试数据
    const postData = Object.assign(new SearchMetricDynamicRequest(), {
      groupId: [1],
      aggsField: [1, 2, 3],
    });

    // 执行测试
    const result = await monitorService.dynamicCardAggs(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result['1_riskLevel']).toBeDefined();
    expect(result['2_monitorCompanyCardTotal']).toBe(10);
    expect(dynamicEsService.searchDynamics).toHaveBeenCalledWith(
      expect.objectContaining({
        groupId: [1],
        aggsField: [1, 2, 3],
        pageSize: 0,
        product: testUser.currentProduct,
        orgId: testUser.currentOrg,
      }),
    );
  });

  it('测试 dynamicCardAggs 方法 - 不包含 aggsField 2', async () => {
    // 准备测试数据 - 不包含 aggsField 2
    const postData = Object.assign(new SearchMetricDynamicRequest(), {
      groupId: [1],
      aggsField: [1, 3],
    });

    // 模拟searchDynamics返回没有包含2_monitorCompanyCardTotal的数据
    jest.spyOn(dynamicEsService, 'searchDynamics').mockResolvedValueOnce({
      data: [],
      total: 0,
      pageSize: 0,
      pageIndex: 1,
      aggsResponse: {
        '1_riskLevel': {
          buckets: [
            { key: 1, doc_count: 5 },
            { key: 2, doc_count: 3 },
          ],
        },
        '3_company': {
          buckets: [{ key: '84c17a005a759a5e0d875c1ebb6c9846', doc_count: 8 }],
        },
      },
    });

    // 执行测试
    const result = await monitorService.dynamicCardAggs(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result['1_riskLevel']).toBeDefined();
    expect(result['2_monitorCompanyCardTotal']).toBeUndefined();
    expect(dynamicEsService.searchDynamics).toHaveBeenCalledWith(
      expect.objectContaining({
        groupId: [1],
        aggsField: [1, 3],
        pageSize: 0,
        product: testUser.currentProduct,
        orgId: testUser.currentOrg,
      }),
    );
  });

  it('测试 dynamicChartAnalyzeForHighRisk 方法', async () => {
    // 准备测试数据，符合 TodayHighRiskDynamicsAggsRequest 类型
    const postData = new TodayHighRiskDynamicsAggsRequest();
    postData.filter = new SearchMetricDynamicRequest();
    postData.filter.groupId = [1];
    postData.xAxis = [1, 2];
    postData.yAxis = [3, 4];

    // 执行测试
    const result = await monitorService.dynamicChartAnalyzeForHighRisk(postData, testUser);

    // 验证结果
    expect(result).not.toBeNull();
    expect(result.items.length).toBe(2);
    expect(result.items[0].xAxis).toBe(1);
    expect(result.items[0].yAxis).toBe(2);
    expect(result.items[0].count).toBe(5);
    expect(dynamicEsService.dynamicChartAnalyzeForHighRisk).toHaveBeenCalledWith(
      expect.objectContaining({
        filter: expect.objectContaining({
          groupId: [1],
        }),
        xAxis: [1, 2],
        yAxis: [3, 4],
      }),
      testUser,
    );
  });

  it.skip('测试 findMetrics 方法', async () => {
    // 清除之前的所有模拟
    jest.clearAllMocks();

    // 准备测试数据 - 实际测试中需要的指标ID
    const metricsIds = [1, 2, 3];

    // 模拟 metricsRepo.find 方法的返回值
    // 注意这里应该与 findMetrics 方法中的 select 选项一致
    const mockMetricsEntities = [
      { metricsId: 1, name: '测试指标1' },
      { metricsId: 2, name: '测试指标2' },
      { metricsId: 3, name: '测试指标3' },
    ];

    // 更准确地模拟 find 方法
    jest.spyOn(monitorService['metricsRepo'], 'find').mockImplementation(async (options: any) => {
      // 验证查询条件是否正确
      expect(options.where.metricsId).toBeDefined();
      expect(options.select).toEqual(['metricsId', 'name']);

      // 返回模拟实体
      return mockMetricsEntities as MetricsEntity[];
    });

    // 直接调用私有方法进行测试
    const result = await (monitorService as any).findMetrics(metricsIds);

    // 验证结果
    expect(result).toEqual(mockMetricsEntities);
    expect(monitorService['metricsRepo'].find).toHaveBeenCalledWith({
      where: {
        metricsId: In(metricsIds),
      },
      select: ['metricsId', 'name'],
    });
  });
});
