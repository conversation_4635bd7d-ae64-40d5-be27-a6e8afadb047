import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { PlatformUser } from '../../../libs/model/common';
import {
  DynamicChartXmaindAggsResponse,
  DynamicChartXmindAggsRequest,
  GetMonitorDynamicRemarkRequest,
  HighRiskChartAggsResponse,
  MetricDynamicSummaryResponse,
  MonitorDynamicHandleRequest,
  QueryMonitorDynamicDetialsRequest,
  QueryRelatedHashKeyRequest,
  SearchMetricDynamicRequest,
  SearchMonitorCompanyResponse,
  TodayHighRiskDynamicsAggsRequest,
} from '../../../libs/model/monitor';
import { MonitorDynamicService } from './monitor.dynamic.service';
import { MetricDynamicsCardAggsResponse } from '../../../libs/model/monitor/MetricDynamicsCardAggsResponse';
// import { MonitorDynamicMessageListener } from './monitor.dynamic.message.listener';
import { ApiGuardExternal } from '../../../libs/guards/api.guard.external';
import { MonitorDynamicRemarkService } from './monitor.dynamic.remark.service';
import { RiskViewResponse } from '../../../libs/model/monitor/response/RiskViewResponse';
import { RelatedCompanyChartRequest } from '../../../libs/model/monitor/RelatedCompanyChartRequest';
import { RelatedCompanyChartResponse } from '../../../libs/model/monitor/RelatedCompanyChartResponse';
import { MonitorDynamicDetailsService } from './process/services/monitor-dynamic-details.service';

@Controller('monitor/external/dynamic')
@ApiTags('监控动态')
@ApiBearerAuth()
@UseGuards(ApiGuardExternal)
export class MonitorDynamicController {
  constructor(
    private readonly monitorDynamicService: MonitorDynamicService,
    // private readonly dynamicMessageListener: MonitorDynamicMessageListener,
    private readonly dynamicDetailsService: MonitorDynamicDetailsService,
    private readonly dynamicRemarkService: MonitorDynamicRemarkService,
  ) {}

  @Post('search')
  @ApiOperation({ description: '查询监控动态' })
  @ApiOkResponse({ type: SearchMonitorCompanyResponse })
  async searchDynamic(@Body() postData: SearchMetricDynamicRequest, @Req() req: any) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.searchDynamics(postData, currentUser);
  }

  @Post('search/related/chart')
  @ApiOperation({ description: '查询关联方图表' })
  @ApiOkResponse({ type: RelatedCompanyChartResponse })
  async searchRelatedChartDynamics(@Body() postData: RelatedCompanyChartRequest, @Req() req: any) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.searchRelatedChartDynamics(postData, currentUser);
  }

  @Post('content/search')
  @ApiOperation({ description: '查询指标指定策略id的动态内容列表' })
  @ApiOkResponse({ type: SearchMonitorCompanyResponse })
  async searchDynamicContent(@Body() postData: QueryMonitorDynamicDetialsRequest, @Req() req: any) {
    const currentUser: PlatformUser = req.user;
    postData.orgId = currentUser.currentOrg;
    return this.dynamicDetailsService.fatchDimensionHitDetails(postData);
  }

  @Post('aggs/card')
  @ApiOperation({ description: '动态监控分析-头部小卡片聚合' })
  @ApiOkResponse({ type: MetricDynamicsCardAggsResponse })
  async dynamicCardAggs(@Body() requestData: SearchMetricDynamicRequest, @Req() req) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.dynamicCardAggs(requestData, currentUser);
  }

  @Post('chart/summary')
  @ApiOperation({ description: '获取动态的概要分析' })
  @ApiOkResponse({ type: MetricDynamicSummaryResponse })
  async dynamicChartAggs(@Body() requestData: SearchMetricDynamicRequest, @Req() req) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.analyzeSummary(requestData, currentUser);
  }

  @Post('chart/highRisk')
  @ApiOperation({ description: '今日高风险动态分析' })
  @ApiOkResponse({ type: HighRiskChartAggsResponse })
  async dynamicAnalyzeHighRisk(@Body() requestData: TodayHighRiskDynamicsAggsRequest, @Req() req) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.dynamicChartAnalyzeForHighRisk(requestData, currentUser);
  }

  @Post('chart/xmind')
  @ApiOperation({ description: '公司-风险动态xmind图标聚合' })
  @ApiOkResponse({ type: DynamicChartXmaindAggsResponse })
  async dynamicAnalyzeXmind(@Body() requestData: DynamicChartXmindAggsRequest, @Req() req) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.dynamicChartAnalyzeForXmind(requestData, currentUser);
  }

  @Post('searchDynamicHashKey')
  async searchHashKey(@Body() requestData: QueryRelatedHashKeyRequest, @Req() req) {
    const currentUser: PlatformUser = req.user;
    return this.monitorDynamicService.queryRelatedHashKey(currentUser, requestData);
  }

  @ApiOperation({ description: '动态处理' })
  @Post('handle')
  @ApiOkResponse({ type: RiskViewResponse })
  public async dynamicHandle(@Req() req, @Body() body: MonitorDynamicHandleRequest) {
    const currentUser: PlatformUser = req.user;
    return this.dynamicRemarkService.dynamicHandle(currentUser, body);
  }

  @ApiOperation({ description: '查询动态的处理记录' })
  @Post('remark')
  @ApiOkResponse({ type: RiskViewResponse })
  public async getDynamicRemark(@Req() req, @Body() body: GetMonitorDynamicRemarkRequest) {
    const currentUser: PlatformUser = req.user;
    return this.dynamicRemarkService.getDynamicRemark(currentUser, body);
  }
}
