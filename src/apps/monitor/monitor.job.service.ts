import { Injectable } from '@nestjs/common';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { InjectRepository } from '@nestjs/typeorm';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { In, Repository } from 'typeorm';
import { QueueService } from '../../libs/config/queue.service';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import * as Bluebird from 'bluebird';
import { MonitorStatusEnums } from '../../libs/enums/monitor/MonitorStatusEnums';
import { ProductCodeEnums } from '../../libs/enums/ProductCodeEnums';
import { PlatformUser } from '../../libs/model/common';
import { UserService } from '../user/user.service';
import { captureException } from '@sentry/node';
import { MonitorCompanyMessagePO } from './po/MonitorCompanyMessagePO';
import { BatchEntity } from '../../libs/entities/BatchEntity';
import { MonitorCompanyEntity } from '../../libs/entities/MonitorCompanyEntity';
import { BatchInfoPO } from '../batch/model/BatchInfoPO';
import * as moment from 'moment/moment';
import { BatchCreatorHelperBase } from '../batch/service/helper/batch.creator.helper.base';
import { BatchBusinessTypeEnums } from '../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchTypeEnums } from '../../libs/enums/batch/BatchTypeEnums';
import { BatchStatusEnums } from '../../libs/enums/batch/BatchStatusEnums';
import { ParsedRecordBase } from '../../libs/model/batch/po/parse/ParsedRecordBase';
import { BatchBaseHelper } from '../batch/service/helper/batch.base.helper';
import { MonitorMetricsDynamicEntity } from '../../libs/entities/MonitorMetricsDynamicEntity';
import { MetricDynamicStatusEnums } from '../../libs/enums/metric/MetricDynamicStatusEnums';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { BatchDiligenceEntity } from '../../libs/entities/BatchDiligenceEntity';
import { MonitorDynamicEsService } from './dynamic/monitor.dynamic.es.service';
import { RemoveDynamicDocPO } from '../../libs/model/monitor/po/RemoveDynamicDocPO';
import { DiligenceSnapshotEsService } from '../diligence/snapshot/diligence.snapshot.es.service';
import { RemoveSnapshotDiligenceDataPO } from '../diligence/snapshot/po/RemoveSnapshotDataPO';
import { DimensionTypeEnums } from '../../libs/enums/diligence/DimensionTypeEnums';
import * as _ from 'lodash';
import { MonitorCompanyRelatedPartyEntity } from '../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { MonitorCompanyService } from './company/monitor.company.service';

@Injectable()
export class MonitorJobService {
  private readonly continuousDiligenceQueue: RabbitMQ;
  private readonly logger: Logger = QccLogger.getLogger(MonitorJobService.name);

  constructor(
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    @InjectRepository(MonitorCompanyEntity) private readonly monitorCompanyRepo: Repository<MonitorCompanyEntity>,
    @InjectRepository(MonitorMetricsDynamicEntity) private readonly monitorMetricsDynamicRepo: Repository<MonitorMetricsDynamicEntity>,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    private readonly queueService: QueueService,
    private readonly userService: UserService,
    private readonly batchCreatorHelperService: BatchCreatorHelperBase,
    private readonly batchBaseHelperService: BatchBaseHelper,
    private readonly monitorDynamicEsService: MonitorDynamicEsService,
    private readonly diligenceSnapshotEsService: DiligenceSnapshotEsService,
    private readonly monitorCompanyService: MonitorCompanyService,
  ) {
    this.continuousDiligenceQueue = this.queueService.continuousDiligenceQueue;
    this.continuousDiligenceQueue.consume(this.startBatchForGroup.bind(this));
  }

  /**
   *
   * 删除已经不存在的企业的监控动态产生的垃圾数据
   * batch  （不能删）
   * batch_job （不能删）
   * batch_result （不能删）
   *
   * dilligence 维度即企业维度，数据可以删
   * due_dilligence
   * batch_dilligence
   * monitor_metrics_dynamic
   * ES 状态为MetricDynamicStatusEnums.Deprecated
   * ES kys_metric_dynamics_test
   * ES kys_snapshot_test 尽调子文档，根据snapshotId删除 dimensionStrategy子文档，diligence子文档
   *    父文档上都没有子文档挂载的数据，父文档也同步删除
   * 2898
   * 2268bc8da3432993d4fe97c843576455
   *
   */
  public async removeDeprecatedMonitorDataJob(monitorGroupId?: number, companyId?: string) {
    this.logger.info(`cleanUpDeprecatedMonitorDataJob(monitorGroupId =${monitorGroupId},companyId=${companyId}), 找到所有已经废弃的监控动态数据并删除`);
    try {
      const pageSize = 50;
      let pageIndex = 1;
      const qb = this.monitorMetricsDynamicRepo
        .createQueryBuilder('monitorMetricsDynamic')
        .where('monitorMetricsDynamic.status = :status', { status: MetricDynamicStatusEnums.Deprecated });
      //qb.andWhere('monitorMetricsDynamic.createDate > :createDate', { createDate: moment().subtract(7, 'days').toDate() }); // 创建时间是近7天的数据
      if (monitorGroupId > 0) {
        qb.andWhere('monitorMetricsDynamic.monitorGroupId =:monitorGroupId', { monitorGroupId });
      }
      if (companyId) {
        qb.andWhere('monitorMetricsDynamic.companyId =:companyId', { companyId });
      }
      qb.orderBy('monitorMetricsDynamic.id', 'DESC');
      do {
        const monitorMetricsDynamics = await qb
          .skip((pageIndex - 1) * pageSize)
          .take(pageSize)
          .getMany();
        this.logger.info(`cleanUpDeprecatedMonitorDataJob(), 找到${monitorMetricsDynamics.length}个需要废弃的监控动态数据`);
        if (monitorMetricsDynamics?.length) {
          // monitorMetricsDynamics 中的数据以diligenceId分组
          const json: { [key: string]: MonitorMetricsDynamicEntity[] } = {};
          monitorMetricsDynamics.forEach((monitorMetricsDynamic) => {
            json[monitorMetricsDynamic.diligenceId] = json[monitorMetricsDynamic.diligenceId] || [];
            json[monitorMetricsDynamic.diligenceId].push(monitorMetricsDynamic);
          });
          await Bluebird.map(Object.keys(json), async (diligenceId) => {
            const monitorMetricsDynamicList = json[diligenceId]; // 同一个尽调记录产生的数据肯定是同一家企业的数据
            const uniqueHashkeyList = monitorMetricsDynamicList.map((i) => i.uniqueHashkey);
            let snapShortdimensionKeys: string[] = [];
            const diligenceHistory = await this.diligenceHistoryRepo.findOne({ where: { id: parseInt(diligenceId) } });
            if (diligenceHistory?.snapshotDetails?.status === 1) {
              snapShortdimensionKeys = _.uniq(diligenceHistory.snapshotDetails?.successHits.map((i) => i.split('_')[0]));
            }
            await this.monitorGroupRepo.manager.transaction(async (manager) => {
              await manager.delete(DiligenceHistoryEntity, { id: parseInt(diligenceId) });
              await manager.delete(BatchDiligenceEntity, { diligenceId: parseInt(diligenceId) });
              await manager.delete(MonitorMetricsDynamicEntity, { id: In(monitorMetricsDynamicList.map((t) => t.id)) });
            });
            // 1. 快照_ES
            if (uniqueHashkeyList?.length) {
              await this.monitorDynamicEsService.deleteDynamicDoc(Object.assign(new RemoveDynamicDocPO(), { uniqueHashkeys: uniqueHashkeyList }));
            }
            // 2. 动态_ES, 删除diligence，删除dimensionStrategy子文档
            if (snapShortdimensionKeys?.length) {
              await this.diligenceSnapshotEsService.removeSnapshotDiligenceData(
                Object.assign(new RemoveSnapshotDiligenceDataPO(), {
                  diligenceId: parseInt(diligenceId),
                  dimensionKeys: snapShortdimensionKeys as DimensionTypeEnums[],
                  orgId: monitorMetricsDynamicList[0].orgId,
                }),
              );
            }
            // 3. TODO 判断没有子文档的数据也同步删除
          });
        }
        if (monitorMetricsDynamics?.length < pageSize) {
          break;
        }
        pageIndex++;
      } while (true);
    } catch (e) {
      this.logger.error('cleanUpDeprecatedMonitorDataJob(), 开启废弃动态数据删除任务失败');
      this.logger.error(e);
      captureException(new Error(`monitorGroupDynamicJob() 风险洞察-开启废弃动态数据删除任务失败`), {
        extra: { message: e.message },
      });
    }
  }

  /**
   *  对开启了监控的监控分组 执行监控分析
   * @param monitorGroupId 指定分组 0-全部
   * @param interval  本次监控任务查询数据的时间区间，
   * @param intervalUnit 时间区间的单位 'year' | 'month' | 'day' | 'hour'
   */
  public async monitorGroupDynamicJob(monitorGroupId = 0, interval = 1, intervalUnit = 'day') {
    this.logger.info(
      `monitorGroupDynamicJob(monitorGroupId =${monitorGroupId},interval=${interval},intervalUnit=${intervalUnit}), 找到所有开启了监控的监控分组并启动监控任务`,
    );

    const products = [ProductCodeEnums.Pro];
    try {
      await Bluebird.map(products, async (product) => {
        const pageSize = 100;
        let pageIndex = 1;
        const qb = this.monitorGroupRepo
          .createQueryBuilder('group')
          .where('group.monitorStatus = :monitorStatus', { monitorStatus: MonitorStatusEnums.Enabled })
          .andWhere('group.orgId > 1'); // 确保BO里创建的用于QA的临时监控分组不会被扫描
        if (monitorGroupId > 0) {
          qb.andWhere('group.monitorGroupId =:monitorGroupId', { monitorGroupId });
        }
        do {
          const groups = await qb
            .skip((pageIndex - 1) * pageSize)
            .take(pageSize)
            .getMany();

          this.logger.info(`monitorGroupDynamicJob(), 找到${groups.length}个监控分组需要开启监控任务`);
          const json: { [key: string]: MonitorGroupEntity[] } = {};
          groups.forEach((group) => {
            json[group.orgId] = json[group.orgId] || [];
            json[group.orgId].push(group);
          });
          await Bluebird.map(
            Object.keys(json),
            async (orgId) => {
              const adminUsers: any[] = await this.userService.getAdminList([parseInt(orgId)], product);
              if (!adminUsers?.[0]?.userId) {
                this.logger.error('monitorGroupDynamicJob(), 未找到adminUsers');
                return;
              }
              const adminUser: PlatformUser = Object.assign(new PlatformUser(), {
                currentProduct: product,
                currentOrg: orgId,
                userId: adminUsers[0].userId,
              });
              await Bluebird.map(
                json[orgId],
                async (monitorGroup) => {
                  this.continuousDiligenceQueue.sendMessageV2(
                    {
                      orgId: orgId,
                      product,
                      monitorGroupId: monitorGroup.monitorGroupId,
                      currentUser: adminUser,
                      interval,
                      intervalUnit,
                    },
                    {},
                  );
                },
                { concurrency: 5 },
              );
            },
            { concurrency: 5 },
          );
          if (groups?.length < pageSize) {
            break;
          }
          pageIndex++;
        } while (true);
      });
    } catch (e) {
      this.logger.error('monitorGroupDynamicJob(), 开启监控任务失败');
      this.logger.error(e);
      captureException(new Error(`monitorGroupDynamicJob() 风险洞察-监控定时任务启动失败`), {
        extra: { message: e.message },
      });
    }
  }

  /**
   * 扫描动态
   * @param msgPO: MonitorCompanyMessagePO
   * @param orgModelIds: 不使用 monitorGroup绑定的orgModelId，直接指定监控模型id
   */
  async startBatchForGroup(msgPO: MonitorCompanyMessagePO, orgModelIds?: number[], refQaTaskId?: number): Promise<BatchEntity> {
    const { orgId, monitorGroupId, product, currentUser, interval, intervalUnit } = msgPO;
    this.logger.info(`startBatchForGroup: ${JSON.stringify(msgPO)}`);
    const [monitorCompanyCount, monitorGroupEntity] = await Bluebird.all([
      this.monitorCompanyRepo.count({ where: { orgId, product, monitorGroupId } }),
      this.monitorGroupRepo.findOne({
        where: {
          orgId,
          product,
          monitorGroupId,
          monitorStatus: MonitorStatusEnums.Enabled,
        },
      }),
    ]);

    if (monitorCompanyCount <= 0) {
      this.logger.info(`startBatchForGroup monitorCompanyCount: ${monitorCompanyCount}`);
      return;
    }
    const batchInfo: BatchInfoPO = {
      asyncBatch: 1, // 标记为异步启动批次，需要后续手动启动
      orgModelIds: orgModelIds || (monitorGroupEntity?.monitorModelId ? [monitorGroupEntity?.monitorModelId] : []),
      cacheHours: 0,
      currentUser,
      monitorGroupId,
      batchStartTime: moment().subtract(interval, intervalUnit).unix(), // 设置动态时间区间范围
      batchEndTime: moment().unix(),
      refQaTaskId,
    };
    this.logger.info(`startBatchForGroup createBatchEntity : ${JSON.stringify(batchInfo)}`);
    let batchEntity = await this.batchCreatorHelperService.createBatchEntity(
      currentUser,
      {
        succeedItems: [],
        failedItems: [],
      },
      BatchBusinessTypeEnums.Diligence_Continuous, //风险监控(持续尽调)
      '',
      '',
      BatchTypeEnums.ContinuousDiligence, //持续尽调
      batchInfo,
      true,
    );
    this.logger.info(`batch(batchId=${batchEntity.batchId}) created for monitor...`);
    if (!monitorGroupEntity?.monitorModelId) {
      //如果没有绑定监控模型， 标记batch失败
      await this.batchCreatorHelperService.changeBatchStatusBeforeStart(
        batchEntity.batchId,
        BatchStatusEnums.Error,
        `当前分组${monitorGroupId}没有指定的监控模型`,
      );
      return;
    }

    try {
      const pageSize = 500;
      let pageIndex = 0;

      let fetched = 0;
      const qb1 = this.monitorCompanyRepo.createQueryBuilder('monitorCompany').where('monitorCompany.monitorGroupId = :monitorGroupId', { monitorGroupId });
      const total = await qb1.getCount();
      qb1.take(pageSize);
      if (total > 0) {
        do {
          const items = await qb1.skip(pageIndex * pageSize).getMany();
          fetched += items.length;
          const relateds = await this.monitorCompanyRepo.manager.find(MonitorCompanyRelatedPartyEntity, {
            where: { monitorGroupId, companyIdRelated: In(items.map((i) => i.companyId)) },
          });
          const toAddItems: ParsedRecordBase[] = items.map((i) => {
            let isRelated = i.primaryObject === 0;
            if (!isRelated && relateds?.find((r) => r.companyIdRelated === i.companyId)) {
              isRelated = true;
            }
            return {
              companyName: i.companyName,
              companyId: i.companyId,
              isRelated,
            };
          });
          //每次扫描的企业动态添加到batch任务里面去
          await this.batchCreatorHelperService.upsertJobsToBatch(toAddItems, batchEntity);
          pageIndex++;
        } while (fetched < total);
      }
      this.logger.info(`scan ${fetched} jobs and add to batch(batchId=${batchEntity.batchId})`);
      const messageResponse = await this.batchBaseHelperService.sendBatchMonitorMessage(batchEntity, currentUser);
      this.logger.info(`batch(batchId=${batchEntity.batchId}) monitor message sent..., response=${messageResponse}`);
      batchEntity = await this.batchRepo.findOne(batchEntity.batchId);
    } catch (e) {
      this.logger.error(e);
      await this.batchCreatorHelperService.changeBatchStatusBeforeStart(
        batchEntity.batchId,
        BatchStatusEnums.Error,
        `scanDynamic() 报错: ${e.message?.substr(0, 200)}`,
      );
    }
    return batchEntity;
  }

  async saveCompanyRelatedDailyJob(monitorGroupId = 0) {
    this.logger.info(`saveCompanyRelatedDailyJob(monitorGroupId =${monitorGroupId}), 找到所有开启了监控的监控分组并给分组内的主体企业记录关联方`);

    const products = [ProductCodeEnums.Pro];
    try {
      // await Bluebird.map(products, async (product) => {
      const pageSize = 100;
      let pageIndex = 1;
      const qb = this.monitorGroupRepo
        .createQueryBuilder('group')
        .where('group.monitorStatus = :monitorStatus', { monitorStatus: MonitorStatusEnums.Enabled })
        .andWhere('group.orgId > 1'); // 确保BO里创建的用于QA的临时监控分组不会被扫描
      if (monitorGroupId > 0) {
        qb.andWhere('group.monitorGroupId =:monitorGroupId', { monitorGroupId });
      }
      do {
        const groups = await qb
          .skip((pageIndex - 1) * pageSize)
          .take(pageSize)
          .getMany();

        this.logger.info(`saveCompanyRelatedDailyJob(), 找到${groups.length}个监控分组需要开启监控任务`);
        await Bluebird.map(
          groups,
          async (group) => {
            await this.monitorCompanyService.saveCompanyRelatedDailyFroGroup(group.orgId, group.monitorGroupId);
          },
          { concurrency: 5 },
        );
        if (groups?.length < pageSize) {
          break;
        }
        pageIndex++;
      } while (true);
      // });
    } catch (e) {
      this.logger.error('saveCompanyRelatedDailyJob(), 开启监控任务失败');
      this.logger.error(e);
      captureException(new Error(`saveCompanyRelatedDailyJob() 风险洞察-监控定时任务启动失败`), {
        extra: { message: e.message },
      });
    }
  }
}
