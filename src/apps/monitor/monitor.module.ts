import { forwardRef, Module } from '@nestjs/common';
// import { MonitorController } from './monitor.controller';
// import { MonitorService } from './monitor.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { MonitorCompanyEntity } from '../../libs/entities/MonitorCompanyEntity';
import { BatchModule } from '../batch/batch.module';
import { MonitorJobService } from './monitor.job.service';
import { MonitorMetricsDynamicEntity } from '../../libs/entities/MonitorMetricsDynamicEntity';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { BatchEntity } from '../../libs/entities/BatchEntity';
import { CompanySearchModule } from '../company/company-search.module';
import { RiskModelModule } from '../risk_model/risk_model.module';
import { CompanyEntity } from '../../libs/entities/CompanyEntity';
// import { MonitorExternalController } from './monitor.external.controller';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { MetricsEntity } from '../../libs/entities/MetricsEntity';
import { DiligenceModule } from '../diligence/diligence.module';
import { DimensionHitStrategyEntity } from '../../libs/entities/DimensionHitStrategyEntity';
import { UserModule } from '../user/user.module';
// import { MonitorDynamicMessageListener } from './dynamic/monitor.dynamic.message.listener';
import { MonitorDynamicService } from './dynamic/monitor.dynamic.service';
import { MonitorDynamicEsService } from './dynamic/monitor.dynamic.es.service';
// import { MonitorDynamicRemarkController } from './dynamic/monitor.dynamic.remark.controller';
import { MonitorDynamicRemarkService } from './dynamic/monitor.dynamic.remark.service';
import { MonitorDynamicsRemarkEntity } from '../../libs/entities/MonitorDynamicsRemarkEntity';
import MyOssService from '../basic/my-oss.service';
import { MonitorCompanyRelatedPartyEntity } from '../../libs/entities/MonitorCompanyRelatedPartyEntity';
import { PushModule } from '../push/push.module';
import { MetricModule } from '../metric/metric.module';
import { PushRuleEntity } from '../../libs/entities/PushRuleEntity';
import { DataModule } from '../data/data.module';
import { MessageEntity } from '../../libs/entities/MessageEntity';
import { MonitorGroupController } from './group/monitor.group.controller';
import { MonitorCompanyController } from './company/monitor.company.controller';
import { MonitorGroupService } from './group/monitor.group.service';
import { MonitorDynamicController } from './dynamic/monitor.dynamic.controller';
import { MonitorCompanyService } from './company/monitor.company.service';
import { DistributedSystemResourceEntity } from '../../libs/entities/DistributedSystemResourceEntity';
import { BatchDiligenceEntity } from '../../libs/entities/BatchDiligenceEntity';
import { MonitorGroupUserEntity } from '../../libs/entities/MonitorGroupUserEntity';
import { MonitorDynamicProcessModule } from './dynamic/process/monitor-dynamic-process.module';
@Module({
  controllers: [MonitorGroupController, MonitorCompanyController, MonitorDynamicController],
  providers: [
    // MonitorService,
    MonitorCompanyService,
    MonitorGroupService,
    MonitorJobService,
    MonitorDynamicService,
    MonitorDynamicEsService,
    MonitorDynamicRemarkService,
    MyOssService,
  ],
  exports: [MonitorCompanyService, MonitorGroupService, MonitorJobService, MonitorDynamicEsService],
  imports: [
    TypeOrmModule.forFeature([
      DimensionHitStrategyEntity,
      MetricsEntity,
      MonitorGroupEntity,
      MonitorCompanyEntity,
      MonitorMetricsDynamicEntity,
      DiligenceHistoryEntity,
      BatchEntity,
      CompanyEntity,
      RiskModelEntity,
      MonitorDynamicsRemarkEntity,
      MonitorCompanyRelatedPartyEntity,
      PushRuleEntity,
      MessageEntity,
      DistributedSystemResourceEntity,
      BatchDiligenceEntity,
      MonitorGroupUserEntity,
    ]),
    forwardRef(() => BatchModule),
    CompanySearchModule,
    RiskModelModule,
    DiligenceModule,
    UserModule,
    PushModule,
    MetricModule,
    DataModule,
    MonitorDynamicProcessModule,
  ],
})
export class MonitorModule {}
