import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { HitDimensionService } from './hit.dimension.service';
import { ModelScorePO } from 'libs/model/diligence/ModelScorePO';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { UpdateDiligenceHitsPO } from '../../../libs/model/diligence/UpdateDiligenceHitsPO';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { calculateScore, reCalculateScore, updateDimensionHits } from './model.score.utils';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { DimensionFilterParams } from '../../../libs/model/diligence/dimension/DimensionStrategyPO';

@Injectable()
export class RiskScoreService {
  private readonly logger = QccLogger.getLogger(RiskScoreService.name);

  constructor(
    protected readonly hitDimensionService: HitDimensionService,
    @InjectRepository(DiligenceHistoryEntity) protected readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
  ) {}

  /**
   * 修改排查记录命中情况和打分
   * @param diligenceId
   * @param changes
   */
  async refreshScoreDetails(diligenceId: number, changes: UpdateDiligenceHitsPO[]): Promise<ModelScorePO> {
    try {
      const dbDiligence: DiligenceHistoryEntity = await this.diligenceHistoryRepo.findOne(diligenceId);
      const { orgId, details } = dbDiligence;

      //修改维度的hits
      updateDimensionHits(details, changes);

      //计算最终分数
      const updatedScorePO: ModelScorePO = reCalculateScore(details);
      const partialUpdate = {
        result: updatedScorePO.result,
        details: updatedScorePO,
        updateDate: new Date(),
      };

      await this.diligenceHistoryRepo.update(diligenceId, partialUpdate);
      return updatedScorePO;
    } catch (error) {
      this.logger.error(error);
    }
  }

  /**
   * 执行排查操作
   * @param orgId
   * @param companyId
   * @param companyName
   * @param orgSetting
   */
  @TraceLog({ throwError: true })
  async getScore(companyId: string, companyName: string, orgModel: RiskModelEntity, dimensionFilter?: DimensionFilterParams): Promise<ModelScorePO> {
    // 获取模型中指标涉使用到的维度命中情况
    const { allHits, timeCost } = await this.hitDimensionService.getDimensionHitResult(companyId, companyName, orgModel, dimensionFilter);

    // 根据模型中指标命中策略，对命中维度进行打分处理，生成排查结果对象
    return calculateScore(allHits, orgModel, timeCost);
  }

  /**
   * 重新计算排查结果(用户手动修改了排查结果)
   * @param orgSetting
   * @param modelScorePO
   * @returns
   */
  async reScore(modelScorePO: ModelScorePO) {
    // const groupDefinitions: DimensionGroupDefinitionType = await this.settingService.getDimensionGroupDefinition(orgSetting);
    return reCalculateScore(modelScorePO);
  }
}
