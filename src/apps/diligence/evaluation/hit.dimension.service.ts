import { Injectable } from '@nestjs/common';
import { EnterpriseLibApiSource } from '../../data/source/enterprise-lib-api.source';
import { CreditApiSource } from '../../data/source/credit-api.source';
import { CompanySearchService } from '../../company/company-search.service';
import { TenderApiSource } from '../../data/source/tender-api.source';
import { CompanyApiSource } from '../../data/source/company-api.source';
import * as Bluebird from 'bluebird';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { CreditEsSource } from '../../data/source/credit-es.source';
import { DiligenceTimeCostPO } from 'libs/model/diligence/dimension/DiligenceTimeCostPO';
import { CaseSource } from '../../data/source/case.source';
import { RoverExceptions } from '../../../libs/exceptions/exceptionConstants';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { NegativeNewsSource } from 'apps/data/source/negative-news.source';
import { JudgementSource } from '../../data/source/judgement.source';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { PledgeSource } from '../../data/source/pledge.source';
import { RiskChangeEsSource } from '../../data/source/risk-change/risk-change-es.source';
import { OuterBlacklistSource } from '../../data/source/outer-blacklist.source';
import { SupervisePunishEsSource } from '../../data/source/supervise-punish-es.source';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { InactiveDataStatus } from '../../../libs/enums/DataStatusEnums';
import { DimensionAllHitPO } from '../../../libs/model/diligence/dimension/DimensionAllHitPO';
import { RelatedCompanySource } from '../../data/source/related-company.source';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { getAllDimensionHitStrategy } from '../../dimension/dimension.helper';
import { ViolationSource } from '../../data/source/violation.source';
import { QccProApiSource } from '../../data/source/qcc-pro-api.source';
import { OvsSanctionsBlacklistSource } from '../../data/source/ovs-sanctions-blacklist.source';
import { DimensionFilterParams } from '../../../libs/model/diligence/dimension/DimensionStrategyPO';
import { AssertESSource } from '../../data/source/asset-es.source';
import { PledgeMergerEsSource } from '../../data/source/pledge-merger-es.source';

@Injectable()
export class HitDimensionService {
  logger: Logger = QccLogger.getLogger(HitDimensionService.name);

  constructor(
    readonly entLibService: EnterpriseLibApiSource,
    readonly creditService: CreditApiSource,
    // readonly roverService: RoverService,
    readonly companySearchService: CompanySearchService,
    readonly tenderService: TenderApiSource,
    readonly companyService: CompanyApiSource,
    readonly creditESService: CreditEsSource,
    readonly relatedCompanyService: RelatedCompanySource,
    readonly caseService: CaseSource,
    readonly negativeNewsService: NegativeNewsSource,
    readonly judgementService: JudgementSource,
    readonly pledgeService: PledgeSource,
    readonly riskChangeService: RiskChangeEsSource,
    readonly outerBlacklistService: OuterBlacklistSource,
    readonly supervisePunishService: SupervisePunishEsSource,
    readonly violationEsService: ViolationSource,
    readonly proService: QccProApiSource,
    readonly ovsSanctionsService: OvsSanctionsBlacklistSource,
    readonly assertESService: AssertESSource,
    readonly pledgeMergerEsService: PledgeMergerEsSource,
  ) {}

  /**
   * 根据模型中所有维度的命中结果
   * @param orgId
   * @param companyId
   * @param companyName
   * @param orgModel
   * @returns allHits: DimensionHitResultPO[], timeCost: DiligenceTimeCostPO
   */
  @TraceLog({ throwError: true })
  async getDimensionHitResult(companyId: string, companyName: string, orgModel: RiskModelEntity, params?: DimensionFilterParams): Promise<DimensionAllHitPO> {
    const timeCost: DiligenceTimeCostPO = Object.assign(new DiligenceTimeCostPO());
    const time1 = Date.now();
    // 将模型打平为一个个独立的风险维度
    const dimHitStrategies: DimensionHitStrategyPO[] = getAllDimensionHitStrategy(orgModel, InactiveDataStatus, params);

    if (!dimHitStrategies?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Setting.Empty);
    }

    const time2 = Date.now();
    // 将需要排查的维度再搜友定义的风险数据源处理器程序中分别排查
    const [
      creditAPIHits,
      creditESHits,
      entHits,
      tenderHits,
      // roverHits,
      esHits,
      caseHits,
      negativeNewsHits,
      judgementHits,
      pledgeHits,
      riskChangeHits,
      outerBlacklistHits,
      supervisePunishHits,
      violationHits,
      relatedHits,
      proHits,
      ovsSanctionHits,
      assertHits,
      pledgeMergerHits,
    ] = await Bluebird.all([
      this.scanCreditAPI(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.credit = Date.now() - time2;
        return r;
      }),
      this.scanCreditES(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.credit = Date.now() - time2;
        return r;
      }),
      this.scanEnterprise(companyId, dimHitStrategies).then((r) => {
        timeCost.ent = Date.now() - time2;
        return r;
      }),
      this.scanTender(companyId, dimHitStrategies).then((r) => {
        timeCost.tender = Date.now() - time2;
        return r;
      }),
      // this.scanRover(companyId, dimHitStrategies).then((r) => {
      //   timeCost.rover = Date.now() - time2;
      //   return r;
      // }),
      this.scanByCompanyEs(companyId, dimHitStrategies).then((r) => {
        timeCost.company = Date.now() - time2;
        return r;
      }),
      this.scanByCaseEs(companyId, dimHitStrategies).then((r) => {
        timeCost.case = Date.now() - time2;
        return r;
      }),
      this.scanByNegativeNewsEs(companyId, dimHitStrategies).then((r) => {
        timeCost.news = Date.now() - time2;
        return r;
      }),
      this.scanByJudgementEs(companyId, dimHitStrategies).then((r) => {
        timeCost.judgement = Date.now() - time2;
        return r;
      }),
      this.scanByPledgeEs(companyId, dimHitStrategies).then((r) => {
        timeCost.pledge = Date.now() - time2;
        return r;
      }),
      this.scanByRiskChangeEs(companyId, dimHitStrategies).then((r) => {
        timeCost.riskChange = Date.now() - time2;
        return r;
      }),
      this.scanOuterBlacklist(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.outerBlacklist = Date.now() - time2;
        return r;
      }),
      this.scanBySupervisePunishEs(companyId, dimHitStrategies).then((r) => {
        timeCost.supervisePunish = Date.now() - time2;
        return r;
      }),
      this.scanByViolationEs(companyId, dimHitStrategies).then((r) => {
        timeCost.violation = Date.now() - time2;
        return r;
      }),
      this.scanRelated(companyId, dimHitStrategies).then((r) => {
        timeCost.related = Date.now() - time2;
        return r;
      }),
      this.scanPro(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.pro = Date.now() - time2;
        return r;
      }),
      this.scanOvsSanction(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.pro = Date.now() - time2;
        return r;
      }),
      this.scanAssert(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.assert = Date.now() - time2;
        return r;
      }),
      this.scanPledgeMerger(companyId, companyName, dimHitStrategies).then((r) => {
        timeCost.assert = Date.now() - time2;
        return r;
      }),
    ]);
    timeCost.total = Date.now() - time1;

    return {
      allHits: [
        ...creditAPIHits,
        ...creditESHits,
        ...entHits,
        ...tenderHits,
        // ...roverHits,
        ...esHits,
        ...caseHits,
        ...negativeNewsHits,
        ...judgementHits,
        ...pledgeHits,
        ...riskChangeHits,
        ...outerBlacklistHits,
        ...supervisePunishHits,
        ...violationHits,
        ...relatedHits,
        ...proHits,
        ...ovsSanctionHits,
        ...assertHits,
        ...pledgeMergerHits,
      ],
      timeCost,
    };
  }

  /**
   * 根据企业库接口扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  public async scanEnterprise(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((d) => d.dimensionDef.source === DimensionSourceEnums.EnterpriseLib);
    if (!targetTypes?.length) {
      return [];
    }
    return this.entLibService.analyze(companyId, targetTypes);
  }

  @TraceLog({ throwError: true })
  private async scanTender(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((d) => d.dimensionDef.source === DimensionSourceEnums.Tender);
    if (!targetTypes?.length) {
      return [];
    }
    return this.tenderService.analyze(companyId, targetTypes);
  }

  // @TraceLog({ throwError: true })
  // private async scanRover(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
  //   const targetDefs = definitions.filter((d) => d.dimensionDef.source === DimensionSourceEnums.Rover);
  //   if (!targetDefs?.length) {
  //     return [];
  //   }
  //   const targetTypes = targetDefs.map((t) => t.key);
  //   const graphTypes = [
  //     ...this.roverGraphService.getPartnerTypes(),
  //     ...this.roverGraphService.getBlacklistTypes(),
  //     DimensionTypeEnums.CustomerPartnerInvestigation,
  //     DimensionTypeEnums.BlacklistPartnerInvestigation,
  //     DimensionTypeEnums.CustomerSuspectedRelation,
  //     DimensionTypeEnums.BlacklistSuspectedRelation,
  //   ];
  //   const results: DimensionHitResultPO[] = [];

  //   if (intersection(targetTypes, graphTypes).length > 0) {
  //     const graphHits = await this.roverGraphService.analyze(
  //       companyId,
  //       targetDefs.filter((t) => graphTypes.some((g) => g === t.key)),
  //     );
  //     if (graphHits?.length) Array.prototype.push.apply(results, graphHits);
  //   }
  //   return results.filter((t) => t);
  // }

  /**
   * 根据 信用大数据 es 索引扫描风险 + 一些特殊维度的扫描
   * @param companyId
   * @param companyName
   * @param passedDefinitions
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanCreditES(companyId: string, companyName: string, passedDefinitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetDefPos = passedDefinitions.filter((d) => d.dimensionDef.source === DimensionSourceEnums.CreditES);
    if (!targetDefPos?.length) {
      return [];
    }
    const targetDefPos2: DimensionHitStrategyPO[] = [];
    for (const t of targetDefPos) {
      // 疑似停业歇业停产或被吊销证照 需要先通过C端接口判断是否命中， 然后查信用大数据es判断命中条数
      if (t.key === DimensionTypeEnums.BusinessAbnormal5) {
        const company = await this.companySearchService.companyDetailsQcc(companyId);
        if (!(['停业', '撤销', '注销', '吊销', '歇业', '责令关闭'].includes(company?.ShortStatus) || company?.EconKind === '个体工商户')) {
          targetDefPos2.push(t);
        }
      } else {
        targetDefPos2.push(t);
      }
    }
    return this.creditESService.analyze(companyId, targetDefPos2, { companyName, keyNo: companyId });
  }

  /**
   * 根据 信用大数据 API 扫描风险
   * @param companyId
   * @param companyName
   * @param passedDefinitions
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanCreditAPI(companyId: string, companyName: string, passedDefinitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetDefPos = passedDefinitions.filter((d) => d.dimensionDef.source === DimensionSourceEnums.CreditAPI);
    if (!targetDefPos?.length) {
      return [];
    }
    return this.creditService.analyze(companyId, targetDefPos, { companyName, keyNo: companyId });
  }

  /**
   * 根据企业库公司详情接口扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByCompanyEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.CompanyDetail);
    if (!targetTypes?.length) {
      return [];
    }
    return this.companyService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 司法案件es 扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByCaseEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.Case);
    if (!targetTypes?.length) {
      return [];
    }
    return this.caseService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 负面新闻es 扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByNegativeNewsEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.NegativeNews);
    if (!targetTypes?.length) {
      return [];
    }
    return this.negativeNewsService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 裁判文书es 扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByJudgementEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.Judgement);
    if (!targetTypes?.length) {
      return [];
    }
    return this.judgementService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 股权出质es 扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByPledgeEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.Pledge);
    if (!targetTypes?.length) {
      return [];
    }
    return this.pledgeService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 股权出质es 扫描风险
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByRiskChangeEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.RiskChange);
    if (!targetTypes?.length) {
      return [];
    }

    return this.riskChangeService.analyze(companyId, targetTypes);
  }

  /**
   * 查询 外部黑名单 ES 数据
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanOuterBlacklist(companyId: string, companyName: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.OuterBlacklist);
    if (!targetTypes?.length) {
      return [];
    }
    return this.outerBlacklistService.analyze(companyId, targetTypes);
  }

  /**
   * 行政处罚 ES 扫描
   * @param companyId
   * @param definitions
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanBySupervisePunishEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.SupervisePunish);
    if (!targetTypes?.length) {
      return [];
    }
    return this.supervisePunishService.analyze(companyId, targetTypes);
  }

  /**
   * 违规违法 ES 扫描
   * @param companyId
   * @param definitions
   * @private
   */
  @TraceLog({ throwError: true })
  private async scanByViolationEs(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.Violation);
    if (!targetTypes?.length) {
      return [];
    }
    return this.violationEsService.analyze(companyId, targetTypes);
  }

  @TraceLog({ throwError: true })
  private async scanRelated(companyId: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.NebulaGraph || t.source === DimensionSourceEnums.RelatedCompany);
    if (!targetTypes?.length) {
      return [];
    }
    return this.relatedCompanyService.analyze(companyId, targetTypes);
  }

  @TraceLog({ throwError: true })
  private async scanPro(companyId: string, companyName: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.Pro);
    if (!targetTypes?.length) {
      return [];
    }
    return this.proService.analyze(companyId, targetTypes, { keyNo: companyId, companyName });
  }

  @TraceLog({ throwError: true })
  private async scanOvsSanction(companyId: string, companyName: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.OvsSanctionES);
    if (!targetTypes?.length) {
      return [];
    }
    return this.ovsSanctionsService.analyze(companyId, targetTypes);
  }

  @TraceLog({ throwError: true })
  private async scanAssert(companyId: string, companyName: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.AssetES);
    if (!targetTypes?.length) {
      return [];
    }
    return this.assertESService.analyze(companyId, targetTypes);
  }

  @TraceLog({ throwError: true })
  private async scanPledgeMerger(companyId: string, companyName: string, definitions: DimensionHitStrategyPO[]): Promise<DimensionHitResultPO[]> {
    const targetTypes = definitions.filter((t) => t.source === DimensionSourceEnums.PledgeMergerES);
    if (!targetTypes?.length) {
      return [];
    }
    return this.pledgeMergerEsService.analyze(companyId, targetTypes);
  }
}
