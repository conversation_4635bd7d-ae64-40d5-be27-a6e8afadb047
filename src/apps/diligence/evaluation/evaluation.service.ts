/* eslint-disable @typescript-eslint/naming-convention */
import { Injectable } from '@nestjs/common';
import { RiskScoreService } from './risk.score.service';
import { MoreThanOrEqual, Repository } from 'typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceResponse, PaidCheckDetail, PaidCheckResponse } from 'libs/model/diligence/req&res/DiligenceResponseV2';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import { ForbiddenStandardCode, SystemDefaultOrgId } from 'libs/constants/common';
import { GetDiligenceResultRequest } from 'libs/model/diligence/req&res/GetDiligenceResultRequest';
import { DiligenceSnapshotService } from '../snapshot/diligence.snapshot.service';
import { AccessResourceDeniedException } from '../../../libs/exceptions/AccessResourceDeniedException';
import { QueueService } from '../../../libs/config/queue.service';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { intersection } from 'lodash';
import { CompanySearchService } from '../../company/company-search.service';
import { generateSnapshotId } from '../snapshot/utils.snapshot';
import { PlatformUser } from '../../../libs/model/common';
import { SingleCompanyDiligenceRequest } from '../../../libs/model/diligence/req&res/SingleCompanyDiligenceRequest';
import * as moment from 'moment';
import * as Bluebird from 'bluebird';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { getFullRiskModel } from '../../../libs/db_helpers/resource.publish.helper';
import { BatchEntity } from '../../../libs/entities/BatchEntity';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { BatchTypeEnums } from '../../../libs/enums/batch/BatchTypeEnums';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { BatchStatusEnums } from '../../../libs/enums/batch/BatchStatusEnums';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { riskModelScopeCheck, RiskModelScopeEnums } from '../../risk_model/risk_model.utils';
import { DueDiligenceType } from '../../../libs/constants/diligence.constants';
import Redlock from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { DiligenceThrottleException } from 'libs/exceptions/DiligenceThrottleException';

@Injectable()
export class EvaluationService {
  public snapshotQueue: RabbitMQ;
  private readonly redlock: Redlock;

  constructor(
    private readonly modelRiskService: RiskScoreService,
    private readonly snapshotService: DiligenceSnapshotService,
    private readonly queueService: QueueService,
    private readonly companySearchService: CompanySearchService,
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchEntity) private readonly batchRepo: Repository<BatchEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    @InjectRepository(RiskModelEntity) private readonly riskModelRepo: Repository<RiskModelEntity>,
    private readonly redisService: RedisService,
  ) {
    this.snapshotQueue = this.queueService.snapshotQueue;
    this.redlock = new Redlock([this.redisService.getClient()], {});
  }

  /**
   * 存储最近一次更改了外部条件(影响尽调结果的条件)的时间
   *
   * @param currentUser
   * @param companyId
   */
  async makeDiligenceForUpdate(currentUser: PlatformUser, companyId?: string) {
    const { currentOrg: orgId } = currentUser;
    // const userBundle: RoverBundleEntityConfig = await this.bundleService.getBundle(currentUser);
    // if (userBundle[RoverBundleCounterType.DiligenceHistoryQuantity].value !== -1) {
    //   // 说明开启了 按排查次数数量 计费模式,
    //   const historyCounter = await this.bundleService.getOrgBundleCounter(currentUser, RoverBundleCounterType.DiligenceHistoryQuantity);
    //   await historyCounter.increase(1);
    // }

    // // 每日尽调上限校验
    // const dailyCounter = await this.bundleService.getOrgLimitationCounter(currentUser, RoverBundleLimitationType.DiligenceDailyQuantity);
    // await dailyCounter.increase(1);

    if (orgId && companyId) {
      // 只需要找出指定公司最新的一条尽调记录，把shouldUpdate 修改成1 ，确保下次不使用他做为缓存即可
      const dbDiligence = await this.diligenceHistoryRepo.findOne(
        {
          orgId,
          companyId,
        },
        { order: { createDate: 'DESC' } },
      );
      if (dbDiligence) {
        return this.diligenceHistoryRepo.update(dbDiligence.id, { shouldUpdate: 1 });
      }
    } else if (orgId) {
      return this.diligenceHistoryRepo.update({ orgId }, { shouldUpdate: 1 });
    }
  }

  async getSpecificDiligenceResult(currentUser: PlatformUser, params: GetDiligenceResultRequest) {
    const { companyId, diligenceId } = params;
    const { currentOrg: orgId } = currentUser;
    const diligenceRes: DiligenceResponse = new DiligenceResponse();
    Object.assign(diligenceRes, { notMatch: false });
    const dbDiligence = await this.diligenceHistoryRepo.findOne(diligenceId, { where: { orgId } });
    if (dbDiligence?.companyId !== companyId) {
      throw new AccessResourceDeniedException();
    }
    Object.assign(diligenceRes, dbDiligence);
    diligenceRes.cached = true;
    return diligenceRes;
  }

  @TraceLog({ throwError: true })
  async getPaidCheckList(currentUser: PlatformUser, params: SingleCompanyDiligenceRequest): Promise<PaidCheckResponse> {
    const { currentOrg: orgId } = currentUser;

    // 1. 权限检查和公司信息查询并行处理
    const [modelScopeCheck, companyInfo] = await Promise.all([
      // 权限检查
      orgId !== SystemDefaultOrgId
        ? riskModelScopeCheck(params.orgModelIds, currentUser, this.riskModelRepo.manager, RiskModelScopeEnums.Active)
        : Promise.resolve(),
      // 获取公司详情
      this.companySearchService.companyDetailsQcc(params.companyId),
    ]);

    // 过滤不支持排查的企业类型
    if ((companyInfo?.standardCode?.length && intersection(companyInfo.standardCode, ForbiddenStandardCode).length) || !companyInfo?.standardCode?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
    }
    // 2. 批量获取风险模型信息
    params.companyName = companyInfo.Name;
    const list = await Bluebird.map(params.orgModelIds, (orgModelId: number) => {
      return this.getPaidCheck(currentUser, params, orgModelId);
    });
    const paidCount = list.filter((item) => item.paid).length;
    return { paidCount, list };
  }

  async getPaidCheck(currentUser: PlatformUser, params: SingleCompanyDiligenceRequest, orgModelId: number): Promise<PaidCheckDetail> {
    const { companyId } = params;
    const { bundleStartDate, currentOrg: orgId } = currentUser;
    const riskModel = await getFullRiskModel(orgModelId, this.diligenceHistoryRepo.manager);
    const query: any = {
      orgId,
      companyId,
      product: currentUser.currentProduct,
    };
    // 模型过滤条件
    if (riskModel.branchCode) {
      query.modelBranchCode = riskModel.branchCode;
    } else {
      query.orgModelId = orgModelId;
    }
    // 计算扣费信息
    if (bundleStartDate) {
      query.createDate = MoreThanOrEqual(bundleStartDate);
    }
    const orgQuery: any = { ...query };
    delete orgQuery.modelBranchCode;
    delete orgQuery.orgModelId;
    const [orgCount, riskModelCount] = await Bluebird.all([this.diligenceHistoryRepo.count(orgQuery), this.diligenceHistoryRepo.count(query)]);
    const isFirstToModel = riskModelCount === 0 ? 1 : 0;
    const isFirstToOrg = orgCount === 0 ? 1 : 0;
    const paid = await this.checkPaid(isFirstToModel, isFirstToOrg, currentUser);
    return { companyId, orgModelId, riskModelName: riskModel.modelName, paid };
  }

  @TraceLog({ throwError: true })
  async getRiskList(currentUser: PlatformUser, params: SingleCompanyDiligenceRequest): Promise<DiligenceResponse[]> {
    const { currentOrg: orgId, userId, currentProduct } = currentUser;
    const lockey = `${orgId}_${currentProduct}_${params.companyId}`;
    const lock = await this.redlock.acquire([lockey], 1000 * 3);
    if (!lock) {
      throw new DiligenceThrottleException();
    }

    // 如果不是BO管理的组织来尽调，需要验证用户是否有这个风险模型的权限
    if (orgId !== SystemDefaultOrgId) {
      await riskModelScopeCheck(params.orgModelIds, currentUser, this.riskModelRepo.manager, RiskModelScopeEnums.Active);
    }

    // 获取公司详情
    const companyInfo = await this.companySearchService.companyDetailsQcc(params.companyId);
    // 过滤不支持排查的企业类型
    if ((companyInfo?.standardCode?.length && intersection(companyInfo.standardCode, ForbiddenStandardCode).length) || !companyInfo?.standardCode?.length) {
      throw new BadParamsException(RoverExceptions.Diligence.Common.Nonsupport);
    }
    params.companyName = companyInfo.Name;

    let batchId = 0;
    if (params.orgModelIds.length > 1) {
      const batch = await this.batchRepo.save(
        Object.assign(new BatchEntity(), {
          product: currentProduct,
          createDate: new Date(),
          orgId,
          createBy: userId,
          batchType: BatchTypeEnums.SpecificRisk,
          businessType: BatchBusinessTypeEnums.Diligence_ID,
          recordCount: params.orgModelIds.length,
          status: BatchStatusEnums.Done,
          comment: '',
          statisticsInfo: '',
        }),
      );
      batchId = batch.batchId;
    }

    return Bluebird.map(params.orgModelIds, (orgModelId: number) => {
      return this.runDiligenceBySpecificRiskModel(currentUser, params, orgModelId, batchId);
    });
  }

  /**
   * 监控尽调
   * @param orgId 组织ID
   * @param params 尽调请求参数
   * @param product 产品
   * @param noSnapshot 是否不生成快照 , 主要是QA测试那边测试使用，不需要快照
   * @returns
   */
  @TraceLog({ throwError: true })
  async runMonitorRisk(orgId: number, params: SingleCompanyDiligenceRequest, product: string) {
    const diligenceRes: DiligenceResponse = new DiligenceResponse();
    // const { currentOrg: orgId, userId } = currentUser;
    const { companyId, companyName, batchId, orgModelIds, isRelated } = params;
    const orgModelId = orgModelIds[0];
    // const riskModel = await getFullRiskModel(orgModelId, this.diligenceHistoryRepo.manager, batchId);
    const batchEntity = await this.batchRepo.findOne({ batchId });
    const { batchInfo } = batchEntity;
    const riskModel = await getFullRiskModel(orgModelId, this.diligenceHistoryRepo.manager);
    const modelScore = await this.modelRiskService.getScore(companyId, companyName, riskModel, {
      startTime: batchInfo?.batchStartTime,
      endTime: batchInfo?.batchEndTime,
      monitorGroupId: batchInfo?.monitorGroupId,
      isRelated,
    });
    // const { allHits, timeCost } = await this.dimensionService.getDimensionHitResult(orgId, companyId, companyName, riskModel);
    // const allHitMetrics = findAllHitMetric(allHits, riskModel);
    // const modelScore = Object.assign(new ModelScorePO(), { cost: timeCost, originalHits: allHitMetrics });
    const snapshotId = generateSnapshotId();
    const diligenceHis = Object.assign(new DiligenceHistoryEntity(), {
      orgId,
      operator: 0,
      name: companyName,
      companyId,
      score: 0, // 监控无需评价分数
      result: 0, // 监控无需尽调结果等级
      details: modelScore,
      snapshotDate: new Date(),
      snapshotId: snapshotId, // 生成快照ID，用于保存维度详情列表
      orgSettingsId: orgModelId,
      creditRate: 0, // 监控无需企查查信用分
      orgModelId,
      modelBranchCode: riskModel.branchCode,
      product: product,
      paid: 1, // 监控默认写死1，已付款，监控从添加监控处控制套餐，batch任务时不扣额度。
      type: DueDiligenceType.RiskMonitor,
    });
    // 保存尽调记录
    const dbDiligence = await this.diligenceHistoryRepo.save(diligenceHis);
    // 对排查结果生成快照
    await this.snapshotService.prepareSnapshot(snapshotId, dbDiligence, batchId);
    Object.assign(diligenceRes, dbDiligence, { riskModelName: riskModel.modelName });
    return diligenceRes;
  }

  private async runDiligenceBySpecificRiskModel(
    currentUser: PlatformUser,
    params: SingleCompanyDiligenceRequest,
    orgModelId: number,
    batchId?: number,
  ): Promise<DiligenceResponse> {
    const { companyId, companyName, cacheHours } = params;
    const { bundleStartDate, currentOrg: orgId, userId } = currentUser;
    const diligenceRes: DiligenceResponse = new DiligenceResponse();
    // Object.assign(diligenceRes, { notMatch: false });
    let cachedDiligence = false;
    let dbDiligence = null;
    const riskModel = await getFullRiskModel(orgModelId, this.diligenceHistoryRepo.manager);

    const query: any = {
      orgId,
      companyId,
      product: currentUser.currentProduct,
    };

    // 模型过滤条件
    if (riskModel.branchCode) {
      query.modelBranchCode = riskModel.branchCode;
    } else {
      query.orgModelId = orgModelId;
    }

    if (cacheHours > 0) {
      // 有缓存设置，取缓存时间内的历史尽调记录
      let createStartDate = moment().subtract(cacheHours, 'hours').toDate();
      if (bundleStartDate) {
        //cacheHours 和 套餐开始时间，取最晚的那个
        createStartDate = new Date(bundleStartDate).getTime() > createStartDate.getTime() ? bundleStartDate : createStartDate;
      }
      dbDiligence = await this.diligenceHistoryRepo.findOne(
        {
          ...query,
          createDate: MoreThanOrEqual(createStartDate),
        },
        { order: { createDate: 'DESC' } },
      );

      // diligenceRes.notMatch = dbDiligence?.companyId !== companyId;
      if (dbDiligence?.companyId === companyId) {
        cachedDiligence = true;
      }
    }
    if (cachedDiligence && dbDiligence) {
      // 命中历史尽调记录，修改计费相关信息后直接返回
      dbDiligence.paid = 0;
      dbDiligence.isFirstToModel = 0;
      dbDiligence.isFirstToOrg = 0;
    } else {
      // 未命中历史尽调记录, 重新尽调
      const modelScore = await this.modelRiskService.getScore(companyId, companyName, riskModel);
      const snapshotId = generateSnapshotId();
      const diligenceHis = Object.assign(new DiligenceHistoryEntity(), {
        orgId,
        operator: userId,
        name: companyName,
        companyId,
        score: modelScore.totalScore, // 评价分数
        result: modelScore.result, // 尽调结果
        details: modelScore,
        snapshotDate: new Date(),
        snapshotId: snapshotId, // 生成快照ID，用于保存维度详情列表
        orgSettingsId: orgModelId,
        creditRate: modelScore.creditRateResult?.Score,
        orgModelId,
        modelBranchCode: riskModel.branchCode,
        product: currentUser.currentProduct,
      });

      // 计算扣费信息
      const paidDetail = await this.getPaidCheck(currentUser, params, orgModelId);
      diligenceHis.paid = paidDetail.paid;

      // 保存尽调记录
      dbDiligence = await this.diligenceHistoryRepo.save(diligenceHis);
      // 对排查结果生成快照
      await this.snapshotService.prepareSnapshot(snapshotId, dbDiligence);
    }

    Object.assign(diligenceRes, dbDiligence, { cached: cachedDiligence, riskModelName: riskModel.modelName });

    if (batchId) {
      // 保存批次和尽调记录关系
      await this.batchDiligenceRepo.save(
        Object.assign(new BatchDiligenceEntity(), {
          batchId,
          diligenceId: dbDiligence.id,
        }),
      );
    }

    return diligenceRes;
  }

  /**
   * 根据不同产品，模型来判断是否计费
   * @private
   */
  private async checkPaid(isFirstToModel: number, isFirstToOrg: number, currentUser: PlatformUser) {
    const { currentProduct } = currentUser;
    let paid = 0;
    switch (currentProduct) {
      case ProductCodeEnums.Rover: {
        paid = isFirstToOrg;
        break;
      }
      case ProductCodeEnums.Pro: {
        paid = isFirstToModel;
      }
    }
    return paid;
  }
}
