import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { CompanySearchModule } from '../company/company-search.module';
import { DiligenceRemarkEntity } from 'libs/entities/DiligenceRemarkEntity';
import { DiligenceExcludesEntity } from 'libs/entities/DiligenceExcludesEntity';
import { BatchEntity } from 'libs/entities/BatchEntity';
import { BatchResultEntity } from 'libs/entities/BatchResultEntity';
import { BatchDiligenceEntity } from 'libs/entities/BatchDiligenceEntity';
import { DiligenceSnapshotHelper } from './snapshot/diligence.snapshot.helper';
import { DiligenceSnapshotService } from './snapshot/diligence.snapshot.service';
import { DiligenceSnapshotEsService } from './snapshot/diligence.snapshot.es.service';
import { DiligenceDetailsController } from './details/diligence.details.controller';
import { DiligenceHistoryService } from './details/diligence.history.service';
import { DimensionDetailService } from './details/dimension.detail.service';
import { EvaluationService } from './evaluation/evaluation.service';
import { RiskScoreService } from './evaluation/risk.score.service';
import { HitDimensionService } from './evaluation/hit.dimension.service';
import { DiligenceController } from './diligence.controller';
import { DiligencePDFService } from './diligence.pdf.service';
import { BasicModule } from '../basic/basic.module';
import { DataModule } from '../data/data.module';
import { DiligenceModifyService } from './details/diligence.modify.service';
import { DiligenceSnapshotEsCompareService } from './snapshot/diligence.snapshot.es.compare.service';
import { DistributedSystemResourceEntity } from '../../libs/entities/DistributedSystemResourceEntity';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { OuterBlacklistSource } from '../data/source/outer-blacklist.source';
import { DiligenceDataCleanerModule } from './diligence-data-cleaner/diligence-data-cleaner.module';

@Module({
  providers: [
    DiligenceHistoryService,
    DiligenceSnapshotEsService,
    DiligenceSnapshotService,
    DiligenceSnapshotHelper,
    RiskScoreService,
    OuterBlacklistSource,
    EvaluationService,
    DiligencePDFService,
    HitDimensionService,
    DimensionDetailService,
    DiligenceModifyService,
    DiligenceSnapshotEsCompareService,
  ],
  exports: [
    DiligenceHistoryService,
    DiligenceSnapshotEsCompareService,
    DiligenceSnapshotEsService,
    DiligenceSnapshotService,
    DiligenceSnapshotHelper,
    // RiskScoreService,
    EvaluationService,
    DiligencePDFService,
    DimensionDetailService,
    // DiligencePDFService,
    // DimensionService,
    // DimensionDetailService,
    // DiligenceModifyService,
  ],
  imports: [
    TypeOrmModule.forFeature([
      DiligenceHistoryEntity,
      DiligenceRemarkEntity,
      DiligenceExcludesEntity,
      BatchEntity,
      BatchResultEntity,
      BatchDiligenceEntity,
      DistributedSystemResourceEntity,
      RiskModelEntity,
    ]),
    CompanySearchModule,
    BasicModule,
    DataModule,
    DiligenceDataCleanerModule,
  ],
  controllers: [DiligenceController, DiligenceDetailsController],
})
export class DiligenceModule {}
