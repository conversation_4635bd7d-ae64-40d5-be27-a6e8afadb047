import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceSnapshotHelper } from './snapshot/diligence.snapshot.helper';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ConfigService } from 'libs/config/config.service';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import * as HBS from 'hbs';
import * as fs from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { cloneDeep, find, forEach, map, upperFirst, replace } from 'lodash';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { AxiosRequestConfig } from 'axios';
import { TABLE_COLUMNS } from './pdf.utils/table-columns.config';
import { LevelGroupDimensionPO } from 'libs/model/group/LevelGroupDimensionPO';
import { CompanySearchService } from 'apps/company/company-search.service';
import * as moment from 'moment';
import { PDF_REPORT_PAGE_HEADER_LOGO } from './pdf.utils/page-assets';
import * as uuidTime from 'uuid-time';
import { isOrganism } from 'apps/company/utils';
import { templateHelperRegister } from 'apps/utils/pdf/pdf-template.util';
import { Cacheable } from '@type-cacheable/core';
import { Benefit } from 'libs/model/diligence/Benefit';
import { CompanyDetailService } from '../company/company-detail.service';
import { GroupMetricScorePO } from '../../libs/model/group/GroupMetricScorePO';
import { DimensionRiskLevelEnum } from '../../libs/enums/diligence/DimensionRiskLevelEnum';
import { getDimesionHitStrategies } from '../dimension/dimension.helper';
import { PersonHelper } from '../data/helper/person.helper';
import { PlatformUser } from '../../libs/model/common';
import { ExportConditionRequest } from '../../libs/model/batch/po/message/BatchExportMessagePO';

enum ActualControllerTypeMaps {
  /** 疑似实际控制人, 判定依据 (李宁（中国）体育用品有限公司) */
  SuspectActual = 'ActualControllerV5YisiActual', // 李宁（中国）体育用品有限公司: 疑似实际控制人, 判定依据
  /** 实际控制人、表决权比例 (乐视网信息技术（北京）股份有限公司) */
  Actual1 = 'ActualControllerV5Actual1',
  /** 实际控制人、直接持股比例、表决权比例 (?) */
  Actual2 = 'ActualControllerV5Actual2',
  Actual2PercentTotal = 'ActualControllerV5Actual2WithPercentTotal',
  Actual2TitleAndControlPercent = 'ActualControllerV5Actual2WithTitleAndControlPercent',
  Actual2ControlPercent = 'ActualControllerV5Actual2WithControlPercent',
  /** 实际控制人、总持股比例 (?) */
  Actual3 = 'ActualControllerV5Actual3',
  /** 实际控制人（公示信息）、总持股比例 (宁德时代新能源科技股份有限公司) */
  Actual3Title = 'ActualControllerV5Actual3WithTitle',
  /** 实际控制人、直接持股比例、总持股比例 (锐奇控股股份有限公司) */
  Actual4 = 'ActualControllerV5Actual4',
  /** 实际控制人、直接持股比例、总持股比例 (锐奇控股股份有限公司) */
  Actual4Title = 'ActualControllerV5Actual4WithTitle',
  /** 实际控制人、总持股比例、表决权比例 (中铁投资集团有限公司/北京天眼查科技有限公司) */
  Actual5 = 'ActualControllerV5Actual5',
  Actual5PercentTotal = 'ActualControllerV5Actual5WithPercentTotal',
  Actual6 = 'ActualControllerV5Actual6',
  /** 实际控制人（公示信息）、直接持股比例 (中国供销集团有限公司) */
  Actual6Title = 'ActualControllerV5Actual6WithTitle',
}

/** 实际控制人: 路径中的角色映射 */
const shareHoldTypeMap = [
  {
    type: 1,
    typeDesc: '参控股-直接持股',
    lineType: 'solid',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 2,
    typeDesc: '参控股-间接持股',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '参控股',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '参控股-未持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 0,
    typeDesc: '参控股-未披露',
    lineType: 'solid',
    isSpecial: false,
  },
];
/** 实际控制人: 路径中的控制类型映射 */
const controlTypeMap = [
  {
    type: 1,
    typeDesc: '普通持股',
    lineType: 'solid',
    isSpecial: false,
  },
  {
    type: 2,
    typeDesc: '协议控制',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露，中间可能会存在折叠路径。',
    lineTextPrefix: '协议控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 3,
    typeDesc: '信托控制',
    lineType: 'dashed',
    tips: '数据源于xxx的公告披露',
    lineTextPrefix: '信托控制',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 4,
    typeDesc: '普通合伙人',
    lineType: 'solid',
    lineTextPrefix: '普通合伙人',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 5,
    typeDesc: '疑似实际控制人',
    lineType: 'dashed',
    tips: '',
    isSpecial: true,
  },
  {
    type: 6,
    typeDesc: '执行事务合伙人',
    lineType: 'solid',
    lineTextPrefix: '执行事务合伙人',
    isSpecial: false,
  },
  {
    type: 7,
    typeDesc: '同股不同权',
    lineType: 'dashed',
    lineTextSuffix: '(表决权)',
    isSpecial: true,
  },
  {
    type: 0,
    typeDesc: '其他',
    lineType: 'solid',
    isSpecial: true,
  },
];

@Injectable()
export class DiligencePDFService {
  private logger: Logger = QccLogger.getLogger(DiligencePDFService.name);
  private readonly pdfTemplate;
  private readonly pdfCoverTemplate;

  constructor(
    @InjectRepository(DiligenceHistoryEntity) private readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    // @InjectRepository(DiligenceRemarkEntity) private readonly diligenceRemarkRepo: Repository<DiligenceRemarkEntity>,
    private readonly snapshotHelper: DiligenceSnapshotHelper,
    redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly httpUtils: HttpUtilsService,
    private readonly searchService: CompanySearchService,
    private readonly personHelper: PersonHelper,
    private readonly companyDetailService: CompanyDetailService,
  ) {
    //useAdapter(redisService.getClient());
    const hbs = HBS.create();

    /**
     * 注册通用渲染模板工具函数
     */
    templateHelperRegister(hbs, {
      tableColumns: TABLE_COLUMNS,
    });

    /**
     * 人工审核风险等级
     */
    hbs.registerHelper('risk_manual_level_to_label', function (level: number) {
      switch (level) {
        case 2:
          return '【高风险】';
        case 1:
          return '【中风险】';
        case 0:
          return '【低风险】';
        default:
          return '-';
      }
    });

    hbs.registerHelper('risk_verify_type_to_label', function (type: number) {
      switch (type) {
        case 1:
          return '【电话核实】';
        case 2:
          return '【实地核实】';
        case 3:
          return '【网络核实】';
        case 4:
          return '【其他】';
        default:
          return '-';
      }
    });

    hbs.registerHelper('risk_level_to_alias', function (level: number, type: string) {
      enum RISK_LEVEL {
        HIGH = 2,
        MEDIUM = 1,
        LOW = 0,
      }

      const RISK_LEVEL_NAME_MAP = {
        [RISK_LEVEL.HIGH]: 'high',
        [RISK_LEVEL.MEDIUM]: 'medium',
        [RISK_LEVEL.LOW]: 'low',
      };
      const RISK_LEVEL_LABEL_MAP = {
        [RISK_LEVEL.HIGH]: '警示',
        [RISK_LEVEL.MEDIUM]: '关注',
        [RISK_LEVEL.LOW]: '无风险',
      };
      const RISK_LEVEL_RESULT_MAP = {
        [RISK_LEVEL.HIGH]: '不通过',
        [RISK_LEVEL.MEDIUM]: '不通过',
        [RISK_LEVEL.LOW]: '通过',
      };
      const RISK_LEVEL_ICON_MAP = {
        [RISK_LEVEL.HIGH]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#FFECEC" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#F04040" d="m11.315 10.23.734-6.43c.024-.207-.246-.308-.363-.136l-5.472 7.99a.2.2 0 0 0 .165.313h4.577l-.725 6.21c-.024.208.248.308.365.134l5.196-7.77a.2.2 0 0 0-.166-.311z" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#FCC" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
        [RISK_LEVEL.MEDIUM]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#FFF4E0" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#FA0" fill-rule="nonzero" d="M8.463 13.905c-.185-.788-1.023-1.433-1.367-1.858a4.92 4.92 0 0 1 .501-6.723 5.035 5.035 0 0 1 6.807 0 4.92 4.92 0 0 1 .499 6.725c-.344.424-1.18 1.068-1.366 1.856zm5.037 1.238v.619c0 .684-.56 1.238-1.25 1.238h-2.5c-.69 0-1.25-.554-1.25-1.238v-.62zm-1.875-6.188v-2.48l-2.812 3.718h1.562v2.477l2.813-3.715z" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#F9E4BB" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
        [RISK_LEVEL.LOW]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#E0F5EC" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#00AD65" fill-rule="nonzero" d="M6.5 8.952H8V15H6.5a.5.5 0 0 1-.5-.504v-5.04c0-.279.224-.504.5-.504M9.146 8.3l3.2-3.226a.25.25 0 0 1 .328-.024l.426.323a.76.76 0 0 1 .277.791L12.8 8.448H16c.552 0 1 .45 1 1.008v1.06q0 .2-.075.384l-1.548 3.788a.5.5 0 0 1-.462.312H9.5a.5.5 0 0 1-.5-.504v-5.84c0-.133.053-.262.146-.356" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#B3EED5" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
      };

      switch (type) {
        case 'icon':
          return RISK_LEVEL_ICON_MAP[level];
        case 'label':
          return RISK_LEVEL_LABEL_MAP[level];
        case 'result':
          return RISK_LEVEL_RESULT_MAP[level];
        case 'name':
        default:
          return RISK_LEVEL_NAME_MAP[level];
      }
    });

    hbs.registerHelper('insights_risk_level_to_alias', function (level: number, type: string) {
      enum RISK_LEVEL {
        HIGH = 2,
        MEDIUM = 1,
        LOW = 0,
      }

      const RISK_LEVEL_NAME_MAP = {
        [RISK_LEVEL.HIGH]: 'high',
        [RISK_LEVEL.MEDIUM]: 'medium',
        [RISK_LEVEL.LOW]: 'low',
      };

      const RISK_LEVEL_LABEL_MAP = {
        [RISK_LEVEL.HIGH]: '红色等级',
        [RISK_LEVEL.MEDIUM]: '黄色等级',
        [RISK_LEVEL.LOW]: '绿色等级',
      };
      const RISK_LEVEL_ICON_MAP = {
        [RISK_LEVEL.HIGH]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#FFECEC" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#F04040" d="m11.315 10.23.734-6.43c.024-.207-.246-.308-.363-.136l-5.472 7.99a.2.2 0 0 0 .165.313h4.577l-.725 6.21c-.024.208.248.308.365.134l5.196-7.77a.2.2 0 0 0-.166-.311z" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#FCC" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
        [RISK_LEVEL.MEDIUM]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#FFF4E0" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#FA0" fill-rule="nonzero" d="M8.463 13.905c-.185-.788-1.023-1.433-1.367-1.858a4.92 4.92 0 0 1 .501-6.723 5.035 5.035 0 0 1 6.807 0 4.92 4.92 0 0 1 .499 6.725c-.344.424-1.18 1.068-1.366 1.856zm5.037 1.238v.619c0 .684-.56 1.238-1.25 1.238h-2.5c-.69 0-1.25-.554-1.25-1.238v-.62zm-1.875-6.188v-2.48l-2.812 3.718h1.562v2.477l2.813-3.715z" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#F9E4BB" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
        [RISK_LEVEL.LOW]: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="22"><defs><path id="a" d="M0 0h22v22H0z"/></defs><g fill="none" fill-rule="evenodd"><mask id="b" fill="#fff"><use xlink:href="#a"/></mask><rect width="18" height="18" x="2" y="2" fill="#E0F5EC" fill-rule="nonzero" mask="url(#b)" rx="2"/><path fill="#00AD65" fill-rule="nonzero" d="M6.5 8.952H8V15H6.5a.5.5 0 0 1-.5-.504v-5.04c0-.279.224-.504.5-.504M9.146 8.3l3.2-3.226a.25.25 0 0 1 .328-.024l.426.323a.76.76 0 0 1 .277.791L12.8 8.448H16c.552 0 1 .45 1 1.008v1.06q0 .2-.075.384l-1.548 3.788a.5.5 0 0 1-.462.312H9.5a.5.5 0 0 1-.5-.504v-5.84c0-.133.053-.262.146-.356" mask="url(#b)"/><rect width="17.5" height="17.5" x="2.25" y="2.25" stroke="#B3EED5" stroke-width=".5" mask="url(#b)" rx="1.75"/></g></svg>`,
      };

      switch (type) {
        case 'icon':
          return RISK_LEVEL_ICON_MAP[level];
        case 'label':
          return RISK_LEVEL_LABEL_MAP[level];
        // case 'result':
        //   return RISK_LEVEL_RESULT_MAP[level];
        case 'name':
        default:
          return RISK_LEVEL_NAME_MAP[level];
      }
    });

    hbs.registerHelper('insights_chart_risk_level_to_alias', function (level: number, type: string) {
      enum RISK_LEVEL {
        L1 = -2,
        L2 = -1,
        L3 = 0,
        L4 = 1,
        L5 = 2,
      }

      const RISK_LEVEL_COLOR_MAP = {
        [RISK_LEVEL.L1]: '#00ad65',
        [RISK_LEVEL.L2]: '#128bed',
        [RISK_LEVEL.L3]: '#ffc043',
        [RISK_LEVEL.L4]: '#ff722d',
        [RISK_LEVEL.L5]: '#a80000',
      };

      const RISK_LEVEL_NAME_MAP = {
        [RISK_LEVEL.L1]: 'l1',
        [RISK_LEVEL.L2]: 'l2',
        [RISK_LEVEL.L3]: 'l3',
        [RISK_LEVEL.L4]: 'l4',
        [RISK_LEVEL.L5]: 'l5',
      };

      const RISK_LEVEL_ICON_MAP = {
        [RISK_LEVEL.L1]: `<svg xmlns="http://www.w3.org/2000/svg" width="151" height="151" fill="none"><defs><clipPath id="a"><rect width="151" height="151" rx="0"/></clipPath><clipPath id="b"><rect width="104" height="104" x="23" y="23" rx="0"/></clipPath></defs><g clip-path="url(#a)"><g fill-rule="evenodd"><path fill="#EEE" d="M47.453 26.575 39.448 12.72q-1.026.587-2.033 1.208-1.007.62-1.993 1.274-.985.653-1.95 1.338-.963.686-1.904 1.403-.94.716-1.857 1.463t-1.809 1.525-1.757 1.582-1.705 1.64-1.65 1.694q-.81.861-1.592 1.748-.783.887-1.535 1.8-.753.912-1.475 1.848-.723.937-1.414 1.897-.69.96-1.35 1.941t-1.286 1.985q-.627 1.003-1.22 2.026-.594 1.023-1.153 2.065-.56 1.042-1.084 2.102-.525 1.06-1.015 2.136t-.944 2.169-.872 2.198-.8 2.226-.726 2.25q-.345 1.132-.652 2.274t-.577 2.294q-.27 1.151-.501 2.311-.232 1.16-.425 2.326t-.348 2.34-.272 2.35-.194 2.356q-.077 1.18-.116 2.362T3.2 75.217q0 .383.004.766h16.001q-.005-.383-.005-.766 0-.92.03-1.841t.091-1.84.151-1.835.212-1.83.271-1.82.332-1.813.39-1.8q.21-.896.45-1.785.24-.89.508-1.77.269-.881.566-1.753.298-.871.624-1.733.326-.86.68-1.711.354-.85.736-1.688.381-.838.79-1.663t.846-1.637q.436-.81.899-1.607.462-.796.95-1.577.49-.78 1.003-1.545.514-.764 1.053-1.511t1.101-1.476 1.15-1.439 1.196-1.4 1.242-1.36 1.286-1.318 1.328-1.276 1.37-1.23 1.41-1.186 1.447-1.138 1.484-1.09 1.52-1.041 1.552-.99 1.585-.94"/><path fill="#FFF" d="m51.833 27.76-1.61-2.786L40.626 8.36l-2.766 1.582Q20.3 19.988 10.15 37.488 0 54.988 0 75.218q0 .4.004.8l.034 3.165h22.411l-.044-3.243q-.005-.362-.005-.723 0-14.221 7.143-26.519t19.495-19.344zm-5.981-3.956 1.6 2.77q-.268.154-.535.31-.776.454-1.537.933t-1.507.982-1.474 1.03-1.44 1.077-1.406 1.122-1.368 1.167-1.33 1.21-1.291 1.252-1.25 1.293-1.207 1.333-1.165 1.37q-.57.695-1.12 1.408-.548.712-1.073 1.442t-1.027 1.477-.98 1.508-.93 1.54-.88 1.567-.829 1.596q-.402.805-.778 1.622-.375.817-.725 1.645t-.672 1.668q-.323.84-.618 1.689-.296.85-.564 1.708t-.508 1.725q-.24.866-.453 1.74-.212.874-.397 1.754-.184.88-.34 1.766t-.283 1.776-.226 1.784-.17 1.79-.11 1.795-.054 1.798q-.005.383-.005.766t.005.766h-16Q3.2 75.6 3.2 75.217t.004-.766q.009-.834.037-1.668.037-1.094.107-2.186t.174-2.182.24-2.176.306-2.167.372-2.158.437-2.144q.234-1.07.502-2.131.267-1.061.566-2.114.3-1.053.631-2.097.331-1.043.694-2.076.363-1.032.757-2.053.394-1.022.819-2.03.425-1.01.88-2.005t.94-1.976q.486-.981 1.001-1.947t1.06-1.916 1.116-1.882 1.174-1.848q.6-.915 1.23-1.811.628-.897 1.283-1.773.655-.877 1.337-1.734.682-.856 1.389-1.691.708-.836 1.44-1.649t1.49-1.604q.756-.79 1.537-1.558t1.584-1.511 1.629-1.462q.826-.718 1.673-1.412.847-.693 1.715-1.36t1.756-1.307 1.794-1.254 1.832-1.198q1.091-.69 2.207-1.34.267-.157.535-.31z"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m104.142 27.259 8.006-13.856q-2.082-1.245-4.243-2.347t-4.392-2.055-4.52-1.756-4.628-1.447-4.714-1.133-4.78-.813q-2.405-.325-4.825-.489T75.2 3.2q-2.34 0-4.673.152t-4.654.455-4.614.756q-2.295.452-4.555 1.053-2.261.601-4.478 1.348-2.217.746-4.38 1.635-2.164.888-4.265 1.916-2.102 1.027-4.132 2.188l8.004 13.854q1.577-.899 3.208-1.694 1.632-.796 3.311-1.484t3.4-1.265q1.72-.578 3.475-1.043 1.755-.466 3.535-.816t3.58-.585 3.612-.352q1.811-.118 3.626-.118 1.902 0 3.799.129t3.782.387 3.746.642 3.695.894 3.625 1.144 3.539 1.386 3.437 1.624 3.32 1.853"/><path fill="#FFF" d="m105.28 31.684 1.633-2.824 9.583-16.586-2.706-1.617Q95.966 0 75.2 0 55.21 0 37.86 9.926l-2.79 1.595 11.205 19.393 2.764-1.577Q61.199 22.4 75.2 22.4q14.702 0 27.288 7.598zm.463-7.196-1.6 2.77q-.922-.555-1.864-1.076-1.547-.854-3.145-1.61-1.597-.755-3.24-1.408-1.641-.653-3.32-1.201-1.68-.548-3.392-.99-1.711-.44-3.446-.773-1.736-.332-3.489-.555-1.753-.222-3.516-.334-1.764-.111-3.531-.111-1.815 0-3.626.118t-3.611.352-3.58.585-3.536.816-3.475 1.043-3.4 1.265-3.31 1.484-3.209 1.694L41.05 15.474l-1.601-2.77q1.39-.796 2.813-1.528 1.902-.979 3.858-1.842t3.962-1.61 4.05-1.372q2.044-.626 4.123-1.13t4.184-.882q2.105-.38 4.228-.633 2.124-.253 4.26-.38Q73.06 3.2 75.2 3.2q2.226 0 4.447.137 2.221.138 4.43.412 2.208.275 4.396.685t4.345.955q2.158.544 4.278 1.221t4.195 1.484 4.095 1.74q2.02.932 3.98 1.988.46.249.918.504.94.525 1.864 1.077z"/></g><g fill-rule="evenodd"><path fill="#EEE" d="M147.191 75.183q0-1.16-.037-2.321-.038-1.16-.112-2.32-.075-1.158-.187-2.313-.113-1.156-.262-2.307t-.336-2.298q-.186-1.146-.41-2.285t-.482-2.271-.556-2.254-.629-2.235-.7-2.214-.771-2.19q-.404-1.089-.842-2.164t-.91-2.135-.98-2.106-1.046-2.072-1.113-2.038q-.573-1.01-1.178-2-.605-.991-1.242-1.962-.636-.971-1.304-1.921t-1.366-1.878-1.425-1.832-1.484-1.786q-.756-.88-1.54-1.737-.785-.856-1.597-1.686t-1.649-1.634-1.701-1.58-1.751-1.525-1.8-1.467-1.846-1.408-1.89-1.348-1.933-1.286-1.973-1.224l-8.006 13.856q.77.465 1.525.954.754.49 1.493 1.003.738.513 1.46 1.05.721.536 1.425 1.096t1.39 1.141 1.352 1.186q.667.603 1.314 1.228.646.625 1.273 1.27t1.232 1.31 1.19 1.349q.583.684 1.145 1.386.561.703 1.1 1.423t1.054 1.457 1.006 1.49.959 1.522q.467.768.909 1.552.442.783.858 1.58.417.797.808 1.606.39.81.755 1.632t.703 1.656.65 1.677q.31.844.594 1.697t.54 1.715q.257.862.485 1.732.229.87.43 1.746.2.877.372 1.76.172.882.316 1.77t.259 1.78.201 1.787.145 1.792q.057.898.086 1.796.03.9.03 1.799 0 .383-.006.766h16.001q.004-.384.004-.767"/><path fill="#FFF" d="M150.387 75.984q.004-.4.004-.8 0-19.843-9.79-37.103-9.79-17.259-26.82-27.441l-2.788-1.667-11.201 19.386 2.687 1.623q11.876 7.169 18.694 19.25t6.818 25.952q0 .36-.005.722l-.044 3.244h22.411zm-3.2-1.567q.004.383.004.766t-.004.767h-16.001q.005-.383.005-.766 0-.384-.005-.767-.012-.887-.052-1.773-.04-.887-.109-1.771-.068-.885-.165-1.767-.096-.882-.22-1.76-.124-.88-.276-1.753-.152-.875-.332-1.744t-.386-1.731-.442-1.72-.495-1.703-.55-1.687q-.287-.84-.602-1.67-.314-.829-.655-1.648-.34-.82-.707-1.628t-.758-1.604-.808-1.58-.859-1.552-.907-1.526q-.465-.755-.955-1.495t-1.001-1.465q-.513-.724-1.048-1.432t-1.092-1.398-1.137-1.363-1.178-1.326-1.22-1.288q-.621-.635-1.261-1.25-.64-.614-1.3-1.207-.659-.594-1.337-1.167t-1.373-1.124q-.695-.55-1.408-1.08-.713-.528-1.442-1.034-.729-.505-1.474-.988-.744-.482-1.504-.94l6.405-11.086 1.6-2.77q1.375.821 2.712 1.703.896.591 1.773 1.209.878.617 1.736 1.26.859.643 1.698 1.312.84.669 1.658 1.362.82.693 1.617 1.41.798.718 1.574 1.459.776.74 1.53 1.504.753.764 1.484 1.55.73.785 1.437 1.593t1.39 1.635 1.34 1.675q.657.848 1.29 1.715.631.867 1.237 1.753t1.185 1.789q.58.903 1.131 1.823.552.92 1.077 1.856t1.02 1.887.964 1.917.907 1.945.848 1.97q.41.993.789 1.996t.729 2.018.668 2.039q.32 1.024.608 2.058.289 1.033.546 2.075.258 1.041.484 2.09t.421 2.104.36 2.115q.163 1.06.294 2.125.132 1.065.232 2.134t.169 2.139.104 2.143q.028.833.037 1.667"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m103.5 123.553 8.005 13.854q1.003-.585 1.987-1.202.983-.617 1.946-1.266.963-.648 1.905-1.328.941-.679 1.86-1.388.92-.71 1.815-1.448.896-.739 1.768-1.506.871-.767 1.718-1.561.846-.795 1.666-1.617.82-.821 1.614-1.67.794-.847 1.56-1.72.765-.872 1.502-1.77.737-.896 1.445-1.817t1.385-1.863 1.325-1.907q.647-.964 1.263-1.948t1.199-1.988 1.134-2.026 1.069-2.061 1.001-2.095.934-2.126.864-2.155.795-2.181.723-2.206q.344-1.11.653-2.229.308-1.119.58-2.248t.507-2.265.434-2.281.36-2.294.286-2.304.212-2.312.137-2.318.062-2.32h-16.002q-.012.898-.053 1.797t-.111 1.795-.17 1.79q-.098.894-.225 1.784-.128.89-.284 1.776t-.34 1.766-.396 1.754q-.213.874-.453 1.74-.24.867-.509 1.725-.268.859-.563 1.708-.296.85-.618 1.689-.323.84-.672 1.668-.35.829-.726 1.646t-.777 1.621-.83 1.596-.88 1.568-.93 1.54-.98 1.508q-.5.746-1.026 1.476-.525.73-1.074 1.442-.548.713-1.12 1.408-.57.694-1.164 1.37-.593.676-1.207 1.333t-1.25 1.293-1.29 1.252-1.331 1.21-1.368 1.167-1.406 1.123-1.44 1.076-1.474 1.03-1.507.982-1.537.933"/><path fill="#FFF" d="m99.138 122.399 1.592 2.755 9.612 16.637 2.776-1.62q17.138-10 27.111-27.155 9.973-17.154 10.184-36.996l.035-3.234h-22.392l-.043 3.156q-.19 13.87-7.173 25.857t-18.955 18.992zm4.363 1.154q.776-.454 1.537-.933t1.507-.982 1.474-1.03 1.44-1.076 1.406-1.123 1.368-1.166 1.33-1.21 1.29-1.253 1.25-1.293 1.208-1.333 1.164-1.37 1.12-1.408 1.074-1.442 1.027-1.476.979-1.509.93-1.54q.453-.776.88-1.567.428-.791.83-1.596.401-.804.777-1.621t.726-1.646.672-1.668q.322-.84.618-1.689.295-.85.563-1.708t.509-1.725q.24-.866.453-1.74.212-.874.396-1.754t.34-1.766.284-1.776.226-1.784.169-1.79.11-1.795q.042-.899.054-1.798h16.002q-.017 1.601-.106 3.2-.058 1.06-.148 2.117t-.21 2.11-.272 2.105-.335 2.095-.396 2.085-.457 2.071-.518 2.058-.578 2.041q-.305 1.017-.639 2.024t-.697 2.003-.757 1.983q-.393.985-.814 1.959-.422.973-.872 1.934-.45.96-.929 1.908-.478.947-.984 1.88t-1.04 1.85q-.532.916-1.092 1.817-.56.902-1.147 1.786-.586.884-1.198 1.75-.612.867-1.25 1.715-.637.848-1.3 1.677-.662.83-1.348 1.639t-1.396 1.597-1.442 1.556-1.488 1.513q-.755.745-1.532 1.468t-1.574 1.422-1.616 1.376q-.818.675-1.655 1.327t-1.694 1.278-1.73 1.227-1.766 1.176q-1.346.868-2.729 1.674l-1.6-2.77z"/></g><g fill-rule="evenodd"><path fill="#00AD65" d="M19.242 75.983H3.241q.013 1.183.064 2.364.051 1.182.142 2.361.09 1.18.219 2.355.128 1.176.296 2.347.167 1.17.373 2.335t.45 2.322.525 2.306q.282 1.148.602 2.287.319 1.139.676 2.267.356 1.127.75 2.243.393 1.115.823 2.217.43 1.101.896 2.189.465 1.087.967 2.158.5 1.071 1.037 2.125t1.107 2.09 1.175 2.053q.604 1.017 1.241 2.013t1.307 1.971 1.371 1.927q.701.953 1.434 1.881.732.929 1.494 1.833t1.554 1.783 1.612 1.731 1.667 1.677 1.722 1.621 1.774 1.564 1.825 1.505 1.873 1.444 1.92 1.382q.97.675 1.963 1.318t2.006 1.253 2.046 1.186l8.006-13.856q-.806-.445-1.597-.917-.791-.471-1.567-.969-.775-.497-1.533-1.02-.758-.522-1.5-1.07-.74-.547-1.462-1.118t-1.426-1.166-1.386-1.212-1.346-1.257-1.303-1.301-1.26-1.343q-.62-.682-1.216-1.384t-1.17-1.422q-.572-.721-1.121-1.461-.55-.74-1.073-1.497-.525-.757-1.024-1.53-.499-.775-.972-1.565-.474-.79-.92-1.595-.448-.805-.868-1.624-.421-.82-.814-1.652-.393-.833-.76-1.678-.365-.845-.703-1.702t-.647-1.724-.59-1.745q-.28-.877-.532-1.763-.251-.886-.474-1.78-.222-.893-.415-1.794-.193-.9-.356-1.807t-.297-1.817q-.133-.912-.236-1.827t-.176-1.833q-.074-.918-.116-1.838t-.056-1.84"/><path fill="#FFF" d="M22.399 72.783H.007l.034 3.234q.215 20.23 10.55 37.62t28.002 27.248l2.753 1.537 11.214-19.41-2.855-1.578q-12.448-6.877-19.758-19.076-7.31-12.198-7.505-26.419zm-3.157 3.2H3.241q.017 1.601.105 3.2.06 1.081.152 2.16t.216 2.154.282 2.147q.157 1.07.345 2.137.19 1.066.41 2.126.221 1.06.474 2.112.252 1.053.537 2.098.284 1.044.6 2.08.315 1.036.661 2.061.347 1.026.724 2.04.377 1.016.784 2.019t.845 1.993q.438.99.905 1.967t.963 1.939 1.02 1.91q.526.946 1.079 1.877.553.93 1.134 1.844t1.189 1.81 1.243 1.772 1.295 1.735 1.347 1.695 1.398 1.653q.71.817 1.446 1.611.735.795 1.494 1.567.76.772 1.541 1.52.782.75 1.586 1.475t1.63 1.425q.825.7 1.67 1.376.847.676 1.713 1.326t1.75 1.273 1.79 1.22 1.824 1.165q.446.276.897.545.923.552 1.862 1.076l6.405-11.085 1.6-2.77q-.94-.521-1.862-1.077-.76-.459-1.504-.941-.745-.482-1.474-.988t-1.442-1.034q-.712-.53-1.408-1.08-.695-.551-1.373-1.124-.678-.572-1.337-1.166-.66-.594-1.3-1.209-.64-.614-1.26-1.248-.62-.635-1.22-1.289t-1.18-1.326q-.578-.672-1.136-1.363-.557-.69-1.092-1.398t-1.048-1.432q-.512-.725-1.001-1.465-.49-.74-.955-1.495-.466-.756-.907-1.525-.442-.77-.858-1.554-.417-.783-.81-1.58-.39-.795-.757-1.603-.367-.809-.707-1.628-.34-.82-.655-1.649t-.603-1.67q-.288-.838-.549-1.686t-.495-1.704q-.235-.856-.442-1.719t-.386-1.732-.332-1.743-.276-1.753-.22-1.76q-.097-.882-.165-1.767t-.109-1.771q-.04-.887-.052-1.774"/></g><g clip-path="url(#b)"><circle cx="75" cy="75" r="44" fill="#EEE" style="opacity:.5"/></g><rect width="1.575" height="4.726" x="73.482" y="24" fill="#EEE" rx=".788"/><rect width="1.575" height="4.726" x="78.839" y="24.198" fill="#EEE" rx=".788" transform="rotate(6 78.839 24.199)"/><rect width="1.575" height="4.726" x="84.153" y="24.993" fill="#EEE" rx=".788" transform="rotate(12 84.154 24.995)skewX(-.001)"/><rect width="1.575" height="4.726" x="89.329" y="26.33" fill="#EEE" rx=".788" transform="rotate(17.999 89.329 26.332)skewX(-.001)"/><rect width="1.575" height="4.726" x="94.384" y="28.206" fill="#EEE" rx=".788" transform="rotate(23.999 94.383 28.207)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="30.534" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="128.206" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="103.755" y="33.387" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="103.755" y="131.058" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="36.681" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="134.353" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="40.413" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="138.086" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="44.496" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="142.169" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="118.261" y="48.928" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="118.261" y="146.601" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.779" y="53.627" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.78" y="151.3" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="58.589" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="156.263" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="63.734" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="161.407" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="125.197" y="68.978" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.198" y="166.652" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.6" y="74.332" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.6" y="172.006" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.402" y="79.688" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="125.402" y="177.361" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="124.607" y="84.994" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="124.607" y="182.668" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="90.164" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="187.837" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="95.196" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="192.87" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="100.017" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="197.69" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="116.212" y="104.549" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="116.212" y="202.221" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="108.795" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="206.467" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="112.68" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="210.352" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="116.128" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="213.799" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="100.621" y="119.121" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="100.621" y="216.793" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="56.14" y="32.406" fill="#EEE" rx=".788" transform="rotate(156.001 56.14 32.405)skewX(.001)"/><rect width="1.575" height="4.726" x="60.728" y="30.763" fill="#EEE" rx=".788" transform="rotate(162.001 60.728 30.763)skewX(.001)"/><rect width="1.575" height="4.726" x="65.446" y="29.573" fill="#EEE" rx=".788" transform="rotate(168 65.446 29.573)skewX(.001)"/><rect width="1.575" height="4.726" x="70.253" y="28.92" fill="#EEE" rx=".788" transform="rotate(174 70.253 28.92)"/></g></svg>`,
        [RISK_LEVEL.L2]: `<svg xmlns="http://www.w3.org/2000/svg" width="151" height="151" fill="none"><defs><clipPath id="a"><rect width="151" height="151" rx="0"/></clipPath><clipPath id="b"><rect width="104" height="104" x="23" y="23" rx="0"/></clipPath></defs><g clip-path="url(#a)"><g fill-rule="evenodd"><path fill="#128BED" d="M47.453 26.575 39.448 12.72q-1.026.587-2.033 1.208-1.007.62-1.993 1.274-.985.653-1.95 1.338-.963.686-1.904 1.403-.94.716-1.857 1.463t-1.809 1.525-1.757 1.582-1.705 1.64-1.65 1.694q-.81.861-1.592 1.748-.783.887-1.535 1.8-.753.912-1.475 1.848-.723.937-1.414 1.897-.69.96-1.35 1.941t-1.286 1.985q-.627 1.003-1.22 2.026-.594 1.023-1.153 2.065-.56 1.042-1.084 2.102-.525 1.06-1.015 2.136t-.944 2.169-.872 2.198-.8 2.226-.726 2.25q-.345 1.132-.652 2.274t-.577 2.294q-.27 1.151-.501 2.311-.232 1.16-.425 2.326t-.348 2.34-.272 2.35-.194 2.356q-.077 1.18-.116 2.362T3.2 75.217q0 .383.004.766h16.001q-.005-.383-.005-.766 0-.92.03-1.841t.091-1.84.151-1.835.212-1.83.271-1.82.332-1.813.39-1.8q.21-.896.45-1.785.24-.89.508-1.77.269-.881.566-1.753.298-.871.624-1.733.326-.86.68-1.711.354-.85.736-1.688.381-.838.79-1.663t.846-1.637q.436-.81.899-1.607.462-.796.95-1.577.49-.78 1.003-1.545.514-.764 1.053-1.511t1.101-1.476 1.15-1.439 1.196-1.4 1.242-1.36 1.286-1.318 1.328-1.276 1.37-1.23 1.41-1.186 1.447-1.138 1.484-1.09 1.52-1.041 1.552-.99 1.585-.94"/><path fill="#FFF" d="m51.833 27.76-1.61-2.786L40.626 8.36l-2.766 1.582Q20.3 19.988 10.15 37.488 0 54.988 0 75.218q0 .4.004.8l.034 3.165h22.411l-.044-3.243q-.005-.362-.005-.723 0-14.221 7.143-26.519t19.495-19.344zm-5.981-3.956 1.6 2.77q-.268.154-.535.31-.776.454-1.537.933t-1.507.982-1.474 1.03-1.44 1.077-1.406 1.122-1.368 1.167-1.33 1.21-1.291 1.252-1.25 1.293-1.207 1.333-1.165 1.37q-.57.695-1.12 1.408-.548.712-1.073 1.442t-1.027 1.477-.98 1.508-.93 1.54-.88 1.567-.829 1.596q-.402.805-.778 1.622-.375.817-.725 1.645t-.672 1.668q-.323.84-.618 1.689-.296.85-.564 1.708t-.508 1.725q-.24.866-.453 1.74-.212.874-.397 1.754-.184.88-.34 1.766t-.283 1.776-.226 1.784-.17 1.79-.11 1.795-.054 1.798q-.005.383-.005.766t.005.766h-16Q3.2 75.6 3.2 75.217t.004-.766q.009-.834.037-1.668.037-1.094.107-2.186t.174-2.182.24-2.176.306-2.167.372-2.158.437-2.144q.234-1.07.502-2.131.267-1.061.566-2.114.3-1.053.631-2.097.331-1.043.694-2.076.363-1.032.757-2.053.394-1.022.819-2.03.425-1.01.88-2.005t.94-1.976q.486-.981 1.001-1.947t1.06-1.916 1.116-1.882 1.174-1.848q.6-.915 1.23-1.811.628-.897 1.283-1.773.655-.877 1.337-1.734.682-.856 1.389-1.691.708-.836 1.44-1.649t1.49-1.604q.756-.79 1.537-1.558t1.584-1.511 1.629-1.462q.826-.718 1.673-1.412.847-.693 1.715-1.36t1.756-1.307 1.794-1.254 1.832-1.198q1.091-.69 2.207-1.34.267-.157.535-.31z"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m104.142 27.259 8.006-13.856q-2.082-1.245-4.243-2.347t-4.392-2.055-4.52-1.756-4.628-1.447-4.714-1.133-4.78-.813q-2.405-.325-4.825-.489T75.2 3.2q-2.34 0-4.673.152t-4.654.455-4.614.756q-2.295.452-4.555 1.053-2.261.601-4.478 1.348-2.217.746-4.38 1.635-2.164.888-4.265 1.916-2.102 1.027-4.132 2.188l8.004 13.854q1.577-.899 3.208-1.694 1.632-.796 3.311-1.484t3.4-1.265q1.72-.578 3.475-1.043 1.755-.466 3.535-.816t3.58-.585 3.612-.352q1.811-.118 3.626-.118 1.902 0 3.799.129t3.782.387 3.746.642 3.695.894 3.625 1.144 3.539 1.386 3.437 1.624 3.32 1.853"/><path fill="#FFF" d="m105.28 31.684 1.633-2.824 9.583-16.586-2.706-1.617Q95.966 0 75.2 0 55.21 0 37.86 9.926l-2.79 1.595 11.205 19.393 2.764-1.577Q61.199 22.4 75.2 22.4q14.702 0 27.288 7.598zm.463-7.196-1.6 2.77q-.922-.555-1.864-1.076-1.547-.854-3.145-1.61-1.597-.755-3.24-1.408-1.641-.653-3.32-1.201-1.68-.548-3.392-.99-1.711-.44-3.446-.773-1.736-.332-3.489-.555-1.753-.222-3.516-.334-1.764-.111-3.531-.111-1.815 0-3.626.118t-3.611.352-3.58.585-3.536.816-3.475 1.043-3.4 1.265-3.31 1.484-3.209 1.694L41.05 15.474l-1.601-2.77q1.39-.796 2.813-1.528 1.902-.979 3.858-1.842t3.962-1.61 4.05-1.372q2.044-.626 4.123-1.13t4.184-.882q2.105-.38 4.228-.633 2.124-.253 4.26-.38Q73.06 3.2 75.2 3.2q2.226 0 4.447.137 2.221.138 4.43.412 2.208.275 4.396.685t4.345.955q2.158.544 4.278 1.221t4.195 1.484 4.095 1.74q2.02.932 3.98 1.988.46.249.918.504.94.525 1.864 1.077z"/></g><g fill-rule="evenodd"><path fill="#EEE" d="M147.191 75.183q0-1.16-.037-2.321-.038-1.16-.112-2.32-.075-1.158-.187-2.313-.113-1.156-.262-2.307t-.336-2.298q-.186-1.146-.41-2.285t-.482-2.271-.556-2.254-.629-2.235-.7-2.214-.771-2.19q-.404-1.089-.842-2.164t-.91-2.135-.98-2.106-1.046-2.072-1.113-2.038q-.573-1.01-1.178-2-.605-.991-1.242-1.962-.636-.971-1.304-1.921t-1.366-1.878-1.425-1.832-1.484-1.786q-.756-.88-1.54-1.737-.785-.856-1.597-1.686t-1.649-1.634-1.701-1.58-1.751-1.525-1.8-1.467-1.846-1.408-1.89-1.348-1.933-1.286-1.973-1.224l-8.006 13.856q.77.465 1.525.954.754.49 1.493 1.003.738.513 1.46 1.05.721.536 1.425 1.096t1.39 1.141 1.352 1.186q.667.603 1.314 1.228.646.625 1.273 1.27t1.232 1.31 1.19 1.349q.583.684 1.145 1.386.561.703 1.1 1.423t1.054 1.457 1.006 1.49.959 1.522q.467.768.909 1.552.442.783.858 1.58.417.797.808 1.606.39.81.755 1.632t.703 1.656.65 1.677q.31.844.594 1.697t.54 1.715q.257.862.485 1.732.229.87.43 1.746.2.877.372 1.76.172.882.316 1.77t.259 1.78.201 1.787.145 1.792q.057.898.086 1.796.03.9.03 1.799 0 .383-.006.766h16.001q.004-.384.004-.767"/><path fill="#FFF" d="M150.387 75.984q.004-.4.004-.8 0-19.843-9.79-37.103-9.79-17.259-26.82-27.441l-2.788-1.667-11.201 19.386 2.687 1.623q11.876 7.169 18.694 19.25t6.818 25.952q0 .36-.005.722l-.044 3.244h22.411zm-3.2-1.567q.004.383.004.766t-.004.767h-16.001q.005-.383.005-.766 0-.384-.005-.767-.012-.887-.052-1.773-.04-.887-.109-1.771-.068-.885-.165-1.767-.096-.882-.22-1.76-.124-.88-.276-1.753-.152-.875-.332-1.744t-.386-1.731-.442-1.72-.495-1.703-.55-1.687q-.287-.84-.602-1.67-.314-.829-.655-1.648-.34-.82-.707-1.628t-.758-1.604-.808-1.58-.859-1.552-.907-1.526q-.465-.755-.955-1.495t-1.001-1.465q-.513-.724-1.048-1.432t-1.092-1.398-1.137-1.363-1.178-1.326-1.22-1.288q-.621-.635-1.261-1.25-.64-.614-1.3-1.207-.659-.594-1.337-1.167t-1.373-1.124q-.695-.55-1.408-1.08-.713-.528-1.442-1.034-.729-.505-1.474-.988-.744-.482-1.504-.94l6.405-11.086 1.6-2.77q1.375.821 2.712 1.703.896.591 1.773 1.209.878.617 1.736 1.26.859.643 1.698 1.312.84.669 1.658 1.362.82.693 1.617 1.41.798.718 1.574 1.459.776.74 1.53 1.504.753.764 1.484 1.55.73.785 1.437 1.593t1.39 1.635 1.34 1.675q.657.848 1.29 1.715.631.867 1.237 1.753t1.185 1.789q.58.903 1.131 1.823.552.92 1.077 1.856t1.02 1.887.964 1.917.907 1.945.848 1.97q.41.993.789 1.996t.729 2.018.668 2.039q.32 1.024.608 2.058.289 1.033.546 2.075.258 1.041.484 2.09t.421 2.104.36 2.115q.163 1.06.294 2.125.132 1.065.232 2.134t.169 2.139.104 2.143q.028.833.037 1.667"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m103.5 123.553 8.005 13.854q1.003-.585 1.987-1.202.983-.617 1.946-1.266.963-.648 1.905-1.328.941-.679 1.86-1.388.92-.71 1.815-1.448.896-.739 1.768-1.506.871-.767 1.718-1.561.846-.795 1.666-1.617.82-.821 1.614-1.67.794-.847 1.56-1.72.765-.872 1.502-1.77.737-.896 1.445-1.817t1.385-1.863 1.325-1.907q.647-.964 1.263-1.948t1.199-1.988 1.134-2.026 1.069-2.061 1.001-2.095.934-2.126.864-2.155.795-2.181.723-2.206q.344-1.11.653-2.229.308-1.119.58-2.248t.507-2.265.434-2.281.36-2.294.286-2.304.212-2.312.137-2.318.062-2.32h-16.002q-.012.898-.053 1.797t-.111 1.795-.17 1.79q-.098.894-.225 1.784-.128.89-.284 1.776t-.34 1.766-.396 1.754q-.213.874-.453 1.74-.24.867-.509 1.725-.268.859-.563 1.708-.296.85-.618 1.689-.323.84-.672 1.668-.35.829-.726 1.646t-.777 1.621-.83 1.596-.88 1.568-.93 1.54-.98 1.508q-.5.746-1.026 1.476-.525.73-1.074 1.442-.548.713-1.12 1.408-.57.694-1.164 1.37-.593.676-1.207 1.333t-1.25 1.293-1.29 1.252-1.331 1.21-1.368 1.167-1.406 1.123-1.44 1.076-1.474 1.03-1.507.982-1.537.933"/><path fill="#FFF" d="m99.138 122.399 1.592 2.755 9.612 16.637 2.776-1.62q17.138-10 27.111-27.155 9.973-17.154 10.184-36.996l.035-3.234h-22.392l-.043 3.156q-.19 13.87-7.173 25.857t-18.955 18.992zm4.363 1.154q.776-.454 1.537-.933t1.507-.982 1.474-1.03 1.44-1.076 1.406-1.123 1.368-1.166 1.33-1.21 1.29-1.253 1.25-1.293 1.208-1.333 1.164-1.37 1.12-1.408 1.074-1.442 1.027-1.476.979-1.509.93-1.54q.453-.776.88-1.567.428-.791.83-1.596.401-.804.777-1.621t.726-1.646.672-1.668q.322-.84.618-1.689.295-.85.563-1.708t.509-1.725q.24-.866.453-1.74.212-.874.396-1.754t.34-1.766.284-1.776.226-1.784.169-1.79.11-1.795q.042-.899.054-1.798h16.002q-.017 1.601-.106 3.2-.058 1.06-.148 2.117t-.21 2.11-.272 2.105-.335 2.095-.396 2.085-.457 2.071-.518 2.058-.578 2.041q-.305 1.017-.639 2.024t-.697 2.003-.757 1.983q-.393.985-.814 1.959-.422.973-.872 1.934-.45.96-.929 1.908-.478.947-.984 1.88t-1.04 1.85q-.532.916-1.092 1.817-.56.902-1.147 1.786-.586.884-1.198 1.75-.612.867-1.25 1.715-.637.848-1.3 1.677-.662.83-1.348 1.639t-1.396 1.597-1.442 1.556-1.488 1.513q-.755.745-1.532 1.468t-1.574 1.422-1.616 1.376q-.818.675-1.655 1.327t-1.694 1.278-1.73 1.227-1.766 1.176q-1.346.868-2.729 1.674l-1.6-2.77z"/></g><g fill-rule="evenodd"><path fill="#128BED" d="M19.242 75.983H3.241q.013 1.183.064 2.364.051 1.182.142 2.361.09 1.18.219 2.355.128 1.176.296 2.347.167 1.17.373 2.335t.45 2.322.525 2.306q.282 1.148.602 2.287.319 1.139.676 2.267.356 1.127.75 2.243.393 1.115.823 2.217.43 1.101.896 2.189.465 1.087.967 2.158.5 1.071 1.037 2.125t1.107 2.09 1.175 2.053q.604 1.017 1.241 2.013t1.307 1.971 1.371 1.927q.701.953 1.434 1.881.732.929 1.494 1.833t1.554 1.783 1.612 1.731 1.667 1.677 1.722 1.621 1.774 1.564 1.825 1.505 1.873 1.444 1.92 1.382q.97.675 1.963 1.318t2.006 1.253 2.046 1.186l8.006-13.856q-.806-.445-1.597-.917-.791-.471-1.567-.969-.775-.497-1.533-1.02-.758-.522-1.5-1.07-.74-.547-1.462-1.118t-1.426-1.166-1.386-1.212-1.346-1.257-1.303-1.301-1.26-1.343q-.62-.682-1.216-1.384t-1.17-1.422q-.572-.721-1.121-1.461-.55-.74-1.073-1.497-.525-.757-1.024-1.53-.499-.775-.972-1.565-.474-.79-.92-1.595-.448-.805-.868-1.624-.421-.82-.814-1.652-.393-.833-.76-1.678-.365-.845-.703-1.702t-.647-1.724-.59-1.745q-.28-.877-.532-1.763-.251-.886-.474-1.78-.222-.893-.415-1.794-.193-.9-.356-1.807t-.297-1.817q-.133-.912-.236-1.827t-.176-1.833q-.074-.918-.116-1.838t-.056-1.84"/><path fill="#FFF" d="M22.399 72.783H.007l.034 3.234q.215 20.23 10.55 37.62t28.002 27.248l2.753 1.537 11.214-19.41-2.855-1.578q-12.448-6.877-19.758-19.076-7.31-12.198-7.505-26.419zm-3.157 3.2H3.241q.017 1.601.105 3.2.06 1.081.152 2.16t.216 2.154.282 2.147q.157 1.07.345 2.137.19 1.066.41 2.126.221 1.06.474 2.112.252 1.053.537 2.098.284 1.044.6 2.08.315 1.036.661 2.061.347 1.026.724 2.04.377 1.016.784 2.019t.845 1.993q.438.99.905 1.967t.963 1.939 1.02 1.91q.526.946 1.079 1.877.553.93 1.134 1.844t1.189 1.81 1.243 1.772 1.295 1.735 1.347 1.695 1.398 1.653q.71.817 1.446 1.611.735.795 1.494 1.567.76.772 1.541 1.52.782.75 1.586 1.475t1.63 1.425q.825.7 1.67 1.376.847.676 1.713 1.326t1.75 1.273 1.79 1.22 1.824 1.165q.446.276.897.545.923.552 1.862 1.076l6.405-11.085 1.6-2.77q-.94-.521-1.862-1.077-.76-.459-1.504-.941-.745-.482-1.474-.988t-1.442-1.034q-.712-.53-1.408-1.08-.695-.551-1.373-1.124-.678-.572-1.337-1.166-.66-.594-1.3-1.209-.64-.614-1.26-1.248-.62-.635-1.22-1.289t-1.18-1.326q-.578-.672-1.136-1.363-.557-.69-1.092-1.398t-1.048-1.432q-.512-.725-1.001-1.465-.49-.74-.955-1.495-.466-.756-.907-1.525-.442-.77-.858-1.554-.417-.783-.81-1.58-.39-.795-.757-1.603-.367-.809-.707-1.628-.34-.82-.655-1.649t-.603-1.67q-.288-.838-.549-1.686t-.495-1.704q-.235-.856-.442-1.719t-.386-1.732-.332-1.743-.276-1.753-.22-1.76q-.097-.882-.165-1.767t-.109-1.771q-.04-.887-.052-1.774"/></g><g clip-path="url(#b)"><circle cx="75" cy="75" r="44" fill="#EEE" style="opacity:.5"/></g><rect width="1.575" height="4.726" x="73.482" y="24" fill="#EEE" rx=".788"/><rect width="1.575" height="4.726" x="78.839" y="24.198" fill="#EEE" rx=".788" transform="rotate(6 78.839 24.199)"/><rect width="1.575" height="4.726" x="84.153" y="24.993" fill="#EEE" rx=".788" transform="rotate(12 84.154 24.995)skewX(-.001)"/><rect width="1.575" height="4.726" x="89.329" y="26.33" fill="#EEE" rx=".788" transform="rotate(17.999 89.329 26.332)skewX(-.001)"/><rect width="1.575" height="4.726" x="94.384" y="28.206" fill="#EEE" rx=".788" transform="rotate(23.999 94.383 28.207)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="30.534" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="128.206" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="103.755" y="33.387" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="103.755" y="131.058" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="36.681" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="134.353" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="40.413" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="138.086" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="44.496" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="142.169" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="118.261" y="48.928" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="118.261" y="146.601" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.779" y="53.627" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.78" y="151.3" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="58.589" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="156.263" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="63.734" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="161.407" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="125.197" y="68.978" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.198" y="166.652" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.6" y="74.332" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.6" y="172.006" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.402" y="79.688" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="125.402" y="177.361" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="124.607" y="84.994" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="124.607" y="182.668" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="90.164" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="187.837" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="95.196" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="192.87" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="100.017" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="197.69" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="116.212" y="104.549" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="116.212" y="202.221" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="108.795" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="206.467" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="112.68" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="210.352" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="116.128" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="213.799" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="100.621" y="119.121" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="100.621" y="216.793" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="56.14" y="32.406" fill="#EEE" rx=".788" transform="rotate(156.001 56.14 32.405)skewX(.001)"/><rect width="1.575" height="4.726" x="60.728" y="30.763" fill="#EEE" rx=".788" transform="rotate(162.001 60.728 30.763)skewX(.001)"/><rect width="1.575" height="4.726" x="65.446" y="29.573" fill="#EEE" rx=".788" transform="rotate(168 65.446 29.573)skewX(.001)"/><rect width="1.575" height="4.726" x="70.253" y="28.92" fill="#EEE" rx=".788" transform="rotate(174 70.253 28.92)"/></g></svg>`,
        [RISK_LEVEL.L3]: `<svg xmlns="http://www.w3.org/2000/svg" width="151" height="151" fill="none"><defs><clipPath id="a"><rect width="151" height="151" rx="0"/></clipPath><clipPath id="b"><rect width="104" height="104" x="23" y="23" rx="0"/></clipPath></defs><g clip-path="url(#a)"><g fill-rule="evenodd"><path fill="#FFC043" d="M47.453 26.575 39.448 12.72q-1.026.587-2.033 1.208-1.007.62-1.993 1.274-.985.653-1.95 1.338-.963.686-1.904 1.403-.94.716-1.857 1.463t-1.809 1.525-1.757 1.582-1.705 1.64-1.65 1.694q-.81.861-1.592 1.748-.783.887-1.535 1.8-.753.912-1.475 1.848-.723.937-1.414 1.897-.69.96-1.35 1.941t-1.286 1.985q-.627 1.003-1.22 2.026-.594 1.023-1.153 2.065-.56 1.042-1.084 2.102-.525 1.06-1.015 2.136t-.944 2.169-.872 2.198-.8 2.226-.726 2.25q-.345 1.132-.652 2.274t-.577 2.294q-.27 1.151-.501 2.311-.232 1.16-.425 2.326t-.348 2.34-.272 2.35-.194 2.356q-.077 1.18-.116 2.362T3.2 75.217q0 .383.004.766h16.001q-.005-.383-.005-.766 0-.92.03-1.841t.091-1.84.151-1.835.212-1.83.271-1.82.332-1.813.39-1.8q.21-.896.45-1.785.24-.89.508-1.77.269-.881.566-1.753.298-.871.624-1.733.326-.86.68-1.711.354-.85.736-1.688.381-.838.79-1.663t.846-1.637q.436-.81.899-1.607.462-.796.95-1.577.49-.78 1.003-1.545.514-.764 1.053-1.511t1.101-1.476 1.15-1.439 1.196-1.4 1.242-1.36 1.286-1.318 1.328-1.276 1.37-1.23 1.41-1.186 1.447-1.138 1.484-1.09q.751-.534 1.52-1.041.768-.508 1.552-.99.785-.483 1.585-.94"/><path fill="#FFF" d="m51.833 27.76-1.61-2.786L40.626 8.36l-2.766 1.582Q20.3 19.988 10.15 37.488 0 54.988 0 75.218q0 .4.004.8l.034 3.165h22.411l-.044-3.243q-.005-.362-.005-.723 0-14.221 7.143-26.519t19.495-19.344zm-5.981-3.956 1.6 2.77q-.268.154-.535.31-.776.454-1.537.933t-1.507.982-1.474 1.03-1.44 1.077-1.406 1.122-1.368 1.167-1.33 1.21-1.291 1.252-1.25 1.293-1.207 1.333-1.165 1.37q-.57.695-1.12 1.408-.548.712-1.073 1.442t-1.027 1.477-.98 1.508q-.477.762-.93 1.54-.452.776-.88 1.567-.427.792-.829 1.596-.402.805-.778 1.622-.375.817-.725 1.645t-.672 1.668q-.323.84-.618 1.689-.296.85-.564 1.708t-.508 1.725q-.24.866-.453 1.74-.212.874-.397 1.754-.184.88-.34 1.766t-.283 1.776-.226 1.784-.17 1.79-.11 1.795-.054 1.798q-.005.383-.005.766t.005.766h-16Q3.2 75.6 3.2 75.217t.004-.766q.009-.834.037-1.668.037-1.094.107-2.186t.174-2.182.24-2.176.306-2.167.372-2.157.437-2.145.502-2.13.566-2.115.631-2.096.694-2.077.757-2.053.819-2.03q.425-1.01.88-2.005t.94-1.976q.486-.981 1.001-1.947t1.06-1.916 1.116-1.882 1.174-1.848q.6-.915 1.23-1.811.628-.897 1.283-1.773.655-.877 1.337-1.734.682-.856 1.389-1.691.708-.836 1.44-1.649t1.49-1.604q.756-.79 1.537-1.558t1.584-1.511 1.629-1.462q.826-.718 1.673-1.412.847-.693 1.715-1.36t1.756-1.307 1.794-1.254 1.832-1.198q1.091-.69 2.207-1.34.267-.157.535-.31z"/></g><g fill-rule="evenodd"><path fill="#FFC043" d="m104.142 27.259 8.006-13.856q-2.082-1.245-4.243-2.347T103.513 9q-2.23-.953-4.52-1.755t-4.628-1.447q-2.338-.646-4.714-1.133t-4.78-.813q-2.404-.325-4.825-.489Q77.626 3.2 75.2 3.2q-2.339 0-4.673.152t-4.654.455-4.614.756-4.555 1.053-4.478 1.348q-2.216.746-4.38 1.635-2.164.888-4.265 1.916-2.102 1.027-4.132 2.188l8.004 13.854q1.577-.899 3.208-1.694 1.632-.796 3.311-1.484t3.4-1.265q1.72-.578 3.475-1.043 1.755-.466 3.535-.816t3.58-.585 3.612-.352q1.811-.118 3.626-.118 1.902 0 3.799.129t3.782.387 3.746.642 3.695.894 3.625 1.144 3.54 1.386q1.745.754 3.436 1.624t3.32 1.853"/><path fill="#FFF" d="m105.28 31.684 1.633-2.824 9.583-16.586-2.706-1.618Q95.966 0 75.2 0 55.21 0 37.86 9.926l-2.79 1.596 11.205 19.392 2.764-1.577Q61.199 22.4 75.2 22.4q14.702 0 27.288 7.598zm.463-7.196-1.6 2.77q-.922-.555-1.864-1.076-1.547-.854-3.145-1.61-1.597-.755-3.24-1.408-1.641-.653-3.321-1.201t-3.39-.99q-1.712-.44-3.447-.773-1.736-.332-3.489-.555-1.753-.222-3.516-.334-1.764-.111-3.531-.111-1.815 0-3.626.118t-3.611.352-3.58.585-3.536.816-3.475 1.043-3.4 1.265-3.31 1.484-3.209 1.694L41.05 15.474l-1.601-2.77q1.39-.796 2.813-1.528 1.902-.978 3.859-1.842 1.956-.864 3.96-1.61t4.05-1.372 4.124-1.13 4.184-.882 4.229-.633q2.123-.253 4.258-.38T75.2 3.2q2.226 0 4.447.137 2.221.138 4.43.412 2.209.275 4.396.685 2.188.41 4.345.955 2.158.544 4.279 1.221t4.194 1.484 4.095 1.74q2.02.932 3.98 1.988.46.249.919.504.94.525 1.863 1.077z"/></g><g fill-rule="evenodd"><path fill="#EEE" d="M147.191 75.183q0-1.16-.037-2.321-.038-1.16-.112-2.32-.075-1.158-.187-2.313-.113-1.156-.262-2.307t-.336-2.298q-.186-1.146-.41-2.285t-.482-2.271-.556-2.254-.629-2.235-.7-2.214-.771-2.19q-.404-1.089-.842-2.164t-.91-2.135-.98-2.106-1.046-2.072-1.113-2.038q-.573-1.01-1.178-2-.605-.991-1.242-1.962-.636-.971-1.304-1.921t-1.366-1.878-1.425-1.832-1.484-1.786q-.756-.88-1.54-1.737-.785-.856-1.597-1.686t-1.649-1.634-1.701-1.58-1.751-1.525-1.8-1.467-1.846-1.408-1.89-1.348-1.933-1.286-1.973-1.224l-8.006 13.856q.77.465 1.525.954.754.49 1.493 1.003.738.513 1.46 1.05.721.536 1.425 1.096t1.39 1.141 1.352 1.186q.667.603 1.314 1.228.646.625 1.273 1.27t1.232 1.31 1.19 1.349q.583.684 1.145 1.386.561.703 1.1 1.423t1.054 1.457 1.006 1.49.959 1.522q.467.768.909 1.552.442.783.858 1.58.417.797.808 1.606.39.81.755 1.632t.703 1.656.65 1.677q.31.844.594 1.697t.54 1.715q.257.862.485 1.732.229.87.43 1.746.2.877.372 1.76.172.882.316 1.77t.259 1.78.201 1.787.145 1.792q.057.898.086 1.796.03.9.03 1.799 0 .383-.006.766h16.001q.004-.384.004-.767"/><path fill="#FFF" d="M150.387 75.984q.004-.4.004-.8 0-19.843-9.79-37.103-9.79-17.259-26.82-27.441l-2.788-1.667-11.201 19.386 2.687 1.623q11.876 7.169 18.694 19.25t6.818 25.952q0 .36-.005.722l-.044 3.244h22.411zm-3.2-1.567q.004.383.004.766t-.004.767h-16.001q.005-.383.005-.766 0-.384-.005-.767-.012-.887-.052-1.773-.04-.887-.109-1.771-.068-.885-.165-1.767-.096-.882-.22-1.76-.124-.88-.276-1.753-.152-.875-.332-1.744t-.386-1.731-.442-1.72-.495-1.703-.55-1.687q-.287-.84-.602-1.67-.314-.829-.655-1.648-.34-.82-.707-1.628t-.758-1.604-.808-1.58-.859-1.552-.907-1.526q-.465-.755-.955-1.495t-1.001-1.465q-.513-.724-1.048-1.432t-1.092-1.398-1.137-1.363-1.178-1.326-1.22-1.288q-.621-.635-1.261-1.25-.64-.614-1.3-1.207-.659-.594-1.337-1.167t-1.373-1.124q-.695-.55-1.408-1.08-.713-.528-1.442-1.034-.729-.505-1.474-.988-.744-.482-1.504-.94l6.405-11.086 1.6-2.77q1.375.821 2.712 1.703.896.591 1.773 1.209.878.617 1.736 1.26.859.643 1.698 1.312.84.669 1.658 1.362.82.693 1.617 1.41.798.718 1.574 1.459.776.74 1.53 1.504.753.764 1.484 1.55.73.785 1.437 1.593t1.39 1.635 1.34 1.675q.657.848 1.29 1.715.631.867 1.237 1.753t1.185 1.789q.58.903 1.131 1.823.552.92 1.077 1.856t1.02 1.887.964 1.917.907 1.945.848 1.97q.41.993.789 1.996t.729 2.018.668 2.039q.32 1.024.608 2.058.289 1.033.546 2.075.258 1.041.484 2.09t.421 2.104.36 2.115q.163 1.06.294 2.125.132 1.065.232 2.134t.169 2.139.104 2.143q.028.833.037 1.667"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m103.5 123.553 8.005 13.854q1.003-.585 1.987-1.202.983-.617 1.946-1.266.963-.648 1.905-1.328.941-.679 1.86-1.388.92-.71 1.815-1.448.896-.739 1.768-1.506.871-.767 1.718-1.561.846-.795 1.666-1.617.82-.821 1.614-1.67.794-.847 1.56-1.72.765-.872 1.502-1.77.737-.896 1.445-1.817t1.385-1.863 1.325-1.907q.647-.964 1.263-1.948t1.199-1.988 1.134-2.026 1.069-2.061 1.001-2.095.934-2.126.864-2.155.795-2.181.723-2.206q.344-1.11.653-2.229.308-1.119.58-2.248t.507-2.265.434-2.281.36-2.294.286-2.304.212-2.312.137-2.318.062-2.32h-16.002q-.012.898-.053 1.797t-.111 1.795-.17 1.79q-.098.894-.225 1.784-.128.89-.284 1.776t-.34 1.766-.396 1.754q-.213.874-.453 1.74-.24.867-.509 1.725-.268.859-.563 1.708-.296.85-.618 1.689-.323.84-.672 1.668-.35.829-.726 1.646t-.777 1.621-.83 1.596-.88 1.568-.93 1.54-.98 1.508q-.5.746-1.026 1.476-.525.73-1.074 1.442-.548.713-1.12 1.408-.57.694-1.164 1.37-.593.676-1.207 1.333t-1.25 1.293-1.29 1.252-1.331 1.21-1.368 1.167-1.406 1.123-1.44 1.076-1.474 1.03-1.507.982-1.537.933"/><path fill="#FFF" d="m99.138 122.399 1.592 2.755 9.612 16.637 2.776-1.62q17.138-10 27.111-27.155 9.973-17.154 10.184-36.996l.035-3.234h-22.392l-.043 3.156q-.19 13.87-7.173 25.857t-18.955 18.992zm4.363 1.154q.776-.454 1.537-.933t1.507-.982 1.474-1.03 1.44-1.076 1.406-1.123 1.368-1.166 1.33-1.21 1.29-1.253 1.25-1.293 1.208-1.333 1.164-1.37 1.12-1.408 1.074-1.442 1.027-1.476.979-1.509.93-1.54q.453-.776.88-1.567.428-.791.83-1.596.401-.804.777-1.621t.726-1.646.672-1.668q.322-.84.618-1.689.295-.85.563-1.708t.509-1.725q.24-.866.453-1.74.212-.874.396-1.754t.34-1.766.284-1.776.226-1.784.169-1.79.11-1.795q.042-.899.054-1.798h16.002q-.017 1.601-.106 3.2-.058 1.06-.148 2.117t-.21 2.11-.272 2.105-.335 2.095-.396 2.085-.457 2.071-.518 2.058-.578 2.041q-.305 1.017-.639 2.024t-.697 2.003-.757 1.983q-.393.985-.814 1.959-.422.973-.872 1.934-.45.96-.929 1.908-.478.947-.984 1.88t-1.04 1.85q-.532.916-1.092 1.817-.56.902-1.147 1.786-.586.884-1.198 1.75-.612.867-1.25 1.715-.637.848-1.3 1.677-.662.83-1.348 1.639t-1.396 1.597-1.442 1.556-1.488 1.513q-.755.745-1.532 1.468t-1.574 1.422-1.616 1.376q-.818.675-1.655 1.327t-1.694 1.278-1.73 1.227-1.766 1.176q-1.346.868-2.729 1.674l-1.6-2.77z"/></g><g fill-rule="evenodd"><path fill="#FFC043" d="M19.242 75.983H3.241q.013 1.183.064 2.364.051 1.182.142 2.361.09 1.18.219 2.355.128 1.176.296 2.347.167 1.17.373 2.335t.45 2.322.525 2.306q.282 1.148.602 2.287.319 1.139.676 2.267.356 1.127.75 2.243.393 1.115.823 2.217.43 1.101.896 2.189.465 1.087.967 2.158.5 1.071 1.037 2.125t1.107 2.09 1.175 2.053q.604 1.017 1.241 2.013t1.307 1.971 1.371 1.927q.701.953 1.434 1.881.732.929 1.494 1.833t1.554 1.783 1.612 1.731 1.667 1.677 1.722 1.621 1.774 1.564 1.825 1.505 1.873 1.444 1.92 1.382q.97.675 1.963 1.318t2.006 1.253 2.046 1.186l8.006-13.856q-.806-.445-1.597-.917-.791-.471-1.567-.969-.775-.497-1.533-1.02-.758-.522-1.5-1.07-.74-.547-1.462-1.118t-1.426-1.166-1.386-1.212-1.346-1.257-1.303-1.301-1.26-1.343q-.62-.682-1.216-1.384t-1.17-1.422q-.572-.721-1.121-1.461-.55-.74-1.073-1.497-.525-.757-1.024-1.53-.499-.775-.972-1.565-.474-.79-.92-1.595-.448-.805-.868-1.624-.421-.82-.814-1.652-.393-.833-.76-1.678-.365-.845-.703-1.702t-.647-1.724-.59-1.745q-.28-.877-.532-1.763-.251-.886-.474-1.78-.222-.893-.415-1.794-.193-.9-.356-1.807t-.297-1.817q-.133-.912-.236-1.827t-.176-1.833q-.074-.918-.116-1.838t-.056-1.84"/><path fill="#FFF" d="M22.399 72.783H.007l.034 3.234q.215 20.23 10.55 37.62t28.002 27.248l2.753 1.537 11.214-19.41-2.855-1.578q-12.448-6.877-19.758-19.076-7.31-12.198-7.505-26.419zm-3.157 3.2H3.241q.017 1.601.105 3.2.06 1.081.152 2.16t.216 2.154.282 2.147q.157 1.07.345 2.137.19 1.066.41 2.126.221 1.06.474 2.112.252 1.053.537 2.098.284 1.044.6 2.08.315 1.036.661 2.061.347 1.026.724 2.04.377 1.016.784 2.019t.845 1.993q.438.99.905 1.967t.963 1.939 1.02 1.91q.526.946 1.079 1.877.553.93 1.134 1.844t1.189 1.81 1.243 1.772 1.295 1.735 1.347 1.695 1.398 1.653q.71.817 1.446 1.611.735.795 1.494 1.567.76.772 1.541 1.52.782.75 1.586 1.475t1.63 1.425q.825.7 1.67 1.376.847.676 1.713 1.326t1.75 1.273 1.79 1.22 1.824 1.165q.446.276.897.545.923.552 1.862 1.076l6.405-11.085 1.6-2.77q-.94-.521-1.862-1.077-.76-.459-1.504-.941-.745-.482-1.474-.988t-1.442-1.034q-.712-.53-1.408-1.08-.695-.551-1.373-1.124-.678-.572-1.337-1.166-.66-.594-1.3-1.209-.64-.614-1.26-1.248-.62-.635-1.22-1.289t-1.18-1.326q-.578-.672-1.136-1.363-.557-.69-1.092-1.398t-1.048-1.432q-.512-.725-1.001-1.465-.49-.74-.955-1.495-.466-.756-.907-1.525-.442-.77-.858-1.554-.417-.783-.81-1.58-.39-.795-.757-1.603-.367-.809-.707-1.628-.34-.82-.655-1.649t-.603-1.67q-.288-.838-.549-1.686t-.495-1.704q-.235-.856-.442-1.719t-.386-1.732-.332-1.743-.276-1.753-.22-1.76q-.097-.882-.165-1.767t-.109-1.771q-.04-.887-.052-1.774"/></g><g clip-path="url(#b)"><circle cx="75" cy="75" r="44" fill="#EEE" style="opacity:.5"/></g><rect width="1.575" height="4.726" x="73.482" y="24" fill="#EEE" rx=".788"/><rect width="1.575" height="4.726" x="78.839" y="24.198" fill="#EEE" rx=".788" transform="rotate(6 78.839 24.199)"/><rect width="1.575" height="4.726" x="84.153" y="24.993" fill="#EEE" rx=".788" transform="rotate(12 84.154 24.995)skewX(-.001)"/><rect width="1.575" height="4.726" x="89.329" y="26.33" fill="#EEE" rx=".788" transform="rotate(17.999 89.329 26.332)skewX(-.001)"/><rect width="1.575" height="4.726" x="94.384" y="28.206" fill="#EEE" rx=".788" transform="rotate(23.999 94.383 28.207)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="30.534" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="128.206" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="103.755" y="33.387" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="103.755" y="131.058" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="36.681" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="134.353" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="40.413" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="138.086" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="44.496" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="142.169" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="118.261" y="48.928" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="118.261" y="146.601" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.779" y="53.627" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.78" y="151.3" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="58.589" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="156.263" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="63.734" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="161.407" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="125.197" y="68.978" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.198" y="166.652" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.6" y="74.332" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.6" y="172.006" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.402" y="79.688" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="125.402" y="177.361" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="124.607" y="84.994" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="124.607" y="182.668" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="90.164" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="187.837" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="95.196" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="192.87" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="100.017" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="197.69" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="116.212" y="104.549" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="116.212" y="202.221" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="108.795" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="206.467" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="112.68" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="210.352" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="116.128" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="213.799" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="100.621" y="119.121" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="100.621" y="216.793" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="56.14" y="32.406" fill="#EEE" rx=".788" transform="rotate(156.001 56.14 32.405)skewX(.001)"/><rect width="1.575" height="4.726" x="60.728" y="30.763" fill="#EEE" rx=".788" transform="rotate(162.001 60.728 30.763)skewX(.001)"/><rect width="1.575" height="4.726" x="65.446" y="29.573" fill="#EEE" rx=".788" transform="rotate(168 65.446 29.573)skewX(.001)"/><rect width="1.575" height="4.726" x="70.253" y="28.92" fill="#EEE" rx=".788" transform="rotate(174 70.253 28.92)"/></g></svg>`,
        [RISK_LEVEL.L4]: `<svg xmlns="http://www.w3.org/2000/svg" width="151" height="151" fill="none"><defs><clipPath id="a"><rect width="151" height="151" rx="0"/></clipPath><clipPath id="b"><rect width="104" height="104" x="23" y="23" rx="0"/></clipPath></defs><g clip-path="url(#a)"><g fill-rule="evenodd"><path fill="#FF722D" d="M47.453 26.575 39.448 12.72q-1.026.587-2.033 1.208-1.007.62-1.993 1.274-.985.653-1.95 1.338-.963.686-1.904 1.403-.94.716-1.857 1.463t-1.809 1.525-1.757 1.582-1.705 1.64-1.65 1.694q-.81.861-1.592 1.748-.783.887-1.535 1.8-.753.912-1.475 1.848-.723.937-1.414 1.897-.69.96-1.35 1.941t-1.286 1.985q-.627 1.003-1.22 2.026-.594 1.023-1.153 2.065-.56 1.042-1.084 2.102-.525 1.06-1.015 2.136t-.944 2.169-.872 2.198-.8 2.226-.726 2.25q-.345 1.132-.652 2.274t-.577 2.294q-.27 1.151-.501 2.311-.232 1.16-.425 2.326t-.348 2.34-.272 2.35-.194 2.356q-.077 1.18-.116 2.362T3.2 75.217q0 .383.004.766h16.001q-.005-.383-.005-.766 0-.92.03-1.841t.091-1.84.151-1.835.212-1.83.271-1.82.332-1.813.39-1.8q.21-.896.45-1.785.24-.89.508-1.77.269-.881.566-1.753.298-.871.624-1.733.326-.86.68-1.711.354-.85.736-1.688.381-.838.79-1.663t.846-1.637q.436-.81.899-1.607.462-.796.95-1.577.49-.78 1.003-1.545.514-.764 1.053-1.511t1.101-1.476 1.15-1.439 1.196-1.4 1.242-1.36 1.286-1.318 1.328-1.276q.675-.626 1.37-1.23.695-.605 1.41-1.186.714-.581 1.447-1.138.733-.558 1.484-1.09.751-.534 1.52-1.041.768-.508 1.552-.99.785-.483 1.585-.94"/><path fill="#FFF" d="m51.833 27.76-1.61-2.786L40.626 8.36l-2.766 1.582Q20.3 19.988 10.15 37.488 0 54.988 0 75.218q0 .4.004.8l.034 3.165h22.411l-.044-3.243q-.005-.362-.005-.723 0-14.221 7.143-26.519t19.495-19.344zm-5.981-3.956 1.6 2.77q-.268.154-.535.31-.776.454-1.537.933t-1.507.982-1.474 1.03-1.44 1.077-1.406 1.122-1.368 1.167-1.33 1.21-1.291 1.252-1.25 1.293-1.207 1.333-1.165 1.37q-.57.695-1.12 1.408-.548.712-1.073 1.442t-1.027 1.477-.98 1.508-.93 1.54-.88 1.567-.829 1.596q-.402.805-.778 1.622-.375.817-.725 1.645t-.672 1.668q-.323.84-.618 1.689-.296.85-.564 1.708t-.508 1.725q-.24.866-.453 1.74-.212.874-.397 1.754-.184.88-.34 1.766t-.283 1.776-.226 1.784-.17 1.79-.11 1.795-.054 1.798q-.005.383-.005.766t.005.766h-16Q3.2 75.6 3.2 75.217t.004-.766q.009-.834.037-1.668.037-1.094.107-2.186t.174-2.182.24-2.176.306-2.167.372-2.158.437-2.144q.234-1.07.502-2.131.267-1.061.566-2.114.3-1.053.631-2.097.331-1.043.694-2.076.363-1.032.757-2.053.394-1.022.819-2.03.425-1.01.88-2.005t.94-1.976q.486-.981 1.001-1.947t1.06-1.916 1.116-1.882 1.174-1.848q.6-.915 1.23-1.811.628-.897 1.283-1.773.655-.877 1.337-1.734.682-.856 1.39-1.691t1.439-1.649 1.49-1.604q.756-.79 1.537-1.558t1.584-1.511 1.629-1.462q.826-.718 1.673-1.412.847-.693 1.715-1.36t1.756-1.307 1.794-1.254 1.832-1.198q1.091-.69 2.207-1.34.267-.157.535-.31z"/></g><g fill-rule="evenodd"><path fill="#FF722D" d="m104.142 27.259 8.006-13.856q-2.082-1.245-4.243-2.347t-4.392-2.055-4.52-1.756-4.628-1.447-4.714-1.133-4.78-.813q-2.405-.325-4.825-.489T75.2 3.2q-2.34 0-4.673.152t-4.654.455-4.614.756q-2.295.452-4.555 1.053-2.261.601-4.478 1.348-2.217.746-4.38 1.635-2.164.888-4.265 1.916-2.102 1.027-4.132 2.188l8.004 13.854q1.577-.899 3.208-1.694 1.632-.796 3.311-1.484t3.4-1.265q1.72-.578 3.475-1.043 1.755-.466 3.535-.816t3.58-.585 3.612-.352q1.811-.118 3.626-.118 1.902 0 3.799.129t3.782.387 3.746.642 3.695.894 3.625 1.144 3.539 1.386 3.437 1.624 3.32 1.853"/><path fill="#FFF" d="m105.28 31.684 1.633-2.824 9.583-16.586-2.706-1.617Q95.966 0 75.2 0 55.21 0 37.86 9.926l-2.79 1.595 11.205 19.393 2.764-1.577Q61.199 22.4 75.2 22.4q14.702 0 27.288 7.598zm.463-7.196-1.6 2.77q-.922-.555-1.864-1.076-1.547-.854-3.145-1.61-1.597-.755-3.24-1.408-1.641-.653-3.32-1.201-1.68-.548-3.392-.99-1.711-.44-3.446-.773-1.736-.332-3.489-.555-1.753-.222-3.516-.334-1.764-.111-3.531-.111-1.815 0-3.626.118t-3.611.352-3.58.585-3.536.816-3.475 1.043-3.4 1.265-3.31 1.484-3.209 1.694L41.05 15.474l-1.601-2.77q1.39-.796 2.813-1.528 1.902-.979 3.858-1.842t3.962-1.61 4.05-1.372q2.044-.626 4.123-1.13t4.184-.882q2.105-.38 4.228-.633 2.124-.253 4.26-.38Q73.06 3.2 75.2 3.2q2.226 0 4.447.137 2.221.138 4.43.412 2.208.275 4.396.685t4.345.955q2.158.544 4.278 1.221t4.195 1.484 4.095 1.74q2.02.932 3.98 1.988.46.249.918.504.94.525 1.864 1.077z"/></g><g fill-rule="evenodd"><path fill="#FF722D" d="M147.191 75.183q0-1.16-.037-2.321-.037-1.16-.112-2.32-.075-1.158-.187-2.314-.112-1.155-.262-2.306t-.336-2.298-.41-2.285q-.222-1.14-.482-2.271t-.556-2.254q-.297-1.123-.629-2.235-.332-1.113-.7-2.214t-.771-2.19-.842-2.164-.91-2.135-.98-2.106-1.046-2.072-1.113-2.038-1.178-2-1.242-1.962q-.636-.971-1.304-1.921t-1.366-1.878-1.425-1.832-1.484-1.786-1.54-1.737-1.596-1.686q-.812-.83-1.65-1.634t-1.7-1.58-1.752-1.525-1.8-1.467-1.846-1.408-1.89-1.348-1.933-1.286-1.973-1.224l-8.006 13.856q.77.465 1.525.954.754.49 1.493 1.003.738.513 1.46 1.05.721.536 1.425 1.096t1.39 1.141 1.352 1.186q.667.603 1.314 1.228t1.273 1.27 1.232 1.31 1.19 1.349q.583.684 1.145 1.386.561.703 1.1 1.423t1.054 1.457 1.007 1.49q.49.753.958 1.522.467.768.909 1.552.442.783.858 1.58.417.797.808 1.607.39.81.755 1.631.365.823.703 1.656t.65 1.677q.31.844.594 1.697t.54 1.715q.257.862.485 1.732.229.87.43 1.746.2.877.372 1.76.172.882.316 1.77t.259 1.78.201 1.787.145 1.792q.057.898.086 1.796.03.9.03 1.799 0 .383-.006.766h16.001q.004-.384.004-.767"/><path fill="#FFF" d="M150.387 75.984q.004-.4.004-.8 0-19.843-9.79-37.103-9.79-17.259-26.82-27.441l-2.788-1.667-11.201 19.386 2.687 1.623q11.876 7.169 18.694 19.25t6.818 25.952q0 .36-.005.722l-.044 3.244h22.411zm-3.2-1.567q.004.383.004.766t-.004.767h-16.001q.005-.383.005-.766 0-.384-.005-.767-.012-.887-.052-1.773-.04-.887-.109-1.771-.068-.885-.165-1.767-.096-.882-.22-1.76-.124-.88-.276-1.753-.152-.875-.332-1.744t-.386-1.731-.442-1.719q-.234-.856-.495-1.704t-.55-1.687-.602-1.67-.655-1.648-.707-1.628-.758-1.604-.808-1.58-.859-1.552-.907-1.525-.954-1.496-1.002-1.465-1.048-1.432-1.092-1.398-1.137-1.363-1.178-1.326-1.22-1.288-1.261-1.25-1.3-1.207-1.336-1.167-1.374-1.124q-.695-.55-1.408-1.08-.713-.528-1.442-1.033-.729-.506-1.473-.989-.745-.482-1.505-.94l6.405-11.086 1.601-2.77q1.375.82 2.711 1.703.896.591 1.773 1.208.878.618 1.736 1.261.86.643 1.698 1.312.84.669 1.658 1.362.82.693 1.617 1.41.798.718 1.574 1.459.776.74 1.53 1.504.753.764 1.484 1.55.73.785 1.437 1.593t1.39 1.635q.682.828 1.34 1.675.657.848 1.29 1.715.631.867 1.237 1.753t1.185 1.789q.58.903 1.131 1.823.552.92 1.077 1.856t1.02 1.887.964 1.917.907 1.945.848 1.97q.41.993.789 1.996t.729 2.018.669 2.039q.319 1.024.607 2.058.289 1.033.546 2.075.258 1.041.484 2.09.227 1.049.422 2.104t.358 2.115.296 2.125.232 2.134.168 2.139.104 2.143q.028.833.037 1.667"/></g><g fill-rule="evenodd"><path fill="#EEE" d="m103.5 123.553 8.005 13.854q1.003-.585 1.987-1.202.983-.617 1.946-1.266.963-.648 1.905-1.328.941-.679 1.86-1.388.92-.71 1.815-1.448.896-.739 1.768-1.506.871-.767 1.718-1.561.846-.795 1.666-1.617.82-.821 1.614-1.67.794-.847 1.56-1.72.765-.872 1.502-1.77.737-.896 1.445-1.817t1.385-1.863 1.325-1.907q.647-.964 1.263-1.948t1.199-1.988 1.134-2.026 1.069-2.061 1.001-2.095.934-2.126.864-2.155.795-2.181.723-2.206q.344-1.11.653-2.229.308-1.119.58-2.248t.507-2.265.434-2.281.36-2.294.286-2.304.212-2.312.137-2.318.062-2.32h-16.002q-.012.898-.053 1.797t-.111 1.795-.17 1.79q-.098.894-.225 1.784-.128.89-.284 1.776t-.34 1.766-.396 1.754q-.213.874-.453 1.74-.24.867-.509 1.725-.268.859-.563 1.708-.296.85-.618 1.689-.323.84-.672 1.668-.35.829-.726 1.646t-.777 1.621-.83 1.596-.88 1.568-.93 1.54-.98 1.508q-.5.746-1.026 1.476-.525.73-1.074 1.442-.548.713-1.12 1.408-.57.694-1.164 1.37-.593.676-1.207 1.333t-1.25 1.293-1.29 1.252-1.331 1.21-1.368 1.167-1.406 1.123-1.44 1.076-1.474 1.03-1.507.982-1.537.933"/><path fill="#FFF" d="m99.138 122.399 1.592 2.755 9.612 16.637 2.776-1.62q17.138-10 27.111-27.155 9.973-17.154 10.184-36.996l.035-3.234h-22.392l-.043 3.156q-.19 13.87-7.173 25.857t-18.955 18.992zm4.363 1.154q.776-.454 1.537-.933t1.507-.982 1.474-1.03 1.44-1.076 1.406-1.123 1.368-1.166 1.33-1.21 1.29-1.253 1.25-1.293 1.208-1.333 1.164-1.37 1.12-1.408 1.074-1.442 1.027-1.476.979-1.509.93-1.54q.453-.776.88-1.567.428-.791.83-1.596.401-.804.777-1.621t.726-1.646.672-1.668q.322-.84.618-1.689.295-.85.563-1.708t.509-1.725q.24-.866.453-1.74.212-.874.396-1.754t.34-1.766.284-1.776.226-1.784.169-1.79.11-1.795q.042-.899.054-1.798h16.002q-.017 1.601-.106 3.2-.058 1.06-.148 2.117t-.21 2.11-.272 2.105-.335 2.095-.396 2.085-.457 2.071-.518 2.058-.578 2.041q-.305 1.017-.639 2.024t-.697 2.003-.757 1.983q-.393.985-.814 1.959-.422.973-.872 1.934-.45.96-.929 1.908-.478.947-.984 1.88t-1.04 1.85q-.532.916-1.092 1.817-.56.902-1.147 1.786-.586.884-1.198 1.75-.612.867-1.25 1.715-.637.848-1.3 1.677-.662.83-1.348 1.639t-1.396 1.597-1.442 1.556-1.488 1.513q-.755.745-1.532 1.468t-1.574 1.422-1.616 1.376q-.818.675-1.655 1.327t-1.694 1.278-1.73 1.227-1.766 1.176q-1.346.868-2.729 1.674l-1.6-2.77z"/></g><g fill-rule="evenodd"><path fill="#FF722D" d="M19.242 75.983H3.241q.013 1.183.064 2.364.051 1.182.142 2.361.09 1.18.219 2.355.128 1.176.296 2.347.167 1.17.373 2.335t.45 2.322.525 2.306q.282 1.148.602 2.287.319 1.139.676 2.267.356 1.127.75 2.243.393 1.115.823 2.217.43 1.101.896 2.189.465 1.087.967 2.158.5 1.071 1.037 2.125t1.107 2.09 1.175 2.053q.604 1.017 1.241 2.013t1.307 1.971 1.371 1.927q.701.953 1.434 1.881.732.929 1.494 1.833t1.554 1.783 1.612 1.731 1.667 1.677 1.722 1.621 1.774 1.564 1.825 1.505 1.873 1.444 1.92 1.382q.97.675 1.963 1.318t2.006 1.253 2.046 1.186l8.006-13.856q-.806-.445-1.597-.917-.791-.471-1.567-.969-.775-.497-1.533-1.02-.758-.522-1.5-1.07-.74-.547-1.462-1.118t-1.426-1.166-1.386-1.212-1.346-1.257-1.303-1.301-1.26-1.343q-.62-.682-1.216-1.384t-1.17-1.422q-.572-.721-1.121-1.461-.55-.74-1.073-1.497-.525-.757-1.024-1.53-.499-.775-.972-1.565-.474-.79-.92-1.595-.448-.805-.868-1.624-.421-.82-.814-1.652-.393-.833-.76-1.678-.365-.845-.703-1.702t-.647-1.724-.59-1.745q-.28-.877-.532-1.763-.251-.886-.474-1.78-.222-.893-.415-1.794-.193-.9-.356-1.807t-.297-1.817q-.133-.912-.236-1.827t-.176-1.833q-.074-.918-.116-1.838t-.056-1.84"/><path fill="#FFF" d="M22.399 72.783H.007l.034 3.234q.215 20.23 10.55 37.62t28.002 27.248l2.753 1.537 11.214-19.41-2.855-1.578q-12.448-6.877-19.758-19.076-7.31-12.198-7.505-26.419zm-3.157 3.2H3.241q.017 1.601.105 3.2.06 1.081.152 2.16t.216 2.154.282 2.147q.157 1.07.345 2.137.19 1.066.41 2.126.221 1.06.474 2.112.252 1.053.537 2.098.284 1.044.6 2.08.315 1.036.661 2.061.347 1.026.724 2.04.377 1.016.784 2.019t.845 1.993q.438.99.905 1.967t.963 1.939 1.02 1.91q.526.946 1.079 1.877.553.93 1.134 1.844t1.189 1.81 1.243 1.772 1.295 1.735 1.347 1.695 1.398 1.653q.71.817 1.446 1.611.735.795 1.494 1.567.76.772 1.541 1.52.782.75 1.586 1.475t1.63 1.425q.825.7 1.67 1.376.847.676 1.713 1.326t1.75 1.273 1.79 1.22 1.824 1.165q.446.276.897.545.923.552 1.862 1.076l6.405-11.085 1.6-2.77q-.94-.521-1.862-1.077-.76-.459-1.504-.941-.745-.482-1.474-.988t-1.442-1.034q-.712-.53-1.408-1.08-.695-.551-1.373-1.124-.678-.572-1.337-1.166-.66-.594-1.3-1.209-.64-.614-1.26-1.248-.62-.635-1.22-1.289t-1.18-1.326q-.578-.672-1.136-1.363-.557-.69-1.092-1.398t-1.048-1.432q-.512-.725-1.001-1.465-.49-.74-.955-1.495-.466-.756-.907-1.525-.442-.77-.858-1.554-.417-.783-.81-1.58-.39-.795-.757-1.603-.367-.809-.707-1.628-.34-.82-.655-1.649t-.603-1.67q-.288-.838-.549-1.686t-.495-1.704q-.235-.856-.442-1.719t-.386-1.732-.332-1.743-.276-1.753-.22-1.76q-.097-.882-.165-1.767t-.109-1.771q-.04-.887-.052-1.774"/></g><g clip-path="url(#b)"><circle cx="75" cy="75" r="44" fill="#EEE" style="opacity:.5"/></g><rect width="1.575" height="4.726" x="73.482" y="24" fill="#EEE" rx=".788"/><rect width="1.575" height="4.726" x="78.839" y="24.198" fill="#EEE" rx=".788" transform="rotate(6 78.839 24.199)"/><rect width="1.575" height="4.726" x="84.153" y="24.993" fill="#EEE" rx=".788" transform="rotate(12 84.154 24.995)skewX(-.001)"/><rect width="1.575" height="4.726" x="89.329" y="26.33" fill="#EEE" rx=".788" transform="rotate(17.999 89.329 26.332)skewX(-.001)"/><rect width="1.575" height="4.726" x="94.384" y="28.206" fill="#EEE" rx=".788" transform="rotate(23.999 94.383 28.207)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="30.534" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="128.206" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="103.755" y="33.387" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="103.755" y="131.058" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="36.681" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="134.353" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="40.413" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="138.086" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="44.496" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="142.169" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="118.261" y="48.928" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="118.261" y="146.601" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.779" y="53.627" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.78" y="151.3" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="58.589" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="156.263" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="63.734" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="161.407" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="125.197" y="68.978" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.198" y="166.652" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.6" y="74.332" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.6" y="172.006" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.402" y="79.688" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="125.402" y="177.361" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="124.607" y="84.994" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="124.607" y="182.668" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="90.164" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="187.837" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="95.196" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="192.87" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="100.017" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="197.69" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="116.212" y="104.549" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="116.212" y="202.221" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="108.795" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="206.467" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="112.68" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="210.352" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="116.128" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="213.799" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="100.621" y="119.121" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="100.621" y="216.793" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="56.14" y="32.406" fill="#EEE" rx=".788" transform="rotate(156.001 56.14 32.405)skewX(.001)"/><rect width="1.575" height="4.726" x="60.728" y="30.763" fill="#EEE" rx=".788" transform="rotate(162.001 60.728 30.763)skewX(.001)"/><rect width="1.575" height="4.726" x="65.446" y="29.573" fill="#EEE" rx=".788" transform="rotate(168 65.446 29.573)skewX(.001)"/><rect width="1.575" height="4.726" x="70.253" y="28.92" fill="#EEE" rx=".788" transform="rotate(174 70.253 28.92)"/></g></svg>`,
        [RISK_LEVEL.L5]: `<svg xmlns="http://www.w3.org/2000/svg" width="151" height="151" fill="none"><defs><clipPath id="a"><rect width="151" height="151" rx="0"/></clipPath><clipPath id="b"><rect width="104" height="104" x="23" y="23" rx="0"/></clipPath></defs><g clip-path="url(#a)"><g fill-rule="evenodd"><path fill="#B30000" d="M47.453 26.575 39.448 12.72q-1.026.587-2.033 1.208-1.007.62-1.993 1.274-.985.653-1.95 1.338-.963.686-1.904 1.403-.94.716-1.857 1.463t-1.809 1.525-1.757 1.582-1.705 1.64-1.65 1.694q-.81.861-1.592 1.748-.783.887-1.535 1.8-.753.912-1.475 1.848-.723.937-1.414 1.897-.69.96-1.35 1.941t-1.286 1.985q-.627 1.003-1.22 2.026-.594 1.023-1.153 2.065-.56 1.042-1.084 2.102-.525 1.06-1.015 2.136t-.944 2.169-.872 2.198-.8 2.226-.726 2.25q-.345 1.132-.652 2.274t-.577 2.294q-.27 1.151-.501 2.311-.232 1.16-.425 2.326t-.348 2.34-.272 2.35-.194 2.356q-.077 1.18-.116 2.362T3.2 75.217q0 .383.004.766h16.001q-.005-.383-.005-.766 0-.92.03-1.841t.091-1.84.151-1.835.212-1.83.271-1.82.332-1.813.39-1.8q.21-.896.45-1.785.24-.89.508-1.77.269-.881.566-1.753.298-.871.624-1.733.326-.86.68-1.711.354-.85.736-1.688.381-.838.79-1.663t.846-1.637q.436-.81.899-1.607.462-.796.95-1.577.49-.78 1.003-1.545.514-.764 1.053-1.511t1.101-1.476 1.15-1.439 1.196-1.4 1.242-1.36 1.286-1.318 1.328-1.276q.675-.626 1.37-1.23.695-.605 1.41-1.186.714-.581 1.447-1.138.733-.558 1.484-1.09.751-.534 1.52-1.041.768-.508 1.552-.99.785-.483 1.585-.94"/><path fill="#FFF" d="m51.833 27.76-1.61-2.786L40.626 8.36l-2.766 1.582Q20.3 19.988 10.15 37.488 0 54.988 0 75.218q0 .4.004.8l.034 3.165h22.411l-.044-3.243q-.005-.362-.005-.723 0-14.221 7.143-26.519t19.495-19.344zm-5.981-3.956 1.6 2.77q-.268.154-.535.31-.776.454-1.537.933t-1.507.982-1.474 1.03-1.44 1.077-1.406 1.122-1.368 1.167-1.33 1.21-1.291 1.252-1.25 1.293-1.207 1.333-1.165 1.37q-.57.695-1.12 1.408-.548.712-1.073 1.442t-1.027 1.477-.98 1.508-.93 1.54-.88 1.567-.829 1.596q-.402.805-.778 1.622-.375.817-.725 1.645t-.672 1.668q-.323.84-.618 1.689-.296.85-.564 1.708t-.508 1.725q-.24.866-.453 1.74-.212.874-.397 1.754-.184.88-.34 1.766t-.283 1.776-.226 1.784-.17 1.79-.11 1.795-.054 1.798q-.005.383-.005.766t.005.766h-16Q3.2 75.6 3.2 75.217t.004-.766q.009-.834.037-1.668.037-1.094.107-2.186t.174-2.182.24-2.176.306-2.167.372-2.158.437-2.144q.234-1.07.502-2.131.267-1.061.566-2.114.3-1.053.631-2.097.331-1.043.694-2.076.363-1.032.757-2.053.394-1.022.819-2.03.425-1.01.88-2.005t.94-1.976q.486-.981 1.001-1.947t1.06-1.916 1.116-1.882 1.174-1.848q.6-.915 1.23-1.811.628-.897 1.283-1.773.655-.877 1.337-1.734.682-.856 1.39-1.691t1.439-1.649 1.49-1.604q.756-.79 1.537-1.558t1.584-1.511 1.629-1.462q.826-.718 1.673-1.412.847-.693 1.715-1.36t1.756-1.307 1.794-1.254 1.832-1.198q1.091-.69 2.207-1.34.267-.157.535-.31z"/></g><g fill-rule="evenodd"><path fill="#B30000" d="m104.142 27.259 8.006-13.856q-2.082-1.245-4.243-2.347t-4.392-2.055-4.52-1.756-4.628-1.447-4.714-1.133-4.78-.813q-2.405-.325-4.825-.489T75.2 3.2q-2.34 0-4.673.152t-4.654.455-4.614.756q-2.295.452-4.555 1.053-2.261.601-4.478 1.348-2.217.746-4.38 1.635-2.164.888-4.265 1.916-2.102 1.027-4.132 2.188l8.004 13.854q1.577-.899 3.208-1.694 1.632-.796 3.311-1.484t3.4-1.265q1.72-.578 3.475-1.043 1.755-.466 3.535-.816t3.58-.585 3.612-.352q1.811-.118 3.626-.118 1.902 0 3.799.129t3.782.387 3.746.642 3.695.894 3.625 1.144 3.539 1.386 3.437 1.624 3.32 1.853"/><path fill="#FFF" d="m105.28 31.684 1.633-2.824 9.583-16.586-2.706-1.617Q95.966 0 75.2 0 55.21 0 37.86 9.926l-2.79 1.595 11.205 19.393 2.764-1.577Q61.199 22.4 75.2 22.4q14.702 0 27.288 7.598zm.463-7.196-1.6 2.77q-.922-.555-1.864-1.076-1.547-.854-3.145-1.61-1.597-.755-3.24-1.408-1.641-.653-3.32-1.201-1.68-.548-3.392-.99-1.711-.44-3.446-.773-1.736-.332-3.489-.555-1.753-.222-3.516-.334-1.764-.111-3.531-.111-1.815 0-3.626.118t-3.611.352-3.58.585-3.536.816-3.475 1.043-3.4 1.265-3.31 1.484-3.209 1.694L41.05 15.474l-1.601-2.77q1.39-.796 2.813-1.528 1.902-.979 3.858-1.842t3.962-1.61 4.05-1.372q2.044-.626 4.123-1.13t4.184-.882q2.105-.38 4.228-.633 2.124-.253 4.26-.38Q73.06 3.2 75.2 3.2q2.226 0 4.447.137 2.221.138 4.43.412 2.208.275 4.396.685t4.345.955q2.158.544 4.278 1.221t4.195 1.484 4.095 1.74q2.02.932 3.98 1.988.46.249.918.504.94.525 1.864 1.077z"/></g><g fill-rule="evenodd"><path fill="#B30000" d="M147.191 75.183q0-1.16-.037-2.321-.037-1.16-.112-2.32-.075-1.158-.187-2.314-.112-1.155-.262-2.306t-.336-2.298-.41-2.285q-.222-1.14-.482-2.271t-.556-2.254q-.297-1.123-.629-2.235-.332-1.113-.7-2.214t-.771-2.19-.842-2.164-.91-2.135-.98-2.106-1.046-2.072-1.113-2.038-1.178-2-1.242-1.962q-.636-.971-1.304-1.921t-1.366-1.878-1.425-1.832-1.484-1.786-1.54-1.737-1.596-1.686q-.812-.83-1.65-1.634t-1.7-1.58-1.752-1.525-1.8-1.467-1.846-1.408-1.89-1.348-1.933-1.286-1.973-1.224l-8.006 13.856q.77.465 1.525.954.754.49 1.493 1.003.738.513 1.46 1.05.721.536 1.425 1.096t1.39 1.141 1.352 1.186q.667.603 1.314 1.228t1.273 1.27 1.232 1.31 1.19 1.349q.583.684 1.145 1.386.561.703 1.1 1.423t1.054 1.457 1.007 1.49q.49.753.958 1.522.467.768.909 1.552.442.783.858 1.58.417.797.808 1.607.39.81.755 1.631.365.823.703 1.656t.65 1.677q.31.844.594 1.697t.54 1.715q.257.862.485 1.732.229.87.43 1.746.2.877.372 1.76.172.882.316 1.77t.259 1.78.201 1.787.145 1.792q.057.898.086 1.796.03.9.03 1.799 0 .383-.006.766h16.001q.004-.384.004-.767"/><path fill="#FFF" d="M150.387 75.984q.004-.4.004-.8 0-19.843-9.79-37.103-9.79-17.259-26.82-27.441l-2.788-1.667-11.201 19.386 2.687 1.623q11.876 7.169 18.694 19.25t6.818 25.952q0 .36-.005.722l-.044 3.244h22.411zm-3.2-1.567q.004.383.004.766t-.004.767h-16.001q.005-.383.005-.766 0-.384-.005-.767-.012-.887-.052-1.773-.04-.887-.109-1.771-.068-.885-.165-1.767-.096-.882-.22-1.76-.124-.88-.276-1.753-.152-.875-.332-1.744t-.386-1.731-.442-1.719q-.234-.856-.495-1.704t-.55-1.687-.602-1.67-.655-1.648-.707-1.628-.758-1.604-.808-1.58-.859-1.552-.907-1.525-.954-1.496-1.002-1.465-1.048-1.432-1.092-1.398-1.137-1.363-1.178-1.326-1.22-1.288-1.261-1.25-1.3-1.207-1.336-1.167-1.374-1.124q-.695-.55-1.408-1.08-.713-.528-1.442-1.033-.729-.506-1.473-.989-.745-.482-1.505-.94l6.405-11.086 1.601-2.77q1.375.82 2.711 1.703.896.591 1.773 1.208.878.618 1.736 1.261.86.643 1.698 1.312.84.669 1.658 1.362.82.693 1.617 1.41.798.718 1.574 1.459.776.74 1.53 1.504.753.764 1.484 1.55.73.785 1.437 1.593t1.39 1.635q.682.828 1.34 1.675.657.848 1.29 1.715.631.867 1.237 1.753t1.185 1.789q.58.903 1.131 1.823.552.92 1.077 1.856t1.02 1.887.964 1.917.907 1.945.848 1.97q.41.993.789 1.996t.729 2.018.669 2.039q.319 1.024.607 2.058.289 1.033.546 2.075.258 1.041.484 2.09.227 1.049.422 2.104t.358 2.115.296 2.125.232 2.134.168 2.139.104 2.143q.028.833.037 1.667"/></g><g fill-rule="evenodd"><path fill="#B30000" d="m103.5 123.553 8.005 13.854q1.003-.585 1.987-1.202.983-.617 1.946-1.266.963-.648 1.905-1.328.941-.679 1.86-1.388.92-.71 1.815-1.448.896-.739 1.768-1.506.871-.767 1.718-1.561.846-.795 1.666-1.617.82-.821 1.614-1.67.794-.847 1.56-1.72.765-.872 1.502-1.77.737-.896 1.445-1.817t1.385-1.863 1.325-1.907q.647-.964 1.263-1.948t1.199-1.988 1.134-2.026 1.069-2.061 1.001-2.095.934-2.126.864-2.155.795-2.181.723-2.206q.344-1.11.653-2.229.308-1.119.58-2.248t.507-2.265.434-2.281.36-2.294.286-2.304.212-2.312.137-2.318.062-2.32h-16.002q-.012.898-.053 1.797t-.111 1.795-.17 1.79q-.098.894-.225 1.784-.128.89-.284 1.776t-.34 1.766-.396 1.754q-.213.874-.453 1.74-.24.867-.509 1.725-.268.859-.563 1.708-.296.85-.618 1.689-.323.84-.672 1.668-.35.829-.726 1.646t-.777 1.621-.83 1.596-.88 1.568-.93 1.54-.98 1.508q-.5.746-1.026 1.476-.525.73-1.074 1.442-.548.713-1.12 1.408-.57.694-1.164 1.37-.593.676-1.207 1.333t-1.25 1.293-1.29 1.252-1.331 1.21-1.368 1.167-1.406 1.123-1.44 1.076-1.474 1.03-1.507.982-1.537.933"/><path fill="#FFF" d="m99.138 122.399 1.592 2.755 9.612 16.637 2.776-1.62q17.138-10 27.111-27.155 9.973-17.154 10.184-36.996l.035-3.234h-22.392l-.043 3.156q-.19 13.87-7.173 25.857t-18.955 18.992zm4.363 1.154q.776-.454 1.537-.933t1.507-.982 1.474-1.03 1.44-1.076 1.406-1.123 1.368-1.166 1.33-1.21 1.29-1.253 1.25-1.293 1.208-1.333 1.164-1.37 1.12-1.408 1.074-1.442 1.027-1.476.979-1.509.93-1.54q.453-.776.88-1.567.428-.791.83-1.596.401-.804.777-1.621t.726-1.646.672-1.668q.322-.84.618-1.689.295-.85.563-1.708t.509-1.725q.24-.866.453-1.74.212-.874.396-1.754t.34-1.766.284-1.776.226-1.784.169-1.79.11-1.795q.042-.899.054-1.798h16.002q-.017 1.601-.106 3.2-.058 1.06-.148 2.117t-.21 2.11-.272 2.105-.335 2.095-.396 2.085-.457 2.071-.518 2.058-.578 2.041q-.305 1.017-.639 2.024t-.697 2.003-.757 1.983q-.393.985-.814 1.959-.422.973-.872 1.934-.45.96-.929 1.908-.478.947-.984 1.88t-1.04 1.85q-.532.916-1.092 1.817-.56.902-1.147 1.786-.586.884-1.198 1.75-.612.867-1.25 1.715-.637.848-1.3 1.677-.662.83-1.348 1.639t-1.396 1.597-1.442 1.556-1.488 1.513q-.755.745-1.532 1.468t-1.574 1.422-1.616 1.376q-.818.675-1.655 1.327t-1.694 1.278-1.73 1.227-1.766 1.176q-1.346.868-2.729 1.674l-1.6-2.77z"/></g><g fill-rule="evenodd"><path fill="#B30000" d="M19.242 75.983H3.241q.013 1.183.064 2.364.051 1.182.142 2.361.09 1.18.219 2.355.128 1.176.296 2.347.167 1.17.373 2.335t.45 2.322.525 2.306q.282 1.148.602 2.287.319 1.139.676 2.267.356 1.127.75 2.243.393 1.115.823 2.217.43 1.101.896 2.189.465 1.087.967 2.158.5 1.071 1.037 2.125t1.107 2.09 1.175 2.053q.604 1.017 1.241 2.013t1.307 1.971 1.371 1.927q.701.953 1.434 1.881.732.929 1.494 1.833t1.554 1.783 1.612 1.731 1.667 1.677 1.722 1.621 1.774 1.564 1.825 1.505 1.873 1.444 1.92 1.382q.97.675 1.963 1.318t2.006 1.253 2.046 1.186l8.006-13.856q-.806-.445-1.597-.917-.791-.471-1.567-.969-.775-.497-1.533-1.02-.758-.522-1.5-1.07-.74-.547-1.462-1.118t-1.426-1.166-1.386-1.212-1.346-1.257-1.303-1.301-1.26-1.343q-.62-.682-1.216-1.384t-1.17-1.422q-.572-.721-1.121-1.461-.55-.74-1.073-1.497-.525-.757-1.024-1.53-.499-.775-.972-1.565-.474-.79-.92-1.595-.448-.805-.868-1.624-.421-.82-.814-1.652-.393-.833-.76-1.678-.365-.845-.703-1.702t-.647-1.724-.59-1.745q-.28-.877-.532-1.763-.251-.886-.474-1.78-.222-.893-.415-1.794-.193-.9-.356-1.807t-.297-1.817q-.133-.912-.236-1.827t-.176-1.833q-.074-.918-.116-1.838t-.056-1.84"/><path fill="#FFF" d="M22.399 72.783H.007l.034 3.234q.215 20.23 10.55 37.62t28.002 27.248l2.753 1.537 11.214-19.41-2.855-1.578q-12.448-6.877-19.758-19.076-7.31-12.198-7.505-26.419zm-3.157 3.2H3.241q.017 1.601.105 3.2.06 1.081.152 2.16t.216 2.154.282 2.147q.157 1.07.345 2.137.19 1.066.41 2.126.221 1.06.474 2.112.252 1.053.537 2.098.284 1.044.6 2.08.315 1.036.661 2.061.347 1.026.724 2.04.377 1.016.784 2.019t.845 1.993q.438.99.905 1.967t.963 1.939 1.02 1.91q.526.946 1.079 1.877.553.93 1.134 1.844t1.189 1.81 1.243 1.772 1.295 1.735 1.347 1.695 1.398 1.653q.71.817 1.446 1.611.735.795 1.494 1.567.76.772 1.541 1.52.782.75 1.586 1.475t1.63 1.425q.825.7 1.67 1.376.847.676 1.713 1.326t1.75 1.273 1.79 1.22 1.824 1.165q.446.276.897.545.923.552 1.862 1.076l6.405-11.085 1.6-2.77q-.94-.521-1.862-1.077-.76-.459-1.504-.941-.745-.482-1.474-.988t-1.442-1.034q-.712-.53-1.408-1.08-.695-.551-1.373-1.124-.678-.572-1.337-1.166-.66-.594-1.3-1.209-.64-.614-1.26-1.248-.62-.635-1.22-1.289t-1.18-1.326q-.578-.672-1.136-1.363-.557-.69-1.092-1.398t-1.048-1.432q-.512-.725-1.001-1.465-.49-.74-.955-1.495-.466-.756-.907-1.525-.442-.77-.858-1.554-.417-.783-.81-1.58-.39-.795-.757-1.603-.367-.809-.707-1.628-.34-.82-.655-1.649t-.603-1.67q-.288-.838-.549-1.686t-.495-1.704q-.235-.856-.442-1.719t-.386-1.732-.332-1.743-.276-1.753-.22-1.76q-.097-.882-.165-1.767t-.109-1.771q-.04-.887-.052-1.774"/></g><g clip-path="url(#b)"><circle cx="75" cy="75" r="44" fill="#EEE" style="opacity:.5"/></g><rect width="1.575" height="4.726" x="73.482" y="24" fill="#EEE" rx=".788"/><rect width="1.575" height="4.726" x="78.839" y="24.198" fill="#EEE" rx=".788" transform="rotate(6 78.839 24.199)"/><rect width="1.575" height="4.726" x="84.153" y="24.993" fill="#EEE" rx=".788" transform="rotate(12 84.154 24.995)skewX(-.001)"/><rect width="1.575" height="4.726" x="89.329" y="26.33" fill="#EEE" rx=".788" transform="rotate(17.999 89.329 26.332)skewX(-.001)"/><rect width="1.575" height="4.726" x="94.384" y="28.206" fill="#EEE" rx=".788" transform="rotate(23.999 94.383 28.207)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="30.534" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="99.216" y="128.206" fill="#EEE" rx=".788" transform="rotate(29.999 99.215 30.536)skewX(-.001)"/><rect width="1.575" height="4.726" x="103.755" y="33.387" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="103.755" y="131.058" fill="#EEE" rx=".788" transform="rotate(35.999 103.755 33.388)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="36.681" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="107.962" y="134.353" fill="#EEE" rx=".788" transform="rotate(41.999 107.962 36.683)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="40.413" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="111.821" y="138.086" fill="#EEE" rx=".788" transform="rotate(47.999 111.82 40.414)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="44.496" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="115.251" y="142.169" fill="#EEE" rx=".788" transform="rotate(53.999 115.25 44.497)skewX(-.002)"/><rect width="1.575" height="4.726" x="118.261" y="48.928" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="118.261" y="146.601" fill="#EEE" rx=".788" transform="rotate(59.999 118.26 48.93)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.779" y="53.627" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="120.78" y="151.3" fill="#EEE" rx=".788" transform="rotate(65.999 120.779 53.628)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="58.589" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="122.809" y="156.263" fill="#EEE" rx=".788" transform="rotate(71.999 122.808 58.59)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="63.734" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="124.245" y="161.407" fill="#EEE" rx=".788" transform="rotate(78 124.244 63.734)skewX(-.001)"/><rect width="1.575" height="4.726" x="125.197" y="68.978" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.198" y="166.652" fill="#EEE" rx=".788" transform="rotate(84 125.197 68.978)"/><rect width="1.575" height="4.726" x="125.6" y="74.332" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.6" y="172.006" fill="#EEE" rx=".788" transform="rotate(90 125.6 74.332)"/><rect width="1.575" height="4.726" x="125.402" y="79.688" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="125.402" y="177.361" fill="#EEE" rx=".788" transform="rotate(96 125.402 79.687)"/><rect width="1.575" height="4.726" x="124.607" y="84.994" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="124.607" y="182.668" fill="#EEE" rx=".788" transform="rotate(102 124.607 84.994)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="90.164" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="123.268" y="187.837" fill="#EEE" rx=".788" transform="rotate(108.001 123.27 90.163)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="95.196" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="121.394" y="192.87" fill="#EEE" rx=".788" transform="rotate(114.001 121.395 95.195)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="100.017" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="119.065" y="197.69" fill="#EEE" rx=".788" transform="rotate(120.001 119.066 100.016)skewX(.001)"/><rect width="1.575" height="4.726" x="116.212" y="104.549" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="116.212" y="202.221" fill="#EEE" rx=".788" transform="rotate(126.001 116.214 104.548)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="108.795" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="112.919" y="206.467" fill="#EEE" rx=".788" transform="rotate(132.001 112.92 108.794)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="112.68" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="109.186" y="210.352" fill="#EEE" rx=".788" transform="rotate(138.001 109.188 112.68)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="116.128" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="105.103" y="213.799" fill="#EEE" rx=".788" transform="rotate(144.001 105.104 116.127)skewX(.002)"/><rect width="1.575" height="4.726" x="100.621" y="119.121" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="100.621" y="216.793" fill="#EEE" rx=".788" transform="rotate(150.001 100.623 119.12)skewX(.001)"/><rect width="1.575" height="4.726" x="56.14" y="32.406" fill="#EEE" rx=".788" transform="rotate(156.001 56.14 32.405)skewX(.001)"/><rect width="1.575" height="4.726" x="60.728" y="30.763" fill="#EEE" rx=".788" transform="rotate(162.001 60.728 30.763)skewX(.001)"/><rect width="1.575" height="4.726" x="65.446" y="29.573" fill="#EEE" rx=".788" transform="rotate(168 65.446 29.573)skewX(.001)"/><rect width="1.575" height="4.726" x="70.253" y="28.92" fill="#EEE" rx=".788" transform="rotate(174 70.253 28.92)"/></g></svg>`,
      };

      switch (type) {
        case 'icon':
          return RISK_LEVEL_ICON_MAP[level];
        case 'color':
          return RISK_LEVEL_COLOR_MAP[level];
        case 'name':
        default:
          return RISK_LEVEL_NAME_MAP[level];
      }
    });

    /**
     * 合同违约风险
     */
    hbs.registerHelper('contract_breach_to_alias', function (level: string) {
      const CONTRACT_BREACH_RISK_LEVEL_MAP = {
        L5: 'L5/极高风险',
        L4: 'L4/高风险',
        L3: 'L3/中风险',
        L2: 'L2/低风险',
        L1: 'L1/极低风险',
      };
      return CONTRACT_BREACH_RISK_LEVEL_MAP[level];
    });

    /**
     * 是否为风险维度
     */
    hbs.registerHelper('is_risk_dimension', function (detail) {
      // scoreDetails 为最终维度
      return !detail.subDimension;
    });

    /**
     * 根据字段（key）判断维度渲染类型
     */
    hbs.registerHelper('get_dimension_type', function (dimensionKey: string) {
      const FIELD_TYPES = {
        // 合同违约 ContractBreach
        CONTRACT_BREACH: ['ContractBreach'],
        // 空壳公司 CompanyShell
        COMPANY_SHELL: ['CompanyShell'],
        // 工商基本信息
        COMPANY_DETAIL: ['CompanyDetail'],
        // 纯文本类型
        TEXT_TYPE: [
          'NoTender',
          'NoQualityCertification',
          'BusinessAbnormal7',
          'FakeSOES',
          'FraudList',
          'NoCertification',
          'NoCertification', // 无有效关键资质认证
          'FinancialInstitution', // 重点金融机构
        ],
        // JSON 类型
        JSON_TYPE: ['BusinessAbnormal1', 'BusinessAbnormal6', 'BusinessAbnormal8', 'EstablishedTime', 'LowCapital', 'FinancialHealth'],
        // 多列单行 JSON 类型
        MULTI_COLUMNS_JSON_TYPE: ['NoCapital', 'BusinessAbnormal4'],
        // 清算信息
        LIQUIDATION: ['Liquidation'],
        // 股权结构复杂-企业风险
        // 涉及高风险行业
        QFK_RISK: ['QfkRisk'],
        // 员工数据不明
        QFK_RISK_SIMPLE_DIMENSION_DESC: ['QfkRisk6615', 'QfkRisk6302'],
        // 控制权分散
        QFK_RISK_SIMPLE_DIMENSION_DESC_OR_COMMON_TABLE: ['QfkRisk6803'],
        // 所有权与经营权分离
        QFK_RISK_DIMENSION_DESC_AND_NESTED_TABLE: ['QfkRisk6802'],
        // 企查分
        QCC_CREDIT_RATE: ['QCCCreditRate'],
      };
      const fieldType = Object.keys(FIELD_TYPES).reduce((result: string, type: string) => {
        const fields = FIELD_TYPES[type];
        if (fields.includes(dimensionKey)) {
          return type;
        }
        return result;
      }, 'COMMON');
      return fieldType;
    });

    /**
     * 查查信用分风险等级映射
     */
    hbs.registerHelper('credit_rate_level_to', function (level: string, type: string) {
      enum RiskCreditColor {
        High = '#FFDCB3',
        Middle = '#B8DDFA',
        Low = '#C4F5E0',
      }

      enum RiskCreditBackground {
        High = 'rgba(255, 137, 0, 1)',
        Middle = 'rgba(18, 139, 237, 1)',
        Low = 'rgba(0, 173, 101, 1)',
      }

      const mapping = {
        'L-1': {
          short: '较差',
          reason: '企业内外部存在较多不确定因素',
          summary: '违约风险高',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-2': {
          short: '较差',
          reason: '受内外部不确定因素的影响大',
          summary: '违约风险较高',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-3': {
          short: '较差',
          reason: '受内外部不确定因素的影响较大',
          summary: '有一定的违约风险',
          color: RiskCreditColor.High,
          background: RiskCreditBackground.High,
        },
        'L-4': {
          short: '一般',
          reason: '较易受不利经济环境的影响',
          summary: '违约风险中等',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-5': {
          short: '一般',
          reason: '受不利经济环境的影响偏大',
          summary: '违约风险中等',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-6': {
          short: '中等',
          reason: '受不利经济环境的影响适中',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-7': {
          short: '中等',
          reason: '经营和风险管理能力尚可',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-8': {
          short: '中等',
          reason: '经营和风险管理能力相对较好',
          summary: '违约风险较低',
          color: RiskCreditColor.Middle,
          background: RiskCreditBackground.Middle,
        },
        'L-9': {
          short: '良好',
          reason: '经营和风险管理能力较好',
          summary: '违约风险低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-10': {
          short: '良好',
          reason: '经营和风险管理能力优秀',
          summary: '违约风险低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-11': {
          short: '优秀',
          reason: '当前处于良性循环状态',
          summary: '违约风险很低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
        'L-12': {
          short: '卓越',
          reason: '当前处于良性循环状态',
          summary: '违约风险很低',
          color: RiskCreditColor.Low,
          background: RiskCreditBackground.Low,
        },
      };
      return mapping?.[level]?.[type];
    });

    /**
     * 根据打分给予风险等级（星）
     */
    hbs.registerHelper('get_credit_rate_star_by_score', function (score: number) {
      const star0 = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAMFBMVEUAAADf39/f39/j4+Pk5OTk5OTh4eHj4+Pi4uLj4+Pi4uLj4+Pi4uLk5OTk5OTj4+OPxBILAAAAD3RSTlMAIBC/389w72BAsJCAnzD/onSqAAAAZ0lEQVQI12NAA4wKcCZzAJwp/xHOPP8NzrT/DNUjKPn//0RBISBz/X8w+ApkpkOYFSAF8SDWFwGQYm4Q8wBEoz9IJRgwgUQFwEw2ELMB4oL/flP+G4CZ6n8VmPZ/AjO1gJJsRgyEAQCwiDK1XzvmgwAAAABJRU5ErkJggg==" />`;
      const star1 = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUBAMAAAB/pwA+AAAAMFBMVEUAAAAQh+oQj+8TjO4SjO4Ri+4Si+0Si+0TiuwUi+sRi+wSiuwSiuwSjO0Qiu8Si+3rPTfdAAAAD3RSTlMAIBC/389w72BAsJCAnzD/onSqAAAAZ0lEQVQI12NAA4wKcCZzAJwp/xHOPP8NzrT/DNUjKPn//0RBISBz/X8w+ApkpkOYFSAF8SDWFwGQYm4Q8wBEoz9IJRgwgUQFwEw2ELMB4oL/flP+G4CZ6n8VmPZ/AjO1gJJsRgyEAQCwiDK1XzvmgwAAAABJRU5ErkJggg==" />`;
      const starHalf = `<img width="10" height="10" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAWlBMVEUAAADf399tsujf399ttuQqlezk5OTj4+Mcj+0jk+zk5OQumOzh4eHi4uLj4+NVqurj4+MdkOwkkus0muwymevj4+M8nevj4+MnkuowlutLouheq+jj4+MSi+1V2M0/AAAAHHRSTlMAIDgQHNfPv/vz38twYEAw79uwq5iQjIB8cFhMN05qJAAAAHNJREFUGNOljtkKhDAQBOMmu96u99n9/7+pMEoSIr5YT0PB0KWe+esb+f2F7kNEgeyJIvwmYu/zYCWBKjqQvZwCBNkbE1fW5VnYWJnZhO6Sk3JIRXr9hiK9/vmSpVfOdKsyuP0mYWuU0gOgrcwXOYpY5Dt2SfsLXuKIcwEAAAAASUVORK5CYII=" />`;

      const stars: string[] = [];
      const allHalfStars = Math.floor(score / 10) + 1;
      const fullStars = Math.floor(allHalfStars / 2);
      const halfStar = allHalfStars % 2;

      for (let i = 0; i < 5; i++) {
        if (i + 1 <= fullStars) {
          stars.push(star1);
        } else if (i === fullStars && halfStar) {
          stars.push(starHalf);
        } else {
          stars.push(star0);
        }
      }
      return stars.join('');
    });

    /**
     * 生成风险等级说明条形图
     */
    hbs.registerHelper('get_credit_rate_bar_chart', function (score: number) {
      const theme = {
        low: 'rgba(255, 137, 0, 1)',
        middle: 'rgba(18, 139, 237, 1)',
        high: 'rgba(0, 173, 101, 1)',
      };
      const locale = {
        'L-1': '信用表现较差，企业内外部存在较多不确定因素，违约风险高',
        'L-2': '信用表现较差，受内外部不确定因素的影响大，违约风险较高',
        'L-3': '信用表现较差，受内外部不确定因素的影响较大，有一定的违约风险',
        'L-4': '信用表现一般，较易受不利经济环境的影响，违约风险中等',
        'L-5': '信用表现一般，受不利经济环境的影响偏大，违约风险中等',
        'L-6': '信用表现中等，受不利经济环境的影响适中，违约风险较低',
        'L-7': '信用表现中等，经营和风险管理能力尚可，违约风险较低',
        'L-8': '信用表现中等，经营和风险管理能力相对较好，违约风险较低',
        'L-9': '信用表现良好，经营和风险管理能力较好，违约风险低',
        'L-10': '信用表现良好，经营和风险管理能力优秀，违约风险低',
        'L-11': '信用表现优秀，当前处于良性循环状态，违约风险很低',
        'L-12': '信用表现卓越，当前处于良性循环状态，违约风险很低',
      };
      const scale = [
        {
          level: 'low',
          percentage: 25,
          min: 0,
          max: 499,
          range: [
            { levelName: 'L-1', min: 0, max: 299, opacity: 0.5, tickLabel: 'min' },
            { levelName: 'L-2', min: 300, max: 399, opacity: 0.4 },
            { levelName: 'L-3', min: 400, max: 499, opacity: 0.3 },
          ],
        },
        {
          level: 'middle',
          percentage: 42,
          min: 500,
          max: 749,
          range: [
            { levelName: 'L-4', min: 500, max: 549, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-5', min: 550, max: 599, opacity: 0.3 },
            { levelName: 'L-6', min: 600, max: 649, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-7', min: 650, max: 699, opacity: 0.5 },
            { levelName: 'L-8', min: 700, max: 749, opacity: 0.6 },
          ],
        },
        {
          level: 'high',
          percentage: 33,
          min: 750,
          max: 2000,
          range: [
            { levelName: 'L-9', min: 750, max: 799, opacity: 0.2, tickLabel: 'min' },
            { levelName: 'L-10', min: 800, max: 899, opacity: 0.3 },
            { levelName: 'L-11', min: 900, max: 999, opacity: 0.4, tickLabel: 'min' },
            { levelName: 'L-12', min: 1000, max: 2000, opacity: 0.5, tickLabel: 'max' },
          ],
        },
      ];

      const levelDirectionMap = {
        low: 'left',
        middle: 'center',
        high: 'right',
      };

      const getIndicator = (score: number) => {
        let level; // high
        let levelName; // L-1
        let color; // #ff8900
        let direction; // left | right | middle
        let percentage;
        let residualPercentage = 0;

        for (const item of scale) {
          const rangeList = item.range; // 将范围列表提取到循环外部

          for (let j = 0; j < rangeList.length; j++) {
            const range = rangeList[j];

            if (score >= range.min && score <= range.max) {
              level = item.level;
              levelName = range.levelName;
              color = theme[item.level];
              direction = levelDirectionMap[item.level];

              const unitPercentage = item.percentage / rangeList.length;
              const initPercentage = unitPercentage * j;
              const scorePercentage = unitPercentage * ((score - range.min) / (range.max - range.min));
              percentage = residualPercentage + initPercentage + scorePercentage;

              // 提前终止内部循环
              break;
            }
          }
          residualPercentage += item.percentage;
        }
        return {
          level,
          levelName,
          color,
          direction,
          percentage,
        };
      };
      const indicator = getIndicator(score);

      const labelInnerDOM = (item) => {
        return item.range
          .map((step, stepIndex, stepList) => {
            return `
              <div
                class="bar"
                style="width: ${100 / stepList.length}%;"
              >
                <span style="opacity: ${step.tickLabel === 'min' ? 1 : 0}">
                  ${step.min}
                </span>
                <span style="opacity: ${step.tickLabel === 'max' ? 1 : 0}">
                  ${step.max}
                </span>
              </div>
            `;
          })
          .join('');
      };

      const labelDOM = scale
        .map((item) => {
          return `
          <div class="range" style="width: ${item.percentage}%">
            ${labelInnerDOM(item)}
          </div>
        `;
        })
        .join('');

      const scaleInnerDOM = scale
        .map((item, index, scaleList) => {
          const color = theme[item.level];

          const barDOM = item.range
            .map((step) => {
              return `
              <div
                class="bar"
                style="width: ${100 / item.range.length}%; background: ${color}; opacity: 0.5"
                data-index="${step.levelName}"
              >
              </div>
          `;
            })
            .join('');

          return `
          <div
            class="range ${index === 0 ? 'first' : ''} ${index === scaleList.length - 1 ? 'last' : ''}"
            style="width: ${item.percentage}%;"
          >
            ${barDOM}
          </div>
        `;
        })
        .join('');

      let descriptionOffset: string;
      if (indicator.direction === 'right') {
        descriptionOffset = `right: ${100 - indicator.percentage}%;`;
      } else {
        descriptionOffset = `left: ${indicator.percentage}%;`;
      }

      return `
        <div class="credit-rate-bar-chart">
          <div class="credit-rate-bar-chart__tick">
          ${labelDOM}
          </div>

          <div class="credit-rate-bar-chart__scale">
            <div class="credit-rate-bar-chart__scale__inner">
              ${scaleInnerDOM}

              <div class="credit-rate-bar-chart__indicator" style="background: ${indicator.color}; left: ${indicator.percentage}%;"></div>

              <div
                class="credit-rate-bar-chart__description ${indicator.direction}"
                style="color: ${indicator.color}; ${descriptionOffset}"
              >
                <p>
                  ${indicator.levelName} ${locale[indicator.levelName]}
                </p>
              </div>

            </div>
          </div>

        </div>
      `;
    });

    /**
     * 维度分组是否为空
     */
    hbs.registerHelper('is_level_group_empty', function (item: Record<string, any>): boolean {
      if (item.totalHits === 0 && Array.isArray(item.levelGroups) && item.levelGroups.every(({ groups }) => groups?.length === 0)) {
        return true;
      }
      return false;
    });

    hbs.registerPartials(join(__dirname, '../../../templates/partials'), {
      rename(name) {
        return name.replace(/\W/g, '_');
      },
    });

    // 生成封面封底
    this.pdfCoverTemplate = hbs.compile(fs.readFileSync(join(__dirname, '../../../templates/cover.pdf.hbs'), { encoding: 'utf-8' }).toString(), {
      preventIndent: true,
    });

    this.pdfTemplate = hbs.compile(fs.readFileSync(join(__dirname, '../../../templates/insights.pdf.hbs'), { encoding: 'utf-8' }).toString(), {
      preventIndent: true,
    });
  }

  /**
   * 风险等级分组
   * @param levelGroup
   */
  private levelGroupFilter(levelGroup: LevelGroupDimensionPO, groupId: number) {
    return [DimensionRiskLevelEnum.High, DimensionRiskLevelEnum.Medium, DimensionRiskLevelEnum.Alert].map((level: DimensionRiskLevelEnum) => {
      return {
        level,
        groups: levelGroup[level].filter((g) => g.groupDefinition.groupId === groupId),
      };
    });
  }

  /**
   * 风险维度: 输出 维度 -> 风险等级分组 -> 子维度
   */
  private getRiskDetails(scoreDetails: GroupMetricScorePO[], levelGroups: LevelGroupDimensionPO) {
    const result = [];

    scoreDetails.forEach((detail) => {
      // if (detail.totalHits > 0) {
      const dimension = {
        name: detail.groupDefinition.groupName,
        totalHits: detail.totalHits,
        groupKey: detail.groupDefinition.groupId,
        levelGroups: this.levelGroupFilter(levelGroups, detail.groupDefinition.groupId),
      };
      // 不输出 `命中为0(空维度)` 或 `levelGroups子集group为空(不含关键项)` 的维度
      // if (dimension.totalHits > 0 || dimension?.levelGroups?.some((g) => g.groups.length > 0)) {
      //   result.push(dimension);
      // }
      result.push(dimension);
    });
    return result;
  }

  async getCompanyInfo(keyno: string) {
    const getResDataByField = (field: string) => (res) => {
      return res ? res[field] : res;
    };
    const takeResResult = getResDataByField('Result');
    const takeResNames = getResDataByField('Names');

    const getCompanyInfo = async () => {
      if (isOrganism(keyno)) {
        const { Result = {} } = await this.searchService.organismDetailsQcc(keyno);
        return {
          KeyNo: keyno,
          Oper: Result.DJInfo?.Oper || {},
          MultipleOper: {
            OperType: Result.DJInfo?.Oper?.OperType,
            OperList: Result.DJInfo?.Oper ? [Result.DJInfo?.Oper] : [],
          },
          Address: Result.DJInfo?.address,
          ...Object.entries(Result.DJInfo).reduce((acc, [k, v]) => ({ ...acc, [upperFirst(k)]: v })),
          ...Result,
          CertDate: Result.DJInfo?.certificatePeriod || '',
          ShortStatus: Result.DJInfo?.status || '',
          Type: 1,
          IsOrganism: true, //是社会组织
          ContactNo: Result.contactNo,
        };
      }
      const result = await this.searchService.companyDetailsQcc(keyno, [
        'Address', // 注册地址
        'CreditCode', // 统一社会信用代码
        'No', // 工商注册号
        'OrgNo', // 组织机构代码
        'KeyNo',
        'LatestAnnualReportAddrInfo', // 最新年报信息  LatestAnnualReportAddrInfo.0.Address
        'Name', // 企业名称
        'EnglishName', // 英文名称
        'OriginalName', // 曾用名
        'Oper', // Oper.Name 法定代表人
        'MultipleOper',
        'RecCap', // 实缴资本
        'RegistCapi', // 注册资本
        'ShortStatus',
        'Staffs', // 员工人数 Staffs.c   Staffs.s 年报信息
        'StartDate', // 成立日期
        'CheckDate', // 核准日期
        'TaxNo', // 	纳税人识别号
        'VTList',
        'QccIndustry',
        'TermStart',
        'TeamEnd',
        'StartDate',
        'Scale', // 资产规模
        'ContactInfo',
        'CompanyRevenue', // CompanyRevenue.Revenue 营业收入
        'Industry', // 所属行业 Industry.Industry
        'EconKind', // 企业类型
        'BelongOrg', //等级机关
        'Scope', //经营范围
        'ChangeDiffInfo', // 变更记录
        // 'Partners', // 股东信息
        // 'Employees', // 主要人员
        'TaxpayerType', // 纳税人资质
        'CountInfo', // 计数信息
      ]);
      return result;
    };
    const companyInfo = await getCompanyInfo();

    // todo 如果是社会组织走独立逻辑
    if (isOrganism(keyno)) {
      // 变更记录 在详情的 ChangeDiffInfo 中有
      // 主要人员
      companyInfo['Employees'] = await this.personHelper.getOrganismPersonPaging(keyno);

      // 控制企业  api/VIP/GetHoldingCompany
      companyInfo['HoldingCompany'] = await this.companyDetailService.getHoldingCompany({ keyNo: keyno }).then(takeResResult);
      return companyInfo;
    }

    const changeInfo = await this.companyDetailService.ChangeRecords({
      keyNo: keyno,
      pageSize: 10,
      pageIndex: 1,
    });
    // 变更记录
    companyInfo['ChangeDiffInfo'] = {
      KeyNo: companyInfo?.KeyNo,
      CompanyName: companyInfo?.Name,
      ChangeList: changeInfo?.Result || [],
      TotalCount: changeInfo?.Paging?.TotalRecords || 0,
    };

    // 股东信息（最新公示）
    companyInfo['IpoPartners'] = await this.personHelper.getPartner(keyno, 'IpoPartners');
    // 股东信息（工商登记）
    companyInfo['Partners'] = await this.personHelper.getPartner(keyno, 'Partners');
    // 主要人员（最新公示）
    companyInfo['IpoEmployees'] = await this.personHelper.getEmployee(keyno, 'IpoEmployees');
    // 主要人员（工商登记）
    companyInfo['Employees'] = await this.personHelper.getEmployee(keyno, 'Employees');
    // 分支机构 api/ECILocal/GetECIBigNodePagingInfo => /api/QccSearch/List/Branch
    companyInfo['Branches'] = await this.companyDetailService.getBranchList(keyno);
    // 实际控制人 （疑似） api/Relation/GetSuspectedActualControllerV5
    const controllersRes = await this.personHelper.getActualController(keyno);
    // 转换实际控制人数据: v5数据结构转换
    const { actual, yisiActual } = this.getActualControllerV5(controllersRes);
    const type = this.getActualControllerV5Type(controllersRes, actual, yisiActual);
    companyInfo['ActualController'] = {
      type,
      actual,
      yisiActual,
    };
    // 控制企业  api/VIP/GetHoldingCompany
    companyInfo['HoldingCompany'] = await this.companyDetailService.getHoldingCompany({ keyNo: keyno }).then(takeResResult);

    // 受益人所有人 api/QccDetail/Benefit/Detail
    companyInfo['BeneficialOwner'] = await this.getBenefitList(keyno, true);
    // 受益人自然人 api/QccDetail/Benefit/Detail
    companyInfo['BeneficialNaturalPerson'] = await this.getBenefitList(keyno, false);
    return companyInfo;
  }

  /**
   * 获取表格配置名称
   */
  private getActualControllerV5Type(data, actual, yisiActual) {
    // 疑似实际控制人
    if (Array.isArray(yisiActual) && yisiActual.length > 0) {
      return ActualControllerTypeMaps.SuspectActual;
    }

    // 实际控制人
    const { ActualControl, FinalActualControl } = data || {};
    // 是否上市 - 变更Name的title为: 实际控制人（公示信息）
    const isPublicName = ActualControl && !FinalActualControl;
    // NOTE: 表决权比例
    const hasControlPercent = actual.some(({ Names }) => Names?.ControlPercent);
    // NOTE: 4.needRowspan 表决权比例 / 表决权比例(共同持有)
    // const hasMoreThanOnPartner = actual.some(({ Names }) => Names?.PersonList?.length > 1);
    const hasMoreThanOnPartner = actual?.length > 1 || actual.some(({ Names }) => Names?.PersonList?.length > 1);

    // NOTE: 直接持股比例
    const hasPartnerPercent = actual.some(({ Names }) => Names?.PersonList.some(({ Percent }) => Percent));
    // NOTE: 总持股比例
    let hasPartnerPercentTotal = actual.some(({ Names }) => Names?.PersonList.some(({ PercentTotal }) => PercentTotal));
    if (hasPartnerPercentTotal) {
      // 最终受益股份与直接持股比例相同时，不呈现最终受益股份
      const isAllEqual = actual.every(({ Names }) => Names?.PersonList.every(({ Percent, PercentTotal }) => parseFloat(Percent) === parseFloat(PercentTotal)));
      hasPartnerPercentTotal = !isAllEqual;
    }

    /**
     * 实际控制人
     */
    // 实际控制人, 直接持股比例, **总持股比例**, 表决权比例
    if (hasPartnerPercent && hasControlPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual2PercentTotal;
    }
    // 实际控制人(公示信息), 直接持股比例, 表决权比例(共同持有)
    if (hasPartnerPercent && hasControlPercent && hasMoreThanOnPartner && isPublicName) {
      return ActualControllerTypeMaps.Actual2TitleAndControlPercent;
    }
    // 实际控制人, 直接持股比例, 表决权比例(共同持有)
    if (hasPartnerPercent && hasControlPercent && hasMoreThanOnPartner) {
      return ActualControllerTypeMaps.Actual2ControlPercent;
    }
    // 实际控制人, 直接持股比例, 表决权比例
    if (hasPartnerPercent && hasControlPercent) {
      return ActualControllerTypeMaps.Actual2;
    }

    // 实际控制人(公示信息), 直接持股比例, 总持股比例';
    if (hasPartnerPercent && hasPartnerPercentTotal && isPublicName) {
      return ActualControllerTypeMaps.Actual4Title;
    }
    // 实际控制人, 直接持股比例, 总持股比例';
    if (hasPartnerPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual4;
    }
    // 公示信息
    if (hasPartnerPercent && isPublicName) {
      return ActualControllerTypeMaps.Actual6Title;
    }
    // 实际控制人
    if (hasPartnerPercent) {
      return ActualControllerTypeMaps.Actual6;
    }

    /**
     * 表决权比例
     */
    // 5.2. 实际控制人, 总持股比例,	表决权比例(共同持有)
    if (hasControlPercent && hasPartnerPercentTotal && hasMoreThanOnPartner) {
      return ActualControllerTypeMaps.Actual5PercentTotal;
    }
    // 5.1 实际控制人, 总持股比例,	表决权比例
    if (hasControlPercent && hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual5;
    }
    // 1. 实际控制人, 表决权比例
    if (hasControlPercent) {
      return ActualControllerTypeMaps.Actual1;
    }

    /**
     * 总持股比例
     */
    // 4. 实际控制人(公示信息), 总持股比例
    if (hasPartnerPercentTotal && isPublicName) {
      return ActualControllerTypeMaps.Actual3Title;
    }
    if (hasPartnerPercentTotal) {
      return ActualControllerTypeMaps.Actual3;
    }

    return ActualControllerTypeMaps.Actual1;
  }

  /**
   * 实际控制人（V5）
   * http://gitlab.greatld.com:18888/qcc/pc-web/-/blob/1e9944b62d693b3e250043a5d1bfeca94fbf026f/src/components/app-datalist/components/companieslist/handle.js#L90
   * https://gitlab.greatld.com:18888/qcc/pc-web/-/blob/d1b3e8ecc2841be6039dc8caaf2d2c7b29ffa6a0/src/routers/company/detail/sections/base/kzrtupu/component.js#L60
   */
  private getActualControllerV5(data) {
    let actual = [];
    let yisiActual = [];
    const { ActualControl, Names, FinalActualControl, MergedActualControl, NameCount } = data || {};
    if (ActualControl || FinalActualControl) {
      let PersonList;
      let ControlPercent;
      if (FinalActualControl) {
        PersonList = FinalActualControl.PersonList;
        ControlPercent = FinalActualControl.ControlPercent;
      } else if (ActualControl) {
        PersonList = ActualControl.PersonList;
        ControlPercent = ActualControl.ControlPercent;
      }

      if (PersonList?.length > 0) {
        const ControlPercentDesc = Number(ControlPercent) > 0 ? ControlPercent + '%' : '';
        const realName = [];
        forEach(PersonList, (item) => {
          realName.push({
            Name: item.Name,
            KeyNo: item.KeyNo,
            Org: item.Org,
            Tags: item.Tags || [],
          });
        });
        forEach(PersonList, (item, index: number) => {
          if (index === 0) {
            item.mutiActual = true;
            item.realNames = realName;
            item.ControlPercent = ControlPercentDesc;
            const obj = {
              mutiActual: true,
              Name: realName,
              lineType: 'dashed',
              lineTextSuffix: '(表决权)',
              percent: item.ControlPercent,
              ControlPercentDesc,
            };
            item.RealPaths = [[[obj]]];
          }
          actual.push({
            ...item,
            Names: {
              ControlPercent: ControlPercentDesc,
              PersonList: [item],
            },
          });
        });
      }
    } else if (Names?.length) {
      forEach(Names, (item) => {
        if (Number(item.IsActual)) {
          actual.push(item);
        } else {
          yisiActual.push(item);
        }
      });
      actual = this.handleRealPaths(actual);
      // 如果NameCount大于1，就处理数据
      if (NameCount > 1 && !yisiActual.length) {
        const pathList = [];
        forEach(actual, (item) => {
          const obj = {
            ...item,
            ...item?.Names?.PersonList?.[0],
            ControlPercent: item?.Names?.ControlPercent,
          };
          const RealPaths = [];
          forEach(item.Paths, (path) => {
            forEach(path, (sub, index: number) => {
              const { lineType, lineTextPrefix, lineTextSuffix, percent } = this.handleLinkForKzr(sub) || {};
              sub.lineType = lineType;
              sub.lineTextPrefix = lineTextPrefix;
              sub.lineTextSuffix = lineTextSuffix;
              sub.percent = percent;
              if (index === path.length - 1) {
                if (index >= 1 && path?.[index - 1].IsPublic) {
                  sub.lineType = 'dashed';
                }
              }
            });
            RealPaths.push([path]);
          });

          obj.Paths = RealPaths;
          pathList.push(obj);
        });
        actual = [
          {
            Names: MergedActualControl,
            isShowMoreInfo: true,
            pathList,
          },
        ];
      }

      yisiActual = this.handleRealPaths(yisiActual);
    }

    // 疑似实际控制人数据处理
    if (yisiActual?.length) {
      yisiActual = map(yisiActual, (item) => {
        return {
          ControlPercent: item.Names.ControlPercent,
          ...item,
          ...item.Names?.PersonList?.[0],
        };
      });
    }

    // 实际控制人数据处理: 补齐关键字段(ControlPercent, Name)
    if (actual.length) {
      // 如果是 `表决权比例(共同持有)` item.Names.PersonList 会存在多个实控人，需要将数据转换为多个打平的数据
      actual = actual.reduce((acc, item) => {
        if (item?.Names?.PersonList?.length) {
          const personList = item.Names.PersonList.map((person) => {
            return {
              ControlPercent: item.Names.ControlPercent,
              ...item,
              ...person,
            };
          });
          return [...acc, ...personList];
        }
        return acc;
      }, []);
    }

    return {
      actual,
      yisiActual,
    };
  }

  private handleRealPaths(list) {
    const actual = cloneDeep(list);
    forEach(actual, (item) => {
      const CollapsedPaths = cloneDeep(item.CollapsedPaths);
      const RealPaths = [];
      forEach(CollapsedPaths, (path) => {
        const { Paths, Children } = path;
        const arr = [Paths, ...Children];
        forEach(arr, (subarr) => {
          forEach(subarr, (sub, index: number) => {
            // const { lineType, lineTextPrefix, lineTextSuffix, percent } = kzrHelper.handleLinkForKzr(sub) || {};
            const { lineType, lineTextPrefix, lineTextSuffix, percent } = this.handleLinkForKzr(sub) || {};
            sub.lineType = lineType;
            sub.lineTextPrefix = lineTextPrefix;
            sub.lineTextSuffix = lineTextSuffix;
            sub.percent = percent;
            if (index === subarr.length - 1) {
              if (index >= 1 && subarr?.[index - 1].IsPublic) {
                sub.lineType = 'dashed';
              }
            }
          });
        });
        RealPaths.push(arr);
      });
      item.RealPaths = RealPaths;
    });
    return actual;
  }

  private handleLinkForKzr(itemData): { lineType: string; percent: string; lineTextPrefix?: string; lineTextSuffix?: string } {
    const getPercent = ({ itemData, isSpecial }) => {
      if (!isSpecial) {
        return +(itemData.Percent || '').split('%')[0] ? itemData.Percent : '';
      }
      if (itemData.ControlPercent) {
        return itemData.ControlPercent + '%';
      }
      if (itemData.ControlRatio) {
        return itemData.ControlRatio + '%';
      }
      if (+itemData.DataType === 5) {
        return '100%';
      }
      return '';
    };
    if (+itemData.DataType === 5) {
      itemData.ControllerType = 6;
    }
    let returnData = {
      type: -1,
      typeDesc: '默认',
      lineType: 'solid',
      lineText: itemData.Percent,
      percent: itemData.Percent,
    };
    let target = null;
    if ([1, 8].indexOf(itemData.ControllerType) > -1 && itemData.ShareHoldType >= 0) {
      target = find(shareHoldTypeMap, (stype) => stype.type === itemData.ShareHoldType);
    } else if (itemData.ControllerType >= 0) {
      target = find(controlTypeMap, (ctype) => ctype.type === itemData.ControllerType);
    }
    if (target) {
      target.percent = getPercent({
        itemData,
        isSpecial: target.isSpecial,
      });
      if (itemData.ControllerType === 5) {
        target.lineText = '';
        target.percent = '';
      } else {
        target.lineText = (target.lineTextPrefix || '') + (target.percent || '');
        if (target.lineText.indexOf('%') < 0 && target.percent) {
          target.lineText += '%';
        }
        target.lineText += target.lineTextSuffix || '';
        if (target.tips && target.tips.indexOf('xxx') > -1) {
          target.tips = itemData.OriginKeyNo ? replace(target.tips, 'xxx', itemData.OriginName) : '';
        } else if (!target.tips) {
          target.tips = '';
        }
      }
      returnData = target;
    }
    return returnData;
  }

  private async getDiligenceData(orgId: number, diligenceId: number) {
    const dbDiligence = await this.diligenceHistoryRepo.findOne({
      where: { id: diligenceId, orgId },
      relations: ['orgModel'], // 取出关联 orgModel 用于渲染动态的风险级别名称
    });
    if (!dbDiligence?.snapshotId) {
      throw new BadParamsException(RoverExceptions.BadParams.NotFound);
    }
    const companyInfo = await this.getCompanyInfo(dbDiligence.companyId);
    const riskInfo = dbDiligence.details;
    const riskDetails = this.getRiskDetails(
      riskInfo.groupMetricScores.sort((a, b) => a.sort - b.sort),
      riskInfo.levelGroup,
    );

    const dimensionHitStrategies = await getDimesionHitStrategies(dbDiligence.orgModelId, dbDiligence.details.dimensionHits, this.diligenceHistoryRepo.manager);

    const riskDimensions = await this.snapshotHelper.getDiligenceDetails(dbDiligence, dimensionHitStrategies);

    const riskReview = {
      level: riskInfo?.result,
      levelGroups: {
        // 高风险（警示）
        2: riskInfo?.levelGroup[DimensionRiskLevelEnum.High],
        // 中风险（关注）
        1: riskInfo?.levelGroup[DimensionRiskLevelEnum.Medium],
        // 低风险（正常）
        0: riskInfo?.levelGroup[DimensionRiskLevelEnum.Alert],
      },
    };

    return {
      dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDetails,
      riskDimensions,
      orgModel: dbDiligence.orgModel,
    };
  }

  async generatePdfView(orgId: number, diligenceId: number) {
    // 恒大地产集团有限公司  diligenceId =  13816  snapshotId: "097ca220-c3a0-11ed-8d1e-db7942b7c9b2"
    const { dbDiligence, companyInfo, riskInfo, riskReview, riskDetails, riskDimensions, orgModel } = await this.getDiligenceData(orgId, diligenceId);

    return this.pdfTemplate({
      orgId,
      diligenceInfo: dbDiligence,
      companyInfo,
      riskInfo,
      riskReview,
      riskDetails,
      riskDimensions,
      orgModel,
    });
  }

  async generatePdf(orgId: number, diligenceId: number) {
    try {
      const { dbDiligence, companyInfo, riskInfo, riskReview, riskDetails, riskDimensions, orgModel } = await this.getDiligenceData(orgId, diligenceId);

      const cover = await this.pdfCoverTemplate({
        title: '风险洞察报告',
        reportName: companyInfo.Name,
        createDate: dbDiligence.createDate,
        pdfType: 'diligence',
      });

      const content = this.pdfTemplate({
        orgId,
        diligenceInfo: dbDiligence,
        companyInfo,
        riskInfo,
        riskReview,
        riskDetails,
        riskDimensions,
        orgModel,
      });

      // PDF 页头
      const defaultStyles = {
        wrapper: `width: calc(100%); margin: -12px 0 0 0; padding: 0 30px;`,
        content: `font-size: 13px; padding: 8px 0 10px 0; line-height: 22px; display: flex; align-items: center; justify-content: space-between; font-family: 'Microsoft YaHei', 'Noto Sans SC', Arial, sans-serif;`,
      };
      const headerTemplate = `
        <div style="${defaultStyles.wrapper}">
          <div style="${defaultStyles.content} border-bottom: 1px solid #eee;">
            <img width="177" height="22" src="${PDF_REPORT_PAGE_HEADER_LOGO}"/>
            <span>联系电话：400-088-8275</span>
          </div>
        </div>
      `;
      // PDF 页脚
      const footerTemplate = `
        <div style="${defaultStyles.wrapper}">
          <div style="${defaultStyles.content}">
            <span>企查查科技股份有限公司</span>
            <span>
              <span class="pageNumber"></span>
              <span> / </span>
              <span class="totalPages"></span>
            </span>
          </div>
        </div>
      `;

      const time = uuidTime.v1(dbDiligence.snapshotId);
      const displayFileName = `风险洞察报告-${dbDiligence.name}${moment(time).format('YYYYMMDDHHmmssSSS')}`;
      const requestParams: AxiosRequestConfig = {
        method: 'POST',
        url: `${this.configService.kzzServer.pdfService}/generate`,
        data: {
          product: 'rover',
          title: displayFileName,
          content: Buffer.from(content).toString('base64'),
          coverContent: Buffer.from(cover).toString('base64'),
          id: dbDiligence.snapshotId + '-' + moment(dbDiligence.updateDate).unix(),
          output: 0,
          remoteContent: 0,
          forceUpdate: 0,
          pdfOptions: {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            outline: true,
            scale: 1,
            margin: {
              left: '0',
              right: '0',
              top: '80px',
              bottom: '80px',
            },
            headerTemplate,
            footerTemplate,
          },
          // aclGrade: 'private',
          needPreView: true,
        },
        headers: {
          'x-kzz-request-id': uuidv4(),
        },
        timeout: 300000,
      };
      const result = await this.httpUtils.sendResquest(requestParams);
      return { fileUrl: result.filePath, fileName: `风险洞察报告-${dbDiligence.name}`, previewUrl: result.previewPath };
    } catch (error) {
      this.logger.error(`generatePdf error diligenceId: ${diligenceId}, error: ${error} `);
      this.logger.error(error);
      throw error;
    }
  }

  /**
   * 获取最终受益人，受益自然人
   * @param keyNo
   * @param isBenefit 是否为最终受益人 true=最终受益人（受益所有人） false=受益自然人，默认为 true
   */
  @Cacheable({ ttlSeconds: 300 })
  public async getBenefitList(keyNo: string, isBenefit = true): Promise<{ Names: Benefit[]; NamesCount: number }> {
    try {
      const res = await this.companyDetailService.getBenefitDetail({
        keyNo,
        isBenefit,
      });
      return {
        Names: res?.Result?.Names ?? [],
        NamesCount: res?.Result?.NameCount ?? 0,
      };
    } catch (error) {
      this.logger.error('getBenefitList error:', error);
      return {
        Names: [],
        NamesCount: 0,
      };
    }
  }

  // 调试接口
  async getDiligenceHtml(currentUser: PlatformUser, condition: ExportConditionRequest) {
    // if ([1003263, 1074454].includes(currentUser.currentOrg)) {
    return await this.generatePdfView(1003263, condition.diligenceId);
    // }
    // throw new BadRequestException(RoverExceptions.Common.RequestFailed);
  }

  /*async createDiligencePDF(currentUser: PlatformUser, condition: ExportConditionRequest) {
    // if ([1003263, 1074454].includes(currentUser.currentOrg)) {
    return await this.generatePdf(1003263, condition.diligenceId);
    // }
    // throw new BadRequestException(RoverExceptions.Common.RequestFailed);
  }*/
}
