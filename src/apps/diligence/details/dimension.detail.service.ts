import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { EnterpriseLibApiSource } from '../../data/source/enterprise-lib-api.source';
import { CreditApiSource } from '../../data/source/credit-api.source';
import { DiligenceSnapshotHelper } from '../snapshot/diligence.snapshot.helper';
import { TenderApiSource } from '../../data/source/tender-api.source';
import { CompanyApiSource } from '../../data/source/company-api.source';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { RelatedCompanySource } from '../../data/source/related-company.source';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { CaseSource } from '../../data/source/case.source';
import { GetHitDetailsParamBase } from 'libs/model/diligence/req&res/GetHitDetailsParam';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DiligenceHistoryEntity } from '../../../libs/entities/DiligenceHistoryEntity';
import { Repository } from 'typeorm';
import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { NegativeNewsSource } from 'apps/data/source/negative-news.source';
import { JudgementSource } from '../../data/source/judgement.source';
import { CreditEsSource } from '../../data/source/credit-es.source';
import { InjectRepository } from '@nestjs/typeorm';
import { TaxEsSource } from '../../data/source/tax-es.source';
import { PledgeSource } from '../../data/source/pledge.source';
import { RiskChangeEsSource } from '../../data/source/risk-change/risk-change-es.source';
import { getDimensionRecordIdForSnapshot } from '../snapshot/utils.snapshot';
import { Cacheable } from 'type-cacheable';
import * as crypto from 'crypto';
import { AccessResourceDeniedException } from '../../../libs/exceptions/AccessResourceDeniedException';
import { SupervisePunishEsSource } from '../../data/source/supervise-punish-es.source';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { getDimensionHitStrategy } from '../../dimension/dimension.helper';
import { ViolationSource } from '../../data/source/violation.source';
import { QccProApiSource } from '../../data/source/qcc-pro-api.source';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Redis } from 'ioredis';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { getFullRiskModel } from '../../../libs/db_helpers/resource.publish.helper';
import { toPlainObject } from 'lodash';
import { OuterBlacklistSource } from '../../data/source/outer-blacklist.source';
import { OvsSanctionsBlacklistSource } from '../../data/source/ovs-sanctions-blacklist.source';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AssertESSource } from '../../data/source/asset-es.source';
import { PledgeMergerEsSource } from '../../data/source/pledge-merger-es.source';

@Injectable()
export class DimensionDetailService {
  protected matchedDimensions: DimensionTypeEnums[] = [];
  protected baseLogger: Logger = QccLogger.getLogger(DimensionDetailService.name);
  protected redisClient: Redis;

  constructor(
    readonly entLibService: EnterpriseLibApiSource,
    readonly creditService: CreditApiSource,
    readonly analyzeCreditService: CreditEsSource,
    readonly proService: QccProApiSource,
    @Inject(forwardRef(() => DiligenceSnapshotHelper))
    readonly snapshotHelperService: DiligenceSnapshotHelper,
    readonly tenderService: TenderApiSource,
    readonly companyService: CompanyApiSource,
    readonly relatedCompanyService: RelatedCompanySource,
    readonly caseService: CaseSource,
    readonly negativeNewsService: NegativeNewsSource,
    readonly judgementService: JudgementSource,
    readonly taxService: TaxEsSource,
    readonly pledgeService: PledgeSource,
    readonly riskChangeService: RiskChangeEsSource,
    readonly outerBlacklistService: OuterBlacklistSource,
    readonly supervisePunishService: SupervisePunishEsSource,
    readonly ovsSanctionsService: OvsSanctionsBlacklistSource,
    readonly assertESService: AssertESSource,
    readonly pledgeMergerEsService: PledgeMergerEsSource,
    readonly violationService: ViolationSource,
    @InjectRepository(DiligenceHistoryEntity) readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchDiligenceEntity) private readonly batchDiligenceRepo: Repository<BatchDiligenceEntity>,
    readonly redisService: RedisService,
  ) {
    this.redisClient = redisService.getClient();
  }

  /**
   * 获取详情目前只会在 风险扫描中出现，所以需要动态判断用户是否有自定义的 dimension definition
   * @param params
   * @param requestData
   * @param retrieveForSnapshot true-来自于生成快照的请求,取最新数据; false-不是来自于生成快照的请求,根据snapshotId取数据
   */
  @Cacheable({
    ttlSeconds: 5,
    cacheKey: (args) => {
      const array = args.map((arg) => {
        if (arg && typeof arg === 'object') {
          return Object.keys(arg)
            .sort()
            .map((key) => {
              return { [key]: arg[key] };
            });
        }
        return arg;
      });
      return crypto.createHash('md5').update(JSON.stringify(array)).digest('hex');
    },
  })
  async getHitsDetails(
    params: GetHitDetailsParamBase,
    requestData: HitDetailsBaseQueryParams,
    retrieveForSnapshot = false,
    diligenceEntityPassed?: DiligenceHistoryEntity,
  ): Promise<HitDetailsBaseResponse> {
    const { snapshotId, keyNo, companyName } = requestData;
    const { orgId, key: dimensionKey, strategyId, dimensionFilter } = params;
    if (!keyNo && !companyName) {
      throw new BadRequestException();
    }
    const diligenceEntity = diligenceEntityPassed || (await this.diligenceHistoryRepo.findOne({ orgId, snapshotId }));
    const modelCacheKey = `dimension_riskModel_${diligenceEntity.orgModelId}`;
    const cached = await this.redisClient.get(modelCacheKey);
    let riskModel: RiskModelEntity;
    if (cached) {
      riskModel = JSON.parse(cached);
    } else {
      riskModel = await getFullRiskModel(diligenceEntity.orgModelId, this.diligenceHistoryRepo.manager);
      await this.redisClient.setex(modelCacheKey, 600, JSON.stringify(toPlainObject(riskModel)));
    }
    const dimension: DimensionHitStrategyPO = await getDimensionHitStrategy(riskModel, dimensionKey, strategyId, this.diligenceHistoryRepo.manager);
    let response: HitDetailsBaseResponse = HitDetailsBaseResponse.ok();
    if (!dimension) {
      return HitDetailsBaseResponse.failed('该维度不存在');
    }

    // requestData.monitorGroupId = dimensionFilter.monitorGroupId;
    // 维度额外过滤条件拼接
    dimension.dimensionFilter = dimensionFilter;
    const dimensionSortField = dimension?.getSortField();

    let realtime = false;
    let snapshotStatus = ApiResponseStatusEnum.Unknown;
    try {
      let snapshotRes: HitDetailsBaseResponse = null;

      //TODO 快照已经支持保存所有数据，是否还需要针对最新一次的尽调去实时获取详情数据
      //if (snapshotId && from === 'record') {
      if (snapshotId && !retrieveForSnapshot) {
        // 请求来源是排查记录, 点击维度详情，直接返回快照数据
        // 持续排查动态视图，点击维度详情，直接返回快
        const sortField = requestData.field2 || dimensionSortField?.fieldSnapshot || requestData?.field || dimensionSortField?.field;
        const sortOrder = requestData.order || dimensionSortField?.order || 'DESC';
        snapshotRes = await this.snapshotHelperService.getSnapshotHitDetails({
          snapshotId,
          dimensionKey: [params.key],
          pagination: {
            pageIndex: requestData.pageIndex || 1,
            pageSize: requestData.pageSize || 5,
          },
          companyName,
          retrieveForSnapshot,
          sortField,
          sortOrder,
          esFilter: requestData.esFilter,
          strategyId,
        });
        snapshotStatus = snapshotRes?.status;
      }
      if (snapshotRes?.status === ApiResponseStatusEnum.OK) {
        response = snapshotRes;
      } else {
        realtime = true;

        if (!dimension) {
          //如果设置项关闭，直接返回response
          response = HitDetailsBaseResponse.failed('该维度不存在');
        } else if (dimension.status === DataStatusEnums.Disabled) {
          //如果设置项关闭，直接返回response
          response = HitDetailsBaseResponse.failed('该维度被关闭');
        } else {
          // 维度详情数据源和排查数据源可分开控制
          const source = dimension?.dimensionDef?.detailSource ? dimension.dimensionDef?.detailSource : dimension.source;
          switch (source) {
            case DimensionSourceEnums.EnterpriseLib: {
              response = await this.entLibService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.CreditES: {
              response = await this.analyzeCreditService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.CreditAPI: {
              response = await this.creditService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.Pro: {
              response = await this.proService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.Case: {
              response = await this.caseService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.NegativeNews: {
              response = await this.negativeNewsService.getDimensionDetail(dimension, requestData);
              break;
            }
            // case DimensionSourceEnums.Rover: {
            //   requestData['orgId'] = params.orgId;
            //   response = await this.getDimensionDetailFromRover(dimension, requestData);
            //   break;
            // }
            case DimensionSourceEnums.Tender: {
              response = await this.tenderService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.CompanyDetail: {
              response = await this.companyService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.Judgement: {
              response = await this.judgementService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.TaxAnnouncement: {
              response = await this.taxService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.Pledge: {
              response = await this.pledgeService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.RiskChange:
              response = await this.riskChangeService.getDimensionDetail(dimension, requestData);
              break;
            case DimensionSourceEnums.OuterBlacklist: {
              response = await this.outerBlacklistService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.SupervisePunish: {
              response = await this.supervisePunishService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.Violation: {
              response = await this.violationService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.RelatedCompany:
            case DimensionSourceEnums.NebulaGraph: {
              response = await this.relatedCompanyService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.OvsSanctionES: {
              response = await this.ovsSanctionsService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.AssetES: {
              response = await this.assertESService.getDimensionDetail(dimension, requestData);
              break;
            }
            case DimensionSourceEnums.PledgeMergerES: {
              response = await this.pledgeMergerEsService.getDimensionDetail(dimension, requestData);
              break;
            }
            default:
              this.baseLogger.error('unreachable code');
          }
        }
      }
    } catch (error) {
      this.baseLogger.error(`getHitsDetails err`);
      this.baseLogger.error(error);
      response.status = ApiResponseStatusEnum.FAILED;
      response.message = error.message || error;
    }
    response.realtime = realtime;
    response.Result = response.Result?.map((r) => {
      if (r.recordId) {
        return r;
      }
      // 为了保证维度详情的唯一性，需要根据维度详情的内容生成一个唯一的id
      return {
        ...r,
        recordId: getDimensionRecordIdForSnapshot(dimensionKey, r, keyNo, orgId),
      };
    });
    response.snapshotStatus = snapshotStatus;
    return response;
  }

  /**
   * 维度详情接口增加前置判断，只有当前org已存在被排查公司排查记录的情况下才允许获取详情接口
   * @param params
   * @param requestData
   */
  async getBundleLimitedHitsDetails(params: GetHitDetailsParamBase, requestData: HitDetailsBaseQueryParams): Promise<HitDetailsBaseResponse> {
    const queryParams = {
      orgId: params.orgId,
      companyId: requestData.keyNo,
    };
    if (requestData.snapshotId) {
      //如果有快照ID， 需要同时确保snapshotId和companyId能一一对应
      queryParams['snapshotId'] = requestData.snapshotId;
    }

    const historyEntity = await this.diligenceHistoryRepo.findOne(queryParams);
    if (!historyEntity) {
      throw new AccessResourceDeniedException();
    }

    return await this.getHitsDetails(params, requestData, false, historyEntity);
  }

  // async getDimensionDetailFromRover(dimension: DimensionHitStrategyPO, requestData: HitDetailsBaseQueryParams) {
  //   let response: HitDetailsBaseResponse = HitDetailsBaseResponse.ok();
  //   //TODO 用 rover.graph.service 扫描的维度，获取详情要用 rover.graph.service 里面的接口
  //   if (
  //     [
  //       ...this.relatedCompanyService.getBlacklistTypes(),
  //       ...this.relatedCompanyService.getPartnerTypes(),
  //       DimensionTypeEnums.CustomerPartnerInvestigation,
  //       DimensionTypeEnums.BlacklistPartnerInvestigation,
  //     ].some((t) => t === dimension.key)
  //   ) {
  //     response = await this.relatedCompanyService.getDimensionDetail(dimension, requestData);
  //   } else if (dimension.key === DimensionTypeEnums.CustomerSuspectedRelation || dimension.key === DimensionTypeEnums.BlacklistSuspectedRelation) {
  //     //  第三方列表交叉重叠疑似关联
  //     // 黑名单列表交叉重叠疑似关联
  //     response = await this.relatedCompanyService.getSuspectedRelation(dimension, requestData);
  //   } else {
  //     // response = await this.roverService.getDimensionDetail(dimension, requestData);
  //   }
  //   return response;
  // }
}
