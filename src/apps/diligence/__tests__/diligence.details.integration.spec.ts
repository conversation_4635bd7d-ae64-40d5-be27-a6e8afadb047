import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { generateUniqueTestIds } from 'apps/test_utils_module/test.user';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { getConnection } from 'typeorm';
import * as resourcePublishHelper from '../../../libs/db_helpers/resource.publish.helper';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionFieldKeyEnums } from '../../../libs/enums/dimension/dimension.filter.params';
import { StrategyRoleEnums } from '../../../libs/enums/StrategyRoleEnums';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from '../../../libs/model/diligence/dimension/DimensionHitStrategyPO';
import { GetHitDetailsParamBase } from '../../../libs/model/diligence/req&res/GetHitDetailsParam';
import { AppTestModule } from '../../app/app.test.module';
import * as dimensionHelper from '../../dimension/dimension.helper';
import { DimensionDetailService } from '../details/dimension.detail.service';
import { DiligenceSnapshotEsService } from '../snapshot/diligence.snapshot.es.service';
import { EnterpriseLibApiSource } from 'apps/data/source/enterprise-lib-api.source';
import { DiligenceSnapshotHelper } from '../snapshot/diligence.snapshot.helper';
import { QccProApiSource } from 'apps/data/source/qcc-pro-api.source';
import { CreditApiSource } from 'apps/data/source/credit-api.source';
import { CreditEsSource } from 'apps/data/source/credit-es.source';
import { BatchDiligenceEntity } from '../../../libs/entities/BatchDiligenceEntity';
import { TenderApiSource } from '../../data/source/tender-api.source';
import { CompanyApiSource } from '../../data/source/company-api.source';
import { RelatedCompanySource } from '../../data/source/related-company.source';
import { CaseSource } from '../../data/source/case.source';
import { NegativeNewsSource } from '../../data/source/negative-news.source';
import { JudgementSource } from '../../data/source/judgement.source';
import { TaxEsSource } from '../../data/source/tax-es.source';
import { PledgeSource } from '../../data/source/pledge.source';
import { RiskChangeEsSource } from '../../data/source/risk-change/risk-change-es.source';
import { OuterBlacklistSource } from '../../data/source/outer-blacklist.source';
import { SupervisePunishEsSource } from '../../data/source/supervise-punish-es.source';
import { ViolationSource } from '../../data/source/violation.source';
import { OvsSanctionsBlacklistSource } from '../../data/source/ovs-sanctions-blacklist.source';

jest.setTimeout(600 * 1000);

const [testOrgId, testUserId] = generateUniqueTestIds('diligence.details.unittest.spec.ts');

describe('DimensionDetailService.getHitsDetails 单元测试', () => {
  let testModule: TestingModule;
  let dimensionDetailService: DimensionDetailService;
  let snapshotHelperService: any;
  let entLibService: any;
  let analyzeCreditService: CreditApiSource;
  let creditService: any;
  let proService: any;

  const mockSnapshotData = {
    status: ApiResponseStatusEnum.OK,
    Result: [
      {
        id: 1,
        title: '快照数据1',
        content: '快照内容1',
      },
    ],
  };

  const mockRealTimeData = {
    status: ApiResponseStatusEnum.OK,
    Result: [
      {
        id: 2,
        title: '实时数据1',
        content: '实时内容1',
      },
    ],
  };

  const mockRiskModel: RiskModelEntity = {
    modelId: 1,
    modelName: 'Test Model',
    groups: [],
  } as RiskModelEntity;

  const mockDimensionStrategy: DimensionHitStrategyPO = {
    source: DimensionSourceEnums.EnterpriseLib,
    key: DimensionTypeEnums.AntiFraud,
    strategyId: 1,
    strategyName: 'Test Strategy',
    strategyRole: StrategyRoleEnums.Normal,
    status: DataStatusEnums.Enabled,
    template: '',
    fieldHitStrategy: {},
    dimensionDef: {
      key: DimensionTypeEnums.AntiFraud,
      source: DimensionSourceEnums.EnterpriseLib,
      name: 'Test Dimension',
    },
    strategyFields: [
      {
        dimensionFieldKey: DimensionFieldKeyEnums.sortField,
        fieldValue: [
          {
            field: 'defaultField',
            fieldSnapshot: 'defaultFieldSnapshot',
            order: 'DESC',
          },
        ],
      },
    ],
    getStrategyFieldByKey: (fieldKey) => mockDimensionStrategy.strategyFields.find((sf) => sf.dimensionFieldKey === fieldKey),
    getCycle: () => 1,
    getCycleField: () => mockDimensionStrategy.strategyFields.find((sf) => sf.dimensionFieldKey === DimensionFieldKeyEnums.cycle),
    getIsValid: () => 1,
    getSortField: () => ({
      field: 'defaultField',
      fieldSnapshot: 'defaultFieldSnapshot',
      order: 'DESC',
    }),
  } as DimensionHitStrategyPO;

  const mockHistoryData = {
    orgModelId: Date.now(),
    orgId: testOrgId,
    // 添加其他必要的字段
  };

  beforeAll(async () => {
    // Mock getFullRiskModel
    jest.spyOn(resourcePublishHelper, 'getFullRiskModel').mockResolvedValue(mockRiskModel);

    // Mock getDimensionHitStrategy
    jest.spyOn(dimensionHelper, 'getDimensionHitStrategy').mockResolvedValue(mockDimensionStrategy);

    // 创建 mock 服务
    const mockDiligenceHistoryRepo = {
      findOne: jest.fn().mockResolvedValue(mockHistoryData),
    };

    snapshotHelperService = {
      getSnapshotHitDetails: jest.fn().mockResolvedValue(mockSnapshotData),
    };

    entLibService = {
      getDimensionDetail: jest.fn().mockResolvedValue(mockRealTimeData),
    };

    analyzeCreditService = {
      getDimensionDetail: jest.fn().mockResolvedValue(mockRealTimeData),
    } as any as CreditApiSource;

    creditService = {
      getDimensionDetail: jest.fn().mockResolvedValue(mockRealTimeData),
    };

    proService = {
      getDimensionDetail: jest.fn().mockResolvedValue(mockRealTimeData),
    };

    testModule = await Test.createTestingModule({
      imports: [AppTestModule],
      providers: [
        DimensionDetailService,
        DiligenceSnapshotEsService,
        {
          provide: getRepositoryToken(DiligenceHistoryEntity),
          useValue: mockDiligenceHistoryRepo,
        },
        {
          provide: getRepositoryToken(BatchDiligenceEntity),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: EnterpriseLibApiSource,
          useValue: entLibService,
        },
        {
          provide: CreditApiSource,
          useValue: creditService,
        },
        {
          provide: CreditEsSource,
          useValue: analyzeCreditService,
        },
        {
          provide: QccProApiSource,
          useValue: proService,
        },
        {
          provide: DiligenceSnapshotHelper,
          useValue: snapshotHelperService,
        },
        {
          provide: TenderApiSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: CompanyApiSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: RelatedCompanySource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: CaseSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: NegativeNewsSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: JudgementSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: TaxEsSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: PledgeSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: RiskChangeEsSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: OuterBlacklistSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: SupervisePunishEsSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: ViolationSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
        {
          provide: OvsSanctionsBlacklistSource,
          useValue: {
            getDimensionDetail: jest.fn(),
          },
        },
      ],
    }).compile();

    dimensionDetailService = testModule.get<DimensionDetailService>(DimensionDetailService);
  });

  beforeEach(async () => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    const connection = getConnection();
    await connection.close();
  });

  it('当提供 snapshotId 时应该使用快照数据', async () => {
    const baseParams = new GetHitDetailsParamBase(testOrgId, DimensionTypeEnums.AntiFraud);
    const queryParams: HitDetailsBaseQueryParams = {
      keyNo: 'testKeyNo',
      companyName: 'testCompany',
      snapshotId: 'testSnapshotId',
      orgId: testOrgId,
    };

    const result = await dimensionDetailService.getHitsDetails(baseParams, queryParams);

    expect(result.status).toBe(ApiResponseStatusEnum.OK);
    expect(snapshotHelperService.getSnapshotHitDetails).toHaveBeenCalled();
    expect(result.Result).toEqual(mockSnapshotData.Result);
  });

  it('当不提供 snapshotId 时应该使用实时数据', async () => {
    const baseParams = new GetHitDetailsParamBase(testOrgId, DimensionTypeEnums.AntiFraud);
    const queryParams: HitDetailsBaseQueryParams = {
      keyNo: 'testKeyNo',
      companyName: 'testCompany',
      orgId: testOrgId,
    };

    const result = await dimensionDetailService.getHitsDetails(baseParams, queryParams);

    expect(result.status).toBe(ApiResponseStatusEnum.OK);
    expect(snapshotHelperService.getSnapshotHitDetails).not.toHaveBeenCalled();
    expect(entLibService.getDimensionDetail).toHaveBeenCalled();
    expect(result.Result).toEqual(mockRealTimeData.Result);
  });

  it('当快照数据获取失败时应该降级使用实时数据', async () => {
    snapshotHelperService.getSnapshotHitDetails.mockResolvedValueOnce({
      status: ApiResponseStatusEnum.FAILED,
      Result: null,
    });

    const baseParams = new GetHitDetailsParamBase(testOrgId, DimensionTypeEnums.AntiFraud);
    const queryParams: HitDetailsBaseQueryParams = {
      keyNo: 'testKeyNo',
      companyName: 'testCompany',
      snapshotId: 'testSnapshotId',
      orgId: testOrgId,
    };

    const result = await dimensionDetailService.getHitsDetails(baseParams, queryParams);

    expect(result.status).toBe(ApiResponseStatusEnum.OK);
    expect(snapshotHelperService.getSnapshotHitDetails).toHaveBeenCalled();
    expect(entLibService.getDimensionDetail).toHaveBeenCalled();
    expect(result.Result).toEqual(mockRealTimeData.Result);
  });

  it('当快照数据为空时应该降级使用实时数据', async () => {
    snapshotHelperService.getSnapshotHitDetails.mockResolvedValueOnce({
      status: ApiResponseStatusEnum.FAILED,
      Result: null,
    });

    const baseParams = new GetHitDetailsParamBase(testOrgId, DimensionTypeEnums.AntiFraud);
    const queryParams: HitDetailsBaseQueryParams = {
      keyNo: 'testKeyNo',
      companyName: 'testCompany',
      snapshotId: 'testSnapshotId',
      orgId: testOrgId,
    };

    const result = await dimensionDetailService.getHitsDetails(baseParams, queryParams);

    expect(result.status).toBe(ApiResponseStatusEnum.OK);
    expect(result.Result).toEqual(mockRealTimeData.Result);
  });

  it('当所有数据源都失败时应该返回错误状态', async () => {
    snapshotHelperService.getSnapshotHitDetails.mockResolvedValueOnce({
      status: ApiResponseStatusEnum.FAILED,
      Result: null,
    });

    entLibService.getDimensionDetail.mockResolvedValueOnce({
      status: ApiResponseStatusEnum.FAILED,
      Result: null,
    });

    const baseParams = new GetHitDetailsParamBase(testOrgId, DimensionTypeEnums.AntiFraud);
    const queryParams: HitDetailsBaseQueryParams = {
      keyNo: 'testKeyNo',
      companyName: 'testCompany',
      snapshotId: 'testSnapshotId',
      orgId: testOrgId,
    };

    const result = await dimensionDetailService.getHitsDetails(baseParams, queryParams);

    expect(result.status).toBe(ApiResponseStatusEnum.FAILED);
    expect(snapshotHelperService.getSnapshotHitDetails).toHaveBeenCalled();
    expect(entLibService.getDimensionDetail).toHaveBeenCalled();
  });
});
