import { DimensionHitResultPO } from '../../../libs/model/diligence/dimension/DimensionHitResultPO';
import { RiskModelEntity } from '../../../libs/entities/RiskModelEntity';
import { DiligenceTimeCostPO } from '../../../libs/model/diligence/dimension/DiligenceTimeCostPO';
import { calculateScore } from '../evaluation/model.score.utils';
import { DimensionRiskLevelEnum } from '../../../libs/enums/diligence/DimensionRiskLevelEnum';
import { RiskModelTypeEnums } from '../../../libs/enums/RiskModelTypeEnums';
import { DiligenceResultDefaultSetting, DueDiligenceResult } from '../../../libs/constants/diligence.constants';
import { ScoreStrategyEnums } from '../../../libs/enums/ScoreStrategyEnums';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionSourceEnums } from '../../../libs/enums/diligence/DimensionSourceEnums';
import { StrategyRoleEnums } from '../../../libs/enums/StrategyRoleEnums';
import { DataStatusEnums } from '../../../libs/enums/DataStatusEnums';
import { MetricTypeEnums } from '../../../libs/enums/metric/MetricTypeEnums';
import { ProductCodeEnums } from '../../../libs/enums/ProductCodeEnums';
import { DataCategoryEnums } from '../../../libs/enums/DataCategoryEnums';
import { GroupMetricRelationEntity } from '../../../libs/entities/GroupMetricRelationEntity';
import { MetricHitStrategyDefinitionPO } from '../../../libs/model/metric/MetricHitStrategyDefinitionPO';
import { MetricsEntity } from '../../../libs/entities/MetricsEntity';
import { GroupEntity } from '../../../libs/entities/GroupEntity';

describe('model.score.utils', () => {
  describe('calculateScore', () => {
    let mockTimeCost: DiligenceTimeCostPO;
    let mockRiskModel: RiskModelEntity;

    beforeEach(() => {
      mockTimeCost = {
        assert: 0,
        credit: 0,
        pro: 0,
        ent: 0,
        rover: 0,
        company: 0,
        tender: 0,
        case: 0,
        news: 0,
        judgement: 0,
        tax: 0,
        pledge: 0,
        creditRate: 0,
        outerBlacklist: 0,
        riskChange: 0,
        total: 0,
        supervisePunish: 0,
        violation: 0,
        graph: 0,
        related: 0,
      };

      mockRiskModel = {
        modelId: 1,
        modelName: '测试风险模型',
        product: ProductCodeEnums.Pro,
        comment: '测试用例',
        createBy: 1,
        publishBy: 1,
        updateBy: 1,
        createDate: new Date(),
        updateDate: new Date(),
        scoreStrategy: ScoreStrategyEnums.Default,
        branchTier: 0,
        branchCount: 1,
        branchCode: 'test',
        modelType: RiskModelTypeEnums.GeneralRiskModel,
        category: DataCategoryEnums.System,
        orgId: 1,
        extendFrom: null,
        versionMajor: 1,
        versionMinor: 0,
        versionPatch: 0,
        status: DataStatusEnums.Enabled,
        resultSetting: DiligenceResultDefaultSetting,
        publishedContent: JSON.parse('{}'),
        groups: [
          {
            groupId: 1,
            modelId: 1,
            groupName: '测试分组',
            productCode: ProductCodeEnums.Pro,
            publishBy: 1,
            riskLevel: DimensionRiskLevelEnum.High,
            status: DataStatusEnums.Enabled,
            createBy: 1,
            updateBy: 1,
            order: 0,
            createDate: new Date(),
            updateDate: new Date(),
            groupMetrics: [],
          } as GroupEntity,
        ],
      } as RiskModelEntity;
    });

    it('应该正确计算基本分数和风险等级(如果命中多个，需要累加分数)', () => {
      const toAddGroupMetrics = [
        {
          id: 1,
          metricsId: 1,
          status: DataStatusEnums.Enabled,
          groupId: 1,
          order: 0,
          createDate: new Date(),
          updateDate: new Date(),
          groupEntity: null,
          metricEntity: {
            metricsId: 1,
            name: '测试指标1',
            riskLevel: DimensionRiskLevelEnum.Medium,
            isVeto: 0,
            metricType: MetricTypeEnums.Simple,
            score: 10,
            productCode: ProductCodeEnums.Pro,
            status: DataStatusEnums.Enabled,
            extendFrom: null,
            category: DataCategoryEnums.System,
            createDate: new Date(),
            updateDate: new Date(),
            publishBy: 1,
            createBy: 1,
            dimensionHitStrategies: [],
            groupMetrics: [],
            detailsJson: {
              scoreStrategy: ScoreStrategyEnums.Default,
            },
            hitStrategy: [
              {
                must: [1],
                must_not: [],
                should: [],
                minimum_should_match: 1,
                scoreSettings: {
                  maxScore: 10,
                  riskLevel: DimensionRiskLevelEnum.High,
                },
                status: DataStatusEnums.Enabled,
                order: 0,
              } as MetricHitStrategyDefinitionPO,
            ],
          } as MetricsEntity,
        } as GroupMetricRelationEntity,
        {
          id: 2,
          metricsId: 2,
          status: DataStatusEnums.Enabled,
          groupId: 1,
          order: 0,
          createDate: new Date(),
          updateDate: new Date(),
          groupEntity: null,
          metricEntity: {
            metricsId: 2,
            name: '测试指标2',
            riskLevel: DimensionRiskLevelEnum.High,
            isVeto: 0,
            metricType: MetricTypeEnums.Simple,
            score: 100,
            productCode: ProductCodeEnums.Pro,
            status: DataStatusEnums.Enabled,
            extendFrom: null,
            category: DataCategoryEnums.System,
            createDate: new Date(),
            updateDate: new Date(),
            publishBy: 1,
            createBy: 1,
            dimensionHitStrategies: [],
            groupMetrics: [],
            detailsJson: {
              scoreStrategy: ScoreStrategyEnums.Default,
            },
            hitStrategy: [
              {
                must: [1, 2],
                must_not: [],
                should: [],
                minimum_should_match: 1,
                scoreSettings: {
                  maxScore: 100,
                  riskLevel: DimensionRiskLevelEnum.High,
                },
                status: DataStatusEnums.Enabled,
                order: 0,
              } as MetricHitStrategyDefinitionPO,
            ],
          } as MetricsEntity,
        } as GroupMetricRelationEntity,
      ];
      mockRiskModel.groups[0].groupMetrics.push(...toAddGroupMetrics);
      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 1,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
        {
          strategyId: 2,
          strategyName: '测试策略2',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 10,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      const targetGroupMetrics = mockRiskModel.groups[0].groupMetrics;
      // 验证结果
      const targetScore = targetGroupMetrics[0].metricEntity.score + targetGroupMetrics[1].metricEntity.score;
      expect(result.totalScore).toBe(targetScore);
      const targetHits = mockHits[0].totalHits * 2 + mockHits[1].totalHits;
      expect(result.totalHits).toBe(targetHits);
      expect(result.result).toBe(DueDiligenceResult.highRisk);
      expect(result.modelType).toBe(RiskModelTypeEnums.GeneralRiskModel);
      expect(result.groupMetricScores).toHaveLength(1);
      expect(result.groupMetricScores[0].score).toBe(targetScore);
      expect(result.groupMetricScores[0].totalHits).toBe(targetHits);
      expect(result.groupMetricScores[0].level).toBe(DimensionRiskLevelEnum.High);
    });

    it('当没有命中任何维度时应该返回通过结果', () => {
      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(0);
      expect(result.totalHits).toBe(0);
      expect(result.result).toBe(DueDiligenceResult.pass);
      expect(result.groupMetricScores).toHaveLength(1);
      expect(result.groupMetricScores[0].score).toBe(0);
      expect(result.groupMetricScores[0].totalHits).toBe(0);
    });

    it('当命中一票否决指标时应该返回高风险结果', () => {
      // 修改模型配置，添加一个一票否决指标
      const toAddGroupMetric = {
        id: 3,
        metricsId: 3,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 3,
          name: '一票否决指标',
          riskLevel: DimensionRiskLevelEnum.High,
          metricType: MetricTypeEnums.Simple,
          score: 5,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          isVeto: 1,
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 5,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity;
      mockRiskModel.groups[0].groupMetrics.push(toAddGroupMetric);

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 3,
          strategyName: '测试策略3',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(5);
      expect(result.result).toBe(DueDiligenceResult.highRisk);
      expect(result.vetoMetrics).toHaveLength(1);
      expect(result.vetoMetrics[0].metricsId).toBe(toAddGroupMetric.metricsId);
    });

    it('指标-当使用最大值评分策略时应该返回最高分数的那个策略的分数为指标的分数', () => {
      // 修改模型配置，使用最大值评分策略
      //   mockRiskModel.scoreStrategy = ScoreStrategyEnums.MaxLevel;
      mockRiskModel.groups[0].groupMetrics.push({
        id: 4,
        metricsId: 4,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 4,
          name: '测试指标4',
          riskLevel: DimensionRiskLevelEnum.Medium,
          metricType: MetricTypeEnums.Simple,
          score: 5,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.MaxLevel,
          },
          hitStrategy: [
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 5,
                riskLevel: DimensionRiskLevelEnum.Medium,
              },
            },
            {
              status: 1,
              must: [4],
              order: 1,
              scoreSettings: {
                maxScore: 10,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity);

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 3,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
        {
          strategyId: 4,
          strategyName: '测试策略2',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 3,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(10); // 应该返回 DimensionRiskLevelEnum.High 的那个策略对应的分数
      expect(result.totalHits).toBe(3);
    });

    it('当指标被禁用时不应该计入分数', () => {
      const toAddGroupMetric = {
        id: 3,
        metricsId: 3,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 3,
          name: '一票否决指标',
          riskLevel: DimensionRiskLevelEnum.High,
          metricType: MetricTypeEnums.Simple,
          score: 5,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          isVeto: 1,
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 5,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity;
      mockRiskModel.groups[0].groupMetrics.push(toAddGroupMetric);
      // 修改第一个指标状态为禁用
      mockRiskModel.groups[0].groupMetrics[0].status = DataStatusEnums.Disabled;

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 1,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(0);
      expect(result.totalHits).toBe(0);
      expect(result.result).toBe(DueDiligenceResult.pass);
    });

    it('当分数在边界值时应该正确判定风险等级', () => {
      const toAddGroupMetric = {
        id: 3,
        metricsId: 3,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 3,
          name: '一票否决指标',
          riskLevel: DimensionRiskLevelEnum.High,
          metricType: MetricTypeEnums.Simple,
          score: 5,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          isVeto: 1,
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 5,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity;
      mockRiskModel.groups[0].groupMetrics.push(toAddGroupMetric);
      // 修改模型配置，设置分数为边界值
      mockRiskModel.groups[0].groupMetrics[0].metricEntity.score = 15;
      mockRiskModel.groups[0].groupMetrics[0].metricEntity.hitStrategy[0].scoreSettings.maxScore = 15;

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 3,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(15);
      expect(result.result).toBe(DueDiligenceResult.highRisk);

      // 修改分数到另一个边界
      mockRiskModel.groups[0].groupMetrics[0].metricEntity.score = 16;
      mockRiskModel.groups[0].groupMetrics[0].metricEntity.hitStrategy[0].scoreSettings.maxScore = 16;

      const result2 = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      expect(result2.totalScore).toBe(16);
      expect(result2.result).toBe(DueDiligenceResult.highRisk);
    });

    it('当使用监控模型时应该记录所有命中策略', () => {
      const toAddGroupMetric = {
        id: 3,
        metricsId: 3,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 3,
          name: '一票否决指标',
          riskLevel: DimensionRiskLevelEnum.High,
          metricType: MetricTypeEnums.Simple,
          score: 5,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          isVeto: 1,
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            // 添加多个策略
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 5,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
            {
              status: DataStatusEnums.Enabled,
              must: [1],
              order: 2,
              scoreSettings: {
                maxScore: 3,
                riskLevel: DimensionRiskLevelEnum.Medium,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity;
      mockRiskModel.groups[0].groupMetrics.push(toAddGroupMetric);
      // 修改为监控模型
      mockRiskModel.modelType = RiskModelTypeEnums.MonitorModel;

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 1,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
        {
          strategyId: 3,
          strategyName: '测试策略3',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 3,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.modelType).toBe(RiskModelTypeEnums.MonitorModel);
      expect(result.totalScore).toBe(5); // 应该使用最高分数
      expect(result.totalHits).toBe(3);
      expect(result.groupMetricScores[0].scoreDetails[0].otherHitDetails).toHaveLength(1); // 应该记录其他命中策略
    });

    it('当有不同风险级别的指标时应该正确分组', () => {
      // 添加不同风险级别的指标
      mockRiskModel.groups[0].groupMetrics.push({
        id: 2,
        metricsId: 2,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 2,
          name: '高风险指标',
          riskLevel: DimensionRiskLevelEnum.High,
          metricType: MetricTypeEnums.Simple,
          score: 20,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            {
              status: 1,
              must: [2],
              order: 1,
              scoreSettings: {
                maxScore: 20,
                riskLevel: DimensionRiskLevelEnum.High,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity);

      mockRiskModel.groups[0].groupMetrics.push({
        id: 3,
        metricsId: 3,
        status: 1,
        groupId: 1,
        createDate: new Date(),
        updateDate: new Date(),
        metricEntity: {
          metricsId: 3,
          name: '提示风险指标',
          riskLevel: DimensionRiskLevelEnum.Alert,
          metricType: MetricTypeEnums.Simple,
          score: 2,
          productCode: ProductCodeEnums.Pro,
          status: DataStatusEnums.Enabled,
          createBy: 1,
          updateBy: 1,
          createDate: new Date(),
          updateDate: new Date(),
          extendFrom: null,
          category: DataCategoryEnums.System,
          publishBy: 1,
          dimensionHitStrategies: [],
          groupMetrics: [],
          detailsJson: {
            scoreStrategy: ScoreStrategyEnums.Default,
          },
          hitStrategy: [
            {
              status: 1,
              must: [3],
              order: 1,
              scoreSettings: {
                maxScore: 2,
                riskLevel: DimensionRiskLevelEnum.Alert,
              },
            },
          ],
        },
      } as GroupMetricRelationEntity);

      // 准备测试数据
      const mockHits: DimensionHitResultPO[] = [
        {
          strategyId: 1,
          strategyName: '测试策略1',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 1,
        },
        {
          strategyId: 2,
          strategyName: '测试策略2',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 2,
        },
        {
          strategyId: 3,
          strategyName: '测试策略3',
          strategyRole: StrategyRoleEnums.Normal,
          status: DataStatusEnums.Enabled,
          dimensionKey: DimensionTypeEnums.Risk_BaseInfo,
          dimensionName: '基础资质',
          source: DimensionSourceEnums.Pro,
          description: '测试描述',
          totalHits: 3,
        },
      ];

      // 执行测试
      const result = calculateScore(mockHits, mockRiskModel, mockTimeCost);

      // 验证结果
      expect(result.totalScore).toBe(22); // 10 + 20 + 2
      expect(result.totalHits).toBe(5);
      //   expect(result.result).toBe(DueDiligenceResult.highRisk);
      expect(result.levelGroup[DimensionRiskLevelEnum.High]).toHaveLength(1);
      expect(result.levelGroup[DimensionRiskLevelEnum.Alert]).toHaveLength(1);
    });
  });
});
