import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { DimensionContentSearchEsPO } from '../../../libs/model/es/DimensionSnapshotEsDoc';
import { flattenDeep } from 'lodash';

/**
 * 针对不同维度提取字段用于搜索
 * @param dimensionObj
 * @param dimensionKey
 */
export const generateDimensionSearchContent = (dimensionObj: object, dimensionKey: DimensionTypeEnums): DimensionContentSearchEsPO => {
  if (!dimensionObj || !dimensionKey) {
    return;
  }
  const doc = new DimensionContentSearchEsPO();
  switch (dimensionKey) {
    case DimensionTypeEnums.Judgement: {
      // 暂时发现  4是代理律师事务所，
      doc.principals = dimensionObj['caserole']
        ?.filter((r) => r.O < 4)
        .map((r) => {
          return {
            role: r.R,
            name: r.P,
            keyNo: r.N,
          };
        });
      doc.caseType = dimensionObj['casetype'];
      doc.reason = dimensionObj['casereason'];
      doc.reasonType = dimensionObj['casereasontype'];
      doc.courtName = dimensionObj['court'];
      doc.courtLevel = dimensionObj['courtlevel'];
      doc.year = dimensionObj['courtyear'];
      doc.amount = extractLong(dimensionObj['amountinvolved']);
      doc.area = dimensionObj['province'];
      doc.trialRound = dimensionObj['trialround'];
      // dimensionStatus 2:已结案 其他默认 -1:未结案
      doc.dimensionStatus = dimensionObj['closecaseflag'] == '2' ? '2' : '-1';
      doc.judgmentType = dimensionObj['doctype'];
      break;
    }
    case DimensionTypeEnums.CourtSessionAnnouncement: {
      doc.reason = dimensionObj['casereason'];
      doc.reasonType = dimensionObj['casereasontype'];
      doc.principals = dimensionObj['RoleList']
        ? flattenDeep(
            dimensionObj['RoleList'].map((d) => {
              return d['Items']?.map((item) => {
                return {
                  role: d['Desc'],
                  name: item['Name'],
                  keyNo: item['KeyNo'],
                };
              });
            }),
          )
        : [];
      doc.courtName = dimensionObj['executegov'];
      break;
    }
    case DimensionTypeEnums.AdministrativePenalties: {
      doc.amount = extractLong(dimensionObj['amount'] || dimensionObj['Amount']);
      doc.courtName = dimensionObj['court'];
      doc.year = dimensionObj['punishyear'];
      break;
    }
    case DimensionTypeEnums.NegativeNews: {
      doc.tags = dimensionObj['codedesc'];
      doc.sourceName = dimensionObj['source'];
      doc.publishTime = extractLong(dimensionObj['publishtime']);
      break;
    }
    case DimensionTypeEnums.RecentInvestCancellationsRiskChange:
    case DimensionTypeEnums.ActualControllerRiskChange:
    case DimensionTypeEnums.ListedEntityRiskChange:
    case DimensionTypeEnums.RiskChange: {
      doc.publishTime = extractLong(dimensionObj['PublishTime']);
      break;
    }
    case DimensionTypeEnums.ControllerCompany:
    case DimensionTypeEnums.AssetInvestigationAndFreezing:
    case DimensionTypeEnums.PledgeMerger: {
      doc.publishTime = extractLong(dimensionObj['Publishdate']);
      break;
    }
    case DimensionTypeEnums.PatentInfo: {
      doc.publishTime = extractLong(dimensionObj['ApplicationDate']);
      break;
    }
    case DimensionTypeEnums.RelatedCompanyChange:
    case DimensionTypeEnums.RelatedCompanies: {
      doc.companyName = dimensionObj['companyNameRelated'];
      break;
    }
  }
  return doc;
};

const extractLong = (val: any) => {
  if (val) {
    try {
      const n = parseInt(val);
      return n;
    } catch (e) {
      return null;
    }
  }
  return null;
};
