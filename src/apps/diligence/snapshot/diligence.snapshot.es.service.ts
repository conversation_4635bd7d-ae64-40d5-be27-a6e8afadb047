import { BadRequestException, Injectable } from '@nestjs/common';
import { ApiResponse, Client } from '@elastic/elasticsearch';
import { ConfigService } from '../../../libs/config/config.service';
import { SnapshotSavedPO } from './po/SnapshotSavedPO';
import { SearchSnapshotPO } from './po/SearchSnapshotPO';
import { PaginationResponse } from '../../../libs/model/common';
import * as _ from 'lodash';
import { chunk, omit } from 'lodash';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { SnapshotIgnorePO } from './po/SnapshotIgnorePO';
import { SearchResponse } from '@kezhaozhao/search-utils';
import { plainToClass } from 'class-transformer';
import { SnapshotSearchDimensionContentResponse, SnapshotSearchPaginationResponse } from './po/SnapshotSearchPaginationResponse';
import { EventEmitter } from 'events';
import { getChildIdForDiligence, getChildIdForDimensionHitStrategy, getDimensionIdForSnapshot, getDimensionRecordIdForSnapshot } from './utils.snapshot';
import { SnapshotEsDimensionDoc } from './po/SnapshotEsDimensionDoc';
import { RemoveSnapshotDiligenceDataPO } from './po/RemoveSnapshotDataPO';
import { EsAggsBucketItemPO } from '../../../libs/model/es/EsAggsBucketItemPO';
import { SnapshotEsDiligenceDoc } from './po/SnapshotEsDiligenceDoc';
import * as Bluebird from 'bluebird';
import { RoverEventEmitter } from '../../../libs/common/RoverEventEmitter';
import { generateDimensionSearchContent } from './snapshot.extract.utils.dimension';
import { SnapshotEsFilterPO } from '../../../libs/model/es/SnapshotEsFilterPO';
import { captureException } from '@sentry/node';
import { dateRangeRelativeToTimestamp } from '../../../libs/utils/date.utils';
import { EnvIsProd } from '../../utils/pdf/utils.env';

@Injectable()
export class DiligenceSnapshotEsService {
  public esClientRead: Client;
  public esClientWrite: Client;
  private readonly indexName: string;
  private readonly logger = QccLogger.getLogger(DiligenceSnapshotEsService.name);
  private docsToInsert: any[] = [];
  private esBatchSize = 1000; //这里是bulk API的参数，所以实际应该是 500个item
  private eventEmitter: EventEmitter = RoverEventEmitter.getInstance();

  constructor(private readonly configService: ConfigService) {
    this.esClientRead = new Client({
      nodes: this.configService.esConfig.snapshot.nodesQuery,
      ssl: { rejectUnauthorized: false },
    });
    this.esClientWrite = new Client({
      nodes: this.configService.esConfig.snapshot.nodesWrite,
      ssl: { rejectUnauthorized: false },
    });
    this.indexName = this.configService.esConfig.snapshot.indexName;
    // setInterval(this.flushEs.bind(this), 3000); //每隔3s执行一次写入
    // this.eventEmitter.on(SystemEvents.ProcessExit, this.flushEs.bind(this, [true]));
  }

  /**
   * TODO 快照的索引 需要增加 dimensionHitStrategy 子文档，用于保存维度和维度命中策略的关系
   * @param params
   */
  public async insertSnapshotData(params: SnapshotSavedPO, refreshNow = true): Promise<void> {
    const start = Date.now();
    this.logger.info(
      `insert snapshot data for diligenceId=${params.diligenceId}, dimensionKey=${params.dimensionKey},strategyId=${params.strategyId}, itemSize=${params.items?.length}`,
    );
    const { dimensionKey, snapshotId, items, orgId, diligenceId, diligenceAt, companyId, batchId, excludedDimensions } = params;
    // let customerEntity = params.customerEntity;
    if (!items?.length) {
      this.logger.warn('insertSnapshotData: items is empty');
      return null;
    }
    // if (!customerEntity) {
    //   customerEntity = await this.fetchCustomerRelatedInfo(orgId, companyId);
    // }
    const parentItems: { dimensionId: string; id: string; dimensionContent: string }[] = items.map((t) => {
      const id = getDimensionRecordIdForSnapshot(dimensionKey as DimensionTypeEnums, t, companyId, orgId);
      return {
        dimensionId: getDimensionIdForSnapshot(dimensionKey as DimensionTypeEnums, t, companyId, orgId),
        id: getDimensionRecordIdForSnapshot(dimensionKey as DimensionTypeEnums, t, companyId, orgId),
        dimensionContent: JSON.stringify({ ...t, recordId: id }),
        dimensionContentSearch: generateDimensionSearchContent(t, dimensionKey as DimensionTypeEnums),
      };
    });

    // TODO 这里需要注意， 目前Dimension会覆盖插入，只要ID一样会自动覆盖，是为了保证同一个 id 的数据一直保持最新，同时新增的 dimensionContentSearch的值也会同步更新，所以暂时保持这个机制
    const parentDocs = _.flatMap(
      parentItems
        // .filter((p) => !existingDimensions?.some((t) => p.id === t))
        .map((item) => {
          if (item) {
            return [
              { index: { _index: this.getWriteIndexName(), _id: item.id } },
              {
                ...item,
                dimensionKey,
                relation: 'dimension',
              },
            ];
          }
        }),
    );
    const body: any[] = [];
    if (parentDocs?.length > 0) {
      //确保父文档先插入，避免子文档插入失败
      // body.push(...parentDoc);
      await this.insertDocsToEs(parentDocs, true, params);
    }
    const diligenceItems: any[] = [];
    const strategyItems: any[] = [];
    // let customerItems: any[] = [];

    const diligenceItemIds: Set<string> = new Set();
    parentItems.forEach((parentDoc) => {
      // 处理 diligence 子文档
      const excludedItems = excludedDimensions?.filter((e) => e.recordId === parentDoc.id);
      const diligenceChildrenDoc = {
        'diligence.diligenceId': +diligenceId,
        'diligence.orgId': orgId,
        'diligence.companyId': companyId,
        'diligence.snapshotId': snapshotId,
        'diligence.diligenceAt': diligenceAt,
        'diligence.createDate': new Date(),
        'diligence.batchId': batchId,
        'diligence.status': 1,
        'diligence.id': getChildIdForDiligence(diligenceId, parentDoc.id),
        relation: {
          name: 'diligence',
          parent: parentDoc.id,
        },
      };
      if (!diligenceItemIds.has(diligenceChildrenDoc['diligence.id'])) {
        //防止插入重复的文档
        if (excludedDimensions?.length && excludedItems[0]) {
          diligenceChildrenDoc['diligence.status'] = 0;
          diligenceChildrenDoc['diligence.updateHistory'] = [
            {
              operatorId: excludedItems[0].operator,
              status: 0,
              content: '维度被人工过滤掉',
              createDate: excludedItems[0].createDate,
              excludedId: excludedItems[0].id,
            },
          ];
        }
        diligenceItems.push(diligenceChildrenDoc);
        diligenceItemIds.add(diligenceChildrenDoc['diligence.id']);
      }

      // 处理 dimensionStrategy 子文档
      if (params.strategyId) {
        const dimensionStrategyDoc = {
          'dimensionStrategy.diligenceId': +diligenceId,
          'dimensionStrategy.orgId': orgId,
          'dimensionStrategy.companyId': companyId,
          'dimensionStrategy.snapshotId': snapshotId,
          'dimensionStrategy.batchId': batchId,
          'dimensionStrategy.strategyId': params.strategyId,
          'dimensionStrategy.id': getChildIdForDimensionHitStrategy(params.strategyId, diligenceId, parentDoc.id),
          relation: {
            name: 'dimensionStrategy',
            parent: parentDoc.id,
          },
        };
        strategyItems.push(dimensionStrategyDoc);
      }
    });
    if (diligenceItems?.length > 0) {
      // 每次创建快照的时候都会 执行 removeSnapshotDiligenceData， 所有无需查询已存在的子文档进行过滤，直接覆盖就行
      // const existingDiligenceItems = await this.fetchExistingDocs(
      //   dimensionKey,
      //   diligenceItems.map((t) => t['diligence.id']),
      //   'diligence.id',
      // );
      // if (existingDiligenceItems?.length > 0) {
      //   diligenceItems = diligenceItems.filter((t) => !existingDiligenceItems.includes(t['diligence.id']));
      // }
      // // childrenItems.push(...diligenceItems);
      diligenceItems.forEach((childrenItem) => {
        body.push(
          {
            index: {
              _index: this.getWriteIndexName(),
              routing: childrenItem.relation.parent,
              _id: childrenItem['diligence.id'],
            },
          },
          childrenItem,
        );
      });
    }

    if (strategyItems.length > 0) {
      strategyItems.forEach((childrenItem) => {
        body.push(
          {
            index: {
              _index: this.getWriteIndexName(),
              routing: childrenItem.relation.parent,
              _id: childrenItem['dimensionStrategy.id'],
            },
          },
          childrenItem,
        );
      });
    }

    // if (refreshNow) {
    await this.insertDocsToEs(body, true, params);
    // } else {
    //   await this.insertSnapshotDocs(body);
    // }

    //
  }

  private async fetchExistingDocs(dimensionKey: string, docIds: string[], field: 'diligence.id' | 'customer.id') {
    if (dimensionKey && docIds?.length > 0) {
      const queryBody: any = {
        bool: {
          must: [
            {
              has_parent: {
                parent_type: 'dimension',
                query: {
                  bool: {
                    must: [
                      {
                        term: { dimensionKey },
                      },
                    ],
                  },
                },
              },
            },
            {
              terms: { [field]: docIds },
            },
          ],
        },
      };
      const { body } = await this.esClientRead.search({
        index: this.getReadIndexName(),
        body: {
          query: queryBody,
          _source: ['id', 'dimensionKey', field],
          size: docIds.length,
        },
      });
      return body?.hits?.hits?.map((item) => item?._source[field]);
    }
    throw new BadRequestException('dimensionKey and docIds must be provided');
  }

  // private async insertSnapshotDocs(items: any[]) {
  //   if (items?.length) {
  //     this.docsToInsert.push(...items);
  //   }
  //   if (this.docsToInsert.length >= this.esBatchSize) {
  //     //this.flushEs();
  //     await this.insertDocsToEs(this.docsToInsert.splice(0, this.esBatchSize));
  //     // if (items.length >= this.esBatchSize * 2) {
  //     //   await this.flushEs(); //执行两次，尽可能减少同一批次插入的数据被分成1以上批次的情况下，第二个批次要等待下次写入动作或者是定时任务来触发真正的写入
  //     // }
  //   }
  // }

  /**
   * 手动把内存中的 docs 刷新到 es中
   * @param exit
   */
  public async flushEs(exit?: boolean) {
    if (exit) {
      this.logger.warn(`receive exit event,  there has ${this.docsToInsert.length} items need to flush`);
    }
    if (this.docsToInsert.length > 0) {
      try {
        do {
          const items = exit ? this.docsToInsert : this.docsToInsert.splice(0, this.esBatchSize);
          // this.logger.info(`flush ${items.length} items to index=${this.getWriteIndex()} with exit=${exit}`);
          await this.insertDocsToEs(items);
        } while (this.docsToInsert.length > 0);
      } catch (e) {
        this.logger.error('flush usage to es throw error');
        this.logger.error(e);
      }
    }
  }

  private async insertDocsToEs(items: any[], refreshNow = true, params?: SnapshotSavedPO) {
    if (!items?.length) {
      return;
    }
    await Bluebird.map(
      chunk(items, this.esBatchSize),
      async (body: any[]) => {
        try {
          const start = Date.now();
          const { body: bulkResponse } = await this.esClientWrite.bulk({ refresh: refreshNow, body });
          if (bulkResponse.errors) {
            const erroredDocuments = [];
            bulkResponse.items.forEach((action: any, i: number) => {
              const operation = Object.keys(action)[0];
              if (action[operation].error) {
                erroredDocuments.push({
                  status: action[operation].status,
                  error: action[operation].error,
                  operation: body[i * 2],
                  document: body[i * 2 + 1],
                });
              }
            });
            const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, error_size=${erroredDocuments.length}`;
            this.logger.error(message);
            captureException(
              { message },
              {
                extra: params ? omit(params, ['items']) : {},
              },
            );
            this.logger.error(erroredDocuments);
          }
          this.logger.info(`insert ${body.length / 2} items to es cost ${Date.now() - start}ms`);
        } catch (error) {
          const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, errorMessage=` + error.message;
          this.logger.error(message);
          captureException(Object.assign(error, { message }), {
            extra: params ? omit(params, ['items']) : {},
          });
          this.logger.error(error);
        }
      },
      { concurrency: 5 },
    );
    if (refreshNow) {
      await this.refresh();
    }
  }

  /**
   *
   * 移除快照数据, 主要是子文档diligence 的数据
   * @param diligenceId
   * @param dimensionKey 如果指定了维度就只清除指定维度，否则就清除diligenceId对应的所有记录
   */
  public async removeSnapshotDiligenceData(params: RemoveSnapshotDiligenceDataPO): Promise<number> {
    const { diligenceId, dimensionKeys, orgId } = params;
    if (!dimensionKeys?.length || !diligenceId || !orgId) {
      return 0;
    }
    const queryBody: any = {
      bool: {
        should: [
          {
            bool: {
              must: [
                {
                  has_parent: {
                    parent_type: 'dimension',
                    query: {
                      bool: {
                        must: [
                          {
                            terms: {
                              dimensionKey: dimensionKeys,
                            },
                          },
                        ],
                      },
                    },
                  },
                },
                {
                  term: {
                    'diligence.orgId': orgId,
                  },
                },
                {
                  term: {
                    'diligence.diligenceId': diligenceId,
                  },
                },
              ],
            },
          },
          {
            bool: {
              must: [
                {
                  has_parent: {
                    parent_type: 'dimension',
                    query: {
                      bool: {
                        must: [
                          {
                            terms: {
                              dimensionKey: dimensionKeys,
                            },
                          },
                        ],
                      },
                    },
                  },
                },
                {
                  term: {
                    'dimensionStrategy.orgId': orgId,
                  },
                },
                {
                  term: {
                    'dimensionStrategy.diligenceId': diligenceId,
                  },
                },
              ],
            },
          },
        ],
        minimum_should_match: 1,
      },
    };
    // if (diligenceId || childIds?.length > 0) {
    //   if (diligenceId) {
    //     queryBody.bool.must.push({
    //       term: { 'diligence.diligenceId': diligenceId },
    //     });
    //   }
    //   if (childIds?.length > 0) {
    //     queryBody.bool.must.push({
    //       terms: { 'diligence.id': childIds },
    //     });
    //   }
    // }
    const re = await this.esClientWrite.delete_by_query({
      index: this.getReadIndexName(),
      refresh: true,
      body: {
        query: queryBody,
      },
    });
    return re?.body?.deleted;
  }

  /**
   *  删除指定batchId对应的 diligenceId 的快照数据
   * @param batchId
   * @param diligenceIds
   * @returns
   */
  public async removeBatchSnapshot(diligenceIds: number[], batchId?: number) {
    if (!diligenceIds?.length) {
      return 0;
    }
    try {
      // 删除子文档
      const queryBody: any = {
        bool: {
          should: [
            {
              // 删除 diligence 子文档
              bool: {
                must: [
                  //{ term: { 'diligence.batchId': batchId } },
                  { terms: { 'diligence.diligenceId': diligenceIds } },
                ],
              },
            },
            {
              // 删除 dimensionStrategy 子文档
              bool: {
                must: [
                  // { term: { 'dimensionStrategy.batchId': batchId } },
                  { terms: { 'dimensionStrategy.diligenceId': diligenceIds } },
                ],
              },
            },
          ],
          minimum_should_match: 1,
        },
      };

      const re = await this.esClientWrite.delete_by_query({
        index: this.getReadIndexName(),
        refresh: true,
        body: {
          query: queryBody,
        },
      });
      return re?.body?.deleted;
    } catch (error) {
      this.logger.error(`Failed to remove batch snapshot: ${error.message}`);
      this.logger.error(error);
      return 0;
    }
  }
  /**
   *  删除指定batchId
   * @param batchId
   * @returns
   */
  public async removeBatchSnapshotByBatch(batchId: number[]) {
    if (!batchId?.length) {
      return 0;
    }
    try {
      // 删除子文档
      const queryBody: any = {
        bool: {
          should: [
            {
              // 删除 diligence 子文档
              bool: {
                must: [{ terms: { 'diligence.batchId': batchId } }],
              },
            },
            {
              // 删除 dimensionStrategy 子文档
              bool: {
                must: [{ terms: { 'dimensionStrategy.batchId': batchId } }],
              },
            },
          ],
          minimum_should_match: 1,
        },
      };

      const re = await this.esClientWrite.delete_by_query({
        index: this.getReadIndexName(),
        refresh: true,
        body: {
          query: queryBody,
        },
      });
      return re?.body?.deleted;
    } catch (error) {
      this.logger.error(`Failed to remove batch snapshot: ${error.message}`);
      this.logger.error(error);
      return 0;
    }
  }

  /**
   * 移除没有diligence子文档的所有dimension节点
   * @param dimensionKey
   */
  public async removeOrphanDimension() {
    // 删除没有子文档的父文档（孤立的父文档）
    try {
      const orphanParentQuery = {
        bool: {
          must: [{ term: { relation: 'dimension' } }],
          must_not: [
            {
              has_child: {
                type: 'diligence',
                query: {
                  match_all: {},
                },
              },
            },
            // 不需要处理 dimensionStrategy，没有diligence节点就可以删除了
            //{
            //   has_child: {
            //     type: 'dimensionStrategy',
            //     query: {
            //       match_all: {},
            //     },
            //   },
            // },
          ],
        },
      };

      const orphanResult = await this.esClientWrite.delete_by_query({
        index: this.getReadIndexName(),
        refresh: true,
        body: {
          query: orphanParentQuery,
        },
      });

      this.logger.info(`Removed ${orphanResult?.body?.deleted || 0} orphaned parent documents after batch snapshot deletion`);
      return orphanResult?.body?.deleted || 0;
    } catch (error) {
      this.logger.error(`Failed to remove orphaned parent documents: ${error.message}`);
      this.logger.error(error);
    }
    return 0;
  }

  /**
   * 更新快照数据
   * 主要用于支持用户手动忽略掉指定的记录
   * 如果params.status 没传值，默认会设置成0（把数据标记为不可用）
   * @param params
   * @param refreshNow
   */
  public async changeSnapshotDataStatus(params: SnapshotIgnorePO, refreshNow = false) {
    const { diligenceId, dimensionKeys, docIds, status } = params;
    if (!diligenceId || (!docIds?.length && !dimensionKeys?.length)) {
      throw new BadRequestException('diligenceId and (docIds or dimensionKeys) must be provided');
    }
    const parentQuery: any = {
      bool: {
        must: [],
      },
    };
    if (docIds?.length === 1) {
      parentQuery.bool.must.push({
        terms: {
          id: docIds,
        },
      });
    }
    if (dimensionKeys?.length === 1) {
      parentQuery.bool.must.push({
        terms: {
          dimensionKey: dimensionKeys,
        },
      });
    }

    const queryBody = {
      query: {
        bool: {
          must: [
            {
              term: {
                'diligence.diligenceId': diligenceId,
              },
            },
            {
              has_parent: {
                parent_type: 'dimension',
                query: parentQuery,
              },
            },
          ],
        },
      },
      script: {
        source:
          'ctx._source["diligence.status"] = params.status;ctx._source["diligence.updateDate"] = params.updateDate;' +
          'if (!ctx._source.containsKey("diligence.updateHistory")) {\n' +
          '            ctx._source["diligence.updateHistory"] = [];\n' +
          '        }\n' +
          '            ctx._source["diligence.updateHistory"].add(params.updateHistoryItem);',
        lang: 'painless',
        params: {
          status: status || 0,
          updateDate: new Date(),
          updateHistoryItem: {
            operatorId: params.operatorId,
            status: status || 0,
            content: params.content,
            createDate: new Date(),
          },
        },
      },
    };
    //修改指定的一些数据的状态
    const { body: response } = await this.esClientWrite.updateByQuery({
      index: this.getWriteIndexName(),
      body: queryBody,
      refresh: refreshNow,
    });
    return response.updated;
  }

  /**
   * 绑定 diligenceId 和 batchId 的关系， 方便批量对比
   * @param diligenceId
   * @param batchId
   * @param remove
   */
  public async refreshBatchDiligenceRelation(diligenceId: number[], batchId: number, remove = false) {
    let script =
      'if (!ctx._source.containsKey("diligence.batchId")) {\n' +
      '                    ctx._source["diligence.batchId"] = [];\n' +
      '                }\n' +
      '                if (!ctx._source["diligence.batchId"].contains(params.batchId)) {\n' +
      '                    ctx._source["diligence.batchId"].add(params.batchId);ctx._source["diligence.updateDate"] = params.updateDate;\n' +
      '                }';
    if (remove) {
      script =
        'if (!ctx._source.containsKey("diligence.batchId")) {\n' +
        '                    ctx._source["diligence.batchId"] = [];\n' +
        '                }\n' +
        '                if (ctx._source["diligence.batchId"].contains(params.batchId)) {\n' +
        '                    ctx._source["diligence.batchId"].remove(ctx._source["diligence.batchId"].indexOf(params.batchId));ctx._source["diligence.updateDate"] = params.updateDate;\n' +
        '                }';
    }
    const body = {
      query: {
        bool: {
          must: [
            {
              terms: {
                'diligence.diligenceId': diligenceId,
              },
            },
          ],
        },
      },
      script: {
        source: script, // 脚本定义了如何更新文档
        lang: 'painless',
        params: {
          batchId: batchId,
          updateDate: new Date(),
        },
      },
    };
    try {
      const { body: response } = await this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        refresh: true,
        body,
      });
      return response.updated;
    } catch (error) {
      this.logger.error(`refreshBatchDiligenceRelation error ! body: ${JSON.stringify(body)}`);
      this.logger.error(error);
    }
  }

  public async refreshSnapshotInfo(diligenceId: number, updateFields: Partial<SnapshotEsDiligenceDoc>) {
    if (!diligenceId || !updateFields?.batchId?.length) {
      return;
    }
    try {
      const { body: response } = await this.esClientWrite.updateByQuery({
        index: this.getWriteIndexName(),
        refresh: false,
        body: {
          query: {
            // 查询条件
            bool: {
              must: [
                {
                  term: {
                    'diligence.diligenceId': diligenceId,
                  },
                },
              ],
            },
          },
          script: {
            source: 'ctx._source.batchId=params.batchId;' + 'ctx._source.updateDate = params.updateDate;',
            lang: 'painless',
            params: {
              batchId: updateFields.batchId || [],
              updateDate: new Date(),
            },
          },
        }, // 更新脚本
      });
      return response.updated;
    } catch (e) {
      this.logger.error('refreshSnapshotInfo throw error:' + e.message);
      this.logger.error(e);
    }
    return -1;
  }

  // public async getDimensionSnapshotByRecordId(recordId: string): Promise<SnapshotEsDimensionDoc> {
  //   const searchResponse = (await this.searchSnapshotData({
  //     recordIds: [recordId],
  //     pageSize: 1,
  //     pageIndex: 1,
  //   })) as SnapshotSearchPaginationResponse;
  //   return searchResponse.data[0];
  // }

  /**
   * 搜索快照数据
   * @param params
   * @param onlyReturnBusiness  是否只返回 dimensionContent 字段的内容
   * @param _source_includes
   */
  public async searchSnapshotData(
    params: SearchSnapshotPO,
    onlyReturnBusiness?: boolean,
    _source_includes?: string[],
  ): Promise<SnapshotSearchDimensionContentResponse | SnapshotSearchPaginationResponse> {
    const { orgId, dimensionKey, companyId, diligenceId, status, snapshotId, recordIds, esFilter, strategyId, sort } = params;
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const status1 = status ?? 1;
    if (!dimensionKey?.length && !recordIds?.length) {
      throw new BadRequestException('dimensionKey or recordIds must be provided');
    }
    const queryBody: any = {
      bool: {
        filter: [],
      },
    };
    const srotQuery = [];
    if (sort?.field) {
      srotQuery.push({
        [sort.field]: {
          order: sort.order || 'desc',
        },
      });
    }
    const esQueryBody: any = {};
    if (!diligenceId?.length && !snapshotId?.length && !recordIds?.length) {
      throw new BadRequestException('diligenceId , snapshotId or recordIds must be provided');
    }

    if (strategyId) {
      queryBody.bool.filter.push({
        has_child: {
          type: 'dimensionStrategy',
          query: {
            bool: {
              must: [
                {
                  term: {
                    'dimensionStrategy.strategyId': {
                      value: strategyId,
                    },
                  },
                },
                {
                  terms: { 'dimensionStrategy.diligenceId': diligenceId },
                },
              ],
            },
          },
        },
      });
    }
    if (dimensionKey) {
      queryBody.bool.filter.push({
        terms: {
          dimensionKey,
        },
      });
    }
    if (recordIds) {
      queryBody.bool.filter.push({
        terms: { id: recordIds },
      });
    }
    const childQuery: any = {
      has_child: {
        type: 'diligence',
        query: {
          bool: {
            filter: [
              {
                term: { 'diligence.status': status1 },
              },
            ],
          },
        },
      },
    };
    if (diligenceId?.length) {
      childQuery.has_child.query.bool.filter.push({
        terms: { 'diligence.diligenceId': diligenceId },
      });
    }
    if (snapshotId?.length) {
      childQuery.has_child.query.bool.filter.push({
        terms: { 'diligence.snapshotId': snapshotId },
      });
    }
    if (companyId) {
      childQuery.has_child.query.bool.filter.push({
        term: { 'diligence.companyId': companyId },
      });
    }
    if (orgId) {
      childQuery.has_child.query.bool.filter.push({
        term: { 'diligence.orgId': orgId },
      });
    }
    queryBody.bool.filter.push(childQuery);
    if (esFilter) {
      this.processEsFilter(params.esFilter, dimensionKey, queryBody, esQueryBody, companyId);
    }
    esQueryBody.query = queryBody;
    esQueryBody.sort = srotQuery;
    const response: ApiResponse<SearchResponse<SnapshotEsDimensionDoc>> = await this.esClientRead.search({
      index: this.getReadIndexName(),
      size: pageSize,
      from: Math.max((pageIndex - 1) * pageSize, 0),
      body: esQueryBody,
      track_total_hits: true,
      _source_includes,
    });
    const paginationResponse = new PaginationResponse();
    paginationResponse.pageSize = pageSize;
    paginationResponse.pageIndex = pageIndex;
    paginationResponse.total = response.body.hits.total.value;
    paginationResponse.data =
      response.body.hits.hits.map((item) => {
        if (onlyReturnBusiness) {
          return item._source.dimensionContent ? (JSON.parse(item._source.dimensionContent) as SnapshotSearchDimensionContentResponse) : null;
        } else {
          return plainToClass(SnapshotEsDimensionDoc, item._source);
        }
      }) || [];
    paginationResponse.aggs = this.handleAggsBuckets(response.body.aggregations);
    if (!EnvIsProd()) {
      const QueryObj = { indexName: this.indexName, indexType: this.getReadIndexName() };
      const info = await this.esClientRead.info();
      const urlHref = info?.meta?.connection?.url?.href || undefined;
      if (urlHref) {
        Object.assign(QueryObj, {
          infos: {
            _search: { url: `${urlHref}${this.indexName}/_search`, method: 'POST', body: esQueryBody },
            _mapping: { url: `${urlHref}${this.indexName}/_mapping`, method: 'GET' },
          },
        });
      }
      Object.assign(paginationResponse, { QueryObj });
    }
    return paginationResponse;
  }

  private handleAggsBuckets(aggs: any) {
    const resObj: any = {};
    if (aggs) {
      Object.keys(aggs).forEach((aggName) => {
        const obj1 = aggs[aggName];
        if (obj1.buckets) {
          //没有更深一层的聚合，本身也是一个bucket 聚合对象
          resObj[aggName] = obj1.buckets;
        } else {
          Object.keys(obj1).forEach((key2) => {
            const obj2 = obj1[key2];
            if (obj2.buckets) {
              resObj[`${aggName}_${key2}`] = obj2.buckets;
            }
          });
        }
        if (aggName == 'principals') {
          if (obj1?.role?.filtered_role_terms?.buckets) {
            resObj['principals'] = obj1.role.filtered_role_terms.buckets;
          }
        }
      });
    }
    return resObj;
  }

  /**
   *
   * @param esFilter
   * @param dimensionKey
   * @param queryBody es query 条件
   * @param esQueryBody es query body ,  query 和 aggs
   * @param companyId
   * @private
   */
  public processEsFilter(
    esFilter: SnapshotEsFilterPO,
    dimensionKey: DimensionTypeEnums[],
    queryBody: { bool: { must: any[]; filter: any[]; should: any[] } },
    esQueryBody: { query: any; aggs: any },
    companyId: string,
  ) {
    if (esFilter) {
      //有该字段说明要通过 es 直接过滤和聚合等操作
      if (esFilter.filter) {
        const filter = esFilter.filter;
        Object.keys(esFilter.filter).forEach((key) => {
          if (filter[key]?.length > 0) {
            if (key == 'principalRole') {
              queryBody.bool.filter.push({
                nested: {
                  path: 'dimensionContentSearch.principals',
                  query: {
                    bool: {
                      filter: [
                        {
                          terms: {
                            'dimensionContentSearch.principals.role': filter[key],
                          },
                        },
                        {
                          term: {
                            'dimensionContentSearch.principals.keyNo': companyId,
                          },
                        },
                      ],
                    },
                  },
                },
              });
            } else if (key == 'publishTime') {
              // 发布时间范围
              const rangeArr = filter[key];
              if (rangeArr.length > 0) {
                const dateRange = dateRangeRelativeToTimestamp(rangeArr[0]);
                queryBody.bool.filter.push({
                  range: {
                    [`dimensionContentSearch.publishTime`]: { gte: dateRange.min, lte: dateRange.max },
                  },
                });
              }
            } else if (key == 'companyName') {
              const keyword = filter[key] as string;
              queryBody.bool.filter.push({
                match_phrase: { 'dimensionContentSearch.companyName': keyword },
              });
              // 企业名称-关联方
            } else if (key == 'amount') {
              // 金额范围
              const rangeArr = filter[key];
              if (rangeArr.length > 0) {
                const numberRange = rangeArr[0];
                if ((numberRange.min || numberRange.min === 0) && (numberRange.max || numberRange.max === 0)) {
                  queryBody.bool.filter.push({
                    range: {
                      [`dimensionContentSearch.amount`]: { gte: numberRange.min, lte: numberRange.max },
                    },
                  });
                } else if (numberRange.min || numberRange.min === 0) {
                  queryBody.bool.filter.push({
                    range: {
                      [`dimensionContentSearch.amount`]: { gte: numberRange.min },
                    },
                  });
                } else if (numberRange.max || numberRange.max === 0) {
                  queryBody.bool.filter.push({
                    range: {
                      [`dimensionContentSearch.amount`]: { lte: numberRange.max },
                    },
                  });
                }
              }
            } else {
              queryBody.bool.filter.push({
                terms: {
                  [`dimensionContentSearch.${key}`]: filter[key],
                },
              });
            }
          }
        });
      }

      if (esFilter.aggs) {
        const aggsObj = this.populateAggsBody(dimensionKey, companyId);
        if (Object.keys(aggsObj).length) {
          esQueryBody.aggs = aggsObj;
        }
      }
    }
  }

  private populateAggsBody(dimensionKey: DimensionTypeEnums[], companyId: string) {
    const aggsBody: any = {};

    dimensionKey.forEach((item) => {
      switch (item) {
        case DimensionTypeEnums.CourtSessionAnnouncement: {
          Object.assign(aggsBody, {
            courtName: {
              terms: {
                field: 'dimensionContentSearch.courtName',
                size: 20,
              },
            },
            year: {
              terms: {
                field: 'dimensionContentSearch.year',
                size: 20,
              },
            },
            reason: {
              terms: {
                field: 'dimensionContentSearch.reason',
                size: 20,
              },
            },
            principals: {
              nested: {
                path: 'dimensionContentSearch.principals',
              },
              aggs: {
                role: {
                  filter: {
                    term: {
                      'dimensionContentSearch.principals.keyNo': companyId,
                    },
                  },
                  aggs: {
                    filtered_role_terms: {
                      terms: {
                        field: 'dimensionContentSearch.principals.role',
                        size: 20,
                      },
                    },
                  },
                },
              },
            },
          });
          break;
        }

        case DimensionTypeEnums.Judgement: {
          Object.assign(aggsBody, {
            area: {
              terms: {
                field: 'dimensionContentSearch.area',
                size: 20,
              },
            },
            trialRound: {
              terms: {
                field: 'dimensionContentSearch.trialRound',
                size: 20,
              },
            },
            courtName: {
              terms: {
                field: 'dimensionContentSearch.courtName',
                size: 20,
              },
            },
            dimensionStatus: {
              terms: {
                field: 'dimensionContentSearch.dimensionStatus',
                size: 20,
              },
            },
            courtLevel: {
              terms: {
                field: 'dimensionContentSearch.courtLevel',
                size: 20,
              },
            },
            year: {
              terms: {
                field: 'dimensionContentSearch.year',
                size: 20,
              },
            },
            judgmentType: {
              terms: {
                field: 'dimensionContentSearch.judgmentType',
                size: 20,
              },
            },
            reason: {
              terms: {
                field: 'dimensionContentSearch.reason',
                size: 20,
              },
            },
            caseType: {
              terms: {
                field: 'dimensionContentSearch.caseType',
                size: 20,
              },
            },
            principals: {
              nested: {
                path: 'dimensionContentSearch.principals',
              },
              aggs: {
                role: {
                  filter: {
                    term: {
                      'dimensionContentSearch.principals.keyNo': companyId,
                    },
                  },
                  aggs: {
                    filtered_role_terms: {
                      terms: {
                        field: 'dimensionContentSearch.principals.role',
                        size: 20,
                      },
                    },
                  },
                },
              },
            },
          });
          break;
        }
        case DimensionTypeEnums.NegativeNews: {
          Object.assign(aggsBody, {
            sourceName: {
              terms: {
                field: 'dimensionContentSearch.sourceName',
                size: 20,
              },
            },
            tags: {
              terms: {
                field: 'dimensionContentSearch.tags',
                size: 20,
              },
            },
          });
          break;
        }
      }
    });
    return aggsBody;
  }

  public async refresh() {
    const response = await this.esClientWrite.indices.refresh({ index: this.getReadIndexName() });
    return response;
  }

  public getWriteIndexName(): string {
    return `${this.indexName}_write`;
  }

  public getReadIndexName(): string {
    return `${this.indexName}_query`;
  }

  /**
   * 找出存在快照的diligence
   * @param diligenceIds
   */
  public async findExistSnapshotByDiligenceIds(diligenceIds: number[]): Promise<number[]> {
    if (!diligenceIds?.length) {
      return [];
    }
    const query: any = {
      bool: {
        must: [
          {
            terms: { 'diligence.diligenceId': diligenceIds },
          },
          {
            term: { 'diligence.status': 1 },
          },
        ],
      },
    };
    const aggs: any = {
      diligence: {
        terms: {
          field: 'diligence.diligenceId',
          size: diligenceIds.length,
        },
      },
    };

    const response: ApiResponse<SearchResponse<SnapshotEsDimensionDoc>> = await this.esClientRead.search({
      index: this.getReadIndexName(),
      size: 0,
      body: {
        query,
        aggs,
      },
    });
    return response.body.aggregations?.diligence?.buckets.filter((t) => t.doc_count > 0).map((item: EsAggsBucketItemPO) => parseInt(item.key)) || [];
  }

  /**
   * 检查是否存在快照数据
   * @param diligenceIds
   */
  public async searchDiligenceByBatchId(diligenceIds: number[]): Promise<number[]> {
    return this.findExistSnapshotByDiligenceIds(diligenceIds);
  }

  public async countChildDocCount(diligenceIds: number[]) {
    const query: any = {
      bool: {
        should: [{ terms: { 'diligence.diligenceId': diligenceIds } }, { terms: { 'dimensionStrategy.diligenceId': diligenceIds } }],
      },
    };
    const response: ApiResponse<SearchResponse<SnapshotEsDimensionDoc>> = await this.esClientRead.search({
      index: this.getReadIndexName(),
      body: { query },
      size: 1,
      track_total_hits: true,
    });
    return response.body.hits.total.value;
  }

  /**
   * 根据 orgId 和 当前指定的 batchIds 找出快照中：
   * 1. orgId 一致
   * 2. 有batchId 但是不在 batchIds 里面的数据
   * @param orgId
   * @param batchId
   */
  public async findUnexpectedSnapshot(orgId: number, batchIds: number[]) {
    if (!orgId || !batchIds?.length) {
      return {
        batchIds: [],
        total: 0,
      };
    }

    try {
      // 查询条件：查找指定组织下，有batchId但不在当前batchIds列表中的数据
      const query = {
        bool: {
          must: [
            { term: { 'diligence.orgId': orgId } },
            {
              exists: {
                field: 'diligence.batchId',
              },
            },
          ],
          must_not: [{ terms: { 'diligence.batchId': batchIds } }],
          // {
          //   bool: {
          //     must: [
          //       { term: { 'dimensionStrategy.orgId': orgId } },
          //       {
          //         exists: {
          //           field: 'dimensionStrategy.batchId',
          //         },
          //       },
          //     ],
          //     must_not: [{ terms: { 'dimensionStrategy.batchId': batchIds } }],
          //   },
          // },
        },
      };

      // 聚合查询，按diligenceId分组
      const aggs = {
        batchIds: {
          terms: {
            field: 'diligence.batchId',
            size: 1000,
          },
        },
      };

      const response: ApiResponse<SearchResponse<SnapshotEsDimensionDoc>> = await this.esClientRead.search({
        index: this.getReadIndexName(),
        size: 0, // 只需要聚合结果，不需要具体文档
        body: {
          query,
          aggs,
        },
      });

      const unexpectedBatchIds = response.body.aggregations?.batchIds?.buckets.map((item: EsAggsBucketItemPO) => parseInt(item.key)) || [];

      return {
        batchIds: unexpectedBatchIds,
        total: response.body.hits.total.value,
      };
    } catch (error) {
      this.logger.error(`查找意外快照数据失败: ${error.message}`);
      this.logger.error(error);
      return {
        batchIds: [],
        total: 0,
      };
    }
  }

  /**
   * 找到已经完成的维度
   * @param diligenceId
   */
  public async findSnapshotedDimensionByDiligenceId(diligenceId: number): Promise<string[]> {
    const childQuery: any = {
      has_child: {
        type: 'diligence',
        query: {
          bool: {
            must: [
              {
                term: { 'diligence.status': 1 },
              },
            ],
          },
        },
      },
    };
    childQuery.has_child.query.bool.must.push({
      term: { 'diligence.diligenceId': diligenceId },
    });
    // if (snapshotId?.length) {
    //   childQuery.has_child.query.bool.must.push({
    //     terms: { 'diligence.snapshotId': snapshotId },
    //   });
    // }
    const body = {
      query: {
        bool: {
          must: [childQuery],
        },
      },
      aggs: {
        dimension: {
          terms: {
            field: 'dimensionKey',
            size: 1000,
          },
        },
      },
    };
    const response: ApiResponse<SearchResponse<SnapshotEsDimensionDoc>> = await this.esClientRead.search({
      index: this.getReadIndexName(),
      size: 0,
      body,
    });
    return response.body.aggregations?.dimension?.buckets.map((item: EsAggsBucketItemPO) => item.key) || [];
  }
}
