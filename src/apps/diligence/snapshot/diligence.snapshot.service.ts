import { Injectable } from '@nestjs/common';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';
import { InjectRepository } from '@nestjs/typeorm';
import { DiligenceHistoryEntity } from 'libs/entities/DiligenceHistoryEntity';
import { Repository } from 'typeorm';
import { RabbitMQ } from '@kezhaozhao/message-queue';
import * as Bluebird from 'bluebird';
import { CreateSnapshotMessagePO, SnapshotDimensionMessagePO, SnapshotMessageBasePO } from 'libs/model/diligence/snapshot/SnapshotDimensionMessagePO';
import { OperationEnums } from 'libs/enums/diligence/OperationEnums';
import { DimensionHitResultPO } from 'libs/model/diligence/dimension/DimensionHitResultPO';
import { GetHitDetailsParamBase } from 'libs/model/diligence/req&res/GetHitDetailsParam';
import { flattenDimensionHitResultPO, flattenDimensionHitResultPOV2 } from '../../../libs/utils/diligence/dimension.utils';
import { ApiResponseStatusEnum } from '../../../libs/enums/ApiResponseStatusEnum';
import { DimensionTypeEnums } from '../../../libs/enums/diligence/DimensionTypeEnums';
import { difference, intersection, isArray, union } from 'lodash';
import { QueueService } from '../../../libs/config/queue.service';
import { SnapshotStatus } from '../../../libs/model/diligence/SnapshotDetail';
import { DimensionDetailService } from '../details/dimension.detail.service';
import { HitDetailsBaseResponse } from '../../../libs/model/diligence/details/response';
import { DiligenceSnapshotEsService } from './diligence.snapshot.es.service';
import Redlock from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { BatchResultEntity } from '../../../libs/entities/BatchResultEntity';
import { DimensionSnapshotException } from '../../../libs/exceptions/DimensionSnapshotException';
import { HitDetailsBaseQueryParams } from '../../../libs/model/diligence/details/request';
import { BatchConstants } from '../../batch/common/batch.constants';
import * as moment from 'moment';
// import { DiligenceAnalyzeTypeEnums } from '../../../libs/model/charts/DiligenceAnalyzeMessagePO';
import { BatchBusinessTypeEnums } from '../../../libs/enums/batch/BatchBusinessTypeEnums';
import { DiligenceHistoryService } from '../details/diligence.history.service';
import { SnapshotQueueTypeEnums } from './po/SnapshotQueueTypeEnums';
import { captureException } from '@sentry/node';
import { Cacheable } from '@type-cacheable/core';
import { BadParamsException, OssService } from '@kezhaozhao/qcc-utils';
import { ConfigService } from '../../../libs/config/config.service';
import * as uuidTime from 'uuid-time';
import { RiskScoreService } from '../evaluation/risk.score.service';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import { Redis } from 'ioredis';
import { SnapshotProgressLock } from '../../../libs/common/distributed_lock/SnapshotProgressLock';
import { DiligenceModifyService } from '../details/diligence.modify.service';
import { BatchEntity } from '../../../libs/entities/BatchEntity';

@Injectable()
export class DiligenceSnapshotService {
  public snapshotQueue: RabbitMQ;
  public snapshotBatchQueue: RabbitMQ;
  private logger: Logger = QccLogger.getLogger(DiligenceSnapshotService.name);
  private readonly redlock: Redlock;
  // public diligenceAnalyzeQueue: RabbitMQ;
  private readonly snapshotFolderName = 'diligence_snapshot';
  private readonly dataFormat = 'YYYY-MM-DD';
  private readonly redisClient: Redis;

  constructor(
    private readonly dimensionDetailService: DimensionDetailService,
    private readonly modelRiskService: RiskScoreService,
    private readonly queueService: QueueService,
    private readonly snapshotEsService: DiligenceSnapshotEsService,
    private readonly redisService: RedisService,
    private readonly diligenceModifyService: DiligenceModifyService,
    private readonly diligenceHistoryService: DiligenceHistoryService,
    private readonly configService: ConfigService,
    private ossService: OssService,
    @InjectRepository(DiligenceHistoryEntity) protected readonly diligenceHistoryRepo: Repository<DiligenceHistoryEntity>,
    @InjectRepository(BatchResultEntity) protected readonly batchResultRepo: Repository<BatchResultEntity>,
    @InjectRepository(BatchEntity) protected readonly batchRepo: Repository<BatchEntity>,
  ) {
    // this.diligenceAnalyzeQueue = this.queueService.diligenceAnalyzeQueue;
    this.redisClient = this.redisService.getClient();
    this.redlock = new Redlock([this.redisService.getClient()], {
      retryCount: 2, // retry only 2 times then fails
    });
    this.snapshotQueue = this.queueService.snapshotQueue;
    this.snapshotBatchQueue = this.queueService.snapshotBatchQueue;
    this.snapshotQueue.consume(this.getHandleFn(SnapshotQueueTypeEnums.Diligence), BatchConstants.Consumer.Snapshot);
    this.snapshotBatchQueue.consume(this.getHandleFn(SnapshotQueueTypeEnums.BatchDiligence), BatchConstants.Consumer.Snapshot);
  }

  getTargetQueue(type: SnapshotQueueTypeEnums) {
    return type === SnapshotQueueTypeEnums.Diligence ? this.snapshotQueue : this.snapshotBatchQueue;
  }

  getHandleFn(type: SnapshotQueueTypeEnums) {
    const fn = (msgPO: SnapshotMessageBasePO) => {
      return this.processSnapshotMessage(msgPO as SnapshotMessageBasePO, type);
    };
    return fn.bind(this);
  }

  /**
   * 处理快照相关的消息
   * Create： 开始生成快照，先按照子维度拆分消息
   * DimensionSnapshot：生成维度快照
   * RefreshBatchRelation: 批量任务完成之后，因为批量中可以使用了已经生成过快照的diligence，所以刷新batch关联的diligenceId
   * @param msg
   */
  @TraceLog({ throwError: true, spanName: 'snapshot/messageHandler' })
  async processSnapshotMessage(msg: SnapshotMessageBasePO, type?: SnapshotQueueTypeEnums): Promise<CreateSnapshotMessagePO> {
    try {
      const { orgId, operation } = msg;
      const batchIds = msg.batchIds;
      const passedOperation = operation as OperationEnums;
      const targetQueue = this.getTargetQueue(type);
      switch (passedOperation) {
        case OperationEnums.Create: {
          const { companyId, companyName, riskData, snapshotId, diligenceAt, diligenceId, batchIds } = msg as CreateSnapshotMessagePO;
          if (orgId && snapshotId && riskData) {
            if (!process.env?.JEST_WORKER_ID) {
              //非 jest 测试环境，加锁
              try {
                //确保指定时间内，不会重复生成快照
                await this.redlock.acquire([snapshotId, diligenceId + ''], 180000, { retryCount: 2 });
              } catch (e) {
                this.logger.error(`3分钟内 snapshotId=${snapshotId},diligenceId=${diligenceId} 已经被处理过`);
                return;
              }
            }

            const dimensionKeys = [];
            // const hitDimensions: DimensionHitResultPO[] = flattenDimensionHitResultPO(riskData.originalHits);
            const hitDimensions: DimensionHitResultPO[] = flattenDimensionHitResultPOV2(riskData.originalHits, riskData.modelType);
            // const hitDimensions =
            if (hitDimensions.length > 0) {
              const msgList: SnapshotDimensionMessagePO[] = hitDimensions.map((scorePO) => {
                dimensionKeys.push(scorePO.dimensionKey);
                const m: SnapshotDimensionMessagePO = {
                  orgId,
                  operation: OperationEnums.DimensionSnapshot,
                  scorePO,
                  companyId,
                  companyName,
                  snapshotId,
                  diligenceAt,
                  diligenceId,
                  batchIds,
                  strategyId: scorePO.strategyId,
                };
                return m;
              });
              await this.snapshotEsService.removeSnapshotDiligenceData({
                orgId,
                diligenceId,
                dimensionKeys,
              });
              await Bluebird.map(
                msgList,
                (m) => {
                  return targetQueue.sendMessageV2(m, {});
                },
                { concurrency: 5 },
              );
            } else {
              this.logger.warn(`没有发现命中维度，跳过快照生成,diligenceId=${diligenceId}，snapshotId=${snapshotId}, diligenceId=${diligenceId}`);
            }
          }
          break;
        }
        case OperationEnums.DimensionSnapshot: {
          const { companyId, companyName, snapshotId, diligenceAt, diligenceId, scorePO: dimHitResultPO, refill } = msg as SnapshotDimensionMessagePO;
          const dimensionHashKey = dimHitResultPO.dimensionKey + '_' + dimHitResultPO.strategyId;
          //补充各个维度详情
          // 关键项未命中也会出现在dimensionScoreDetails，需要过滤掉
          const originalHitCount = dimHitResultPO.totalHits;
          if (dimHitResultPO.totalHits <= 0) {
            return;
          }
          const dbDiligence = await this.diligenceHistoryService.getDiligenceHistoryById(diligenceId);
          if (!dbDiligence) {
            this.logger.error(`快照生成失败，找不到对应的diligence记录，diligenceId=${diligenceId}`);
            return;
          }

          if (!refill && dbDiligence.snapshotDetails.successHits?.includes(dimensionHashKey)) {
            this.logger.warn(`snapshot is finished, skip: diligenceId=${diligenceId},refill=${refill},snapshotId=${snapshotId} ,key=${dimensionHashKey}`);
            return;
          }
          const snapshotProgressLock = new SnapshotProgressLock(this.redisClient, snapshotId, 900);
          const finishedKeys = await snapshotProgressLock.getKeys();
          if (finishedKeys.length === 0) {
            //说明15分钟内是第一次生成快照，需要初始化进度锁, 这里不再从es中聚合，要从 diligence history 中获取， 因为 同一个维度在一个diligence中可能会出现多次，所以需要配合 strategyId去重

            // const esFinishedDimensionKeys = await this.snapshotEsService.findDimensionByDiligenceId(diligenceId);
            const finishedDimensionKeys = dbDiligence.snapshotDetails?.successHits;
            if (finishedDimensionKeys?.length > 0) {
              await snapshotProgressLock.addKeys(finishedDimensionKeys);
              finishedKeys.push(...finishedDimensionKeys);
            }
          }
          const exist = finishedKeys.includes(dimensionHashKey);
          if (!refill && exist) {
            this.logger.warn(`snapshot is finished, skip: diligenceId=${diligenceId},refill=${refill},snapshotId=${snapshotId} ,key=${dimensionHashKey}`);
            return;
          }

          if (refill) {
            this.logger.info(`refill snapshot,diligenceId=${diligenceId},refill=${refill},snapshotId=${snapshotId} ,key=${dimensionHashKey}`);
          }
          const hitDetailParam: GetHitDetailsParamBase = {
            key: dimHitResultPO.dimensionKey,
            orgId,
            strategyId: dimHitResultPO.strategyId,
            dimensionFilter: dimHitResultPO.dimensionFilter,
          };
          try {
            const start = Date.now();
            const [items, excludeEntities] = await Bluebird.all([
              this.fetchDimensionDetails(
                hitDetailParam,
                {
                  keyNo: companyId,
                  companyName,
                },
                snapshotId,
                true,
              ),
              this.diligenceModifyService.fetchAllExcludedIds(orgId, companyId, dimHitResultPO.dimensionKey as DimensionTypeEnums),
            ]);
            const newHitCount = items.length;
            const batchIds = msg.batchIds || [];
            const dbBatchIds = dbDiligence.batchEntities?.map((b) => b.batchId);
            await this.snapshotEsService.insertSnapshotData({
              snapshotId,
              diligenceId,
              diligenceAt,
              orgId,
              companyId,
              dimensionKey: dimHitResultPO.dimensionKey,
              items,
              batchId: union(batchIds, dbBatchIds),
              excludedDimensions: excludeEntities,
              strategyId: dimHitResultPO.strategyId,
            });
            const { allKeys: finishedKeys } = await snapshotProgressLock.addAndGetKeys([dimensionHashKey]);
            const dimensionHits = dbDiligence.details?.dimensionHitsUnique;
            if (intersection(finishedKeys, dimensionHits).length === dimensionHits.length) {
              await this.diligenceHistoryRepo.update(dbDiligence.id, {
                snapshotDetails: {
                  status: SnapshotStatus.SUCCESS,
                  successHits: dimensionHits,
                },
              });
              // await this.redisClient.del(cacheKey);
              await snapshotProgressLock.release();
            } else {
              await this.addSnapshotSuccessHit(snapshotId, dimensionHashKey);
            }
            if (newHitCount != originalHitCount) {
              await this.modelRiskService.refreshScoreDetails(diligenceId, [
                {
                  dimensionKey: dimHitResultPO.dimensionKey,
                  hitsCountChanged: newHitCount - originalHitCount,
                },
              ]);
            }
            if (refill) {
              //如果是重新补充快照，需要刷新关联的 年检分析结果
              // await this.diligenceAnalyzeHelper.reAnalyzeByRelatedDiligence(orgId, diligenceId);
              // await this.diligenceAnalyzeQueue.sendMessage({
              //   orgId,
              //   operationType: DiligenceAnalyzeTypeEnums.RefreshDiligence,
              //   data: {
              //     diligenceId,
              //   },
              // });
            }
            this.logger.info(
              `generate snapshot success ,diligenceId=${diligenceId},snapshotId=${snapshotId} ,key=${dimHitResultPO.dimensionKey} cost=${Date.now() - start}ms`,
            );
          } catch (e: any) {
            const message = `生成快照失败: diligenceId=${diligenceId} ,snapshotId=${snapshotId} ,key=${dimHitResultPO.dimensionKey}`;
            this.logger.error(message, e);
            this.logger.error(e);
            captureException(e, {
              extra: {
                message,
              },
            });
            const retryTimes = msg.retryTimes || 0;
            if (retryTimes > 1) {
              await this.updateSnapshotFail(snapshotId);
              this.logger.error(`snapshotId=${snapshotId} 重试次数超过最大值，放弃处理`);
              this.logger.error(new DimensionSnapshotException(companyId as DimensionTypeEnums, dimHitResultPO.dimensionKey, snapshotId));
            } else {
              msg.retryTimes = retryTimes + 1;
              await this.getTargetQueue(type).sendMessageV2(msg, {});
            }
          }
          break;
        }
        case OperationEnums.ReCreateByBatch: {
          // 根据batchId 检查是否有没生成快照的diligence，重新生成快照
          if (batchIds?.length) {
            await this.refreshSnapshotByBatch(batchIds[0], orgId);
          }
          break;
        }
        default: {
          this.logger.warn(`unknown operation ${operation}, skip`);
          break;
        }
      }
      return msg as any;
    } catch (e: any) {
      this.logger.error(e);
      throw e; // throw 出去，确保消息会再次被消费
    }
  }

  /**
   * 准备根据snapshotId开始准备快照数据
   * @param snapshotId
   * @param diligenceEntity 可选参数，如果传值了，就不再去查数据库了
   * @param bindToBatchId 绑定的batchId
   */
  @TraceLog({ throwError: true })
  public async prepareSnapshot(snapshotId: string, diligenceEntity?: DiligenceHistoryEntity, bindToBatchId?: number): Promise<string> {
    this.logger.debug(`begin to prepare data for snapshotId=${snapshotId}`);
    const diligenceHistory = diligenceEntity?.batchEntities ? diligenceEntity : await this.diligenceHistoryService.getDiligenceBySnapshotId(snapshotId);
    if (!diligenceHistory) {
      this.logger.error(`can not find diligence when take snapshot with snapshotId=${snapshotId}`);
      return null;
    }
    const status = diligenceHistory?.details?.dimensionHits?.length ? SnapshotStatus.PROCESSING : SnapshotStatus.SUCCESS;
    await this.diligenceHistoryRepo.update(
      { snapshotId },
      {
        snapshotDetails: {
          status,
          successHits: [],
        },
      },
    );
    if (!diligenceHistory?.details?.dimensionHits?.length) {
      this.logger.info(`diligence no hits snapshot with snapshotId=${snapshotId}`);
      return diligenceHistory.snapshotId;
    }

    const batchIds = diligenceHistory.batchEntities?.filter((b) => [BatchBusinessTypeEnums.Diligence_ID].includes(b.businessType))?.map((b) => b.batchId) || [];
    if (bindToBatchId && !batchIds.includes(bindToBatchId)) {
      batchIds.push(bindToBatchId);
    }
    const snapshotMsg: CreateSnapshotMessagePO = {
      orgId: diligenceHistory.orgId,
      operation: OperationEnums.Create,
      riskData: diligenceHistory.details,
      companyId: diligenceHistory.companyId,
      companyName: diligenceHistory.name,
      snapshotId: diligenceHistory.snapshotId,
      diligenceId: diligenceHistory.id,
      diligenceAt: diligenceHistory.createDate,
      batchIds: batchIds,
    };
    await this.getTargetQueue(bindToBatchId ? SnapshotQueueTypeEnums.BatchDiligence : SnapshotQueueTypeEnums.Diligence).sendMessageV2(snapshotMsg, {
      retries: 3,
      breakTrace: false,
      traceTags: [
        {
          key: 'snapshotId',
          val: snapshotId,
          overridable: true,
        },
        {
          key: 'companyId',
          val: diligenceHistory.companyId,
          overridable: true,
        },
      ],
    });
    return diligenceHistory.snapshotId;
  }

  public async prepareBatchSnapshot(snapshotId: string, bindToBatchId: number) {
    return this.prepareSnapshot(snapshotId, null, bindToBatchId);
  }

  public async addSnapshotSuccessHit(snapshotId: string, key: string) {
    await this.diligenceHistoryRepo.query(
      `update due_diligence
         set snapshot_details = JSON_ARRAY_APPEND(snapshot_details, '$.successHits', ?)
         where snapshot_id = ?
           and JSON_CONTAINS(snapshot_details, ?, '$.successHits') = 0`,
      [key, snapshotId, `"${key}"`],
    );
  }

  public async updateSnapshotFail(snapshotId: string) {
    await this.diligenceHistoryRepo.query(
      `update due_diligence
         set snapshot_details = JSON_SET(snapshot_details, '$.status', 2)
         where snapshot_id = ?`,
      [snapshotId],
    );
  }

  /**
   * 针对快照，获取维度列表
   * @param hitDetailParam
   * @param params
   * @param snapshotId TODO 这里是不是不需要传 snapshotId 过去，这里本来就要获取维度的列表然后生成快照用的
   * @param fullSnapshot 是否需要生成全量快照数据
   */
  public async fetchDimensionDetails(
    hitDetailParam: GetHitDetailsParamBase,
    params: HitDetailsBaseQueryParams,
    snapshotId: string,
    fullSnapshot = true,
    retrieveForSnapshot = true,
  ) {
    this.logger.debug(`fetching details for dimension=${hitDetailParam.key}, snapshotId=${snapshotId}`);
    const pagination = {
      pageSize: 299,
      pageIndex: 1,
    };
    const items: any[] = [];
    do {
      //TODO 这里是不是不需要传 snapshotId 过去，这里本来就要获取维度的列表然后生成快照用的
      const res: HitDetailsBaseResponse = await this.dimensionDetailService.getHitsDetails(
        hitDetailParam,
        { ...params, ...pagination, snapshotId },
        retrieveForSnapshot,
      );
      if (res?.status === ApiResponseStatusEnum.FAILED) {
        this.logger.warn(res);
        break;
        // throw new GetDimensionDetailsException(hitDetailParam.key, params.keyNo, snapshotId);
      }
      Array.prototype.push.apply(items, res.Result || []);
      if (!res.Result.length || !fullSnapshot || (fullSnapshot && items.length >= res.Paging.TotalRecords) || items.length > 9000) {
        if (items.length > 9000) {
          captureException(`items.length > 9000, snapshotId=${snapshotId}, dimensionKey=${hitDetailParam.key}, keyNo=${params.keyNo},  orgId=${params.orgId}`);
          this.logger.error(`items.length > 9000, snapshotId=${snapshotId}, dimensionKey=${hitDetailParam.key}, keyNo=${params.keyNo},  orgId=${params.orgId}`);
        }
        break;
      }
      pagination.pageIndex++;
    } while (true);

    this.logger.debug(
      `fetching details for dimension=${hitDetailParam.key}, snapshotId=${snapshotId} items.length : ${items.length},dimension=${hitDetailParam.key} `,
    );

    return items;
  }

  /**
   * 强制刷新指定维度的快照数据
   * @param snapshotId
   * @param dimensionKeys
   */
  @TraceLog({ throwError: true })
  public async refreshSnapshot(diligenceId: number, dimensionKey?: DimensionTypeEnums) {
    // try {
    //   await this.redlock.acquire([diligenceId + '', dimensionKeys], 60000, { retryCount: 2 });
    // } catch (e) {
    //   this.logger.error(`1分钟内diligenceId=${diligenceId} 已经被处理过`);
    //   return;
    // }
    const diligence = await this.diligenceHistoryService.getDiligenceHistoryById(diligenceId);

    if (!diligence) {
      this.logger.error(`can not find diligence when refresh snapshot with diligenceId=${diligenceId}`);
      return;
    }
    if (!dimensionKey) {
      //如果不指定key，那么重新创建整个快照
      return this.prepareSnapshot(diligence.snapshotId, diligence);
    } else {
      const list = flattenDimensionHitResultPO(diligence.details.originalHits);
      const scorePO = list.find((s) => s.dimensionKey === dimensionKey);
      if (!scorePO) {
        const msg = `can not find scorePo when refresh snapshot with diligenceId=${diligenceId}, dimensionKey=${dimensionKey}`;
        this.logger.error(msg);
        throw new BadParamsException({ message: msg, code: 400 });
      }
      const { orgId, companyId, snapshotId, name: companyName, createDate: diligenceAt } = diligence;
      const batchIds = diligence.batchEntities?.map((b) => b.batchId);
      const m: SnapshotDimensionMessagePO = {
        orgId,
        operation: OperationEnums.DimensionSnapshot,
        scorePO,
        companyId,
        companyName,
        snapshotId,
        diligenceAt,
        diligenceId,
        batchIds,
      };
      await this.snapshotEsService.removeSnapshotDiligenceData({
        orgId,
        diligenceId,
        dimensionKeys: [scorePO.dimensionKey],
      });
      return this.processSnapshotMessage(m, SnapshotQueueTypeEnums.Diligence);
    }
  }

  /**
   * 检查batchId对应的快照是否已经生成并正常绑定关系(正常应该已经生成，但是核batchId 的绑定关系可能会有问题，所以这里做个保护)
   * 同时检查还没有生成快照的数据，进行刷新
   * @param batchId
   */
  public async refreshSnapshotByBatch(batchId: number, orgId: number) {
    const pageSize = 400;
    let pageIndex = 1;
    const result = {
      refreshedRelation: 0,
      createSnapshot: 0,
    };
    do {
      const qb = this.diligenceHistoryRepo
        .createQueryBuilder('diligence')
        .where('diligence.orgId = :orgId', { orgId })
        .leftJoin('diligence.batchEntities', 'batchEntities')
        .andWhere('batchEntities.batchId = :batchId', { batchId })
        .andWhere('batchEntities.businessType in (:...types)', {
          types: [BatchBusinessTypeEnums.Diligence_ID],
        })
        .andWhere(" (diligence.details is not null AND JSON_LENGTH(details, '$.dimensionHits') > 0) ")
        .take(pageSize)
        .skip((pageIndex - 1) * pageSize);
      const diligenceEntities = await qb.getMany();
      if (diligenceEntities.length === 0) {
        break;
      }
      pageIndex++;
      const diligenceIds = diligenceEntities.map((e) => e.id);
      const existingSnapshots = await this.snapshotEsService.findExistSnapshotByDiligenceIds(diligenceIds);
      // 应该存在快照，但是没有数据 需要重新生成数据
      const nonSnapshotDiligenceIds = difference(diligenceIds, existingSnapshots);
      if (nonSnapshotDiligenceIds.length > 0) {
        const diligenceHistoryList = diligenceEntities.filter((d) => nonSnapshotDiligenceIds.includes(d.id));
        result.createSnapshot += nonSnapshotDiligenceIds.length;
        await Bluebird.map(
          diligenceHistoryList,
          (diligenceHistory: DiligenceHistoryEntity) => {
            return this.prepareBatchSnapshot(diligenceHistory.snapshotId, batchId);
          },
          { concurrency: 10 },
        );
      }
      // 存在快照，刷新一下绑定关系
      if (existingSnapshots?.length) {
        await this.snapshotEsService.refreshBatchDiligenceRelation(existingSnapshots, batchId);
      }
    } while (true);
    return result;
  }
}
