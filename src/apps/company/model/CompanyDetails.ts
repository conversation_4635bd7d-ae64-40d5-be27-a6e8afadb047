/* eslint-disable @typescript-eslint/naming-convention */
import { ApiProperty } from '@nestjs/swagger';

export class CompanyDetails {
  @ApiProperty({ type: String, description: '是分支机构' })
  IsBranch: number;

  @ApiProperty({ type: String, description: '地址' })
  Address: string;

  @ApiProperty({ type: String, description: '统一社会信用代码' })
  CreditCode: string;

  @ApiProperty({ isArray: true, description: '历史电话' })
  HisTelList: object;

  @ApiProperty({ type: String, description: 'keyno' })
  KeyNo: string;

  @ApiProperty({ description: '上次年报地址 [{Address: "", Year: ""}]' })
  LatestAnnualReportAddrInfo: object;

  @ApiProperty({ type: String, description: '公司名称' })
  Name: string;

  @ApiProperty({ isArray: true, description: '相同电话地址' })
  SameTelAddressList: object;

  @ApiProperty({ description: '法人信息' })
  Oper: any;

  @ApiProperty({ type: String, description: '实缴资本带单位 398944.0192万元人民币' })
  RecCap: string;

  @ApiProperty({ type: String, description: '注册资本带单位，398944.0192万元人民币' })
  RegistCapi: string;

  @ApiProperty({ type: String, description: '注册资本，398944 外币换算人民币后的金额 ' })
  Registcapiamount: number;

  @ApiProperty({ type: String, description: '经营状态' })
  ShortStatus: string;

  @ApiProperty({ type: String, description: '员工人数' })
  Staffs: string;

  @ApiProperty({ type: String, description: '经营期限开始日期' })
  StartDate: number;

  @ApiProperty({ type: String, description: '纳税人识别号' })
  TaxNo: string;

  @ApiProperty({ type: String, description: '更多联系方式' })
  VTList: string;

  @ApiProperty({ type: String, description: '企查查行业' })
  QccIndustry: object;

  @ApiProperty({ type: String, description: '企查查行业' })
  QccEmergingIndustry: object;

  @ApiProperty({ type: Number, description: '经营期限开始日期' })
  TermStart: number;

  @ApiProperty({ type: Number, description: '经营期限截止日期' })
  TeamEnd: number;

  @ApiProperty({ type: String, description: '企业年收入' })
  CompanyRevenue: any;

  @ApiProperty({ type: String, description: '资产规模' })
  Scale: any;

  @ApiProperty({ type: String, description: '经营范围' })
  Scope: string;

  @ApiProperty({ description: '联系方式' })
  ContactInfo: object;

  @ApiProperty({ type: String, description: '变更信息' })
  ChangeDiffInfo: any;

  @ApiProperty({ type: String, description: 'TagsInfoV2信息' })
  TagsInfoV2: any;

  @ApiProperty({ type: String, description: 'tag信息' })
  Tags: any[];

  @ApiProperty({ type: String, description: '纳税人资质类型' })
  TaxpayerType: string;

  CountInfo: any;

  EconKind: string;

  @ApiProperty({ type: String, description: '公司类型' })
  Type: string;

  standardCode: string[];

  @ApiProperty({ type: String, description: '国标行业' })
  IndustryV3: any;
}

export class CoyInfo {
  @ApiProperty({ type: String, description: '公司KeyNo' })
  KeyNo: string;

  @ApiProperty({ type: String, description: '公司名称' })
  Name: string;

  @ApiProperty({ type: String, description: '公司类型' })
  Type: string;
}
