import { CompanyClient, Contact, KysCompanyResponseDetails, KysCompanySearchRequest } from '@kezhaozhao/company-search-api';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { ESResponse } from '@kezhaozhao/search-utils';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Logger } from 'log4js';
import { ConfigService } from 'libs/config/config.service';
import { HttpUtilsService } from 'libs/config/httputils.service';
import { Cacheable, useIoRedisAdapter } from 'type-cacheable';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { AreaMapping, ESDetailResopne, IndustryMapping } from '@kezhaozhao/qcc-model';
import { RoverExceptions } from 'libs/exceptions/exceptionConstants';
import * as _ from 'lodash';
import { chunk, compact, difference, find, flatten, intersection, pick, union } from 'lodash';
import { CompanyDetails } from './model/CompanyDetails';
import { SearchMultiSelectionRequest } from './model/SearchMultiSelectionRequest';
import { CompaniesWithFreeTextRequest, MultiMatchCompanyRequest, SupplierCustomerWithFreeTextRequest } from './model/MultiMatchCompanyRequest';
import { CoyInfoHighlight } from './model/CoyInfoHighlight';
import * as Bluebird from 'bluebird';
import { getRevenue, isOrganism } from './utils';
import { CompanyEntity } from '../../libs/entities/CompanyEntity';
import * as moment from 'moment/moment';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlatformUser } from '../../libs/model/common';
import { SearchCertificationRequest } from './model/SearchCertificationRequest';
import { SearchCreditRequest } from './model/SearchCreditRequest';
import { CreditRateResult } from './model/CreditRateResult';
import { DATE_FORMAT, ForbiddenStandardCode } from '../../libs/constants/common';
import { CompanyBusinessInfo, CompanyFinanceInfoResponse } from './model/CompanyBusinessInfo';
import { BadParamsException } from '@kezhaozhao/qcc-utils';
import { CompanyBasicInfo } from '../../libs/model/company/CompanyBasicInfo';
import { CompanyDetailService } from './company-detail.service';
import { EconType, EnterpriseType, StatusCode } from '../../libs/constants/company.constants';
import { Client, RequestParams } from '@elastic/elasticsearch';

enum CompanyDetailCommonList {
  companyScale = '32',
}

@Injectable()
export class CompanySearchService {
  private readonly logger: Logger = QccLogger.getLogger(CompanySearchService.name);
  private readonly companySearchClient: CompanyClient;
  private readonly creditFinanceClient: Client;
  private readonly creditFinanceIndexName: string;

  constructor(
    @InjectRepository(CompanyEntity) private readonly companyRepo: Repository<CompanyEntity>,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly httpUtils: HttpUtilsService,
    private readonly detailService: CompanyDetailService,
  ) {
    this.companySearchClient = new CompanyClient({
      server: this.configService.kzzServer.companySearchApi,
      requestFrom: process.env.PROJECT_NAME || 'qcc-dd-platform',
    });
    this.creditFinanceClient = new Client({
      nodes: configService.esConfig.finance.nodes,
      ssl: { rejectUnauthorized: false },
    });
    this.creditFinanceIndexName = configService.esConfig.finance.indexName;
    //@ts-ignore
    useIoRedisAdapter(this.redisService.getClient());
  }

  /**
   *
   * @param companyId keyno
   * @returns
   */
  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsKys(companyId: string): Promise<ESDetailResopne<KysCompanyResponseDetails>> {
    return await this.companySearchClient.kysCompanyDetails(companyId);
  }

  /**
   * 批量获取企业信息
   * @param companyIds
   */
  // public async companyDetailsKysByCompanyIds(companyIds: string[]): Promise<ESDetailResopne<KysCompanyResponseDetails>[]> {
  //   return Bluebird.map(
  //     companyIds,
  //     async (companyId) => {
  //       return await this.companySearchClient.kysCompanyDetails(companyId);
  //     },
  //     { concurrency: 5 },
  //   );
  // }

  // public async batchCompanyDetailsQcc(companyIds: string[]) {
  //   const results = await Bluebird.map(
  //     companyIds,
  //     async (companyId) => {
  //       return await this.companyDetailsQcc(companyId);
  //     },
  //     { concurrency: 5 },
  //   );
  //   return results;
  // }

  @Cacheable({ ttlSeconds: 600 })
  public async companyDetailsQcc(
    companyId: string,
    fields = [
      'IsBranch',
      'Address',
      'CreditCode',
      'HisTelList',
      'KeyNo',
      'LatestAnnualReportAddrInfo',
      'Name',
      'SameTelAddressList',
      'Oper',
      'MultipleOper',
      'RecCap',
      'RegistCapi',
      'ShortStatus',
      'Staffs',
      'StartDate',
      'TaxNo',
      'VTList',
      'QccIndustry',
      'TermStart',
      'TeamEnd',
      'Scope',
      'Scale',
      'ContactInfo',
      'CompanyRevenue',
      'IndustryV3',
      'TagsInfoV2',
      'OriginalName',
      'CountInfo',
      'EconKind',
      'Type',
      'CommonList',
      'CompanyStatus',
      'Tags',
      'TaxpayerType',
    ],
  ): Promise<CompanyDetails | null> {
    if (!companyId) {
      throw new BadParamsException(RoverExceptions.Diligence.Detail.NeedCompanyId);
    }
    try {
      if (isOrganism(companyId)) {
        //社会组织
        return this.getOrganismDetail(companyId);
      }
      const data = await this.httpUtils.getRequest(this.configService.dataServer.companyDetail, {
        keyNo: companyId,
      });

      const result: CompanyDetails = Object.assign(new CompanyDetails(), pick(data.Result, fields));
      const financeData = await this.doGetCompanyFinance(companyId, data);
      if (financeData) {
        result['assetsTotal'] = financeData.assetsTotal;
        result['assetsTotalSource'] = financeData.assetsTotalSource;
        result['netProfit'] = financeData.netProfit;
        result['netProfitSource'] = financeData.netProfitSource;
        result['operIncTotal'] = financeData.operIncTotal;
        result['operIncTotalSource'] = financeData.operIncTotalSource;
      }
      result['Tags'] = data.Result?.Tags;
      result['TagsInfo'] = data.Result?.TagsInfoV2?.find((element) => element?.Type == 302);
      if (data.Result?.CommonList) {
        // 注册资本金额(外币换算人民币后的金额)
        result['Registcapiamount'] = data.Result?.CommonList?.find((element) => element?.Key == 19);
        //
        result['CommonList'] = data.Result?.CommonList?.filter((element) => element?.Key == 40);
        const standard_code = data.Result.CommonList.find((e) => e && e.Key == 52);
        if (standard_code) {
          const organizationalCode: string[] = compact(JSON.parse(standard_code.Value)?.organizational_code?.split(',')) || [];
          const esCode: string[] = compact(JSON.parse(standard_code.Value)?.es_code?.split(',')) || [];
          result.standardCode = union(organizationalCode, esCode);
        }
      }
      return result;
    } catch (e) {
      this.logger.error(`companyDetailsQcc() err:` + e.message);
      this.logger.error(e);
      return null;
    }
  }

  /**
   * 获取公司logo
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getLogoByKeyNos(keyNos: string[]) {
    try {
      return await this.httpUtils.postRequest(this.configService.dataServer.getLogoByKeyNos, { keyNos });
    } catch (e) {
      this.logger.error(`http Post getLogoByKeyNos err:`, e);
      return null;
    }
  }

  /**
   * 获取社会组织详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async organismDetailsQcc(companyId: string) {
    return await this.detailService.organismDetailsQcc(companyId);
  }

  @Cacheable({ ttlSeconds: 60 })
  public async getCompanyCertificationSummary(keyNo: string, startYear = '', searchKey = undefined) {
    try {
      const record = {
        keyNo,
        startYear,
        searchKey,
        certificateStatus: '1',
        isNew: true,
        isNewAgg: true,
      };
      const resp = await this.detailService.getCompanyCertificationSummary(record);
      if (resp?.Result?.length) {
        // 把质量管理体系认证、环境管理体系认证、安全生产许可证排在前面
        resp?.Result.sort((a, b) => {
          if (a?.CertificateCode == b?.CertificateCode) {
            return 0;
          }
          if (a?.CertificateCode == '*********') {
            return -1;
          }
          if (b?.CertificateCode == '*********') {
            return 1;
          }
          if (a?.CertificateCode == '*********') {
            return -1;
          }
          if (b?.CertificateCode == '*********') {
            return 1;
          }
          if (a?.CertificateCode == '011') {
            return -1;
          }
          if (b?.CertificateCode == '011') {
            return 1;
          }
          return 0;
        });
      }
      return resp;
    } catch (e) {
      this.logger.error(`http getCompanyCertificationSummary err:`, e);
      return null;
    }
  }

  public async getCompanyCertificationList(body: SearchCertificationRequest) {
    try {
      const param = {
        isNew: true,
        isNewAgg: true,
        certificateStatus: '1',
      };
      if (body) {
        Object.assign(param, body);
      }
      const resp = await this.detailService.getCompanyCertificationList(param);
      if (resp) {
        if (!resp.Paging) {
          resp.Paging = {
            PageIndex: body.pageIndex,
            PageSize: body.pageSize,
            TotalRecords: 0,
          };
        }
        if (!resp.Result) {
          resp.Result = [];
        }
        if (!resp.GroupItems) {
          resp.GroupItems = [];
        }
      }
      return resp;
    } catch (e) {
      this.logger.error(`http getCompanyCertificationList err:`, e);
      return null;
    }
  }

  @Cacheable({ ttlSeconds: 60 })
  public async getCompanyCertificationDetail(id: string) {
    return await this.detailService.getCompanyCertificationDetail(id);
  }

  @Cacheable({ ttlSeconds: 60 })
  public async getCompanyCreditSummary(keyNo: string) {
    return await this.detailService.getCompanyCreditSummary(keyNo);
  }

  public async getCompanyCreditList(body: SearchCreditRequest) {
    try {
      return await this.detailService.getCompanyCreditList(body);
    } catch (e) {
      this.logger.error(`http getCompanyCreditList err:`, e);
      return null;
    }
  }

  /**
   * 获取社会组织详情(返回结构改为CompanyDetails)
   * @param companyId
   * @private
   */
  private async getOrganismDetail(companyId: string): Promise<CompanyDetails> {
    try {
      const [qccDetail, kysDetail] = await Bluebird.all([this.organismDetailsQcc(companyId), this.getCompanyDetails(companyId)]);
      const djInfo = qccDetail?.Result?.DJInfo;
      const result: CompanyDetails = Object.assign(new CompanyDetails(), {
        Name: djInfo?.name,
        Address: djInfo?.address,
        CreditCode: djInfo?.creditCode,
        Oper: djInfo?.Oper,
        RegistCapi: djInfo?.registCapi,
        StartDate: djInfo?.startDate,
        VTList: qccDetail?.Result?.VTList,
        ContactInfo: qccDetail?.Result?.ContactInfo,
        ChangeDiffInfo: qccDetail?.Result?.ChangeDiffInfo,
        ShortStatus: djInfo?.status,
        TagsInfoV2: qccDetail?.Result?.TagsInfoV2,
        Type: '0', //对社会组织特殊处理企业类型,
        standardCode: ['001005'],
      });

      result['CommonList'] = qccDetail?.Result?.CommonList;
      result['TagsInfo'] = qccDetail?.Result?.TagsInfoV2;
      return result;
    } catch (e) {
      this.logger.error(`http Get Organism/GetDetailV2 err:`, e);
      return null;
    }
  }

  /**
   * C端高级搜索接口
   * @param query
   * @returns
   */
  // @Cacheable({ ttlSeconds: 600 })
  public async companySearchForQcc(query: SearchMultiSelectionRequest) {
    query.searchKey = query?.searchKey?.trim()?.substring(0, 100);
    // const { currentOrg: orgId, userId } = currentUser;
    if (!query?.searchKey?.trim()) {
      throw new BadRequestException(RoverExceptions.BadParams.Company.SearchKeyNotFound);
    }
    const { searchKey, searchIndex, pageIndex, pageSize, sortField, isSortAsc, includeFields, filter } = query;
    const requestData = {
      searchKey,
      searchIndex: 'default',
      //  0: '大陆企业', 1: '社会组织', 11: '事业单位', 12: '律师事务所',
      filter: JSON.stringify(filter),
      sortField,
      isSortAsc,
      pageIndex,
      pageSize,
    };

    if (searchIndex?.length) {
      const searchKey = {};
      query.searchIndex.forEach((index) => {
        searchKey[index] = query.searchKey;
      });
      requestData.searchIndex = 'multicondition';
      requestData.searchKey = JSON.stringify(searchKey);
    }
    try {
      const data = await this.detailService.searchMultiSelection(requestData);
      if (data?.Status == 200) {
        let Result = data.Result;

        if (data?.Result?.length) {
          Result = await Bluebird.map(
            data?.Result,
            (company) => {
              const hitReason = company['HitReasons'].find((r) => r.Field == '公司名称');
              if (hitReason) {
                company['HitReason'] = { Field: hitReason.Field, Value: hitReason.Value };
              }
              if (includeFields?.length) {
                return pick(company, includeFields);
              }
              return company;
            },
            { concurrency: 1 },
          );
        }

        return { Status: data.Status, Paging: data.Paging, Result };
      }
      return { Status: data.Status, Paging: {}, Result: {} };
    } catch (e) {
      this.logger.error(`searchMultiSelection err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  @Cacheable({ ttlSeconds: 600 })
  public async companySearchForKys(requestData: KysCompanySearchRequest): Promise<ESResponse<KysCompanyResponseDetails>> {
    return this.companySearchClient.kysSearch(requestData);
  }

  /**
   * 查询金融机构企业
   * @param fncList 金融机构类型
   * @param shortStatusCodes 企业登记状态
   */
  @Cacheable({ ttlSeconds: 6000 })
  public async searchFNCCompany(fncList: string[], shortStatusCodes: string[]): Promise<KysCompanyResponseDetails[]> {
    if (!fncList?.length || !shortStatusCodes.length) {
      return [];
    }
    const requestParam = Object.assign(new KysCompanySearchRequest(), {
      includeFields: ['id'],
      filter: { termsFlag: fncList, sc: shortStatusCodes },
      pageSize: 200,
      pageIndex: 1,
    });
    const limited = 3000;
    let totalFetched = 0;
    const resultItems = [];
    do {
      const res1 = await this.companySearchForKys(requestParam);
      if (res1.Result?.length) {
        totalFetched += res1.Result.length;
        resultItems.push(...res1.Result);
      }
      if (!res1.Result?.length || res1.Result.length < requestParam.pageSize || totalFetched >= limited) {
        break;
      }
      requestParam.pageIndex++;
    } while (true);
    return resultItems;
  }

  /**
   * 搜索工商信息
   * @param ids keyNo
   * @param includeFields 返回 id（keyNo）、creditcode（信用代码）
   */
  public async searchCompanyDetail(ids: string[], includeFields = ['id', 'creditcode']) {
    const details: KysCompanyResponseDetails[] = [];
    if (!ids?.length) {
      return details;
    }
    const chunks = chunk(ids, 100);
    await Bluebird.map(
      chunks,
      async (chunk) => {
        const kysDetails = await this.companySearchForKys(
          Object.assign(new KysCompanySearchRequest(), {
            includeFields,
            pageIndex: 1,
            pageSize: chunk.length,
            filter: { ids: chunk },
          }),
        );
        kysDetails?.Result?.forEach((companyDetail) => {
          let commonList: any[];
          if (typeof companyDetail.commonlist === 'string') {
            try {
              commonList = JSON.parse(companyDetail.commonlist);
            } catch (error) {
              commonList = [];
            }
          }
          // key:32 企业规模, key:36 员工人数
          const scaleInfo = find(commonList, { k: CompanyDetailCommonList.companyScale });
          companyDetail['scale'] = scaleInfo?.v; //企业规模
        });
        details.push(...kysDetails.Result);
      },
      { concurrency: 5 },
    );
    return details;
  }

  /**
   * 获取企业详情<companyId,CompanyBusinessInfo> Map
   * @param companyList
   */
  public async getCompanyBusinessInfoMap<R1 extends CompanyBasicInfo>(companyList: R1[]): Promise<Map<string, CompanyBusinessInfo>> {
    const companyDetailMap: Map<string, CompanyBusinessInfo> = new Map<string, CompanyBusinessInfo>();
    if (!companyList?.length) {
      return companyDetailMap;
    }
    const companyIds = companyList.map((d) => d.companyId);
    //需要查公司详情
    const companyDetails = await this.searchCompanyDetail(companyIds, [
      'id',
      'creditcode',
      'address',
      'insuredcount',
      'creditcode',
      'commonlist',
      'econkind',
      'yysramount',
      'registcapi',
      'startdatecode',
      'industry',
      'areacode',
      'statuscode',
      'credit_score',
      't_type',
      'opername', // 法人名称
      'province', // 省份
    ]);
    const detailMap: Map<string, KysCompanyResponseDetails> = new Map<string, KysCompanyResponseDetails>();
    companyDetails.forEach((x) => detailMap.set(x.id, x));
    companyList.forEach((cur) => {
      const detail: KysCompanyResponseDetails = detailMap.get(cur.companyId);
      const acc: CompanyBusinessInfo = new CompanyBusinessInfo();
      if (detail) {
        // const text = detail?.commonlist?.toString();
        // const commonList = text ? JSON.parse(text) : [];
        const commonList = detail?.commonlist || [];
        acc.companyId = cur.companyId;
        acc.companyName = cur.companyName;
        acc.creditcode = detail?.creditcode || '-';
        acc.registrationStatus = StatusCode[detail?.statuscode] || '-';
        acc.scale = detail?.['scale'] ? detail?.['scale'] : commonList?.find((c) => c.k == '32')?.v || '-';
        acc.address = detail?.address || '-';
        acc.insuredcount = commonList?.find((c) => c.k == '57') ? JSON.parse(commonList.find((c) => c.k == '57').v).D : '-';
        //营业收入原数据中金额为万元，需要转换为元
        acc.companyRevenue = getRevenue(commonList, '48', 1, 4);
        acc.econkind = detail?.econkind || '-';
        acc.registcapi = detail?.registcapi || '-';
        acc.startDateCode = detail?.startdatecode ? moment(detail?.startdatecode, moment.ISO_8601).format(DATE_FORMAT) : '-';
        acc.industry = IndustryMapping[detail?.industry] || '-';
        const areacode = detail?.areacode;
        // areacode.map((area) => AreaMapping[area]).join('/');
        acc.area =
          [AreaMapping[detail?.province], AreaMapping[areacode?.[0]], AreaMapping[areacode?.[1]], AreaMapping[areacode?.[2]]].filter((d) => d).join('/') || '-';
        acc.type = detail?.econkind || '-';
        acc.t_type = detail?.t_type || '-';
        acc.opername = detail?.opername || '-';
      }
      // cur.companyDetail = acc;
      companyDetailMap.set(cur.companyId, acc);
    });
    return companyDetailMap;
  }

  // @Cacheable({ ttlSeconds: 600 })
  // public async getGlossaryInfo(glossaryId: string) {
  //   if (!glossaryId) {
  //     throw new BadRequestException(RoverExceptions.BadParams.Common);
  //   }
  //   const url = `${this.configService.proxyServer.userService}/extra/getGlossaryInfo`;
  //   try {
  //     return await this.httpUtils.postRequest(url, { glossaryId });
  //   } catch (e) {
  //     this.logger.error(`http Post ${url} err:`, e);
  //     return '';
  //   }
  // }

  /**
   * kys 获取 company详情
   * @param companyId
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getCompanyDetails(companyId: string): Promise<ESDetailResopne<KysCompanyResponseDetails>> {
    return this.companySearchClient.kysCompanyDetails(companyId);
  }

  /**
   * 获取公司的联系方式列表
   * @param companyId
   * @returns
   */
  @Cacheable({ ttlSeconds: 600 })
  public async getContact(companyId: string): Promise<Contact> {
    return this.companySearchClient.kysCompanyContact(companyId);
  }

  /**
   * 校验工商信息
   * @param names 公司完整企业名称name/统一社会信用代码creditcode/注册号regno 数组
   * @param includeFields
   * @param allowType 3:香港企业，4:机关单位，5：台湾企业
   * @param supportOverSeas
   * @returns
   */
  public async matchCompanyInfo(
    names: string[],
    includeFields = ['id', 'name', 'creditcode', 'regno'],
    allowType = ['0', '1', '11', '12'],
    supportOverSeas = false,
  ) {
    try {
      // 增加返回企业类型
      includeFields.push('t_type');
      const unsupported = [];
      const matchedNames: string[] = [];
      //调用接口匹配，并返回结果，找到names中没有匹配到的
      let unmatchedNames: string[] = names;
      // 匹配到的公司信息
      let matchedCompanyInfos: KysCompanyResponseDetails[] = [];
      if (names.length) {
        // const result = coyRes.Result;
        const result = await this.companySearchClient.kysExactMatch({
          searchKey: names,
          searchFields: ['name', 'creditcode', 'regno'],
          includeFields: includeFields,
          searchType: undefined,
        });
        if (result?.length) {
          matchedCompanyInfos = result;
          matchedCompanyInfos.forEach((e) => {
            if (names.includes(e['name_tra'])) {
              e.name = e['name_tra'];
            }
            if (allowType.includes(e.t_type)) {
              matchedNames.push(e.name);
            } else {
              unsupported.push(e.name);
            }
          });
          // 未命中完整企业名称name
          unmatchedNames = difference(names, matchedNames, unsupported);
          const matchedCreditCode = matchedCompanyInfos.map((e) => e.creditcode);
          // 未命中统一社会信用代码creditcode
          unmatchedNames = difference(unmatchedNames, matchedCreditCode);
          const matchedRegno = matchedCompanyInfos.map((e) => e.regno);
          // 未命中注册号regno
          unmatchedNames = difference(unmatchedNames, matchedRegno);
        }
        // 剩余未匹配的再进行曾用名的匹配
        if (unmatchedNames.length) {
          const resultOrginalName = await this.companySearchClient.kysExactMatch({
            searchKey: unmatchedNames,
            searchFields: ['originalname'],
            includeFields: includeFields,
            searchType: undefined,
          });
          if (resultOrginalName.length) {
            matchedCompanyInfos.push(...resultOrginalName);
            const matchedOriginalName = compact(flatten(resultOrginalName.map((e) => e.originalname)));
            // 未命中曾用名originalname
            unmatchedNames = difference(unmatchedNames, matchedOriginalName);
            matchedNames.push(...matchedOriginalName);
          }
        }
        if (unmatchedNames.length && supportOverSeas) {
          // 支持海外类型
          // @ts-ignore
          const overseas: KysCompanyResponseDetails[] = compact(
            await Bluebird.map(
              unmatchedNames,
              async (companyName) => {
                const query: SearchMultiSelectionRequest = Object.assign(new SearchCertificationRequest(), { searchKey: companyName });
                const data = await this.companySearchForQcc(query);
                let result;
                if (data?.Result?.length) {
                  result = find(data?.Result, (companyInfo) => {
                    const name = companyInfo['Name'].replaceAll(/<em>/g, '').replaceAll(/<\/em>/g, '');
                    return companyName === name;
                  });
                }
                if (result) {
                  return {
                    id: result['KeyNo'],
                    name: companyName,
                    econkind: result['EconKind'],
                  };
                }
              },
              { concurrency: 1 },
            ),
          );
          if (overseas.length) {
            matchedCompanyInfos.push(...overseas);
            const overseasName = compact(flatten(overseas.map((e) => e.name)));
            // 未命中海外类型
            unmatchedNames = difference(unmatchedNames, overseasName);
            matchedNames.push(...overseasName);
          }
        }
      }
      if (supportOverSeas) {
        return {
          matchedCompanyInfos,
          unmatchedNames,
          matchedNames,
          unsupported,
        };
      }
      return {
        matchedCompanyInfos: matchedCompanyInfos.filter((e) => allowType.includes(e.t_type)),
        unmatchedNames,
        matchedNames,
        unsupported,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param companyId
   * @param companyName
   * @param update true-强制更新
   * @returns
   */
  public async createCompanyInfo(companyId: string, companyName: string, update = false): Promise<CompanyEntity> {
    try {
      const company = await this.companyRepo.findOne({ companyId });
      if (company && !update) {
        return company;
      }
      const companyEntity = new CompanyEntity();
      // 从kys索引中获取公司详情
      const kysResponse = await this.getCompanyDetails(companyId);
      const companyDetail = kysResponse.result;
      // 企业性质
      let econType = '0';
      // 机构类型
      let enterpriseType = '0';
      let standardcodes = [];
      if (companyDetail) {
        standardcodes = companyDetail['standard_code'];
        Object.assign(companyEntity, {
          companyId: companyDetail.id,
          name: companyDetail.name,
          econkind: companyDetail?.econkindcode?.join(','),
          econkindDesc: companyDetail?.econkind,
          province: companyDetail?.province,
          city: companyDetail?.areacode[0],
          district: companyDetail?.areacode[1],
          industry1: companyDetail?.industry,
          industry2: companyDetail?.subind[0],
          industry3: companyDetail?.subind[1],
          industry4: companyDetail?.subind[2],
          registcapi: companyDetail?.registcapi,
          registcapiAmount: companyDetail?.registcapiamount,
          startDateCode: moment(companyDetail?.startdatecode, 'YYYYMMDD').toDate(),
          statusCode: companyDetail?.statuscode || 0,
          creditRate: companyDetail?.['credit_score'],
          reccap: companyDetail?.reccap,
          reccapamount: companyDetail?.reccapamount,
          scale: companyDetail?.commonlist?.find((c) => c.k == '32')?.v,
        });
      } else {
        //  从kys索引中 没找到该公司，则再次查询企查查公司搜索接口找到对应公司
        const data = await this.companySearchForQcc(Object.assign(new SearchCertificationRequest(), { searchKey: companyName }));
        const result = find(data?.Result, (companyInfo) => companyId === companyInfo['KeyNo']);
        if (result) {
          // commonlist key=50 注册资本金额
          const registcapiAmount = result?.CountInfo?.find((info) => info.k == '50')?.v;
          // commonlist key=52 信用评分
          const creditRate = result?.CountInfo?.find((info) => info.k == '52')?.v;
          // commonlist key=30 企业类型标准化
          standardcodes = result?.CountInfo?.find((info) => info.k == '30')?.v?.split(',');
          const creditRateValue = creditRate ? JSON.parse(creditRate)?.s : undefined;

          //实缴资本
          const reccapamount = result?.CountInfo?.find((info) => info.k == '16')?.v;
          //企业规模
          const scale = result?.CountInfo?.find((c) => c.k == '32')?.v || '-';
          Object.assign(companyEntity, {
            companyId: companyId,
            name: companyName,
            econkind: '',
            econkindDesc: result?.EconKind,
            province: result?.Area?.ProvinceCode,
            city: result?.Area?.CityCode,
            district: result?.Area?.CountyCode,
            industry1: result?.Industry?.IndustryCode,
            industry2: result?.Industry?.SubIndustryCode,
            industry3: result?.Industry?.MiddleCategoryCode,
            industry4: result?.Industry?.qccListDetail,
            registcapi: result?.RegistCapi,
            registcapiAmount,
            startDateCode: moment(result?.StartDate).toDate(),
            statusCode: result?.statuscode || 0,
            creditRate: creditRateValue,
            reccapamount,
            scale,
          });
        }
      }
      if (standardcodes?.length) {
        enterpriseType = intersection(Object.keys(EnterpriseType), standardcodes).join(',') || '0';
        econType = intersection(Object.keys(EconType), standardcodes).join(',') || '0';
      }

      Object.assign(companyEntity, {
        econType,
        enterpriseType,
      });
      if (companyEntity?.companyId) {
        if (company && update) {
          await this.companyRepo.update(company.companyIntId, companyEntity);
        } else {
          return this.companyRepo.save(companyEntity);
        }
      }
      return companyEntity;
    } catch (e) {
      this.logger.error(`保存company详情信息失败:${JSON.stringify(e)}`);
      throw e;
    }
  }

  /**
   * 批量创建 company数据
   */
  public async createCompanyInfoBatch(companyMap: Map<string, string>): Promise<CompanyEntity[]> {
    const res = await Bluebird.map(
      companyMap.keys(),
      async (companyId) => {
        return this.createCompanyInfo(companyId, companyMap.get(companyId));
      },
      { concurrency: 10 },
    );

    return flatten(res);
  }

  public async multiMatchCompany(currentUser: PlatformUser, searchBody: MultiMatchCompanyRequest): Promise<CoyInfoHighlight[]> {
    const responseList: CoyInfoHighlight[] = [];
    await Bluebird.map(
      searchBody?.searchKey,
      async (key) => {
        const advanceResult = await this.companySearchForQcc({
          searchKey: key,
          pageIndex: 1,
          pageSize: 1,
          includeFields: ['KeyNo', 'Name', 'HitReasons', 'Type'],
        });
        const response: CoyInfoHighlight = Object.assign(new CoyInfoHighlight(), {
          searchKey: key,
          KeyNo: '',
          Name: '',
          Type: '',
          hitFields: [],
        });
        if (advanceResult?.Status === 200 && advanceResult?.Result?.length) {
          response.KeyNo = advanceResult?.Result[0]?.KeyNo;
          response.Name = advanceResult?.Result[0]?.Name;
          response.Type = advanceResult?.Result[0]?.Type;
          //命中原因
          const hitReasons = advanceResult.Result[0].HitReasons;

          //处理匹配到的名称中的HTML标签,regName 正则匹配到的名称
          const reg = /<\/?.+?\/?>/g;
          const regName = advanceResult?.Result[0]?.Name?.replace(reg, '');
          response.Name = regName;

          if (key === regName) {
            //精确匹配
            response.hitFields.push('name.keyword');
          } else if (hitReasons.find((reason) => reason.Field === '曾用名')) {
            //曾用名匹配
            response.hitFields.push('originalname');
          }
          //去重
          if (!responseList.find((e) => e.Name === response.Name)) {
            responseList.push(response);
          }
        } else {
          responseList.push(response);
        }
      },
      { concurrency: 5 },
    );
    return responseList;
  }

  public async searchAdvance(searchKey: string, pageIndex?: string, pageSize?: string, searchType?: string, searchIndex?: string, dataSource?: string) {
    const searchData = { pageIndex, pageSize, searchKey, searchType, searchIndex, dataSource };
    try {
      return await this.httpUtils.postRequest(this.configService.dataServer.searchAdvance, searchData);
    } catch (e) {
      this.logger.error(`http GET ${this.configService.dataServer.searchAdvance} err:`, e);
      return '';
    }
  }

  public async getCompaniesWithFreeText(body: CompaniesWithFreeTextRequest) {
    try {
      const data = await this.detailService.getCompaniesWithFreeText(body.text);
      if (data?.Status == 200) {
        return { Status: data.Status, Paging: data.Paging, Result: data.Result };
      }
      return data;
    } catch (e) {
      this.logger.error(`GetCompaniesWithFreeText err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  /**
   * 模糊匹配
   * @param body
   */
  public async getLinkCompaniesWithFreeText(body: CompaniesWithFreeTextRequest) {
    try {
      const data = await this.detailService.getLinkCompaniesWithFreeText(body.text);
      if (data?.Status == 200) {
        return { Status: data.Status, Paging: data.Paging, Result: data.Result };
      }
      return data;
    } catch (e) {
      this.logger.error(`getLinkCompaniesWithFreeText err:`, e);
      throw new BadRequestException(RoverExceptions.Common.RequestFailed);
    }
  }

  /**
   * 查询信用评分详情
   * @param keyNo
   */
  public async getCreditRate(keyNo: string): Promise<CreditRateResult | undefined> {
    try {
      const data = await this.httpUtils.postRequest(this.configService.dataServer.getCreditRate, {
        keyNo,
      });
      if (data?.Status == 200) {
        return data.Result;
      } else {
        this.logger.error(`http POST ${this.configService.dataServer.getCreditRate} err:`, data);
        return undefined;
      }
    } catch (e) {
      this.logger.error(`http POST ${this.configService.dataServer.getCreditRate} err:`, e);
      return undefined;
    }
  }

  /**
   * 根据企业名称获取企业对应客户和供应商信息
   * @param body
   */
  public async getSupplierCustomerWithFreeText(body: SupplierCustomerWithFreeTextRequest) {
    const { pageSize, pageIndex, dataType } = body;
    const companiesWithFreeText = await this.getCompaniesWithFreeText(body);
    if (companiesWithFreeText.Status == 200) {
      const keyNo = companiesWithFreeText?.Result[0]?.LinkCompany[0]?.KeyNo;
      if (!keyNo) {
        return { Paging: { PageIndex: pageIndex, PageSize: pageSize, TotalRecords: 0 }, Result: [] };
      }
      const dataResult = await this.detailService.getSupplierCustomer(keyNo, pageIndex, pageSize, dataType);
      if (dataResult?.Status > 201) {
        this.logger.info(`GetCoyHistoryInfo 获取供应商客户信息失败,id:${keyNo},result:${JSON.stringify(dataResult)}`);
        throw new BadRequestException(RoverExceptions.Common.RequestFailed);
      }
      return { ...pick(dataResult, ['Paging', 'Result']) };
    }
  }

  private getListStatus(company: any) {
    const listingStatusKw = company.listingstatuskw;
    if (listingStatusKw?.length > 0 && listingStatusKw.includes('F_4')) {
      // 已上市
      return 1;
    }
    // 未上市
    return 2;
  }

  /**
   * 先查 C 端接口，没有再查 ES
   * @param companyId
   * @param data
   * @private
   */
  @Cacheable({ ttlSeconds: 600 })
  public async doGetCompanyFinance(companyId: string, data?: any) {
    const companyFinance = await this.detailService.getCompanyFinance(companyId, [4], 1);
    if (!data) {
      data = await this.httpUtils.getRequest(this.configService.dataServer.companyDetail, {
        keyNo: companyId,
      });
    }
    const companyFinanceModel = new CompanyFinanceInfoResponse();
    companyFinanceModel.companyId = companyId;
    if (data?.Status == 200 && data?.Result) {
      companyFinanceModel.operIncTotal = data.Result.CompanyRevenue?.Revenue;
      if (data.Result.CompanyRevenue?.EndDate) {
        companyFinanceModel.operIncTotalSource = moment(data.Result.CompanyRevenue.EndDate).year() + '年年报';
      }
    }
    if (companyFinance?.Status == 200 && companyFinance?.Result) {
      const result = companyFinance.Result;
      const lastYear = moment().add('-1', 'years').startOf('year').unix(); // 近1年
      const assetsTotal = result.ReportFields.find((f) => f.AccountName === 'assets_total'); // 资产总计
      const operIncTotal = result.ReportFields.find((f) => f.AccountName === 'oper_inc_total'); // 营业收入总计
      const netProfit = result.ReportFields.find((f) => f.AccountName === 'net_profit'); // 净利润
      let hitFlag = false;
      if (assetsTotal && operIncTotal && netProfit) {
        const assetsTotalField = assetsTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastYear);
        if (assetsTotalField) {
          companyFinanceModel.assetsTotal = assetsTotalField.ShowValue;
          companyFinanceModel.assetsTotalSource = assetsTotalField.ReportPeriodName;
          hitFlag = true;
        }
        const operIncTotalField = operIncTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastYear);
        if (operIncTotalField && !companyFinanceModel.operIncTotal) {
          companyFinanceModel.operIncTotal = operIncTotalField.ShowValue;
          companyFinanceModel.operIncTotalSource = operIncTotalField.ReportPeriodName;
          hitFlag = true;
        }
        const netProfitField = netProfit.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastYear);
        if (netProfitField) {
          companyFinanceModel.netProfit = netProfitField.ShowValue;
          companyFinanceModel.netProfitSource = netProfitField.ReportPeriodName;
          hitFlag = true;
        }
      }
      if (!hitFlag) {
        // 使用 ES 数据
        await this.getCompanyFinanceFromEs(companyFinanceModel);
      }
      if (!hitFlag) {
        // 近一年年报、ES 都未命中数据，则使用近3年年报数据
        if (assetsTotal && operIncTotal && netProfit) {
          const lastThreeYear = moment().add('-3', 'years').startOf('year').unix(); // 近3年
          const assetsTotalField = assetsTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastThreeYear);
          if (assetsTotalField) {
            companyFinanceModel.assetsTotal = assetsTotalField.ShowValue;
            companyFinanceModel.assetsTotalSource = assetsTotalField.ReportPeriodName;
          }
          const operIncTotalField = operIncTotal.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastThreeYear);
          if (operIncTotalField && !companyFinanceModel.operIncTotal) {
            companyFinanceModel.operIncTotal = operIncTotalField.ShowValue;
            companyFinanceModel.operIncTotalSource = operIncTotalField.ReportPeriodName;
          }
          const netProfitField = netProfit.FieldList?.find((f) => f?.ReportDate && f.ReportDate >= lastThreeYear);
          if (netProfitField) {
            companyFinanceModel.netProfit = netProfitField.ShowValue;
            companyFinanceModel.netProfitSource = netProfitField.ReportPeriodName;
          }
        }
      }
    } else {
      // 使用 ES 数据
      await this.getCompanyFinanceFromEs(companyFinanceModel);
    }
    return companyFinanceModel;
  }

  /**
   *
   * @param companyFinanceModel
   * @private
   */
  private async getCompanyFinanceFromEs(companyFinanceModel: CompanyFinanceInfoResponse) {
    const companyId = companyFinanceModel.companyId;
    const { data } = await this.getCompanyFinanceById(companyId);
    if (data.length > 0) {
      //           "net_profit_hierarchy" : "P23",
      //           "name" : "苏尼特右旗农村信用合作联社桑宝力嘎分社",
      //           "id" : "b1df8de8b7c54bdc691e52135dd42fb4",
      //           "op_revenue_hierarchy" : "P23",
      //           "tot_assets_hierarchy" : "P24"
      const netProfit = data[0].net_profit_hierarchy;
      const opRevenue = data[0].op_revenue_hierarchy;
      const totAssets = data[0].tot_assets_hierarchy;
      if (!companyFinanceModel.assetsTotal) {
        companyFinanceModel.assetsTotal = this.getAssetMap().get(totAssets) || '-';
      }
      if (!companyFinanceModel.operIncTotal) {
        companyFinanceModel.operIncTotal = this.getAssetMap().get(opRevenue) || '-';
      }
      if (!companyFinanceModel.netProfit) {
        companyFinanceModel.netProfit = this.getAssetMap().get(netProfit) || '-';
      }
    }
    return companyFinanceModel;
  }

  protected searchEs(body, preference: string) {
    const searchRequest: RequestParams.Search = {
      index: this.creditFinanceIndexName,
      type: '_doc',
      body,
      preference,
    };
    return this.creditFinanceClient.search(searchRequest);
  }

  getAssetMap = () => {
    const assetMap = new Map();
    // assetMap.set('N1', '-');
    // assetMap.set('N2', '小于 -100 万元');
    // assetMap.set('N3', '-100--50 万元');
    // assetMap.set('N4', '-50--40 万元');
    // assetMap.set('N5', '-40--30 万元');
    // assetMap.set('N6', '-30--20 万元');
    // assetMap.set('N7', '-20--10 万元');
    // assetMap.set('P0', 0);
    // assetMap.set('P1', '0-1 万元');
    // assetMap.set('P2', '1-5 万元');
    // assetMap.set('P3', '5-10 万元');
    // assetMap.set('P4', '10-20 万元');
    // assetMap.set('P5', '20-30 万元');
    // assetMap.set('P6', '30-50 万元');
    // assetMap.set('P7', '50-100 万元');
    // assetMap.set('P8', '100-150 万元');
    // assetMap.set('P9', '150-200 万元');
    assetMap.set('N1', '300 万元以内');
    assetMap.set('N2', '300 万元以内');
    assetMap.set('N3', '300 万元以内');
    assetMap.set('N4', '300 万元以内');
    assetMap.set('N5', '300 万元以内');
    assetMap.set('N6', '300 万元以内');
    assetMap.set('N7', '300 万元以内');
    assetMap.set('P0', '300 万元以内');
    assetMap.set('P1', '300 万元以内');
    assetMap.set('P2', '300 万元以内');
    assetMap.set('P3', '300 万元以内');
    assetMap.set('P4', '300 万元以内');
    assetMap.set('P5', '300 万元以内');
    assetMap.set('P6', '300 万元以内');
    assetMap.set('P7', '300 万元以内');
    assetMap.set('P8', '300 万元以内');
    assetMap.set('P9', '300 万元以内');
    assetMap.set('P10', '300 万元以内');
    assetMap.set('P11', '300-500 万元');
    assetMap.set('P12', '500-1000 万元');
    assetMap.set('P13', '1000-2000 万元');
    assetMap.set('P14', '2000-5000 万元');
    assetMap.set('P15-1', '5000-8000 万元');
    assetMap.set('P15-2', '8000-10000 万元');
    assetMap.set('P16', '1-2 亿元');
    assetMap.set('P17', '2-5 亿元');
    assetMap.set('P18-1', '5-8 亿元');
    assetMap.set('P18-2', '8-10 亿元');
    assetMap.set('P19-1', '10-12 亿元');
    assetMap.set('P19-2', '12-50 亿元');
    assetMap.set('P20', '50-100 亿元');
    assetMap.set('P21', '100-200 亿元');
    assetMap.set('P22', '200-500 亿元');
    assetMap.set('P23', '500-1000 亿元');
    assetMap.set('P24', '1000 亿元以上');
    return assetMap;
  };

  private async getCompanyFinanceById(companyId: string) {
    const body = {
      query: {
        bool: {
          must: [
            {
              term: {
                id: {
                  value: companyId,
                },
              },
            },
          ],
        },
      },
      _source: {
        includes: ['tot_assets_hierarchy', 'net_profit_hierarchy', 'op_revenue_hierarchy', 'id', 'name'],
      },
    };
    const response = await this.searchEs(body, companyId);
    return {
      total: response?.body?.hits?.total?.value || 0,
      data: response?.body?.hits?.hits?.map((d) => d._source) || [],
    };
  }

  public async checkCompanyStandardCode(companyIds: string[]) {
    //优先去重校验
    companyIds = _.uniq(companyIds);
    // check 暂不支持对注册地在中国香港、中国澳门、中国台湾及境外的企业、机关单位发起监控
    const companyInfos: ESResponse<KysCompanyResponseDetails> = await this.companySearchForKys(
      Object.assign(new KysCompanySearchRequest(), {
        pageIndex: 1,
        pageSize: companyIds.length,
        includeFields: ['id', 'name', 'standard_code'],
        filter: { ids: companyIds },
      }),
    );
    const supportedCompanies: KysCompanyResponseDetails[] = [];
    const unsupportedCompanies: KysCompanyResponseDetails[] = [];

    companyIds.forEach((x) => {
      const companyInfo = companyInfos?.Result?.find((t) => t.id === x);
      if (companyInfo) {
        if (
          (companyInfo?.['standard_code']?.length && intersection(companyInfo?.['standard_code'], ForbiddenStandardCode).length) ||
          !companyInfo?.['standard_code']?.length
        ) {
          unsupportedCompanies.push(companyInfo);
        } else {
          supportedCompanies.push(companyInfo);
        }
      } else {
        //如果kys中不存在该公司，则直接拦截
        const kysCompany = new KysCompanyResponseDetails();
        kysCompany.id = x;
        unsupportedCompanies.push(kysCompany);
      }
    });

    // companyInfos.Result.forEach((companyInfo) => {
    //   // 过滤不支持排查的企业类型
    //   if (
    //     (companyInfo?.['standard_code']?.length && intersection(companyInfo?.['standard_code'], ForbiddenStandardCode).length) ||
    //     !companyInfo?.['standard_code']?.length
    //   ) {
    //     unsupportedCompanies.push(companyInfo);
    //     // } else {
    //     //   supportedCompanies.push(companyInfo);
    //   }
    // });
    return { unsupportedCompanies, supportedCompanies, companyInfos };
  }
}
