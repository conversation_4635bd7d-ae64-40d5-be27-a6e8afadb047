import { MailerService } from '@kezhaozhao/nest-mailer';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt/dist/jwt.service';
import * as _ from 'lodash';
import { toPlainObject } from 'lodash';
import { Logger } from 'log4js';
import { EntityManager, In } from 'typeorm';
import { DimensionRiskLevelContant } from '../../libs/constants/model.constants';
import { DiligenceHistoryEntity } from '../../libs/entities/DiligenceHistoryEntity';
import { MonitorCompanyEntity } from '../../libs/entities/MonitorCompanyEntity';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { RiskModelEntity } from '../../libs/entities/RiskModelEntity';
import { DimensionRiskLevelEnum } from '../../libs/enums/diligence/DimensionRiskLevelEnum';
import { MetricTypeEnums } from '../../libs/enums/metric/MetricTypeEnums';
import { ProductCodeEnums } from '../../libs/enums/ProductCodeEnums';
import { RiskModelTypeEnums } from '../../libs/enums/RiskModelTypeEnums';
import { PlatformUser } from '../../libs/model/common';
import { GetJwtTokenRequest } from '../../libs/model/internal/InternalRequest';
import { SearchDynamicEsPO } from '../../libs/model/monitor/SearchDynamicEsPO';
import { MonitorContext, RiskItemData } from '../message/MonitorContext';
import { MonitorDynamicEsService } from '../monitor/dynamic/monitor.dynamic.es.service';
import { manuallyModifyRelatedSettings } from '../risk_model/risk_model.utils';

@Injectable()
export class InternalService {
  private readonly logger: Logger = QccLogger.getLogger(InternalService.name);
  // public esClient_rickChangeCopy: Client;
  // public esClient_rickChangeSource: Client;
  // private readonly riskChangeCopyIndexName: string;
  // private esBatchSize = 1000;

  // private readonly rickChangeSourceIndexName: string;

  constructor(
    private readonly entityManager: EntityManager, // 注入 EntityManager
    readonly redisService: RedisService,
    private readonly jwtService: JwtService,
    private readonly mailerService: MailerService,
    private readonly dynamicEsService: MonitorDynamicEsService,
  ) {
    // this.esClient_rickChangeCopy = new Client({
    //   nodes: this.configService.esConfig.rickChangeCopy.nodes,
    //   ssl: { rejectUnauthorized: false },
    // });
    // this.riskChangeCopyIndexName = this.configService.esConfig.rickChangeCopy.indexName;
    // this.esClient_rickChangeSource = new Client({
    //   nodes: this.configService.esConfig.riskChangeList.nodes,
    //   ssl: { rejectUnauthorized: false },
    // });
    // this.rickChangeSourceIndexName = this.configService.esConfig.riskChangeList.indexName;
  }

  /**
   * 修改件监控模型中的监控范围设置，直接修改已发布模型的数据，不用重新生成模型变更模型id
   * @param modelId
   * @param newFieldValue
   * @returns
   */
  public async manuallyModifyRelatedSettings(modelId: number, newFieldValue: any[]) {
    return manuallyModifyRelatedSettings(modelId, newFieldValue, this.entityManager, this.redisService);
  }

  public async sendEmailTest(uniqueHashkeys: string, orgId: number, email: string) {
    const uniqueHashkeyList = uniqueHashkeys.split(',');
    const response = await this.dynamicEsService.searchDynamics(
      Object.assign(new SearchDynamicEsPO(), {
        product: ProductCodeEnums.Pro,
        orgId: orgId,
        uniqueHashkeys: uniqueHashkeyList.slice(0, 1000),
        pageIndex: 1,
        pageSize: 1000, // 默认给到1000个
        aggsField: [4],
        sortField: ['createDate'],
        sortOrder: 'DESC',
      }),
    );

    const companyCount = response?.aggsResponse?.['4_companyCount']?.value || 0;
    const companyNames =
      companyCount > 1
        ? Array.from(new Set(response.data.map((x) => x.companyName)))
            .slice(0, 3)
            .join('、') + '等'
        : Array.from(new Set(response.data.map((x) => x.companyName)))
            .slice(0, 1)
            .join('、');
    const riskDataList: RiskItemData[] = [];
    response.data.forEach((x, index) => {
      const item = new RiskItemData();
      item.index = index + 1;
      item.companyName = x.companyName;
      // item.metricsContent = x.metricsContent;
      item.metricsContent = this.getContent(x.metricsContent.displayContent[0].dimensionContent[0], x.metricsContent.displayContent[0].dimensionKey) || '';
      item.riskLevel = DimensionRiskLevelContant[x.riskLevel];
      item.metricName = x.metricsName;
      item.updateTime = '2025-01-01 00:00:00';
      riskDataList.push(item);
    });

    const mc = Object.assign(new MonitorContext(), {
      subject: '【企查查风险洞察】风险监控变动通知',
      companyCount: response?.aggsResponse?.['4_companyCount']?.value || 0,
      riskCount: response?.total,
      highRiskCount: response?.aggsResponse?.['4_riskLevel']?.buckets?.find((m) => Number(m.key) === DimensionRiskLevelEnum.High)?.doc_count || 0,
      mediumRiskCount: response?.aggsResponse?.['4_riskLevel']?.buckets?.find((m) => Number(m.key) === DimensionRiskLevelEnum.Medium)?.doc_count || 0,
      alertRiskCount: response?.aggsResponse?.['4_riskLevel']?.buckets?.find((m) => Number(m.key) === DimensionRiskLevelEnum.Alert)?.doc_count || 0,
      companyNames: companyNames,
      businessMetricTypeCount: response?.aggsResponse?.['4_metricsType']?.buckets?.find((m) => m.key === MetricTypeEnums.MonitorBusinessMetric)?.doc_count || 0,
      monitorMetricTypeCount:
        response?.aggsResponse?.['4_metricsType']?.buckets?.find((m) => m.key === MetricTypeEnums.MonitorSupervisionMetric)?.doc_count || 0,
      industryMetricTypeCount: 0,
      operateMetricTypeCount: 0,
      judicialMetricTypeCount: 0,
      risks: riskDataList.slice(0, 100),
    });
    await this.mailerService.sendMail({
      to: email,
      subject: mc.subject,
      template: 'monitor_email_template',
      context: mc,
    });
  }

  private getContent(item, dimensionKey = 'RiskChange') {
    let newContent;
    if (!item) {
      return '-';
    }
    switch (dimensionKey) {
      case 'RiskChange':
        newContent = item.Content;
        break;
      case 'RelatedCompanies':
        newContent = this.getRelationContent(item);
        break;
      default:
        newContent = '';
        break;
    }
    return newContent;
  }

  // 关联信息的内容
  private getRelationContent(item) {
    const { companyNameRelated, companyKeynoRelated, relatedTypeDescList, riskTypeDescList } = item;
    let res = '';
    if (companyNameRelated && companyKeynoRelated) {
      res += '关联公司：';
      res += companyNameRelated;
      res += '\n';
    }
    if (relatedTypeDescList) {
      res += '关联类型：';
      res += relatedTypeDescList.join('、');
      res += '\n';
    }

    if (riskTypeDescList) {
      res += '企业状态：';
      res += riskTypeDescList.join('、');
    }
    return res;
  }

  public async getJwtToken(currentUser: PlatformUser, reqBody: GetJwtTokenRequest) {
    const result: PlatformUser = toPlainObject({
      loginUserId: reqBody.loginUserId,
      userId: reqBody.userId,
      currentProduct: reqBody.currentProduct,
      currentOrg: reqBody.currentOrg,
      orgName: reqBody.orgName,
    });

    const JWT_SECRET = process.env.JWT_SECRET;
    const jwtToken = await this.jwtService.signAsync(result, {
      secret: JWT_SECRET,
      expiresIn: 3600 + 's',
    });
    return 'Bearer ' + jwtToken;
  }

  // public async refresh() {
  //   const response = await this.esClient_rickChangeCopy.indices.refresh({ index: this.getReadIndexName() });
  //   return response;
  // }

  // public getWriteIndexName(): string {
  //   return `${this.riskChangeCopyIndexName}_write`;
  // }

  // public getReadIndexName(): string {
  //   return `${this.riskChangeCopyIndexName}_read`;
  // }

  // public async writeRiskChange() {
  //   try {
  //     const toInsertDataList = await this.getLastFiveDayRiskChange();
  //     const items = flatMap(
  //       toInsertDataList.map((entity) => {
  //         if (entity) {
  //           return [{ index: { _index: this.getWriteIndexName(), _id: entity.Id } }, { ...entity }];
  //         }
  //       }),
  //     );
  //     await this.insertDocsToEs(items, true);
  //     return true;
  //   } catch (error) {
  //     this.logger.error(`数据插入失败，错误: ${error.message}`);
  //     throw error;
  //   }
  // }

  // private async insertDocsToEs(items: any[], refreshNow = true) {
  //   if (!items?.length) {
  //     return;
  //   }
  //   await Bluebird.map(
  //     chunk(items, this.esBatchSize),
  //     async (body: any[]) => {
  //       try {
  //         const start = Date.now();
  //         const { body: bulkResponse } = await this.esClient_rickChangeCopy.bulk({ refresh: refreshNow, body });
  //         if (bulkResponse.errors) {
  //           const erroredDocuments = [];
  //           bulkResponse.items.forEach((action: any, i: number) => {
  //             const operation = Object.keys(action)[0];
  //             if (action[operation].error) {
  //               erroredDocuments.push({
  //                 status: action[operation].status,
  //                 error: action[operation].error,
  //                 operation: body[i * 2],
  //                 document: body[i * 2 + 1],
  //               });
  //             }
  //           });
  //           const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, error_size=${erroredDocuments.length}`;
  //           this.logger.error(message);
  //           captureException({ message });
  //           this.logger.error(erroredDocuments);
  //         }
  //         this.logger.info(`insert ${body.length / 2} items to es cost ${Date.now() - start}ms`);
  //       } catch (error) {
  //         const message = `insert docs to index failed(partial failure):  indexName=${this.getWriteIndexName()}, errorMessage=` + error.message;
  //         this.logger.error(message);
  //         captureException(Object.assign(error, { message }));
  //         this.logger.error(error);
  //       }
  //     },
  //     { concurrency: 5 },
  //   );
  //   if (refreshNow) {
  //     await this.refresh();
  //   }
  // }

  // private async getLastFiveDayRiskChange(category?: number) {
  //   const startOfDay = moment().subtract(5, 'day').format(DATE_TIME_FORMAT);
  //   const endOfDay = moment().format(DATE_TIME_FORMAT);
  //   let categoryList: number[] = [];
  //   if (category) {
  //     categoryList = [category];
  //   } else {
  //     categoryList = Object.values(RiskChangeCategoryEnum).filter((value) => typeof value === 'number') as number[];
  //   }
  //   const data = await Bluebird.map(categoryList, async () => {
  //     const searchRequest: RequestParams.Search = {
  //       size: 100, // 不需要返回文档，只聚合
  //       from: 0,
  //       track_total_hits: true,
  //       index: this.rickChangeSourceIndexName,
  //       type: '_doc',
  //       body: {
  //         query: {
  //           bool: {
  //             filter: [
  //               {
  //                 term: {
  //                   IsValid: '1',
  //                 },
  //               },
  //               {
  //                 terms: {
  //                   Category: categoryList,
  //                 },
  //               },
  //               {
  //                 range: {
  //                   ChangeDate: {
  //                     time_zone: '+08:00',
  //                     gte: startOfDay,
  //                     lte: endOfDay,
  //                   },
  //                 },
  //               },
  //             ],
  //           },
  //         },
  //       },
  //     };
  //     const results = await this.esClient_rickChangeSource.search(searchRequest);
  //     const hits = results.body.hits.hits;
  //     return hits.map((hit: { _source }) => {
  //       return hit._source;
  //     });
  //   });
  //   const results = _.flatMap(data);
  //   return results;
  // }

  // /**
  //  * 聚合最新的1000家企业
  //  */
  // public async getNewestRiskChangeCompany() {
  //   const startOfDay = moment().subtract(20, 'day').format(DATE_TIME_FORMAT);
  //   const endOfDay = moment().format(DATE_TIME_FORMAT);

  //   const categoryList: number[] = Object.values(RiskChangeCategoryEnum).filter((value) => typeof value === 'number') as number[];
  //   const data = await Bluebird.map(categoryList, async (category) => {
  //     const searchRequest: RequestParams.Search = {
  //       size: 100, // 不需要返回文档，只聚合
  //       from: 0,
  //       track_total_hits: true,
  //       index: this.rickChangeSourceIndexName,
  //       type: '_doc',
  //       _source: ['Name', 'KeyNo'],
  //       body: {
  //         query: {
  //           bool: {
  //             filter: [
  //               {
  //                 term: {
  //                   IsValid: '1',
  //                 },
  //               },
  //               {
  //                 term: {
  //                   Category: category,
  //                 },
  //               },
  //               {
  //                 range: {
  //                   ChangeDate: {
  //                     time_zone: '+08:00',
  //                     gte: startOfDay,
  //                     lte: endOfDay,
  //                   },
  //                 },
  //               },
  //             ],
  //           },
  //         },
  //         // 不用聚合，数据量大，聚合算count慢
  //       },
  //     };
  //     const results = await this.esClient_rickChangeSource.search(searchRequest);
  //     const hits = results.body.hits.hits;
  //     return hits.map((hit: { _source: { Name: string; KeyNo: string } }) => {
  //       return {
  //         companyName: hit._source.Name,
  //         companyId: hit._source.KeyNo,
  //       };
  //     });
  //   });
  //   const results = _.uniqBy(_.flatMap(data), 'companyId');
  //   return results;
  // }

  /**
   *  验证企业的命中情况
   * @param modelId
   * @param currentUser
   */
  public async checkModuleCompanyValidate(modelId: number, modelType: RiskModelTypeEnums) {
    const riskModelList = await this.entityManager.find(RiskModelEntity, {
      modelType,
      modelId,
    });
    if (!riskModelList?.length) {
      return;
    }
    if (modelType === RiskModelTypeEnums.MonitorModel) {
      const moduleCompanys: { moduleId: number; companyIds: string[] }[] = [];
      const modelIds = riskModelList.map((item) => item.modelId);
      for (const modelId of modelIds) {
        const companyIds: string[] = [];
        const monitorGroups = await this.entityManager.find(MonitorGroupEntity, {
          monitorModelId: modelId,
        });
        const monitorGroupIds = monitorGroups.map((item) => item.monitorGroupId);
        if (monitorGroupIds?.length) {
          for (const groupId of monitorGroupIds) {
            const monitorCompanys = await this.entityManager.find(MonitorCompanyEntity, {
              monitorGroupId: groupId,
            });
            if (monitorCompanys?.length) {
              const groupCompanyIds = monitorCompanys.map((item) => item.companyId);
              companyIds.push(...groupCompanyIds);
            }
          }
          if (companyIds?.length) {
            moduleCompanys.push({
              moduleId: modelId,
              companyIds: _.uniq(companyIds),
            });
          }
        }
      }
      if (moduleCompanys?.length) {
        for (const item of moduleCompanys) {
          const companyIds = item.companyIds;
          const moduleId = item.moduleId;
          const dueDilligenceList = await this.entityManager.find(DiligenceHistoryEntity, {
            orgModelId: moduleId,
            companyId: In(companyIds),
          });
          if (dueDilligenceList?.length) {
            for (const dueDilligence of dueDilligenceList) {
              //TODO dueDilligence.originalHits;
            }
          }
        }
      }
    }
  }
}
