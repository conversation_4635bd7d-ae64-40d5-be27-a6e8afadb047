import { Injectable } from '@nestjs/common';
import { Cron, NestDistributedSchedule } from 'nest-schedule';
import Redlock from 'redlock';
import { RedisService } from '@kezhaozhao/nestjs-redis';
import { Logger } from 'log4js';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { TraceLog } from '@kezhaozhao/qcc-logger/lib/qcc/annotation/trace.annotation';
import process from 'process';
import * as moment from 'moment';
import { DATE_TIME_FORMAT } from '../../libs/constants/common';
import { PushJobService } from '../push/push.job.service';
import { MonitorJobService } from '../monitor/monitor.job.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MonitorGroupEntity } from '../../libs/entities/MonitorGroupEntity';
import { MonitorCompanyService } from '../monitor/company/monitor.company.service';

@Injectable()
export class ScheduleService extends NestDistributedSchedule {
  private readonly schedulelogger: Logger = QccLogger.getLogger('schedule');
  private readonly redLock: Redlock;

  constructor(
    redisService: RedisService,
    private readonly pushJobService: PushJobService,
    private readonly monitorJobService: MonitorJobService,
    @InjectRepository(MonitorGroupEntity) private readonly monitorGroupRepo: Repository<MonitorGroupEntity>, //@InjectRepository(OrgBundleEntity, 'saasdb') private readonly orgBundleRepo: Repository<OrgBundleEntity>,
  ) {
    super();
    this.redLock = new Redlock([redisService.getClient()], { retryCount: 1 });
  }

  async tryLock(method: string): Promise<TryRelease> {
    this.schedulelogger.info(`schedule tryLock:  ${process?.env?.JEST_WORKER_ID}`);
    if (process?.env?.JEST_WORKER_ID) {
      // jest 启动测试的时候禁用定时任务
      return false;
    }
    this.schedulelogger.info('try apply lock: ' + method);

    try {
      await this.redLock.acquire(['lock_' + method], 300 * 1000); // lock 5 minutes

      this.schedulelogger.info('applied lock: ' + method);

      return () => {
        this.schedulelogger.info('cron job done: ' + method);
      };
    } catch (err) {
      // this.schedulelogger.error(err.message);
      return false;
    }
  }

  @Cron('0 0-23/1 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每1小时执行消息推送
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async pushRuleRuleJob() {
    try {
      const beginTime = moment();
      this.schedulelogger.info(`[pushRuleRuleJob]Run cron job for push rule job begin at ${beginTime.format(DATE_TIME_FORMAT)}`);
      await this.pushJobService.pushRuleRuleJob();
      const endTime = moment();
      const takeTime = endTime.diff(beginTime, 'seconds');
      this.schedulelogger.info(`[pushRuleRuleJob]Run cron job for push rule job end at ${endTime.format(DATE_TIME_FORMAT)}, take ${takeTime} seconds`);
    } catch (error) {
      this.schedulelogger.error('[pushRuleRuleJob]Run cron job for push rule job failed', error);
    }
  }

  // @Cron('0 6,14,22 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 在每天的6点、14点和22点触发执行监控任务 间隔8小时
  @Cron('0 6 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每24个小时执行监控任务
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async MonitorGroupDynamicJob() {
    try {
      const beginTime = moment();
      this.schedulelogger.info(`[MonitorGroupDynamicJob]Run cron job for monitor group dynamic  begin at ${beginTime.format(DATE_TIME_FORMAT)}`);
      await this.monitorJobService.monitorGroupDynamicJob(0, 24, 'hour');
      const endTime = moment();
      const takeTime = endTime.diff(beginTime, 'seconds');
      this.schedulelogger.info(
        `[MonitorGroupDynamicJob]Run cron job for monitor group dynamic end at ${endTime.format(DATE_TIME_FORMAT)}, take ${takeTime} seconds`,
      );
    } catch (error) {
      this.schedulelogger.error('[MonitorGroupDynamicJob]Run cron job for monitor group dynamic failed', error);
    }
  }

  @Cron('0 7 * * *', { enable: !process?.env?.JEST_WORKER_ID }) // 每24个小时执行记录监控企业关联方日志
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async MonitorCompanyRelatedDailyJob() {
    try {
      const beginTime = moment();
      this.schedulelogger.info(`[MonitorCompanyRelatedDailyJob]Run cron job for monitor company related  begin at ${beginTime.format(DATE_TIME_FORMAT)}`);
      await this.monitorJobService.saveCompanyRelatedDailyJob(0);
      const endTime = moment();
      const takeTime = endTime.diff(beginTime, 'seconds');
      this.schedulelogger.info(
        `[MonitorCompanyRelatedDailyJob]Run cron job for monitor company related end at ${endTime.format(DATE_TIME_FORMAT)}, take ${takeTime} seconds`,
      );
    } catch (error) {
      this.schedulelogger.error('[MonitorCompanyRelatedDailyJob]Run cron job for monitor company related failed', error);
    }
  }

  /**
   *
   * 删除已经不存在的企业的监控动态产生的垃圾数据
   * monitor_metrics_dynamic
   * ES 状态为MetricDynamicStatusEnums.Deprecated
   * ES kys_metric_dynamics_test
   * batch_dilligence
   * due_dilligence
   *
   * ES kys_snapshot_test 尽调子文档，根据snapshotId删除 dimensionStrategy子文档，diligence子文档
   *    父文档上都没有子文档挂载的数据，父文档也同步删除
   */
  @Cron('0 0 12 * * 6', { enable: !process?.env?.JEST_WORKER_ID }) // 每周六凌晨12点
  @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
  async removeDeprecatedMonitorDataJob() {
    try {
      const beginTime = moment();
      this.schedulelogger.info(`[removeDeprecatedMonitorDataJob]Run cron job for deprecated monitor data  begin at ${beginTime.format(DATE_TIME_FORMAT)}`);
      await this.monitorJobService.removeDeprecatedMonitorDataJob();
      const endTime = moment();
      const takeTime = endTime.diff(beginTime, 'seconds');
      this.schedulelogger.info(
        `[removeDeprecatedMonitorDataJob]Run cron job for  deprecated monitor data end at ${endTime.format(DATE_TIME_FORMAT)}, take ${takeTime} seconds`,
      );
    } catch (error) {
      this.schedulelogger.error('[removeDeprecatedMonitorDataJob]Run cron job for deprecated monitor data failed', error);
    }
  }

  /**
   * 针对套餐已经到期的组织，每天凌晨4点，关闭当前组织的一些定时任务的job
   * 1. 关闭监控任务
   */
  //@Cron('0 0 1 * * *', { enable: !process?.env?.JEST_WORKER_ID })
  /* @TraceLog({ throwError: true, reCreateContext: true, spanType: 3 })
   async closeDataJob() {
     try {
       const beginTime = moment();
       this.schedulelogger.info(`[closeDataJob]Run cron job for close data Job  begin at ${beginTime.format(DATE_TIME_FORMAT)}`);
       await this.closePlatformDataJob();
       const endTime = moment();
       const takeTime = endTime.diff(beginTime, 'seconds');
       this.schedulelogger.info(`[closeDataJob]Run cron job for close data Job end at ${endTime.format(DATE_TIME_FORMAT)}, take ${takeTime} seconds`);
     } catch (error) {
       this.schedulelogger.error('[closeDataJob]Run cron job for close data Job failed', error);
     }
   }*/

  /**
   * 关闭数据任务
   */
  /*private async closePlatformDataJob() {
    const proBundles = await this.orgBundleRepo
      .createQueryBuilder('orgBundle')
      .leftJoin('orgBundle.bundle', 'bundle')
      //.where('orgBundle.orgId = :orgId', { orgId })
      .andWhere('orgBundle.active = 0') // 过期套餐
      .andWhere('bundle.serviceCode = :serviceCode', { serviceCode: ProductCodeEnums.Pro })
      .andWhere('bundle.type IN (1,3)') // 套餐类型：1-标准套餐；2-升级包；3-自定义套餐， 1和3都是基础套餐
      .getMany();
    // 1, 关闭监控分组，分组消息推送
    if (proBundles?.length) {
      const orgIds = proBundles.map((i) => i.orgId);
      const monitorGroups = await this.monitorGroupRepo.find({
        where: {
          orgId: In(orgIds),
          monitorStatus: MonitorStatusEnums.Enabled,
        },
      });
      if (monitorGroups?.length) {
        await this.monitorGroupRepo.update(
          monitorGroups.map((i) => i.monitorGroupId),
          { monitorStatus: MonitorStatusEnums.Disabled },
        );
      }
    }
  }*/
}
