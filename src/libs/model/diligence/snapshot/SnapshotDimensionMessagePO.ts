import { OperationEnums } from 'libs/enums/diligence/OperationEnums';
import { ModelScorePO } from '../ModelScorePO';
import { DimensionHitResultPO } from '../dimension/DimensionHitResultPO';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class SnapshotMessageBasePO {
  operation: OperationEnums;
  userId?: number;
  orgId: number;
  batchIds: number[]; // 1. 手动指定 diligenceId 关联的 batch  2. 当 operation=RefreshBatchRelation 的时候，是刷新batch对应的diligenceId
  retryTimes?: number;
}

export class CreateSnapshotMessagePO extends SnapshotMessageBasePO {
  companyId?: string;
  companyName?: string;
  snapshotId: string;
  diligenceId: number;
  diligenceAt: Date;
  riskData?: ModelScorePO;

  @ApiPropertyOptional({ description: '命中这批数据策略ID，非必填' })
  strategyId?: number;
}

export class SnapshotDimensionMessagePO extends CreateSnapshotMessagePO {
  @ApiPropertyOptional({ description: '是否是补充某个维度的快照信息' })
  refill?: number;
  scorePO?: DimensionHitResultPO;
  // forceUpdate?: boolean;
}
