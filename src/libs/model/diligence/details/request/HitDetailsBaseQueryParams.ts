import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';
import { DimensionTypeEnums } from '../../../../enums/diligence/DimensionTypeEnums';
import { PaginationParams } from '../../../common';
import { SnapshotEsFilterPO } from '../../../es/SnapshotEsFilterPO';

export class HitDetailsBaseQueryParams extends PaginationParams {
  @ApiPropertyOptional({ description: '如果该字段不为空，会获取该id对应的尽调结果(快照ID)' })
  @MaxLength(100)
  @IsOptional()
  snapshotId?: string;

  @ApiPropertyOptional({ description: '维度关联的策略id' })
  strategyId?: number;

  @ApiProperty({ description: 'orgId' })
  orgId?: number;

  @ApiProperty({ description: '监控分组id' })
  monitorGroupId?: number;

  @ApiProperty({ required: true, description: '公司id' })
  @IsString()
  keyNo: string;

  @ApiProperty({ required: true, description: '公司名称' })
  @IsString()
  companyName?: string;

  @ApiProperty({ required: true, description: '子维度key' })
  subDimensionKey?: DimensionTypeEnums;

  @ApiProperty({ required: false, description: '公司ids' })
  companyIds?: string[];

  @ApiProperty({ required: false, description: '请求来源, record-排查记录过来的请求' })
  from?: string;

  @ApiProperty({ required: false, description: '指定数据源' })
  source?: string;

  @ApiPropertyOptional({ description: '排序参数' })
  field?: string;

  @ApiPropertyOptional({ description: '排序参数' })
  field2?: string;

  @ApiPropertyOptional({ description: '排序规则', enum: ['ASC', 'DESC'] })
  order?: 'ASC' | 'DESC';

  /**
   * 创建时间
   */
  createDate?: Date;

  @ApiPropertyOptional({ type: SnapshotEsFilterPO, description: 'es查询参数' })
  @IsOptional()
  @ValidateNested()
  esFilter?: SnapshotEsFilterPO;
}

export class HitDetailsBaseQueryPaginationParams extends HitDetailsBaseQueryParams {
  // @ApiPropertyOptional({ description: '排序参数' })
  // field?: string;
  //
  // @ApiPropertyOptional({ description: '排序规则', enum: ['ASC', 'DESC'] })
  // order?: 'ASC' | 'DESC';
}

export class DimensionHitDetailsClientRequest extends HitDetailsBaseQueryPaginationParams {
  @ApiProperty({ required: true, description: '维度key' })
  key: DimensionTypeEnums;
}
