/* eslint-disable @typescript-eslint/naming-convention */
export enum DimensionSourceEnums {
  /**
   * 专业版
   */
  Pro = 'Pro',
  /**
   * 信用用大数据ES
   */
  CreditES = 'CreditES',
  /**
   * 信用用大数据
   */
  CreditAPI = 'CreditAPI',
  /**
   * 企业库
   */
  EnterpriseLib = 'EnterpriseLib',
  /**
   * 企业详情
   */
  CompanyDetail = 'CompanyDetail',

  /**
   * Rover
   */
  Rover = 'Rover',

  /**
   * 标讯
   */
  Tender = 'Tender',
  /**
   * 司法案件
   */
  Case = 'Case',

  /**
   * 负面新闻
   */
  NegativeNews = 'NegativeNews',

  /**
   * 裁判文书
   */
  Judgement = 'Judgement',

  /**
   * 税务公告
   */
  TaxAnnouncement = 'TaxAnnouncement',

  /**
   * 股权出质
   */
  Pledge = 'Pledge',
  /**
   * 风险ES
   */
  RiskChange = 'RiskChange',
  /**
   * 特殊黑名单 仅用来详情搜索
   */
  SpecialBlacklist = 'SpecialBlacklist',
  OuterBlacklist = 'OuterBlacklist',

  /**
   * 行政处罚
   * 独立索引
   */
  SupervisePunish = 'SupervisePunish',
  /**
   * 违规处理
   * 目前使用专业版的违规处理同步过来的数据，只有近5年的记录且过滤了个体户跟非正常经营的企业
   */
  Violation = 'Violation',

  /**
   * 关联方
   */
  RelatedCompany = 'RelatedCompany',

  /**
   * 图谱（旧版关联方源枚举，已废弃为了兼容使用）
   */
  NebulaGraph = 'NebulaGraph',
  /**
   * 出口管制合规风险企业清单ES
   */
  OvsSanctionES = 'OvsSanctionES',
  /**
   * 资产查冻
   */
  AssetES = 'AssetES',

  /**
   * 动产抵押融合ES
   */
  PledgeMergerES = 'PledgeMergerES',
}
