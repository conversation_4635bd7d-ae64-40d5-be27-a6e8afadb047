export enum BatchStatusEnums {
  /** 待处理 */
  Waiting = 0,
  /** 处理中 */
  Processing = 1,
  /** 处理完成 */
  Done = 2,
  /** 批量任务超时了(仍然被标记成功了，可以重试) */
  Done_Timeout = 21, // 批量任务超时了(仍然被标记成功了，可以重试)
  /** 处理失败 */
  Error = 3,
  /** 队列中排队 */
  Queuing = 4, //队列中排队
  /** 套餐失效被冻结 */
  Suspended = 5, // 套餐失效被冻结
  /** 工商信息无效，需要人工确认 */
  invaild = 6, // 工商信息无效，需要人工确认
  /** 已中止取消 */
  Caneled = 7,
  /** 关联的数据已删除 */
  Deleted = 8,
}

// flow:  job 插入数据库时候 Waiting， job 被扫描并推送到队列中 Queuing，
// 队列中等待执行， 执行中 Processing， 执行完成 Done， 执行失败 Error， 已中止取消 Caneled， 套餐失效被冻结 Suspended， 工商信息无效，需要人工确认 invaild
export const ShouldExitBatchStatusEnums = [
  BatchStatusEnums.Done,
  BatchStatusEnums.Error,
  BatchStatusEnums.Caneled,
  BatchStatusEnums.Suspended,
  BatchStatusEnums.Done_Timeout,
];
