import { DimensionTypeEnums } from '../diligence/DimensionTypeEnums';
import { flatten } from 'lodash';

export enum DimensionFieldKeyEnums {
  /** 控制时间*/
  controllerTime = 'controllerTime',
  /** 是否国资委*/
  isStateOwned = 'isStateOwned',
  /** 股东角色*/
  shareholdRole = 'shareholdRole',
  /** 直接持股比例*/
  directShareholdPercentage = 'directShareholdPercentage',
  /** 专利统计指标维度 */
  patentStatistics = 'patentStatistics',
  /** 企业招聘统计指标维度 */
  recruitmentStatistics = 'recruitmentStatistics',
  /** 关联方变更类型： Added: 新增加符合关联方规则的企业  Removed:已监控关联方中不在符合关联方规则的企业 */
  relatedChangeType = 'relatedChangeType',
  /** 总持股比例 */
  percentTotal = 'percentTotal',
  /** 实缴资本异常 */
  realRegistrationError = 'realRegistrationError',
  /** 企业注册时间 */
  registerDate = 'registerDate',
  /** 国际专利状态*/
  internationPatentStatus = 'internationPatentStatus',
  /** 是否历史专利 */
  isHistoryPatent = 'isHistoryPatent',
  /** 专利来源 */
  patentSource = 'patentSource',
  /** 专利稳定性*/
  patentStable = 'patentStable',
  /** 发明人角色*/
  patentRole = 'patentRole',
  /** 专利状态*/
  patentStatus = 'patentStatus',
  /** 专利类型*/
  patentType = 'patentType',
  /** 区间左值 */
  leftRatio = 'leftRatio',
  /** 区间右值 */
  rightRatio = 'rightRatio',
  /** 是否产业链核型企业*/
  isIndustrialChainCoreCompany = 'isIndustrialChainCoreCompany',
  /** 是否是投资机构*/
  isInstitutionalInvestor = 'isInstitutionalInvestor',
  /** 是否PC，VE 融资 */
  isPEVC = 'isPEVC',
  /** 定制的关联方*/
  customizedRelated = 'customizedRelated',
  /** 绝对值比例*/
  absRatio = 'absRatio',
  /** 差值比例*/
  differenceRatio = 'differenceRatio',
  /** 投资机构的上榜榜单来源*/
  sourcesInvestInstiteRank = 'sourcesInvestInstiteRank',
  /** 企业无自然人股东 */
  NoNaturalPersonShareholder = 'NoNaturalPersonShareholder',
  /** 一级股东异常 **/
  primaryShareholderAbnormality = 'primaryShareholderAbnormality',
  /** 二级股东异常 **/
  secondaryShareholderAbnormality = 'secondaryShareholderAbnormality',
  /** 关联方循环持股 */
  CircularShareholdingBetweenRelatedParties = 'CircularShareholdingBetweenRelatedParties',
  /** 是否有员工持股平台 */
  HasEmployeeStockPlatform = 'HasEmployeeStockPlatform',
  /** 是否存在省级以上荣誉资质被取消*/
  HasCertificationRevoked = 'HasCertificationRevoked',
  /** n期变异系数 */
  cvXn = 'cvXn',
  /** n期平均值X */
  avgXn = 'avgXn',
  /** 时间周期: 近xx年 */
  yearPeriod = 'yearPeriod',
  /** 周期内持股比例变更 */
  periodShareRatioChange = 'periodShareRatioChange',
  /** 连续X年经营活动产生的现金流量净额 */
  cashFlowFromActivitiesAmount = 'cashFlowFromActivitiesAmount',
  /**负债合计/资产合计*/
  totalLiabToAssetsRatio = 'totalLiabToAssetsRatio',
  /**（货币资金+交易性金融资产）/（短期借款+应付票据）*/
  cmAndStbRatio = 'cmAndStbRatio',
  /**有息负债/年度总收入*/
  ibdAnnualRevRatio = 'ibdAnnualRevRatio',
  /**有息负债同比*/
  interestBearingLiabilitiesRatio = 'interestBearingLiabilitiesRatio',
  /**存货同比*/
  inventoryRatio = 'inventoryRatio',
  /**应收账款同比*/
  AccountsReceivableRatio = 'AccountsReceivableRatio',
  /**营业收入同比*/
  revenueRatio = 'revenueRatio',
  /**净利润同比*/
  netProfitRatio = 'netProfitRatio',
  /** 净利润 */
  netProfitAmount = 'netProfitAmount',
  /** 年报类型 */
  annualReportType = 'annualReportType',
  /** 阈值规则 */
  thresholdRule = 'thresholdRule',
  /** 投资持股比例范围  0:不限 1:<=5% 2:>5% 3:>20% 4:>50% 5:>66.66% 6:=100% */
  fundedRatioLevel = 'fundedRatioLevel',
  /** 股比变更幅度 */
  shareChangeRate = 'shareChangeRate',
  /** 股权冻结-股权数额*/
  equityFrozenAmount = 'equityFrozenAmount',
  /** 质押股权-质押股份数量(股)   */
  stockPledgeQuantity = 'stockPledgeQuantity',
  /** 质押股权-占总股本比例   */
  stockPledgeRatio = 'stockPledgeRatio',
  /** 股权出质-出质股权数额   */
  equityPledgeAmount = 'equityPledgeAmount',
  /** 股权出质- 质押股份数量(股)*/
  equityPledgeQuantity = 'equityPledgeQuantity',
  /** 股权出质-出质比例  */
  equityPledgeRatio = 'equityPledgeRatio',
  /** 企查分信用评分 */
  qccCreditScore = 'qccCreditScore',
  /** 股权质押状态 1-未达预警线,2-已解除质押，3-已达预警线未达平仓线*/
  sharePledgeStatus = 'sharePledgeStatus',
  /** 股权出质的状态 1-有效，2-无效*/
  equityPledgeStatus = 'equityPledgeStatus',
  /** 股权角色 1-大股东，2-实际控制人，3-公司主体, 4- 第一大股东*/
  holderRole = 'holderRole',
  /** 时间周期: 近xx个月 */
  timePeriod = 'timePeriod',
  /** 变更数量: 超过XX个 */
  thresholdCount = 'thresholdCount',
  /** 大多数控股子公司的行业与本公司行业不一致 **/
  industryThreshold = 'industryThreshold',
  /** 知识产权专业分类 1-出质人，2-质权人 */
  intellectualRole = 'intellectualRole',
  /** 知识产权类型 1-专利，2-商标 */
  intellectualType = 'intellectualType',
  /** 企业变更的角色 */
  compChangeRole = 'compChangeRole',
  /** 处罚单位 */
  penaltyUnit = 'penaltyUnit',
  /** 行政处罚单位 */
  penaltyIssuingUnit = 'penaltyIssuingUnit',
  /** 股比变更趋势 0减少, 1增加 */
  shareChangeStatus = 'shareChangeStatus',
  /** 减资幅度 **/
  capitalReductionRate = 'capitalReductionRate',
  /** 抽样检查结果*/
  inspectionResultType = 'inspectionResultType',
  /** 金融监管处罚原因 */
  financialPenaltyCause = 'financialPenaltyCause',
  /** 欠税金额*/
  taxOwedAmount = 'taxOwedAmount',
  /** 诉讼金额 **/
  lawsuitAmount = 'lawsuitAmount',
  /** 行政处罚的种类*/
  punishType = 'punishType',
  /** 处罚的金额 **/
  punishAmount = 'punishAmount',
  /** 红牌处罚 **/
  punishRedCard = 'punishRedCard',
  /** 被担保主债权数额 **/
  guaranteedPrincipal = 'guaranteedPrincipal',
  /** 拍卖类型： 1-破产拍卖 ， 2-司法拍卖*/
  auctionType = 'auctionType',
  /** 股权冻结范围： 1-企业股权被冻结 ， 2-持有股权被冻结*/
  equityFreezeScope = 'equityFreezeScope',
  /** 司法案件角色： 被告人/被告/被上诉人/被申请人/公诉人/原告/上诉人/申请人/第三人/其他当事人 */
  judicialRole = 'judicialRole',
  /** 限制高消费对象1-企业本身，2-法人代表 */
  restricterType = 'restricterType',
  /** 周期内注册资本变更 */
  periodRegisCapital = 'periodRegisCapital',
  /** 周期内注册资本变更幅度 **/
  changeRangeRegisCapitalCycle = 'changeRangeRegisCapitalCycle',
  /** 经营状态 40-清算, 90-吊销, 85-责令关闭, 70-停业，99-注销, 80-撤销, 75-歇业, 50-迁入, 60-迁出 */
  businessStatus = 'businessStatus',
  /** 注册资本变更比例 */
  regisCapitalChangeRatio = 'regisCapitalChangeRatio',
  /** 注册资本变更趋势 1减少, 2增加 */
  regisCapitalTrend = 'regisCapitalTrend',
  /** 是否币种变更 1币种变更, 0不是币种变更 */
  currencyChange = 'currencyChange',
  /** 受益人类型 1-受益所有人, 2-最终受益人 */
  beneficiaryType = 'beneficiaryType',
  /** 股东持股比例变化后身份 0-身份未变化 1-成为大股东 2-不再是大股东 */
  isBP = 'isBP',
  /** 变更前持股比例 */
  beforeContent = 'beforeContent',
  /** 变更后持股比例 */
  afterContent = 'afterContent',
  /** 变更趋势 1增加, 2减少 */
  changeStatus = 'changeStatus',
  /** 时间基准  ： 1-自然年； 2-自然年 */
  baselineDate = 'baselineDate',
  /** 企业负责人定义 1-法定代表人 2-执行事务合伙人， 3-负责人 ..*/
  layTypes = 'layTypes',
  /** 关联类型   */
  relationShips = 'relationShips',
  /** 风险动态类型   */
  riskCategories = 'riskCategories',
  /** 命中记录条数   */
  hitCount = 'hitCount',
  /** 1:当前有效，0:历史, -1:不限
   * - 0-历史
   - 1-有效
   - 2-逻辑删除
   - 3-重复
   - 4-过滤
   -  5-爬虫异常数据
   - 10-临时屏蔽
   - 6-待审核
   - 11-网信办屏蔽
   - 12-人行屏蔽
   - 13-客户屏蔽
   - 14-网信办屏蔽
   - 91-网信办屏蔽历史
   - 92-人行屏蔽历史
   - 93-客户屏蔽历史
   - 94-法院屏蔽历史

   0-历史
   1-有效
   10-临时屏蔽
   11-网信办屏蔽
   13-客户屏蔽
   91-网信办屏蔽历史
   93-客户屏蔽历史
   */
  isValid = 'isValid',
  /**  统计周期 不可以传0
   * -1 表示不限 compareType = Equal
   * 当 compareType = GreaterThan  表示 近x年  发生时间 > 通过cycle取到的时间；
   * 当 compareType = LessThanOrEqual  表示 x年前  发生时间 ＜= 通过cycle取到的时间；
   *  */
  cycle = 'cycle',
  /**  统计周期(自然年周期) 不可以传0
   * -1 表示不限 compareType = Equal
   * 当 compareType = GreaterThan  表示 近x年  发生时间 > 通过cycle取到的时间自然年年初的时间；
   * 当 compareType = LessThanOrEqual  表示 x年前  发生时间 ＜= 通过cycle取到的时间自然年年初的时间；
   *  */
  naturalCycle = 'naturalCycle',
  /** 时间范围,传两个时间戳范围之内的数据 */
  range = 'range',
  /** 维度列表排序  ['fieldName', 'desc']   */
  sortField = 'sortField',
  /** 股权数额   */
  equityAmount = 'equityAmount', //
  /** 处罚类型   */
  penaltiesType = 'penaltiesType', //
  /** 处罚事由类型   */
  punishReasonType = 'punishReasonType', //
  /** 处罚金额   */
  penaltiesAmount = 'penaltiesAmount', //
  /** 欠税金额   */
  taxArrearsAmount = 'taxArrearsAmount',
  /** 执行标的   */
  executionTarget = 'executionTarget', //
  /** 被执行总金额   */
  executionSum = 'executionSum', //
  /** 注册金额   */
  registrationAmount = 'registrationAmount', //
  /** 资本降幅   */
  capitalReduction = 'capitalReduction', //
  /** 变更阈值   */
  changeThreshold = 'changeThreshold', //
  // operator = 'operator', //运算符
  /** 成立时长 月   */
  duration = 'duration', //
  /** 涉案总金额   */
  amountInvolved = 'amountInvolved', //
  /** 作为被告方占比，如50   */
  percentAsDefendant = 'percentAsDefendant', //
  /** 经营异常类型   */
  businessAbnormalType = 'businessAbnormalType',
  /** 裁判文书身份类型（排除） prosecutor-原告 thirdpartyrole-第三人   */
  judgementRoleExclude = 'judgementRoleExclude',
  /** 裁判文书身份类型（包含） defendant-被告 prosecutor-原告 thirdpartyrole-第三人   */
  judgementRoleInclude = 'judgementRoleInclude',
  /** 投资任职关联类型 */
  types = 'types',
  /** 投资任职关联层级 */
  depth = 'depth',
  /** 持股/投资股权比例 */
  percentage = 'percentage',
  /** 节点排除类型 */
  excludedTypes = 'excludedTypes', //节点排除类型
  // excludedNodes = 'excludedNodes', //疑似关系节点排除类型
  /** 筛选数据范围（黑名单，第三方）   */
  dataRange = 'dataRange',
  /** 新闻主体类型   */
  topics = 'topics',
  /** 担保金额   */
  guaranteeAmount = 'guaranteeAmount',
  /** 土地抵押金额   */
  landMortgageAmount = 'landMortgageAmount',
  /** 动产抵押 被担保主债权数额   */
  chattelMortgageMainAmount = 'chattelMortgageMainAmount',
  /** 未履行总金额   */
  failure = 'failure',
  /** 案件总金额   */
  caseAmount = 'caseAmount',
  /** 担保风险金额   */
  guaranteedprincipal = 'guaranteedprincipal',
  /** 资质证书   */
  certification = 'certification',
  /** 临近到期时间   */
  nearExpirationType = 'nearExpirationType',
  /** 设置的数量值   */
  limitCount = 'limitCount',
  /** 资产负债率   */
  assetLiabilityRatio = 'assetLiabilityRatio',
  // 营业执照
  businessLicense = 'businessLicense',
  /** 关联对象   */
  associateObject = 'associateObject',
  /** 关联对象排除   */
  associateExclude = 'associateExclude',
  /** 人员分组   */
  personGroups = 'personGroups',
  /**  外部黑名单-出口管制合规风险企业清单  listcodes*/
  sanctionListCodes = 'sanctionListCodes',
  /**  股权质押状态 */
  pledgeStatus = 'pledgeStatus',
  /** 欠缴金额 */
  AmountOwed = 'AmountOwed',
  /** 企风控风险指标项目 */
  QfkRiskItem = 'QfkRiskItem',
  /** 裁判文书案由案件类型  xs-刑事案件，ms-民事案件 */
  CaseType = 'CaseType',
  /** 裁判文书案由类别 compareType 只支持 ContainsAny(包含任一) 和 ExceptAny(不包含任一)  */
  CaseReasonType = 'CaseReasonType',
  /** 是否为合同纠纷 **/
  isContractDispute = 'isContractDispute',
  /** 是否为金融涉诉*/
  isFinancialReason = 'isFinancialReason',
  /** 是否上市 */
  isListed = 'isListed',
  /** 股权变更频率 */
  equityChangeFrequency = 'equityChangeFrequency',
  /** 董监高法变动频率 */
  MainMembersChangeFrequency = 'MainMembersChangeFrequency',
  /** 纳税人资质变化 */
  TaxpayerCertificationChange = 'TaxpayerCertificationChange',
  /** 是否为银行或者金融租赁公司*/
  isBankOrFinancialLeasing = 'isBankOrFinancialLeasing',
  /** 裁判文书-角色类型*/
  judgementRole = 'judgementRole',
  /** 企业状态  吊销、注销、撤销、停业、歇业、责令关闭 */
  companyStatus = 'companyStatus',
  /** 企业注册资本 */
  CompanyRegistrationAmount = 'CompanyRegistrationAmount',
  /** 企业实缴资本 */
  realRegistrationAmount = 'realRegistrationAmount',
  /** 企风控指标 */
  QfkCode = 'QfkCode',
  /** 企业实缴资本比例（注册资本/实缴资本） */
  registrationRatio = 'registrationRatio',
  /** 企业实缴资本筛选*/
  realCapitalSelect = 'realCapitalSelect',
  /** 企业性质 */
  companyEconType = 'companyEconType',
  /** 排查主体 */
  targetInvestigation = 'targetInvestigation',
  /** 关联关系（外部关联风险） */
  relatedRoleType = 'relatedRoleType',
  /** 过滤港澳台海外企业 1.过滤港澳台海外企业 2.不过滤港澳台海外企业 */
  isFilterRelatedCompany = 'isFilterRelatedCompany',
  /** 风险类型（外部关联风险） */
  relatedRiskType = 'relatedRiskType',
  /** 上市企业*/
  companyListed = 'companyListed',
  /** 企业标识*/
  companyFlag = 'companyFlag',
  /** 分支机构*/
  companyIsBranch = 'companyIsBranch',
  /** 法人代表持股率   */
  legalRepresentHoldingRatio = 'legalRepresentHoldingRatio',
  /** 投资持股率   */
  investRatio = 'investRatio',
  /** 开庭公告-角色类型*/
  courtType = 'courtType',
  /** 开庭公告-案由*/
  courtCaseReason = 'courtCaseReason',
  /** 开庭公告-身份*/
  courtRole = 'courtRole',
  /** 黑名单-类型*/
  blackType = 'blackType',
  /** 净利润 */
  netProfit = 'netProfit',
  /**财富榜单*/
  wealthRank = 'wealthRank',
  /**上市板块*/
  listedIndustry = 'listedIndustry',
  /** 处理机构(一级)*/
  ProcessingAgencyLevelOne = 'ProcessingAgencyLevelOne',
  /** 处理机构(二级)*/
  ProcessingAgencyLevelTwo = 'ProcessingAgencyLevelTwo',
  /** 企业状态*/
  companyShortStatus = 'companyShortStatus',
  /** 联系方式类型*/
  contactType = 'contactType',
  /**关联方成员公司关联类型*/
  associateCompanyRelatedType = 'associateCompanyRelatedType',
  /** 金融机构类型 */
  financialInstitutionType = 'financialInstitutionType',
  /** 产品源 */
  productSource = 'productSource',
  /** 融资动态类型 */
  financingDynamicType = 'financingDynamicType',
  /** 所持股份被出质比例/总股本被出质比例 */
  equityPledgedRatioOrHolding = 'equityPledgedRatioOrHolding',
  /** 所持股份被质押比例/总股本被质押比例 */
  pledgedRatioOrHolding = 'pledgedRatioOrHolding',
  /** 简易注销类型 */
  simpleCancelType = 'simpleCancelType',
  /** 起拍价 */
  listingPrice = 'listingPrice',
  /** 企业公告类型 */
  announcementReportType = 'announcementReportType',
  /** 起拍价（元）资产拍卖 */
  quoteResultPrice = 'quoteResultPrice',
  /** 评估价格（元）询价评估 */
  evaluationPrice = 'evaluationPrice',
  /** 企业经营范围 */
  companySocpe = 'companySocpe',
  /** 企业名称 */
  companyName = 'companyName',
  /** 排除企业名称*/
  excludeCompanyName = 'excludeCompanyName',
  /** 国标行业  */
  companyIndustry = 'companyIndustry',
  /** 企查查行业  */
  qccIndustry = 'qccIndustry',

  /** 不命中是否提示 */
  isShowTip = 'isShowTip',
}

export const getUniqueFieldKeys = (dimension?: DimensionTypeEnums): string[] => {
  let allDimension: DimensionTypeEnums[] = [dimension];
  if (!dimension) {
    allDimension = Object.values(DimensionTypeEnums);
  }

  return flatten(
    allDimension.map((dimension) => {
      return Object.values(DimensionFieldKeyEnums).map((f) => `${dimension}~${f}`);
    }),
  );
};
