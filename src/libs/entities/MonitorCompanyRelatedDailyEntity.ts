import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn } from 'class-validator';
import { MonitorCompanyRelatedStatusEnum } from '../enums/monitor/MonitorCompanyStatusEnums';
import { ProductCodeAllowValues, ProductCodeEnums } from '../enums/ProductCodeEnums';

@Entity('monitor_company_related_daily')
export class MonitorCompanyRelatedDailyEntity {
  @PrimaryGeneratedColumn({
    type: 'int',
    name: 'id',
  })
  id: number;

  @ApiProperty({ description: '主公司的ID' })
  @Column('varchar', {
    nullable: false,
    length: 45,
    name: 'company_id_primary',
  })
  companyId: string;

  @ApiProperty({ description: '关联方公司的IDs' })
  @Column('text', {
    nullable: true,
    name: 'related_ids',
  })
  relatedIds: string;

  @ApiProperty({ description: '对应的分组id' })
  @Column('int', {
    nullable: false,
    name: 'monitor_group_id',
  })
  monitorGroupId: number;

  @Column('int', {
    nullable: false,
    name: 'org_id',
  })
  orgId: number;

  @Column('varchar', {
    nullable: false,
    name: 'product',
  })
  @ApiProperty({ description: '产品编码', enum: ProductCodeAllowValues })
  @IsIn(ProductCodeAllowValues)
  product: ProductCodeEnums;

  @Column('datetime', {
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'update_date',
  })
  updateDate: Date;

  @Column({
    type: 'int',
    name: 'status',
    nullable: false,
    default: () => '1',
  })
  @ApiProperty({
    description: '0:失效 1 有效',
    enum: Object.values(MonitorCompanyRelatedStatusEnum),
  })
  @IsIn(Object.values(MonitorCompanyRelatedStatusEnum))
  status?: number;

  @Column('datetime', {
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
    name: 'create_date',
  })
  createDate: Date;

  // @Column('varchar', {
  //   nullable: true,
  //   length: 200,
  //   name: 'related_types',
  // })
  // @ApiPropertyOptional({ description: '关联方的类型， 逗号分割' })
  // relatedTypeStr: string;

  // get relatedType(): string[] {
  //   return this.relatedTypeStr?.split(',') || [];
  // }
}
