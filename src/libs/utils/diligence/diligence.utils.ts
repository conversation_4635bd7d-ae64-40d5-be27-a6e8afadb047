import * as moment from 'moment';
import { DATE_TIME_FORMAT, HistoryValidNumbers, NoLimitValidNumbers } from '../../constants/common';
import { DimensionFieldCompareTypeEnums } from '../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DateRangeRelative } from '@kezhaozhao/qcc-model';
import { UnitEnums } from '../../enums/dimension/UnitEnums';
import { OperatorException } from '../../exceptions/OperatorException';
import { isNull } from 'lodash';

export const getExceptionDescription = (template: string, limit: number) => {
  return template?.replace(/#\w*#/g, (m) => {
    const key = m.replace(/#/g, '');
    if (key == 'limit') {
      return limit ? `${limit}` : '-';
    } else {
      return limit ? limit.toString() : ' - ';
    }
  });
};

/**
 * 获取准入排查计费周期的开始时间
 * @param startDate
 * @returns
 */
export const getBundleStart = (startDate: string | Date): string => {
  // 套餐使用量查询时，如果套餐有效期超过1年, 只统计最近一年套餐时间有效期内的准入排查额度使用量
  const activeDate = moment(startDate);
  const today = moment();
  const yearDiff = moment(today).diff(activeDate, 'year').valueOf();
  if (yearDiff > 0) {
    return activeDate.add(yearDiff, 'year').format(DATE_TIME_FORMAT);
  }
  return activeDate.format(DATE_TIME_FORMAT);
};

export const getIsValidValue = (isValid: string | number): string => {
  if (isValid == -1) {
    return NoLimitValidNumbers;
  } else if (isValid == 0) {
    return HistoryValidNumbers;
  } else if (isValid == 1) {
    return '1';
  }
  //默认返回 1
  return '1';
};

/**
 * 根据CompareType比较两个数字
 * @param value1
 * @param value2
 * @param operator 如果是 between: value2 <= value1 <= value3, value2是null表示无下限, value3是null表示无上限
 * @param value3
 * @returns
 */
export const getCompareResult = (value1: number, value2: number, compareType: DimensionFieldCompareTypeEnums, value3?: number): boolean => {
  switch (compareType) {
    case DimensionFieldCompareTypeEnums.LessThan:
      //  成立日期 在  baseMonthCount 个月 之前
      return value1 < value2;
    case DimensionFieldCompareTypeEnums.LessThanOrEqual:
      //  成立日期 在  baseMonthCount 个月 之前
      return value1 <= value2;
    case DimensionFieldCompareTypeEnums.GreaterThan:
      return value1 > value2;
    case DimensionFieldCompareTypeEnums.GreaterThanOrEqual:
      return value1 >= value2;
    case DimensionFieldCompareTypeEnums.Equal:
      return value1 == value2;
    case DimensionFieldCompareTypeEnums.Between:
      // 区间第一位是null, 则表示无下限
      if (isNull(value2)) {
        return value1 <= value3;
      }
      // 区间第二位是null, 则表示无上限
      if (isNull(value3)) {
        return value1 >= value2;
      }
      return value1 >= value2 && value1 <= value3;
    default:
      throw new OperatorException(`operator:[${compareType}] is not allowed in getCompareResult!`);
  }
};

/**
 * timeValue 是数字，比如设置为 1，根据 option中的 unit 单位是天，且 compareType 是 LessThanOrEqual，则表示成立日期小于当前日期1天
 * @param timeValue 时间值
 * @param unit 时间单位
 * @returns 目标日期时间戳（单位：毫秒）
 */
export const getTargetDate = (timeValue: number, unit: string): number => {
  const today = new Date();

  // 根据不同的时间单位计算目标日期
  const targetDate = new Date();

  switch (unit) {
    case '天':
    case 'day':
      targetDate.setDate(today.getDate() - timeValue);
      break;
    case '月':
    case 'month':
      targetDate.setMonth(today.getMonth() - timeValue);
      break;
    case '年':
    case 'year':
      targetDate.setFullYear(today.getFullYear() - timeValue);
      break;
    default:
      // 默认按天处理
      targetDate.setDate(today.getDate() - timeValue);
  }

  // 返回时间戳（毫秒）
  return targetDate.getTime();
};

/**
 * 将带单位的实缴资本字符串转换为统一单位（万元）的数值
 * @param paidCapi 实缴资本字符串，如"1000万元人民币"、"500万元港币"、"200万元美元"
 * @returns 转换后的实缴资本数值（单位：万元），无法解析则返回null
 */
export const normalizeCapitalToWanYuan = (paidCapi: string): number | null => {
  if (!paidCapi) return 0;

  // 提取数字部分
  const numberMatch = paidCapi.match(/^[\d.]+/);
  if (!numberMatch) return null;

  const amount = parseFloat(numberMatch[0]);
  if (isNaN(amount)) return null;

  // 检查单位并转换
  if (paidCapi.includes('万元')) {
    // 已经是万元单位，根据货币类型调整
    if (paidCapi.includes('港币')) {
      return amount * 0.9; // 假设汇率，港币:人民币 = 0.9:1
    } else if (paidCapi.includes('美元')) {
      return amount * 7; // 假设汇率，美元:人民币 = 7:1
    } else {
      return amount; // 人民币万元，不需要转换
    }
  } else if (paidCapi.includes('亿元')) {
    // 亿元转万元
    return amount * 10000;
  } else {
    // 其他情况，默认为元，转换为万元
    return amount / 10000;
  }
};

/**
 * 根据CompareType比较两个数组
 * @param fieldOperator
 * @param fieldVal
 * @param baseVal
 * @returns
 */
export const getCompareResultForArray = (compareType: DimensionFieldCompareTypeEnums, fieldVal: any[], baseVal: any[]) => {
  switch (compareType) {
    case DimensionFieldCompareTypeEnums.ContainsAny: //包含任一
      return baseVal.some((item) => fieldVal?.includes(item));
    case DimensionFieldCompareTypeEnums.ExceptAll: // 都不包含
      return fieldVal?.every((item) => !baseVal.includes(item));
    case DimensionFieldCompareTypeEnums.ContainsAll: // 都包含
      return fieldVal?.every((item) => baseVal.includes(item));
    case DimensionFieldCompareTypeEnums.ExceptAny: // 任意一个不包含
      return !baseVal.some((item) => fieldVal.includes(item));
    default:
      return false;
  }
};

function extractNumbers(str) {
  // 匹配数字，可以包含负号、小数点和可选的逗号分隔符
  const regex = /-?\d+(?:,\d{3})*(?:\.\d+)?/g;
  let match;
  const numbers = [];
  while ((match = regex.exec(str)) !== null) {
    // 去掉千分位符，转换为数字并添加到数组中
    const number = parseFloat(match[0].replace(/,/g, ''));
    numbers.push(number);
  }
  return numbers;
}

/**
 * 把带元单位的字符串根据 unit 转换为数字
 * @param value
 * @param unit
 */
export const convertByUnit = (value: string, unit: UnitEnums): number[] => {
  //如果 value 字符串中包含万元，则将其转换为单位为万元的数字,如果 value是区间字符串，则返回区间数组
  if (!value) {
    throw new Error('value is not defined!');
  }
  const matchInfo = extractNumbers(value);
  if (!matchInfo?.length) {
    throw new Error(`value does not match number: ${value}`);
  }
  const response = matchInfo.map((item) => {
    const num = Number(item);
    if (value.includes(UnitEnums.hundred_million_yuan) || value.includes(UnitEnums.hundred_million)) {
      if (unit === UnitEnums.yuan) {
        return num * 10000 * 10000;
      } else if (unit === UnitEnums.ten_thousand_yuan) {
        return num * 10000;
      }
      return num;
    }
    if (value.includes(UnitEnums.ten_thousand_yuan) || value.includes(UnitEnums.ten_thousand)) {
      if (unit === UnitEnums.yuan) {
        return num * 10000;
      } else if (unit === UnitEnums.hundred_million_yuan) {
        return Math.floor(num / 10000);
      }
      return num;
    }
    if (value.includes(UnitEnums.yuan)) {
      if (unit === UnitEnums.ten_thousand_yuan) {
        return Math.floor(num / 10000);
      } else if (unit === UnitEnums.hundred_million_yuan) {
        return Math.floor(num / 100000000);
      }
      return num;
    }
    return num;
  });
  return response;
};

/**
 * 构造查询区间
 * @param fieldOperator
 * @param fieldVal
 * @returns
 */
export const getAmountConditions = (fieldOperator: DimensionFieldCompareTypeEnums, fieldVal: any) => {
  switch (fieldOperator) {
    // case OperatorEnums.gt: //大于
    //   return [{ min: fieldVal }];
    case DimensionFieldCompareTypeEnums.GreaterThanOrEqual: //大于等于
      return [{ min: fieldVal }, { min: fieldVal, max: fieldVal }];
    // case OperatorEnums.eq: //等于
    //   return [{ min: fieldVal, max: fieldVal }];
    // case OperatorEnums.lt: //小于
    //   return [{ max: fieldVal }];
    case DimensionFieldCompareTypeEnums.LessThanOrEqual: //小于等于
      return [{ max: fieldVal }, { min: fieldVal, max: fieldVal }];
    case DimensionFieldCompareTypeEnums.ContainsAny: //包含任一
      break;
    case DimensionFieldCompareTypeEnums.ExceptAll: // 都不包含
      break;
    default:
      break;
  }
};

/**
 * 担保风险金额筛选获取 start，end
 * @param value
 * @param operator
 */
export const getStartEnd = (value: number, operator: DimensionFieldCompareTypeEnums): { start?: number; end?: number } => {
  //因为是左闭右开的，所以等于的不适合，只能让右边稍微大一点点
  switch (operator) {
    case DimensionFieldCompareTypeEnums.LessThan:
    case DimensionFieldCompareTypeEnums.LessThanOrEqual: {
      return { end: value + 0.1 };
    }
    case DimensionFieldCompareTypeEnums.GreaterThan:
    case DimensionFieldCompareTypeEnums.GreaterThanOrEqual: {
      return { start: value };
    }
    case DimensionFieldCompareTypeEnums.Equal:
      return { start: value, end: value + 0.1 };
    default:
      throw new OperatorException(`operator:[${operator}] is not allowed in getStartEnd!`);
  }
};

export const getDateRange = (num: number): DateRangeRelative[] => {
  return [
    Object.assign(new DateRangeRelative(), {
      currently: true,
      flag: 1,
      number: num * 365,
      unit: 'day',
    }),
  ];
};

/**
 *
 * @param cycle 年数，必须大于等于0
 * @return 当前时间减去cycle年数后的时间（当天的开始），毫秒级时间戳
 */
export const getStartTimeByCycle = (cycle: number): number => {
  const now = moment().startOf('day');
  if (cycle <= 0) {
    return now.toDate().getTime();
  }

  return now.subtract(cycle, 'years').toDate().getTime();

  // return moment(RequestUtils.ProcessDRR(getDateRange(cycle)[0]).start, 'YYYY-MM-DD')
  //   .toDate()
  //   .getTime();
};

/**
 * 获取自然周期的开始时间
 * @param naturalCycle 年数，必须大于等于0
 * @return 当前时间减去naturalCycle年数后的时间（当年的开始），毫秒级时间戳
 */
export const getStartTimeByNaturalCycle = (naturalCycle: number): number => {
  const now = moment().startOf('day');
  if (naturalCycle <= 0) {
    return now.toDate().getTime();
  }
  return now.subtract(naturalCycle, 'years').startOf('year').toDate().getTime();
};

export const getStartTimeByNaturalCycleMonth = (naturalCycle: number): number => {
  const now = moment().startOf('day');
  if (naturalCycle <= 0) {
    return now.toDate().getTime();
  }
  return now.subtract(naturalCycle, 'month').startOf('month').toDate().getTime();
};

export const excludeAmountUnits = (target: string): number => {
  // 定义数字部分的正则表达式
  const numberRegex = /(\d+\.?\d*)/;
  if (target && target.length > 0) {
    // 使用正则表达式提取数字部分
    const match = target.match(numberRegex);
    if (match) {
      const amount = parseFloat(match[0]);
      return isNaN(amount) ? 0 : amount;
    }
  }
  return 0;
};
