import { Injectable } from '@nestjs/common';
import { KafkaQueue, KafkaQueueGenerator, KafkaQueueSettings, PulsarGenerator, RabbitMQ } from '@kezhaozhao/message-queue';
import { ConfigService } from './config.service';
import { QueueNames } from '../constants/common';
import * as process from 'process';

@Injectable()
export class QueueService {
  public qccQueue: PulsarGenerator;
  public batchVerificationQueue: RabbitMQ;
  public batchDiligenceQueue: RabbitMQ;
  public batchJobMonitorQueue: RabbitMQ;
  public snapshotQueue: RabbitMQ;
  public snapshotBatchQueue: RabbitMQ;
  public continuousDiligenceQueue: RabbitMQ;
  public continuousDiligenceAnalyzeQueue: RabbitMQ;
  private kafkaQueue: KafkaQueueGenerator;
  public strategicCustomerKafkaQueue: KafkaQueue;
  public batchExportQueue: RabbitMQ;
  public batchResultBusinessQueue: RabbitMQ;
  // public scheduleQueue: RabbitMQ;
  public messageQueue: RabbitMQ;

  constructor(private readonly configService: ConfigService) {
    // plusar
    this.qccQueue = this.getPulsarGenerator();
    this.batchVerificationQueue = this.qccQueue.createQueue(QueueNames.BatchJobVerification, false);
    this.batchDiligenceQueue = this.qccQueue.createQueue(QueueNames.BatchJobDD, false);
    this.batchJobMonitorQueue = this.qccQueue.createQueue(QueueNames.BatchMonitor, true);
    this.snapshotQueue = this.qccQueue.createQueue(QueueNames.DiligenceSnapshot, false);
    this.snapshotBatchQueue = this.qccQueue.createQueue(QueueNames.BatchDiligenceSnapshot, false);
    this.continuousDiligenceQueue = this.qccQueue.createQueue(QueueNames.ContinuousDiligence, false);
    this.continuousDiligenceAnalyzeQueue = this.qccQueue.createQueue(QueueNames.ContinuousDiligenceAnalyze, true);
    this.batchExportQueue = this.qccQueue.createQueue(QueueNames.BatchExportJob, true);
    this.batchResultBusinessQueue = this.qccQueue.createQueue(QueueNames.BatchResultBusinessQueue, false);
    // this.scheduleQueue = this.qccQueue.createQueue(QueueNames.ScheduleQueue, false);
    this.messageQueue = this.qccQueue.createQueue(QueueNames.MessageQueue, true);

    // kafka
    this.kafkaQueue = this.getKafkaGenerator();
    this.strategicCustomerKafkaQueue = this.kafkaQueue.createQueue({
      topic: configService.kafkaTopic.strategicCustomerMonitor.name,
      producerConfig: configService.kafkaTopic.strategicCustomerMonitor.producerConfig,
    });
  }

  private getKafkaGenerator() {
    const shouldMock = this.shouldMockMessageQueue();
    if (!shouldMock) {
      return KafkaQueueGenerator.getInstance(this.configService.kafkaClientConfig);
    }
    return {
      createQueue(settings: KafkaQueueSettings): KafkaQueue {
        // @ts-ignore
        const obj = {
          sendMessage() {
            // console.log('mock kafka sendMessage()');
            return Promise.resolve();
          },
          sendMessageV2() {
            // console.log('mock kafka sendMessage()');
            return Promise.resolve();
          },
          process() {
            // console.log('mock kafka process()');
            return Promise.resolve();
          },
        };
        // jest.spyOn(obj, 'sendMessage');
        // jest.spyOn(obj, 'process');
        // @ts-ignore
        return obj as KafkaQueue;
      },
    } as KafkaQueueGenerator;
  }

  private getPulsarGenerator() {
    // if (!this.shouldMockMessageQueue()) {
    if (process.env.DEBUG_PULSAR_URL) {
      this.configService.pulsarMQ = {
        tenant: 'public',
        namespace: 'default',
        clientConfig: {
          serviceUrl: process.env.DEBUG_PULSAR_URL,
        },
      };
    }
    if (process.env.PULSAR_NS) {
      Object.assign(this.configService.pulsarMQ, {
        namespace: process.env.PULSAR_NS,
      });
    }
    if (!this.shouldMockMessageQueue() || process.env.PULSAR_NS == 'test') {
      return PulsarGenerator.getInstance(this.configService.pulsarMQ);
    }
    return {
      createQueue(queueName: string, delayed?: boolean): RabbitMQ {
        const obj = {
          sendMessage() {
            // console.log('mock pulsar  sendMessage()');
            return Promise.resolve();
          },
          sendMessageV2() {
            // console.log('mock pulsar sendMessageV2()');
            return Promise.resolve();
          },
          consume() {
            // console.log('mock pulsar consume()');
            return Promise.resolve();
          },
          on() {
            // console.log('mock pulsar on()');
            return Promise.resolve();
          },
        };
        // jest.spyOn(obj, 'sendMessage');
        // jest.spyOn(obj, 'sendMessageV2');
        // jest.spyOn(obj, 'consume');
        // jest.spyOn(obj, 'on');
        // @ts-ignore
        return obj as RabbitMQ;
      },
    } as PulsarGenerator;
  }

  private shouldMockMessageQueue() {
    // return process.env.NODE_ENV==='test' && process.env.JEST_WORKER_ID;
    const shouldMock = !!(
      (process.env.JEST_WORKER_ID && process.env.MOCK_MESSAGE_QUEUE_NOT !== 'true') ||
      !process.env.STAGE ||
      // 集群环境下，只有 primary 节点才会消费消息
      (process.env.POD_NAME && !process.env.POD_NAME.includes('primary'))
    );
    return process.env.MOCK_MESSAGE_QUEUE === 'true' || shouldMock; // 当puls 命名空间为test时，不进行mock
  }
}
