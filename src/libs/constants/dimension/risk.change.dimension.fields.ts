import { DataStatusEnums } from '../../enums/DataStatusEnums';
import { DimensionFieldKeyEnums } from '../../enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '../../enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldTypeEnums } from '../../enums/dimension/DimensionFiledDataTypeEnums';
import { CourtRole } from '../case.constants';
// import { GetFields } from '../dimension.base.fields.constants';
import { BusinessAbnormalType, FinancialSupervisionType, PenaltiesType, ProcessingAgencyLevelOneMap } from '../punish.constants';
import {
  ActionTypeMap,
  AnnouncementReportType,
  AnnualReportType,
  BaseLineDateSelect,
  BusinessStatusMap,
  CapitalReductionRateType,
  ChangeStatusMap,
  CompChangeAnalysisRole,
  CurrencyChangeMap,
  EquityFreezeScopeMap,
  EquityPledgeStatusType,
  HolderRoleType,
  InspectionResultTypeMap,
  IntellectualRole,
  IntellectualType,
  IsBPMap,
  IsPEVCMap,
  keyCauseActionMap,
  LayTypeMap,
  PenaltyUnitType,
  RegisCapitalTrendMap,
  RestricterTypeMap,
  RiskChangeCategoryList,
  ShareChangeStatusMap,
  SharePledgeStatusType,
  SimpleCancelTypeConstant,
  TaxOwedAmountSelectType,
} from '../risk.change.constants';
import { NegativePositiveTopicTypes } from '../news.constants';

export const RiskChangeDimensionFields = [
  {
    fieldName: '风险动态类型',
    fieldKey: DimensionFieldKeyEnums.riskCategories,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '风险动态类型 18-开庭公告',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [18],
    options: RiskChangeCategoryList,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '企业负责人定义',
    fieldKey: DimensionFieldKeyEnums.layTypes,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '企业负责人 1-法定代表人，2-执行事务合伙人...',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2, 3, 4, 5, 6, 7, 9],
    options: LayTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 2,
  },
  {
    fieldName: '变更阈值',
    fieldKey: DimensionFieldKeyEnums.changeThreshold,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更阈值 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [30],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 3,
  },
  {
    fieldName: '时间基准',
    fieldKey: DimensionFieldKeyEnums.baselineDate,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '时间基准 1年期末, 2年期末 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: BaseLineDateSelect,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 4,
  },
  {
    fieldName: '变更趋势',
    fieldKey: DimensionFieldKeyEnums.changeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更趋势 1增加, 2减少',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: ChangeStatusMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 5,
  },
  {
    fieldName: '变更后持股比例',
    fieldKey: DimensionFieldKeyEnums.afterContent,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更后持股比例<50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [50],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 6,
  },
  {
    fieldName: '变更前持股比例',
    fieldKey: DimensionFieldKeyEnums.beforeContent,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更前持股比例>50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 7,
  },
  {
    fieldName: '是否成为控股股东',
    fieldKey: DimensionFieldKeyEnums.isBP,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否变更为控股股东 1成为控股股东, 2不再是控股股东',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: IsBPMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 8,
  },
  /*  {
        fieldName: '受益人类型',
        fieldKey: DimensionFieldKeyEnums.beneficiaryType,
        dataType: DimensionFieldTypeEnums.Number,
        comment: '受益所有人 1受益所有人, 2最终受益人',
        defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
        defaultValue: [1],
        options: BeneficiaryTypeMap,
        isArray: 0,
        inputType: DimensionFieldInputTypeEnums.Select,
        status: DataStatusEnums.Enabled,
        fieldOrder: 9,
      },*/
  {
    fieldName: '是否币种变更',
    fieldKey: DimensionFieldKeyEnums.currencyChange,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否币种变更 1币种变更, 0不是币种变更',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: CurrencyChangeMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 10,
  },
  {
    fieldName: '注册资本变更趋势',
    fieldKey: DimensionFieldKeyEnums.regisCapitalTrend,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '注册资本变更趋势 1减少, 2增加',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: RegisCapitalTrendMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 11,
  },
  {
    fieldName: '注册资本变更比例',
    fieldKey: DimensionFieldKeyEnums.regisCapitalChangeRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '注册资本变更比例<50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [50],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 12,
  },
  {
    fieldName: '经营状态',
    fieldKey: DimensionFieldKeyEnums.businessStatus,
    dataType: DimensionFieldTypeEnums.String,
    comment: '经营状态 40-清算, 90-吊销, 85-责令关闭, 70-停业，99-注销, 80-撤销, 75-歇业, 50-迁入, 60-迁出',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [40, 90, 85, 70, 99, 80, 75, 50, 60],
    options: BusinessStatusMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 13,
  },
  {
    fieldName: '周期内注册资本变更',
    fieldKey: DimensionFieldKeyEnums.periodRegisCapital,
    dataType: DimensionFieldTypeEnums.Object,
    comment: '1个自然年度内拟减少注册资本超过其原注册资本5%',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [
      {
        valuePeriodTrend: 1,
        valuePeriodThreShold: 5,
        valuePeriodThreSholdCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
        valuePeriodBaseLine: 1,
      },
    ],
    options: [
      {
        valuePeriodTrend: { label: '变更趋势', value: RegisCapitalTrendMap },
        valuePeriodThreShold: { label: '占比', value: { unit: '%', min: 0, max: 100 } },
        valuePeriodThreSholdCompareType: {
          label: '占比比较(大于/小于)',
          value: DimensionFieldCompareTypeEnums.GreaterThan,
        },
        valuePeriodBaseLine: {
          label: '时间基准',
          value: BaseLineDateSelect,
        },
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 14,
  },
  {
    fieldName: '限消对象',
    fieldKey: DimensionFieldKeyEnums.restricterType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '限制高消费对象 1-企业本身，2-法人代表',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: RestricterTypeMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 15,
  },
  {
    fieldName: '案件角色',
    fieldKey: DimensionFieldKeyEnums.judicialRole,
    dataType: DimensionFieldTypeEnums.String,
    comment: '案件角色 11-原告, 13-上诉人, 12-申请执行人, 14-申请人, 21-被告, 23-被上诉人, 22-被执行人, 24-被申请人, 91-第三人, 99-其他',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['11', '13', '12', '14', '21', '23', '22', '24', '91', '99'],
    options: CourtRole,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 16,
  },
  {
    fieldName: '冻结范围',
    fieldKey: DimensionFieldKeyEnums.equityFreezeScope,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权冻结范围： 1-企业股权被冻结 ， 2-持有股权被冻结',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: EquityFreezeScopeMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 17,
  },
  {
    fieldName: '拍卖类型',
    fieldKey: DimensionFieldKeyEnums.auctionType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '拍卖类型： 1-破产拍卖 ， 2-司法拍卖',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: ActionTypeMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 18,
  },
  {
    fieldName: '处罚类别',
    fieldKey: DimensionFieldKeyEnums.punishType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '处罚类别： 0901-警告,0902-通报批评',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['0901'],
    options: PenaltiesType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 19,
  },
  {
    fieldName: '经营异常类型',
    fieldKey: DimensionFieldKeyEnums.businessAbnormalType,
    dataType: DimensionFieldTypeEnums.String,
    comment:
      '经营异常类型: 0803-公示信息隐瞒真实情况/弄虚作假, 0801-登记的住所/经营场所无法联系企业, 0805-未在规定期限公示年度报告，0802-未按规定公示企业信息，0804-未在登记所从事经营活动，0806-商事主体名称不适宜，0807-其他原因',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['0801', '0802', '0803', '0804', '0805', '0806', '0807'],
    options: BusinessAbnormalType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 20,
  },
  {
    fieldName: '欠税金额',
    fieldKey: DimensionFieldKeyEnums.taxOwedAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '欠税金额筛选: 500万以上,200万元-500万元,200万元以上',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [[200000, 5000000]],
    options: TaxOwedAmountSelectType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 21,
  },
  {
    fieldName: '动产抵押',
    fieldKey: DimensionFieldKeyEnums.guaranteedPrincipal,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '被担保主债权数额在0万元以上',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 22,
  },
  {
    fieldName: '土地抵押',
    fieldKey: DimensionFieldKeyEnums.landMortgageAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '抵押金额在0万元以上',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 23,
  },
  {
    fieldName: '担保信息',
    fieldKey: DimensionFieldKeyEnums.guaranteeAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '担保金额 小于 50000 万元',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [50000],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 24,
  },
  {
    fieldName: '税务催报',
    fieldKey: DimensionFieldKeyEnums.AmountOwed,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '欠缴金额 小于 500 万元',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [500],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 25,
  },
  {
    fieldName: '处罚原因',
    fieldKey: DimensionFieldKeyEnums.financialPenaltyCause,
    dataType: DimensionFieldTypeEnums.String,
    comment:
      '经营异常类别： 101-公开谴责，102-经营异常，103-经营异常，104-经营异常，105-经营异常，106-经营异常，107-经营异常，108-经营异常，109-经营异常，110-经营异常',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['101', '102', '103', '104', '105', '106', '107', '108', '109', '110'],
    options: FinancialSupervisionType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 26,
  },
  {
    fieldName: '检查结果',
    fieldKey: DimensionFieldKeyEnums.inspectionResultType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '检查结果： 0-不合格',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [0],
    options: InspectionResultTypeMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 27,
  },
  {
    fieldName: '减资幅度',
    fieldKey: DimensionFieldKeyEnums.capitalReductionRate,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '减资幅度 20% 50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [[20, 50]],
    options: CapitalReductionRateType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 28,
  },
  {
    fieldName: '食品安全',
    fieldKey: DimensionFieldKeyEnums.productSource,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '销售的产品被抽检不合格；生产的产品被抽检不合格',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: [
      { value: 1, label: '销售的产品' },
      { value: 2, label: '生产的产品' },
    ],
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 29,
  },
  {
    fieldName: '简易注销',
    fieldKey: DimensionFieldKeyEnums.simpleCancelType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '简易注销',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2, 3, 4, 5, 6],
    options: SimpleCancelTypeConstant,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 30,
  },
  {
    fieldName: '股权出质',
    fieldKey: DimensionFieldKeyEnums.equityPledgedRatioOrHolding,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '所持股份被出质比例/总股本被出质比例',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [95],
    options: [
      {
        unit: '%',
        min: 0,
        max: 100,
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 31,
  },
  {
    fieldName: '股比变更趋势',
    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股比变更趋势 1股比增加, 0股比减少',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: ShareChangeStatusMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 32,
  },
  {
    fieldName: '股比变更幅度',
    fieldKey: DimensionFieldKeyEnums.shareChangeRate,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '本次股比变更幅度 > 10%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [10],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 33,
  },
  {
    fieldName: '处罚单位',
    fieldKey: DimensionFieldKeyEnums.penaltyUnit,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '行政处罚处罚单位 1-税务局, 2-市场监督管理局,3-街道办事处',
    defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAny,
    defaultValue: [1],
    options: PenaltyUnitType,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 33,
  },
  {
    fieldName: '变更角色',
    fieldKey: DimensionFieldKeyEnums.compChangeRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更角色： 1-董事长,2-总经理',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1],
    options: CompChangeAnalysisRole,
    isArray: false,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 34,
  },
  {
    fieldName: '股权质押',
    fieldKey: DimensionFieldKeyEnums.pledgedRatioOrHolding,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '所持股份被质押比例/总股本被质押比例',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [95],
    options: [
      {
        unit: '%',
        min: 0,
        max: 100,
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 39,
  },
  {
    fieldName: '起拍价（元）',
    fieldKey: DimensionFieldKeyEnums.listingPrice,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '起拍价：不限，> n ，默认不限',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [],
    options: [{ unit: '元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 40,
  },
  {
    fieldName: '企业公告类型',
    fieldKey: DimensionFieldKeyEnums.announcementReportType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '企业公告类型-默认不限',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [],
    options: AnnouncementReportType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 41,
  },
  {
    fieldName: '起拍价（元）',
    fieldKey: DimensionFieldKeyEnums.quoteResultPrice,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '起拍价（元）：不限，> n ，默认不限，资产拍卖指标使用',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [],
    options: [{ unit: '元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 42,
  },
  {
    fieldName: '询价结果（元）',
    fieldKey: DimensionFieldKeyEnums.evaluationPrice,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '询价结果（元）：不限，> n ，默认不限，询价评估指标使用',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [],
    options: [{ unit: '元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 43,
  },
  {
    fieldName: '产权角色',
    fieldKey: DimensionFieldKeyEnums.intellectualRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '企业产权角色 1-出质人, 2-质权人',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1],
    options: IntellectualRole,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 44,
  },
  {
    fieldName: '产权类别',
    fieldKey: DimensionFieldKeyEnums.intellectualType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '产权类别 1-专利, 2-商标',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1],
    options: IntellectualType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 45,
  },
  {
    fieldName: '时间周期',
    fieldKey: DimensionFieldKeyEnums.timePeriod,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '时间周期： 近xx个月',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [3],
    options: [{ unit: '月', min: 1, max: 12 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 46,
  },
  {
    fieldName: '变更数量',
    fieldKey: DimensionFieldKeyEnums.thresholdCount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更数量: 超过XX个',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [5],
    options: [{ unit: '个', min: 1, max: 50 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 47,
  },
  {
    fieldName: '股权角色',
    fieldKey: DimensionFieldKeyEnums.holderRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权角色：1-大股东,2-实际控制人',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: HolderRoleType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 48,
  },
  {
    fieldName: '股权出质状态',
    fieldKey: DimensionFieldKeyEnums.equityPledgeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权出质状态：1-有效,2-无效',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: EquityPledgeStatusType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 49,
  },
  {
    fieldName: '股权质押的状态',
    fieldKey: DimensionFieldKeyEnums.sharePledgeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权质押的状态：1-未达预警线, 2-已解除质押，3-已达预警线未达平仓线',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2, 3],
    options: SharePledgeStatusType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 50,
  },
  {
    fieldName: '出质比例',
    fieldKey: DimensionFieldKeyEnums.equityPledgeRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '出质比例: 出质比例/总股本 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.Between,
    defaultValue: [1, 5],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 51,
  },
  {
    fieldName: '出质股权数额',
    fieldKey: DimensionFieldKeyEnums.equityPledgeAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '出质股权数额',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50000000],
    options: [{ unit: '元', min: 0, max: 99999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 52,
  },
  {
    fieldName: '质押占总股本比例',
    fieldKey: DimensionFieldKeyEnums.stockPledgeRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权质押: 占总股本比例 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.Between,
    defaultValue: [5, 10],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 53,
  },
  {
    fieldName: '质押股份数量',
    fieldKey: DimensionFieldKeyEnums.stockPledgeQuantity,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '质押股份数量(股)',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50000000],
    options: [{ unit: '股', min: 0, max: 99999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 54,
  },
  {
    fieldName: '股权数额',
    fieldKey: DimensionFieldKeyEnums.equityFrozenAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '冻结股权数额',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50000000],
    options: [{ unit: '元', min: 0, max: 99999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 55,
  },
  {
    fieldName: '出质股份数量',
    fieldKey: DimensionFieldKeyEnums.equityPledgeQuantity,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '出质股份数量(股)',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50000000],
    options: [{ unit: '股', min: 0, max: 99999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 56,
  },
  {
    fieldName: '案由类别',
    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 关键案由',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [],
    options: keyCauseActionMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 57,
  },
  {
    fieldName: '年报类型',
    fieldKey: DimensionFieldKeyEnums.annualReportType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '年报类型 年报,季报,半年报',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [202, 203, 204],
    options: AnnualReportType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 58,
  },
  {
    fieldName: '净利润',
    fieldKey: DimensionFieldKeyEnums.netProfitAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '净利润为0',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
    defaultValue: [0],
    options: [{ unit: '元', min: 0, max: 999999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 59,
  },
  {
    fieldName: '净利润同比',
    fieldKey: DimensionFieldKeyEnums.netProfitRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '净利润同比 净利润同比 < 30% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [30],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 60,
  },
  {
    fieldName: '营业收入同比',
    fieldKey: DimensionFieldKeyEnums.revenueRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '营业收入同比 营业收入同比 < 30% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [30],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 61,
  },
  {
    fieldName: '应收账款同比',
    fieldKey: DimensionFieldKeyEnums.AccountsReceivableRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '应收账款同比 应收账款同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 62,
  },
  {
    fieldName: '存货同比',
    fieldKey: DimensionFieldKeyEnums.inventoryRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '存货同比 存货同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 63,
  },
  {
    fieldName: '有息负债同比',
    fieldKey: DimensionFieldKeyEnums.interestBearingLiabilitiesRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '有息负债同比 有息负债同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 64,
  },
  {
    fieldName: '有息负债/年度总收入',
    fieldKey: DimensionFieldKeyEnums.ibdAnnualRevRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '有息负债占年度总收入比例 有息负债/年度总收入 > 40% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [40],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 65,
  },
  {
    fieldName: '（货币资金+交易性金融资产）/（短期借款+应付票据）',
    fieldKey: DimensionFieldKeyEnums.cmAndStbRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '（货币资金+交易性金融资产）/（短期借款+应付票据） <1 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [100],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 66,
  },
  {
    fieldName: '是否为合同纠纷',
    fieldKey: DimensionFieldKeyEnums.isContractDispute,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为合同纠纷',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非合同纠纷', value: 0 },
      { label: '是合同纠纷', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 62,
  },
  {
    fieldName: '负债合计/资产合计',
    fieldKey: DimensionFieldKeyEnums.totalLiabToAssetsRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '负债合计/资产合计 >80 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [80],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 66,
  },
  {
    fieldName: '连续X年经营活动产生的现金流量净额',
    fieldKey: DimensionFieldKeyEnums.cashFlowFromActivitiesAmount,
    dataType: DimensionFieldTypeEnums.Object,
    comment: '连续X年经营活动产生的现金流量净额',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [
      {
        consecutiveYearCount: 3,
        cashFlowFromActivitiesAmount: 0,
        cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ],
    options: [
      {
        consecutiveYearCount: { label: '连续X年', value: 3 },
        cashFlowFromActivitiesAmount: { unit: '元', min: 0, max: 99999999 },
        cashFlowFromActivitiesAmountCompareType: {
          label: '占比比较(大于/小于)',
          value: DimensionFieldCompareTypeEnums.GreaterThan,
        },
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 67,
  },
  {
    fieldName: '诉讼金额',
    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '诉讼金额',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 59,
  },
  {
    fieldName: '处罚金额',
    fieldKey: DimensionFieldKeyEnums.punishAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '处罚金额',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 60,
  },
  {
    fieldName: '红牌处罚',
    fieldKey: DimensionFieldKeyEnums.punishRedCard,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为红牌处罚',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非红牌处罚', value: 0 },
      { label: '是红牌处罚', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 61,
  },
  {
    fieldName: '大多数控股子公司的行业与本公司行业不一致',
    fieldKey: DimensionFieldKeyEnums.industryThreshold,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为大多数控股子公司的行业与本公司行业不一致',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '大多数控股子公司的行业与本公司行业一致', value: 0 },
      { label: '大多数控股子公司的行业与本公司行业不一致', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 62,
  },
  {
    fieldName: '裁判文书案由案件类型',
    fieldKey: DimensionFieldKeyEnums.CaseType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '裁判文书案由案件类型  xs-刑事案件，ms-民事案件',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: ['ms'],
    options: [
      { label: '民事案件', value: 'ms', caseReasonValue: 'B' },
      { label: '刑事案件', value: 'xs', caseReasonValue: 'A' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 63,
  },
  {
    fieldName: '新闻主体类型',
    fieldKey: DimensionFieldKeyEnums.topics,
    dataType: DimensionFieldTypeEnums.String,
    comment: '新闻主体类型',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['all'],
    options: NegativePositiveTopicTypes,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 64,
  },
  {
    fieldName: '绝对值比例',
    fieldKey: DimensionFieldKeyEnums.absRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '绝对值比例 > 10%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [10],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 65,
  },
  {
    fieldName: '差值比例',
    fieldKey: DimensionFieldKeyEnums.differenceRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '差值比例 > 10%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 66,
  },
  {
    fieldName: '是否金融涉诉',
    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否金融涉诉',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非金融涉诉', value: 0 },
      { label: '是金融涉诉', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 67,
  },
  {
    fieldName: '原告是否为银行或者金融租赁公司',
    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '原告是否为银行或者金融租赁公司',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '原告非银行或者金融租赁公司', value: 0 },
      { label: '原告是银行或者金融租赁公司', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 68,
  },
  {
    fieldName: '行政处罚单位',
    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
    dataType: DimensionFieldTypeEnums.String,
    comment: '行政处罚处罚单位 1-税务局, 2-市场监督管理局,3-街道办事处',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1],
    options: ProcessingAgencyLevelOneMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 69,
  },
  {
    fieldName: '是否上市',
    fieldKey: DimensionFieldKeyEnums.isListed,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否上市',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '未上市', value: 0 },
      { label: '已上市', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 70,
  },
  {
    fieldName: '是否为PE，VC机构融资',
    fieldKey: DimensionFieldKeyEnums.isPEVC,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为PE，VC机构融资',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: IsPEVCMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 71,
  },
  {
    fieldName: '阈值规则',
    fieldKey: DimensionFieldKeyEnums.thresholdRule,
    dataType: DimensionFieldTypeEnums.Number,
    comment: 'value是一组设置规则， 规则字段的要求再options里定义',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [
      { investCount: [1, 5], threshold: 60 },
      { investCount: [6, 15], threshold: 50 },
      { investCount: [16, null], threshold: 40 },
    ],
    options: [
      {
        investCount: { label: '投资企业数', min: 1, inputType: DimensionFieldInputTypeEnums.NumberRange },
        threshold: { label: '阈值', unit: '%', min: 0, max: 100, inputType: DimensionFieldInputTypeEnums.Text },
      },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.RuleList,
    status: DataStatusEnums.Enabled,
    fieldOrder: 104,
  },
  {
    fieldName: '周期内持股比例变更',
    fieldKey: DimensionFieldKeyEnums.periodShareRatioChange,
    dataType: DimensionFieldTypeEnums.Object,
    comment: '周期内持股比例变更',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [
      {
        timePeriod: 3,
        shareChangeStatus: 0,
        shareChangeRate: 10,
        shareChangeRateCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ],
    options: [
      {
        timePeriod: {
          label: '时间周期',
          unit: '月',
          min: 1,
          max: 12,
        },
        shareChangeStatus: {
          label: '股比变更趋势',
          options: ShareChangeStatusMap,
        },
        shareChangeRate: {
          label: '变更比例',
          unit: '%',
          min: 0,
          max: 100,
        },
        shareChangeRateCompareType: {
          label: '占比比较(大于/小于)',
          options: DimensionFieldCompareTypeEnums.GreaterThan,
        },
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.RuleList,
    status: DataStatusEnums.Enabled,
    fieldOrder: 14,
  },
];
