import { DataStatusEnums } from '../../enums/DataStatusEnums';
import { DimensionFieldKeyEnums } from '../../enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '../../enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldTypeEnums } from '../../enums/dimension/DimensionFiledDataTypeEnums';
import { NebulaRelatedEdgeEnums } from '../../enums/dimension/NebulaRelatedEdgeEnums';
import { NebulaRiskTagEnums } from '../../enums/dimension/NebulaRiskTagEnums';
import { CourtNumberType } from '../case.constants';
import { EntStatusList } from '../company.constants';
import { GetFields } from '../dimension.base.fields.constants';
import { CaseReasonTypeMap, CaseTypes } from '../judgement.constants';
import { RelatedTypeMap, SameContactTypeMap, CustomizedRelatedEnums, RelatedRiskTypesMap, RelatedChangeTypeEnums } from '../related.constants';
import { isFilterRelatedCompanyMap } from '../risk.change.constants';

/**
 * 关联方企业维度字段
 */
export const RelatedCompaniesDimensionFields = [
  {
    fieldName: '过滤关联方港澳台及海外企业',
    fieldKey: DimensionFieldKeyEnums.isFilterRelatedCompany,
    dataType: DimensionFieldTypeEnums.String,
    comment: '是否过滤关联方港澳台及海外企业 1.过滤港澳台及海外企业 2.不过滤港澳台及海外企业',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: isFilterRelatedCompanyMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  {
    fieldName: '关联方类型',
    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '关联方类型',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRelatedEdgeEnums.Employ],
    options: [...RelatedTypeMap, ...SameContactTypeMap],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  // {
  //   fieldName: '企业标识',
  //   fieldKey: DimensionFieldKeyEnums.companyFlag,
  //   dataType: DimensionFieldTypeEnums.String,
  //   comment: '企业标识: FNC_JR-金融机构; FNC_BX-保险机构;FNC_ZJ-保险中介机构',
  //   defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAll,
  //   defaultValue: ['FNC_JR', 'FNC_BX', 'FNC_ZJ'],
  //   options: [
  //     { label: '金融机构', value: 'FNC_JR' },
  //     { label: '保险机构', value: 'FNC_BX' },
  //     { label: '保险中介机构', value: 'FNC_ZJ' },
  //   ],
  //   isArray: 1,
  //   inputType: DimensionFieldInputTypeEnums.MultiSelect,
  //   status: DataStatusEnums.Enabled,
  //   fieldOrder: 2,
  // },
  {
    fieldName: '企业状态',
    fieldKey: DimensionFieldKeyEnums.companyStatus,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业状态：注销、吊销',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['99', '90'],
    options: EntStatusList,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 3,
  },
  // {
  //   fieldName: '实缴资本',
  //   fieldKey: DimensionFieldKeyEnums.realRegistrationAmount,
  //   dataType: DimensionFieldTypeEnums.Number,
  //   comment: '实缴资本数额小于等于 0 万元',
  //   defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
  //   defaultValue: [0],
  //   options: [{ unit: '万元', min: 0, max: 99999999 }],
  //   isArray: 0,
  //   inputType: DimensionFieldInputTypeEnums.Text,
  //   status: DataStatusEnums.Enabled,
  //   fieldOrder: 4,
  // },
  // {
  //   fieldName: '企业成立日期',
  //   fieldKey: DimensionFieldKeyEnums.CompanyStartDate,
  //   dataType: DimensionFieldTypeEnums.Number,
  //   comment: '企业成立日期 小于等于 3 个月',
  //   defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
  //   defaultValue: [3],
  //   options: [{ unit: '月', min: 1, max: 12 }],
  //   isArray: 0,
  //   inputType: DimensionFieldInputTypeEnums.Text,
  //   status: DataStatusEnums.Enabled,
  //   fieldOrder: 5,
  // },
  GetFields(DimensionFieldKeyEnums.hitCount, 6, [20]),
];

/**
 * 关联方变更维度字段
 */
export const RelatedCompanyChangeDimensionFields = [
  {
    fieldName: '定制的关联方类型',
    fieldKey: DimensionFieldKeyEnums.customizedRelated,
    dataType: DimensionFieldTypeEnums.String,
    comment: '',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [CustomizedRelatedEnums.HtfRelated],
    options: [
      { label: '汇添富定义关联方(Nebula查询)', value: CustomizedRelatedEnums.HtfRelated },
      { label: 'IcbcSF关联方(独立接口)', value: CustomizedRelatedEnums.IcbcSFRelated },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 8,
  },
  {
    fieldName: '关联方变更类型',
    fieldKey: DimensionFieldKeyEnums.relatedChangeType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [RelatedChangeTypeEnums.Added],
    options: [
      { label: '新增加符合关联方规则的企业', value: RelatedChangeTypeEnums.Added },
      { label: '已监控关联方中不在符合关联方规则的企业', value: RelatedChangeTypeEnums.Removed },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 8,
  },

  // 汇添富定义关联方支持下面的所有， IcbcSF关联方 只支持过滤港澳台企业
  ...RelatedCompaniesDimensionFields,
];

/**
 * 同地址或同联系方式关联方异常信息维度字段
 */
export const BusinessAnomaliesWithSamePhoneAndAddressDimensionFields = [
  {
    fieldName: '关联方类型',
    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '只支持相同地址、相同联系方式、相同邮箱',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRelatedEdgeEnums.HasPhone],
    options: SameContactTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  {
    fieldName: '企业标识',
    fieldKey: DimensionFieldKeyEnums.companyFlag,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业标识: FNC_JR-金融机构; FNC_BX-保险机构;FNC_ZJ-保险中介机构',
    defaultCompareType: DimensionFieldCompareTypeEnums.ExceptAll,
    defaultValue: ['FNC_JR', 'FNC_BX', 'FNC_ZJ'],
    options: [
      { label: '金融机构', value: 'FNC_JR' },
      { label: '保险机构', value: 'FNC_BX' },
      { label: '保险中介机构', value: 'FNC_ZJ' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '风险类型',
    fieldKey: DimensionFieldKeyEnums.relatedRiskType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '风险类型：经营异常',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRiskTagEnums.Exception],
    options: RelatedRiskTypesMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  GetFields(DimensionFieldKeyEnums.cycle, 2, [3]),
  GetFields(DimensionFieldKeyEnums.isValid, 3, [1]),
  {
    fieldName: '案由类别',
    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 洗钱罪 [A030437]',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
    defaultValue: [['A030437']],
    options: CaseReasonTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 5,
  },
  {
    fieldName: '案件类型',
    fieldKey: DimensionFieldKeyEnums.CaseType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 刑事案件 xs',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
    defaultValue: ['xs'],
    options: CaseTypes,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '角色类型',
    fieldKey: DimensionFieldKeyEnums.judgementRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '角色类型： 0：原告 1：被告 2：第三人 3：其他',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: CourtNumberType,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 7,
  },
];

/**
 * 关联方刑事案件维度字段
 */
export const MoneyLaunderingDimensionFields = [
  {
    fieldName: '关联方类型',
    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '关联方类型',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRelatedEdgeEnums.Employ],
    options: RelatedTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  {
    fieldName: '案由类别',
    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 洗钱罪 [A030437]',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
    defaultValue: [['A030437']],
    options: CaseReasonTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '案件类型',
    fieldKey: DimensionFieldKeyEnums.CaseType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 刑事案件 xs',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAll,
    defaultValue: ['xs'],
    options: CaseTypes,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '风险类型',
    fieldKey: DimensionFieldKeyEnums.relatedRiskType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '风险类型：裁判文书',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRiskTagEnums.Judgement], //裁判文书暂时只能配置裁判文书
    options: [{ label: '裁判文书', value: NebulaRiskTagEnums.Judgement, code: 6 }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  GetFields(DimensionFieldKeyEnums.cycle, 3, [3]),
  GetFields(DimensionFieldKeyEnums.isValid, 4, [1]),
  {
    fieldName: '角色类型',
    fieldKey: DimensionFieldKeyEnums.judgementRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '角色类型： 0：原告 1：被告 2：第三人 3：其他',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: CourtNumberType,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 6,
  },
];

/**
 * 关联方开庭公告维度字段
 */
export const RelatedAnnouncementDimensionFields = [
  {
    fieldName: '关联方类型',
    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '关联方类型',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRelatedEdgeEnums.Employ],
    options: RelatedTypeMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 0,
  },
  {
    fieldName: '风险类型',
    fieldKey: DimensionFieldKeyEnums.relatedRiskType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '风险类型：开庭公告',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [NebulaRiskTagEnums.Announcement], //开庭公告暂时只能配置开庭公告
    options: [{ label: '开庭公告', value: NebulaRiskTagEnums.Announcement, code: 5 }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '角色类型',
    fieldKey: DimensionFieldKeyEnums.courtType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '角色类型： 0：原告 1：被告 2：第三人 3：其他',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: CourtNumberType,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 2,
  },
  GetFields(DimensionFieldKeyEnums.cycle, 3, [3]),
  GetFields(DimensionFieldKeyEnums.isValid, 4, [1]),
  {
    fieldName: '投资持股比例',
    fieldKey: DimensionFieldKeyEnums.investRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '投资持股比例>50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThanOrEqual,
    defaultValue: [50],
    options: [{ unit: '%', min: 50, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 5,
  },
];
