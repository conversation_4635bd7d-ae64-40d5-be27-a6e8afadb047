import { DataStatusEnums } from '../../enums/DataStatusEnums';
import { DimensionFieldKeyEnums } from '../../enums/dimension/dimension.filter.params';
import { DimensionFieldCompareTypeEnums } from '../../enums/dimension/DimensionFieldCompareTypeEnums';
import { DimensionFieldInputTypeEnums } from '../../enums/dimension/DimensionFieldInputTypeEnums';
import { DimensionFieldTypeEnums } from '../../enums/dimension/DimensionFiledDataTypeEnums';
import { RelatedTypeEnums } from '../../enums/dimension/RelatedTypeEnums';
import {
  AnnualReportType,
  BusinessStatusMap,
  ChangeStatusMap,
  HolderRoleType,
  IsBPMap,
  keyCauseActionMap,
  RestricterTypeMap,
  RiskChangeCategoryList,
  ShareChangeStatusMap,
} from '../risk.change.constants';
import { ProcessingAgencyLevelOneMap } from '../punish.constants';
import { NegativePositiveTopicTypes } from '../news.constants';

export const RelatedRiskChangeDimensionFields = [
  {
    fieldName: '排除企业名称',
    fieldKey: DimensionFieldKeyEnums.excludeCompanyName,
    dataType: DimensionFieldTypeEnums.String,
    comment: '排除企业名称包含关键词',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['有限合伙'],
    options: [{ value: '有限合伙', label: '有限合伙' }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '企查查行业',
    fieldKey: DimensionFieldKeyEnums.qccIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企查查行业',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['28-2801'],
    options: [{ value: '28-2801', label: '房地产开发' }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '国标行业',
    fieldKey: DimensionFieldKeyEnums.companyIndustry,
    dataType: DimensionFieldTypeEnums.String,
    comment: '国标行业',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['K-70-701'],
    options: [{ value: 'K-70-701', label: '房地产开发经营' }],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '企业名称',
    fieldKey: DimensionFieldKeyEnums.companyName,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业名称包含关键词',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['房地产开发'],
    options: [
      { value: '房地产开发', label: '房地产开发' },
      { value: '小额贷款', label: '小额贷款' },
      { value: '互联网金融', label: '互联网金融' },
      { value: '典当', label: '典当' },
      { value: '保理', label: '保理' },
      { value: '担保', label: '担保' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '经营范围',
    fieldKey: DimensionFieldKeyEnums.companySocpe,
    dataType: DimensionFieldTypeEnums.String,
    comment: '企业经营范围关键词',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['土地开发', '地产开发', '商品房销售', '房地产项目投资'],
    options: [
      { value: '土地开发', label: '土地开发' },
      { value: '地产开发', label: '地产开发' },
      { value: '商品房销售', label: '商品房销售' },
      { value: '房地产项目投资', label: '房地产项目投资' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '风险动态类型',
    fieldKey: DimensionFieldKeyEnums.riskCategories,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '风险动态类型 18-开庭公告',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [18],
    options: RiskChangeCategoryList,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 1,
  },
  {
    fieldName: '时间周期',
    fieldKey: DimensionFieldKeyEnums.timePeriod,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '时间周期： 近xx个月',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [3],
    options: [{ unit: '月', min: 1, max: 12 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 3,
  },
  {
    fieldName: '变更数量',
    fieldKey: DimensionFieldKeyEnums.thresholdCount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更数量: 超过XX个',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [5],
    options: [{ unit: '个', min: 1, max: 50 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 5,
  },
  {
    fieldName: '股权角色',
    fieldKey: DimensionFieldKeyEnums.holderRole,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股权角色：1-大股东,2-实际控制人',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: HolderRoleType,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 7,
  },

  {
    fieldName: '阈值规则',
    fieldKey: DimensionFieldKeyEnums.thresholdRule,
    dataType: DimensionFieldTypeEnums.Number,
    comment: 'value是一组设置规则， 规则字段的要求再options里定义',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [
      { investCount: [1, 5], threshold: 60 },
      { investCount: [6, 15], threshold: 50 },
      { investCount: [16, null], threshold: 40 },
    ],
    options: [
      {
        investCount: { label: '投资企业数', min: 1, inputType: DimensionFieldInputTypeEnums.NumberRange },
        threshold: { label: '阈值', unit: '%', min: 0, max: 100, inputType: DimensionFieldInputTypeEnums.Text },
      },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.RuleList,
    status: DataStatusEnums.Enabled,
    fieldOrder: 17,
  },
  {
    fieldName: '查询关联方',
    fieldKey: DimensionFieldKeyEnums.relatedRoleType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '可以指定改维度查询的关联方对象',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [RelatedTypeEnums.InvestCompany],
    options: [
      { value: RelatedTypeEnums.InvestCompany, label: '对外投资企业' },
      { value: RelatedTypeEnums.ActualController, label: '实际控制人' },
      { value: RelatedTypeEnums.StockControlCompany, label: '上市主体企业' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 19,
  },
  {
    fieldName: '持股比例范围',
    fieldKey: DimensionFieldKeyEnums.fundedRatioLevel,
    dataType: DimensionFieldTypeEnums.String,
    comment: '投资持股比例范围，C端对外投资企业列表接口指定的参数',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [2],
    options: [
      { value: 0, label: '不限' },
      { value: 1, label: '<=5%' },
      { value: 2, label: '>5%' },
      { value: 3, label: '>20%' },
      { value: 4, label: '>50%' },
      { value: 5, label: '>66.66%' },
      { value: 6, label: '=100%' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 21,
  },
  {
    fieldName: '经营状态',
    fieldKey: DimensionFieldKeyEnums.businessStatus,
    dataType: DimensionFieldTypeEnums.String,
    comment: '经营状态 40-清算, 90-吊销, 85-责令关闭, 70-停业，99-注销, 80-撤销, 75-歇业, 50-迁入, 60-迁出',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [40, 90, 85, 70, 99, 80, 75, 50, 60],
    options: BusinessStatusMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 23,
  },
  {
    fieldName: '是否大多数控股子公司的行业与本公司行业不一致',
    fieldKey: DimensionFieldKeyEnums.industryThreshold,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为大多数控股子公司的行业与本公司行业不一致',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '大多数控股子公司的行业与本公司行业一致', value: 0 },
      { label: '大多数控股子公司的行业与本公司行业不一致', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 24,
  },
  {
    fieldName: '是否成为控股股东',
    fieldKey: DimensionFieldKeyEnums.isBP,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否变更为控股股东 1成为控股股东, 2不再是控股股东',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: IsBPMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 25,
  },
  {
    fieldName: '股比变更趋势',
    fieldKey: DimensionFieldKeyEnums.shareChangeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '股比变更趋势 1股比增加, 0股比减少',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: ShareChangeStatusMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 26,
  },
  {
    fieldName: '年报类型',
    fieldKey: DimensionFieldKeyEnums.annualReportType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '年报类型 年报,季报,半年报',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [202, 203, 204],
    options: AnnualReportType,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 27,
  },
  {
    fieldName: '净利润',
    fieldKey: DimensionFieldKeyEnums.netProfitAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '净利润为0',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThanOrEqual,
    defaultValue: [0],
    options: [{ unit: '元', min: 0, max: 999999999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 28,
  },
  {
    fieldName: '营业收入同比',
    fieldKey: DimensionFieldKeyEnums.revenueRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '营业收入同比 营业收入同比 < 30% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [30],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 29,
  },
  {
    fieldName: '净利润同比',
    fieldKey: DimensionFieldKeyEnums.netProfitRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '净利润同比 净利润同比 < 30% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [30],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 30,
  },
  {
    fieldName: '应收账款同比',
    fieldKey: DimensionFieldKeyEnums.AccountsReceivableRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '应收账款同比 应收账款同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 31,
  },
  {
    fieldName: '存货同比',
    fieldKey: DimensionFieldKeyEnums.inventoryRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '存货同比 存货同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 32,
  },
  {
    fieldName: '有息负债同比',
    fieldKey: DimensionFieldKeyEnums.interestBearingLiabilitiesRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '有息负债同比 有息负债同比 < 20% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [20],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 33,
  },
  {
    fieldName: '有息负债/年度总收入',
    fieldKey: DimensionFieldKeyEnums.ibdAnnualRevRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '有息负债占年度总收入比例 有息负债/年度总收入 > 40% ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [40],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 34,
  },
  {
    fieldName: '（货币资金+交易性金融资产）/（短期借款+应付票据）',
    fieldKey: DimensionFieldKeyEnums.cmAndStbRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '（货币资金+交易性金融资产）/（短期借款+应付票据） <1 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [100],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 35,
  },
  {
    fieldName: '负债合计/资产合计',
    fieldKey: DimensionFieldKeyEnums.totalLiabToAssetsRatio,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '负债合计/资产合计 >80 ',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [80],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 36,
  },
  {
    fieldName: '变更趋势',
    fieldKey: DimensionFieldKeyEnums.changeStatus,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更趋势 1增加, 2减少',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: ChangeStatusMap,
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 37,
  },
  {
    fieldName: '行政处罚单位',
    fieldKey: DimensionFieldKeyEnums.penaltyIssuingUnit,
    dataType: DimensionFieldTypeEnums.String,
    comment: '行政处罚处罚单位 1-税务局, 2-市场监督管理局,3-街道办事处',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1],
    options: ProcessingAgencyLevelOneMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 40,
  },
  {
    fieldName: '案由类别',
    fieldKey: DimensionFieldKeyEnums.CaseReasonType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '默认值为 关键案由',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [],
    options: keyCauseActionMap,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 41,
  },
  {
    fieldName: '裁判文书案由案件类型',
    fieldKey: DimensionFieldKeyEnums.CaseType,
    dataType: DimensionFieldTypeEnums.String,
    comment: '裁判文书案由案件类型  xs-刑事案件，ms-民事案件',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: ['ms'],
    options: [
      { label: '民事案件', value: 'ms', caseReasonValue: 'B' },
      { label: '刑事案件', value: 'xs', caseReasonValue: 'A' },
    ],
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 42,
  },
  {
    fieldName: '限消对象',
    fieldKey: DimensionFieldKeyEnums.restricterType,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '限制高消费对象 1-企业本身，2-法人代表',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: [1, 2],
    options: RestricterTypeMap,
    isArray: true,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 43,
  },
  {
    fieldName: '是否金融涉诉',
    fieldKey: DimensionFieldKeyEnums.isFinancialReason,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否金融涉诉',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非金融涉诉', value: 0 },
      { label: '是金融涉诉', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 44,
  },
  {
    fieldName: '是否为合同纠纷',
    fieldKey: DimensionFieldKeyEnums.isContractDispute,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '是否为合同纠纷',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '非合同纠纷', value: 0 },
      { label: '是合同纠纷', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 45,
  },
  {
    fieldName: '原告是否为银行或者金融租赁公司',
    fieldKey: DimensionFieldKeyEnums.isBankOrFinancialLeasing,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '原告是否为银行或者金融租赁公司',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [1],
    options: [
      { label: '原告非银行或者金融租赁公司', value: 0 },
      { label: '原告是银行或者金融租赁公司', value: 1 },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Select,
    status: DataStatusEnums.Enabled,
    fieldOrder: 46,
  },
  {
    fieldName: '新闻主体类型',
    fieldKey: DimensionFieldKeyEnums.topics,
    dataType: DimensionFieldTypeEnums.String,
    comment: '新闻主体类型',
    defaultCompareType: DimensionFieldCompareTypeEnums.ContainsAny,
    defaultValue: ['all'],
    options: NegativePositiveTopicTypes,
    isArray: 1,
    inputType: DimensionFieldInputTypeEnums.MultiSelect,
    status: DataStatusEnums.Enabled,
    fieldOrder: 47,
  },
  {
    fieldName: '变更后持股比例',
    fieldKey: DimensionFieldKeyEnums.afterContent,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更后持股比例<50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.LessThan,
    defaultValue: [50],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 48,
  },
  {
    fieldName: '变更前持股比例',
    fieldKey: DimensionFieldKeyEnums.beforeContent,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '变更前持股比例>50%',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [50],
    options: [{ unit: '%', min: 0, max: 100 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 49,
  },
  {
    fieldName: '诉讼金额',
    fieldKey: DimensionFieldKeyEnums.lawsuitAmount,
    dataType: DimensionFieldTypeEnums.Number,
    comment: '诉讼金额',
    defaultCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
    defaultValue: [0],
    options: [{ unit: '万元', min: 0, max: 99999999 }],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 50,
  },
  {
    fieldName: '连续X年经营活动产生的现金流量净额',
    fieldKey: DimensionFieldKeyEnums.cashFlowFromActivitiesAmount,
    dataType: DimensionFieldTypeEnums.Object,
    comment: '连续X年经营活动产生的现金流量净额',
    defaultCompareType: DimensionFieldCompareTypeEnums.Equal,
    defaultValue: [
      {
        consecutiveYearCount: 3,
        cashFlowFromActivitiesAmount: 0,
        cashFlowFromActivitiesAmountCompareType: DimensionFieldCompareTypeEnums.GreaterThan,
      },
    ],
    options: [
      {
        consecutiveYearCount: { label: '连续X年', value: 3 },
        cashFlowFromActivitiesAmount: { unit: '元', min: 0, max: 99999999 },
        cashFlowFromActivitiesAmountCompareType: {
          label: '占比比较(大于/小于)',
          value: DimensionFieldCompareTypeEnums.GreaterThan,
        },
      },
    ],
    isArray: 0,
    inputType: DimensionFieldInputTypeEnums.Text,
    status: DataStatusEnums.Enabled,
    fieldOrder: 200,
  },
];
