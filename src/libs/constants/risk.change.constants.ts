//import shared from 'apps/data/risk.copy.from.c/shared';
import shared from '../../apps/data/risk.copy.from.c/shared';

export const RiskChangeCategoryList = [
  { value: 2, label: '失信被执行人' },
  { value: 3, label: '被执行人' },
  { value: 4, label: '工商风险' },
  { value: 11, label: '经营异常' },
  { value: 12, label: '股权出质' },
  { value: 14, label: '抽查检查' },
  { value: 15, label: '动产抵押' },
  { value: 17, label: '对外投资' },
  { value: 18, label: '开庭公告' },
  { value: 20, label: '严重违法' },
  { value: 23, label: '简易注销' },
  { value: 24, label: '大股东变更' },
  { value: 25, label: '实际控制人变更' },
  { value: 26, label: '股权冻结' },
  { value: 29, label: '税收违法' },
  { value: 30, label: '土地抵押' },
  { value: 31, label: '欠税公告' },
  //  if (extend1.T === 1) {
  //      desc.Subtitle = '注册资本币种变更'
  //  }else {
  //    if (Number(changeInfo.T) === 2) {
  //           desc.Subtitle = '注册资本增加'
  //         } else {
  //           desc.Subtitle = '注册资本减少'
  //         }
  //  }
  { value: 37, label: '注册资本变更' },
  { value: 38, label: '经营状态变更' },
  { value: 39, label: '法定代表人变更' },
  { value: 40, label: '企业地址变更' },
  { value: 44, label: '股东变更' },
  { value: 46, label: '主要成员变更' },
  { value: 49, label: '立案信息' },
  { value: 50, label: '股权质押' },
  { value: 55, label: '限制高消费' },
  { value: 56, label: '终本案件' },
  { value: 57, label: '司法拍卖' },
  { value: 58, label: '破产重整' },
  { value: 59, label: '询价评估' },
  { value: 60, label: '企业名称变更' },
  { value: 61, label: '注销备案' },
  { value: 75, label: '资产拍卖' },
  { value: 78, label: '产品召回' },
  { value: 79, label: '食品安全' },
  { value: 91, label: '限制出境' },
  { value: 98, label: '未准入境' },
  { value: 101, label: '担保信息' },
  { value: 107, label: '行政处罚' },
  { value: 108, label: '票据违约' },
  { value: 109, label: '公安通告' },
  { value: 110, label: '债券违约' },
  { value: 114, label: '最终受益人变更' },
  { value: 117, label: '被列入非正常户' },
  { value: 121, label: '监管处罚' },
  { value: 123, label: '减资公告' },
  { value: 129, label: '上市进程' },
  { value: 130, label: '税务催报' },
  { value: 131, label: '税务催缴' },
];

/** 企业负责人定义 */
export const LayTypeMap = [
  // { value: 0, label: '法定代表人' },
  { value: 1, label: '法定代表人' },
  { value: 2, label: '执行事务合伙人' },
  { value: 3, label: '负责人' },
  { value: 4, label: '经营者' },
  { value: 5, label: '投资人' },
  { value: 6, label: '董事长' },
  { value: 7, label: '理事长' },
  { value: 9, label: '代表人' },
];

export const BaseLineDateSelect = [
  { value: 1, label: '当年初' },
  { value: 2, label: '去年初' },
  { value: 3, label: '前年初' },
];

/** 股东持股比例变化后 */
export const IsBPMap = [
  { value: 0, label: '身份未变化' },
  { value: 1, label: '成为大股东' },
  { value: 2, label: '不再是大股东' },
];

export const isFilterRelatedCompanyMap = [
  { value: 1, label: '过滤港澳台及海外企业' },
  { value: 2, label: '不过滤港澳台及海外企业' },
];

export const ChangeStatusMap = [
  { value: 1, label: '增加' },
  { value: 2, label: '减少' },
];

export const RealRegistrationErrorMap = [
  { value: 1, label: '当前企业无实缴资本' },
  { value: 2, label: '当前企业注册资本大于母公司(向上一层)的实缴资本' },
];

export const ShareChangeStatusMap = [
  { value: 1, label: '股比增加' },
  { value: 0, label: '股比减少' },
];

export const IsPEVCMap = [
  { value: 1, label: '是PE，VC融资' },
  { value: 0, label: '不是PE，VC融资' },
];

export const CurrencyChangeMap = [
  { value: 1, label: '币种变更' },
  { value: 0, label: '非币种变更' },
];

export const RegisCapitalTrendMap = [
  { value: 1, label: '减少' },
  { value: 2, label: '增加' },
];

/*export const BeneficiaryTypeMap = [
  { value: 1, label: '受益所有人' },
  { value: 2, label: '最终受益人' },
];*/

export const BusinessStatusMap = [
  { label: '清算', value: 40 },
  { label: '吊销', value: 90 },
  { label: '责令关闭', value: 85 },
  { label: '停业', value: 70 },
  { label: '注销', value: 99 },
  { label: '撤销', value: 80 },
  { label: '歇业', value: 75 },
  { label: '迁入', value: 50 },
  { label: '迁出', value: 60 },
];

export const penaltyResult107Map = [{ label: '责令停产停业', value: 1 }];

export const penaltyResult22Map = [
  { label: '吊销排污许可证', value: 1 },
  { label: '弄虚作假', value: 2 },
  { label: '虚假材料', value: 3 },
];

export const penaltyReason107Map = [
  { label: '弄虚作假', value: 1 },
  { label: '虚假材料', value: 2 },
  { label: '吊销排污许可证', value: 3 },
  { label: '未取得排污许可证', value: 4 },
  { label: '伪造排污许可证', value: 5 },
];

export const penaltyReason22Map = [
  { label: '未取得排污许可证', value: 1 },
  { label: '伪造排污许可证', value: 2 },
  { label: '弄虚作假', value: 3 },
  { label: '虚假材料', value: 4 },
  { label: '吊销排污许可证', value: 5 },
];

export const excludePenaltyResult107Map = [{ label: '情节严重的,责令停产停业', value: 1 }];

export const excludePenaltyResult22Map = [
  { label: '情节严重的,吊销排污许可证', value: 1 },
  { label: '情节严重的,限制开展生产经营活动、责令停产停业或者吊销排污许可证', value: 1 },
];

export const keyCauseActionMap = [
  { label: '伪造货币罪', value: 'A030401' },
  { label: '出售、购买、运输假币罪', value: 'A030402' },
  { label: '金融工作人员购买假币、以假币换取货币罪', value: 'A030403' },
  { label: '持有、使用假币罪', value: 'A030404' },
  { label: '变造货币罪', value: 'A030405' },
  { label: '擅自设立金融机构罪', value: 'A030406' },
  { label: '伪造、变造、转让金融机构经营许可证、批准文件罪', value: 'A030407' },
  { label: '高利转贷罪', value: 'A030409' },
  { label: '骗取贷款、票据承兑、金融票证罪', value: 'A030410' },
  { label: '非法吸收公众存款罪', value: 'A030411' },
  { label: '伪造、变造金融票证罪', value: 'A030412' },
  { label: '妨害信用卡管理罪', value: 'A030413' },
  { label: '窃取、收买、非法提供信用卡信息罪', value: 'A030414' },
  { label: '内幕交易、泄露内幕信息罪', value: 'A030418' },
  { label: '利用未公开信息交易罪', value: 'A030419' },
  { label: '操纵证券、期货市场罪', value: 'A030426' },
  { label: '背信运用受托财产罪', value: 'A030427' },
  { label: '吸收客户资金不入账罪', value: 'A030432' },
  { label: '违规出具金融票证罪', value: 'A030434' },
  { label: '洗钱罪', value: 'A030437' },
  { label: '骗购外汇罪', value: 'A030438' },
  { label: '集资诈骗罪', value: 'A030501' },
  { label: '贷款诈骗罪', value: 'A030502' },
  { label: '票据诈骗罪', value: 'A030503' },
  { label: '金融凭证诈骗罪', value: 'A030504' },
  { label: '信用证诈骗罪', value: 'A030505' },
  { label: '信用卡诈骗罪', value: 'A030506' },
  { label: '有价证券诈骗罪', value: 'A030507' },
  { label: '保险诈骗罪', value: 'A030508' },
  { label: '逃税罪', value: 'A030602' },
  { label: '虚开增值税专用发票、用于骗取出口退税、抵扣税款发票罪', value: 'A030606' },
  { label: '非法出售增值税专用发票罪', value: 'A030609' },
  { label: '非法经营罪', value: 'A030806' },
  { label: '虚假广告罪', value: 'A030802' },
  { label: '金融借款合同纠纷', value: 'B04012401' },
  { label: '民间借贷纠纷', value: 'B04012404' },
  { label: '小额借款合同纠纷', value: 'B04012405' },
  { label: '金融不良债权转让合同纠纷', value: 'B04012406' },
  { label: '金融不良债权追偿纠纷', value: 'B04012407' },
  { label: '融资租赁合同纠纷', value: 'B040133' },
  { label: '储蓄存款合同纠纷', value: 'B040130' },
  { label: '银行卡纠纷', value: 'B040131' },
  { label: '信用证纠纷', value: 'B0810' },
  { label: '票据纠纷', value: 'B0809' },
  { label: '进出口押汇纠纷', value: 'B040129' },
  { label: '证券交易合同纠纷', value: 'B080502' },
  { label: '证券回购合同纠纷', value: 'B080507' },
  { label: '融资融券交易纠纷', value: 'B080516' },
  { label: '金融衍生品种交易纠纷', value: 'B080503' },
  { label: '海上、通海水域保险合同纠纷', value: 'B0738' },
  { label: '财产保险合同纠纷', value: 'B080801' },
  { label: '人身保险合同纠纷', value: 'B080802' },
  { label: '信托纠纷', value: 'B0807' },
  { label: '保证合同纠纷', value: 'B040125' },
  { label: '抵押合同纠纷', value: 'B040126' },
  { label: '质押合同纠纷', value: 'B040127' },
  { label: '追偿权纠纷', value: 'B040161' },
  { label: '证券内幕交易责任纠纷', value: 'B08051301' },
  { label: '操纵证券交易市场责任纠纷', value: 'B08051302' },
  { label: '证券虚假陈述责任纠纷', value: 'B08051303' },
  { label: '保险人代位求偿权纠纷', value: 'B08080105' },
  { label: '金融委托理财合同纠纷', value: 'B04014001' },
  { label: '网络服务合同纠纷', value: 'B04015513' },
  { label: '借款合同纠纷案件执行', value: 'C02040124' },
  { label: '因申请诉前财产保全损害责任纠纷', value: 'B0926' },
  { label: '仲裁程序中的财产保全', value: 'B100905' },
  { label: '非国家工作人员受贿罪', value: 'A030311' },
  { label: '对非国家工作人员行贿罪', value: 'A030313' },
  { label: '违法发放贷款罪', value: 'A030430' },
  { label: '破坏计算机信息系统罪', value: 'A060122' },
  { label: '非法获取计算机信息系统数据、非法控制计算机信息系统罪', value: 'A060120' },
  { label: '非法侵入计算机信息系统罪', value: 'A060119' },
];

export const RestricterTypeMap = [
  { value: 1, label: '企业本身' },
  { value: 2, label: '法人代表' },
];

export const EquityFreezeScopeMap = [
  { value: 1, label: '企业股权被冻结' },
  { value: 2, label: '持有股权被冻结' },
];

export const ActionTypeMap = [
  { value: 1, label: '破产拍卖' },
  { value: 2, label: '司法拍卖' },
];

export const operatingAnomaliesTypeMap = [
  { value: 1, label: '公示企业信息隐瞒真实情况、弄虚作假' },
  { value: 2, label: '其他原因' },
];

/**
 * 欠税金额筛选
 */
export const TaxOwedAmountSelectType = [
  { label: '200万元以下', value: [0, 2000000] },
  { label: '200万元-500万元', value: [2000000, 5000000] },
  { label: '500万元以上', value: [5000000] },
];

export const InspectionResultTypeMap = [{ value: 0, label: '不合格' }];

/**
 * 减资幅度筛选
 */
export const CapitalReductionRateType = [
  { label: '20%以下', value: [0, 20] },
  { label: '20%-50%', value: [20, 50] },
  { label: '50%及以上', value: [50] },
];

/**
 * 注册资本变更趋势筛选
 */
export const ChangeRegisteredCapitalType = [
  { label: '20%以下', value: [0, 20] },
  { label: '20%-50%', value: [20, 50] },
  { label: '50%以上', value: [50] },
];

/**
 * 注册资本变更趋势筛选
 */
export const STIChangeRegisteredCapitalType = [
  { label: '5%以下', value: [0, 5] },
  { label: '5%-25%', value: [5, 25] },
  { label: '25%-50%', value: [25, 50] },
  { label: '50%-75%', value: [50, 75] },
  { label: '75%以上', value: [75] },
];

export const registrationRatioType = [
  { label: '0%', value: [0, 0] },
  { label: '40%以下', value: [0, 40] },
  { label: '40%-60%', value: [40, 60] },
  { label: '60%-80%', value: [60, 80] },
  { label: '80%-100%', value: [80, 100] },
  { label: '100%以上', value: [100] },
];

/**
 * 处罚单位
 */
export const PenaltyUnitType = [
  { value: 1, label: '税务局' },
  { value: 2, label: '市场监督管理局' },
  { value: 3, label: '街道办事处' },
  { value: 4, label: '体育局' },
  { value: 5, label: '交通运输局' },
];

/**
 * 企业人员变更分析的角色
 */
export const CompChangeAnalysisRole = [
  { value: 1, label: '董事长' },
  { value: 2, label: '总经理' },
  { value: 3, label: '董事' },
  { value: 4, label: '监事' },
];

/**
 * 知识产权角色：1-出质人，2-质权人
 */
export const IntellectualRole = [
  { value: 1, label: '出质人' },
  { value: 2, label: '质权人' },
];

/**
 * 知识产权类别：1-专利，2-商标
 */
export const IntellectualType = [
  { value: 1, label: '专利' },
  { value: 2, label: '商标' },
];

/**
 * 股权角色
 */
export const HolderRoleType = [
  { value: 1, label: '大股东' },
  { value: 2, label: '实际控制人' },
  { value: 3, label: '公司主体' },
];

/**
 * 股权出质状态
 */
export const EquityPledgeStatusType = [
  { value: 1, label: '有效' },
  { value: 2, label: '无效' },
];

/**
 * 股权质押状态
 */
export const SharePledgeStatusType = [
  { value: 1, label: '未达预警线' },
  { value: 2, label: '已解除质押' },
  { value: 3, label: '已达预警线未达平仓线' },
];

/**
 * 简易注销的类型
 */
export const SimpleCancelTypeConstant = [
  { value: 1, label: '正在进行简易注销公告' },
  { value: 2, label: '准许简易注销（未开业、无债权债务）' },
  { value: 3, label: '已撤销简易注销公告' },
  { value: 4, label: '准许简易注销' },
  { value: 5, label: '不予受理' },
  { value: 6, label: '准予简易注销' },
];

/**
 * 年报类型
 */
export const AnnualReportType = [
  { value: 203, label: '年报' },
  { value: 204, label: '季报' },
  { value: 202, label: '半年报' },
];

//将 Map<number,string>转换为[{value:number,label:string}]
/**
 * 企业公告类型
 */
export const AnnouncementReportType = Array.from(shared.ANNOUNCEMENT_REPORT_TYPE).map(([value, label]) => ({
  value: value,
  label: label,
}));
