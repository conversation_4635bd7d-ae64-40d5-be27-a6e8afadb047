import { NebulaRiskTagEnums } from '../enums/dimension/NebulaRiskTagEnums';
import { NebulaRelatedEdgeEnums } from '../enums/dimension/NebulaRelatedEdgeEnums';
import { RelatedTypeEnums } from '../enums/dimension/RelatedTypeEnums';

/**
 * C端关联风险中关联方角色类型 -1:不限 1 法人 2 股东 3 主要人员 4 对外投资 5 分支机构
 */
export const RelatedRoleTypeMap = [
  { label: '不限', value: -1 },
  { label: '法人', value: 1 },
  { label: '股东', value: 2 },
  { label: '主要人员', value: 3 },
  { label: '对外投资', value: 4 },
  { label: '分支机构', value: 5 },
];

/** 尽调结果等级 */
export enum DueDiligenceResult {
  /** 高风险 */
  highRisk = 2,
  /** 中高风险 */
  mediumRisk = 1,
  /** 中风险 */
  Warning = 0,
  /** 中低风险 */
  tips = -1,
  /** 良好 */
  pass = -2,
}

export const RelatedRoleEnums = {
  1: '法人',
  2: '股东',
  3: '主要人员',
  4: '对外投资',
  5: '分支机构',
  6: '担保方',
  7: '实际控制人',
  8: '法人股东',
  9: '法人股东法人',
};

/**
 * 客户定义关联方枚举
 */
export enum CustomizedRelatedEnums {
  /** 汇添富关联方 */
  HtfRelated = 'HtfRelated',
  /** icbc sf 关联方 */
  IcbcSFRelated = 'IcbcSFRelated',
}

/**
 * 关联方变更类型枚举
 */
export enum RelatedChangeTypeEnums {
  /** 新增加符合关联方规则的企业 */
  Added = 'Add',
  /**已监控关联方中不在符合关联方规则的企业 */
  Removed = 'Removed',
}

/**
 * dd服务中的关联方类型定义
 */
export const RelatedTypeMap = [
  { label: '主要人员控制企业', value: RelatedTypeEnums.PrincipalControl, edgeType: NebulaRelatedEdgeEnums.Employ },
  {
    label: '法定代表人控制企业',
    value: RelatedTypeEnums.LegalRepresentativeControl,
    edgeType: NebulaRelatedEdgeEnums.Legal,
  },
  {
    label: '实际控制人控制企业',
    value: RelatedTypeEnums.ActualControllerControl,
    edgeType: NebulaRelatedEdgeEnums.ActualController,
  },
  {
    label: '受益人控制企业',
    value: RelatedTypeEnums.BeneficiaryControl,
    edgeType: NebulaRelatedEdgeEnums.FinalBenefit,
  },
  { label: '分支机构', value: RelatedTypeEnums.Branch, edgeType: NebulaRelatedEdgeEnums.Branch },
  {
    label: '母公司（股东信息(股比>50%)）',
    value: RelatedTypeEnums.MotherCompanyMajorityShareholder,
    edgeType: NebulaRelatedEdgeEnums.MotherCompanyMajorityShareholder,
  },
  {
    label: '母公司控制企业',
    value: RelatedTypeEnums.MotherCompanyControl,
    edgeType: NebulaRelatedEdgeEnums.MotherCompanyControl,
  },
  {
    label: '子公司（对外投资(>50%的企业)）',
    value: RelatedTypeEnums.MajorityInvestment,
    edgeType: NebulaRelatedEdgeEnums.MajorityInvestment,
  },
  {
    label: '实际控制人控制企业',
    value: RelatedTypeEnums.AC,
    edgeType: NebulaRelatedEdgeEnums.AC,
  },
  // {
  //   label: '关联方成员企业',
  //   value: RelatedTypeEnums.RelatedMember,
  //   edgeType: NebulaRelatedEdgeEnums.RelatedMember,
  // },
];

/** ICBC sf定义的关联方 */
export const RelatedTypeMapForIcbcSF = [
  {
    label: '实际控制人',
    value: RelatedTypeEnums.ActualController,
  },
  {
    label: '控制企业',
    value: RelatedTypeEnums.ControlCompany,
  },
  {
    label: '实际控制人控制企业',
    value: RelatedTypeEnums.ActualController,
  },
  {
    label: '实控路线企业',
    value: RelatedTypeEnums.ShareholderChain,
  },
];

/**
 * graph关联风险中相同联系方式类型
 */
export const SameContactTypeMap = [
  { label: '相同地址', value: NebulaRelatedEdgeEnums.HasAddress, edgeType: NebulaRelatedEdgeEnums.HasAddress },
  { label: '相同电话', value: NebulaRelatedEdgeEnums.HasPhone, edgeType: NebulaRelatedEdgeEnums.HasPhone },
  { label: '相同邮箱', value: NebulaRelatedEdgeEnums.HasEmail, edgeType: NebulaRelatedEdgeEnums.HasEmail },
];

export const ALLRelatedType = [...RelatedTypeMap, ...SameContactTypeMap];

/**
 * C端关联风险中关联方风险类型
 * 22 失信被执行人;  24 被执行人;  23 限制高消费;  25 终本案件; 41 严重违法; 42 行政处罚; 53 经营异常
 */
export const RelatedRiskTypeMap = [
  { label: '失信被执行人', value: 22 },
  { label: '被执行人', value: 24 },
  { label: '限制高消费', value: 23 },
  { label: '终本案件', value: 25 },
  { label: '严重违法', value: 41 },
  { label: '行政处罚', value: 42 },
  { label: '经营异常', value: 53 },
];

// /**
//  * 关联方类型 -1:不限 1 主要人员控制企业 2 法定代表人控制企业 3 实际控制人控制企业 4 受益人控制企业 5 分支机构 6 母公司（股东信息(股比>50%)）7 母公司控制企业 8 子公司（对外投资(>50%的企业)）
//  */
// export const RelatedPartyTypeMap = [
//   { label: '不限', value: -1 },
//   { label: '主要人员控制企业', value: 1 },
//   { label: '法定代表人控制企业', value: 2 },
//   { label: '实际控制人控制企业', value: 3 },
//   { label: '受益人控制企业', value: 4 },
//   { label: '分支机构', value: 5 },
//   { label: '母公司', value: 6, rate: 50 },
//   { label: '母公司控制企业', value: 7 },
//   { label: '子公司', value: 8, rate: 50 },
// ];

/**
 * 关联方风险类型 -1:不限 1 裁判文书 2 经营异常 3 严重违法 4 失信人 5 税收违法 6 行政处罚 7 开庭公告
 *
 * Case, Exception, SeriousViolation, BadCreditExecuted, TaxIllegal, Punish, Announcement
 */
export const RelatedPartyRiskTypeMap = [
  { label: '不限', value: -1 },
  { label: '裁判文书', value: 1 },
  { label: '经营异常', value: 2 },
  { label: '严重违法', value: 3 },
  { label: '失信被执行人', value: 4 },
  { label: '税收违法', value: 5 },
  { label: '行政处罚', value: 6 },
  { label: '开庭公告', value: 7 },
];

export const RelatedPartyBusinessAnomaliesTypeMap = [
  { label: '不限', value: '-1' },
  { label: '注销', value: '注销' },
  { label: '吊销', value: '吊销' },
];

export const RelatedRiskTypesMap = [
  { label: '裁判文书', value: NebulaRiskTagEnums.Judgement, code: 6 },
  { label: '经营异常', value: NebulaRiskTagEnums.Exception, code: 0 },
  { label: '严重违法', value: NebulaRiskTagEnums.SeriousViolation, code: 1 },
  { label: '失信被执行人', value: NebulaRiskTagEnums.BadCreditExecuted, code: 2 },
  { label: '税收违法', value: NebulaRiskTagEnums.TaxIllegal, code: 3 },
  { label: '行政处罚', value: NebulaRiskTagEnums.Punish, code: 4 },
  { label: '开庭公告', value: NebulaRiskTagEnums.Announcement, code: 5 },
];

export const RelatedPartySeriousViolationTypeMap = [
  { label: '已吊销', value: 1 },
  { label: '严重违法', value: 2 },
  { label: '失信被执行人', value: 3 },
  { label: '税收违法', value: 4 },
];
