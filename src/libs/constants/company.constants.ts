/**
 * 经营状态
 */
export const StatusCode = {
  10: '正常', // 正常
  20: '存续',
  30: '筹建',
  40: '清算',
  50: '迁入',
  60: '迁出',
  70: '停业',
  75: '歇业',
  80: '撤销',
  87: '除名',
  85: '责令关闭',
  90: '吊销',
  99: '注销',
  93: '其他',
  92: '仍注册',
  94: '已告解散',
  95: '已终止营业地点',
  96: '不再是独立的实体',
  97: '休止活动',
  100: '废止',
  101: '废止清算完结',
  102: '废止许可',
  103: '废止许可完结',
  104: '废止认许',
  105: '废止认许完结',
  106: '接管',
  107: '撤回认许',
  108: '撤回认许完结',
  110: '撤销设立',
  111: '撤销完结',
  112: '撤销无需清算',
  113: '撤销许可',
  114: '撤销认许',
  115: '撤销认许完结',
  116: '核准报备',
  117: '核准设立',
  118: '设立但已解散',
  119: '核准许可报备',
  120: '核准许可登记',
  121: '核准认许',
  122: '清理',
  123: '清理完结',
  124: '破产',
  125: '破产清算完结',
  126: '破产程序终结',
  127: '解散',
  128: '解散清算完结',
  129: '重整',
  130: '合并解散',
  131: '终止破产',
  132: '涂销破产',
  133: '核准许可',
  134: '核准登记',
  135: '分割解散',
  136: '废止登记完结',
  137: '废止登记',
  138: '撤销登记完结',
  139: '撤销登记',
  140: '撤回登记完结',
  141: '撤回登记',
  9910: '注销',
};

/**
 * 企业状态
 */
export const EntStatusList = [
  {
    name: '正常状态',
    list: [
      { name: '在业', value: '10' },
      { name: '存续', value: '20' },
      { name: '迁入', value: '50' },
      { name: '迁出', value: '60' },
      { name: '设立', value: '117' },
    ],
  },
  {
    name: '异常状态',
    list: [
      { name: '注销', value: '99' },
      { name: '吊销', value: '90' },
    ],
  },
  {
    name: '其他状态',
    list: [
      { name: '设立失败', value: '142' },
      { name: '撤销', value: '80' },
      { name: '破产', value: '124' },
      { name: '重整', value: '129' },
      { name: '清算', value: '40' },
      { name: '清理', value: '122' },
      { name: '废止', value: '100' },
      { name: '撤回', value: '143' },
      { name: '停业', value: '70' },
      { name: '歇业', value: '75' },
      { name: '除名', value: '87' },
      { name: '责令关闭', value: '85' },
    ],
  },
];

export const EntStatusMap = EntStatusList.reduce((acc, statusGroup) => {
  statusGroup.list.forEach((item) => {
    acc[item.value] = item.name;
  });
  return acc;
}, {});

/**
 * 机构类型
 */
export const EnterpriseType = {
  '0': '其他',
  '001001001': '有限责任公司',
  '001001002': '股份有限公司',
  '001006': '个人独资企业',
  '001007001': '普通合伙',
  '001007002': '有限合伙',
  '001002004': '股份合作企业',
  '001009': '个体工商户',
  '001008': '农民专业合作社（联合社）',
  '001003': '机关单位',
  '001004': '事业单位',
  '001005': '社会组织',
  '001011': '律师事务所',
  '001016': '学校',
  '001015': '医疗机构',
};

/**
 * 机构类型
 */
export const EnterpriseTypeList = [
  {
    name: '有限责任公司',
    value: '001001001',
    list: [
      { name: '一人有限责任公司', value: '001001001001' },
      { name: '其他有限责任公司', value: '001001001002' },
    ],
  },
  { name: '股份有限公司', value: '001001002' },
  { name: '个体工商户', value: '001009' },
  {
    name: '合伙企业',
    value: '001007',
    list: [
      { name: '普通合伙', value: '001007001' },
      { name: '有限合伙', value: '001007002' },
    ],
  },
  { name: '全民所有制', value: '001002001' },
  { name: '集体所有制', value: '001002002' },
  { name: '联营企业', value: '001002003' },
  { name: '股份合作企业', value: '001002004' },
  { name: '个人独资企业', value: '001006' },
  { name: '农民专业合作社（联合社）', value: '001008' },
  { name: '机关单位', value: '001003' },
  { name: '事业单位', value: '001004' },
  {
    name: '社会组织',
    value: '001005',
    list: [
      { name: '社会团体', value: '001005001' },
      { name: '民办非企业单位', value: '001005002' },
      { name: '基金会', value: '001005003' },
      { name: '其他社会组织', value: '001005004' },
    ],
  },
  { name: '律师事务所', value: '001011' },
  { name: '学校', value: '001016' },
  { name: '医疗机构', value: '001015' },
  { name: '中国香港企业', value: '001013' },
  { name: '中国台湾企业', value: '001014' },
];

/**
 * 企业标识
 */
export const CompFlagType = [
  { label: '涉嫌非法社会组织', value: 'SXFFS' },
  { label: '已取缔非法社会组织', value: 'YQDFFS' },
  { label: '金融机构', value: 'FNC_JR' },
  { label: '保险机构', value: 'FNC_BX' },
  { label: '保险中介机构', value: 'FNC_ZJ' },
];

/**
 * 企业实缴资本筛选
 */
export const CompRealCapitalSelectType = [
  { label: '1亿-5亿', value: [10000, 50000] },
  { label: '5亿-20亿', value: [50000, 200000] },
  { label: '20亿-50亿', value: [200000, 500000] },
  { label: '50亿-100亿', value: [500000, 1000000] },
  { label: '100亿以上', value: [1000000] },
];

/**
 * 净利润率筛选
 */
export const NetProfitSelectType = [
  { label: '20亿-50亿', value: [200000, 500000] },
  { label: '50亿-100亿', value: [500000, 1000000] },
  { label: '100亿以上', value: [1000000] },
];

export const CompWealthRank = [
  { label: '中国500强', value: '140002' },
  { label: '世界500强', value: '140003' },
];

export const ListingIndustry = [
  { label: '上交所主板', value: 'S_10101' },
  { label: '上交所科创板', value: 'S_10401' },
  { label: '深交所主板', value: 'S_10201' },
  { label: '深交所创业板', value: 'S_10301' },
  { label: '北交所', value: 'S_10501' },
  { label: '中概股（港股）', value: 'S_801' },
  // { label: '中概股（港股）赴港上市企业', value: 'S_80101' },
  // { label: '中概股（港股）香港上市企业', value: 'S_80102' },
  { label: '港股主板', value: 'S_802' },
  { label: '港股创业板', value: 'S_803' },
  { label: '中概股（美股）', value: 'S_701' },
  { label: '纳斯达克', value: 'S_702' },
  { label: '纽交所', value: 'S_703' },
  { label: 'AMEX', value: 'S_704' },
  { label: '新三板', value: 'S_3' },
  { label: '新四板', value: 'S_10' },
];

/**
 * 上市标识
 */
export const ListingMarkType = [
  { label: '上市', value: 1 },
  { label: '非上市', value: 2 },
  { label: '上市(非ST/*ST)', value: 3 },
];

/**
 * 企业性质
 */
export const EconType = {
  '0': '其他',
  '002001001': '国有企业',
  '002001001001': '央企',
  '002001001002': '央企子公司',
  '002001001003': '省管国企',
  '002001001004': '市管国企',
  '002001001005': '国有全资企业',
  '002001001006': '国有独资企业',
  '002001001007': '国有控股企业',
  '002001001008': '国有实际控制企业',
  '001002002': '集体所有制',
  '001002003': '联营企业',
  '002006': '民营企业',
  '002002': '港澳台投资企业',
  '002003': '外商投资企业',
};

/**
 * 企业性质
 */
export const EconTypeList = [
  {
    name: '国有企业',
    value: '002001001',
    list: [
      { name: '央企', value: '002001001001' },
      { name: '央企子公司', value: '002001001002' },
      { name: '省管国企', value: '002001001003' },
      { name: '市管国企', value: '002001001004' },
      { name: '国有全资企业', value: '002001001005' },
      { name: '国有独资企业', value: '002001001006' },
      { name: '国有控股企业', value: '002001001007' },
      { name: '国有实际控制企业', value: '002001001008' },
    ],
  },
  { name: '民营企业', value: '002006' },
  {
    name: '港澳台投资企业',
    value: '002002',
    list: [
      { name: '港澳台合资经营企业', value: '002002001' },
      { name: '港澳台合作经营企业', value: '002002002' },
      { name: '港澳台独资企业', value: '002002003' },
      { name: '港澳台投资股份有限公司', value: '002002005' },
      { name: '其他港澳台投资企业', value: '002002004' },
    ],
  },
  {
    name: '外商投资企业',
    value: '002003',
    list: [
      { name: '中外合资经营企业', value: '002003001' },
      { name: '中外合作经营企业', value: '002003002' },
      { name: '外资企业（独资）', value: '002003003' },
      { name: '外商投资股份有限公司', value: '002003005' },
      { name: '其他外商投资企业', value: '002003004' },
    ],
  },
];

/** 数据频道-金融机构类型 */
export const FinancialInstitutions = [
  { value: '1', lable: '银行' },
  { value: '2', lable: '信托' },
  { value: '3', lable: '保险' },
  { value: '4', lable: '证券' },
  { value: '5', lable: '期货' },
  { value: '6', lable: '基金' },
  { value: '7', lable: '担保' },
  { value: '8', lable: '租赁' },
  { value: '9', lable: '银行理财子公司' },
  { value: '10', lable: '券商子公司' },
  { value: '11', lable: '财务公司' },
  { value: '12', lable: '消费金融公司' },
  { value: '13', lable: '汽车金融公司' },
  { value: '14', lable: '金融资管公司' },
  { value: '15', lable: '商业保理公司' },
];

/** 投资机构上榜榜单来源 */
export const SourcesInvestInstiteRankConstant = [
  { value: 1, label: '《21世纪经济报道》' },
  { value: 2, label: '21世纪创投研究院' },
  { value: 3, label: '36氪' },
  { value: 4, label: '36氪创投研究院' },
  { value: 5, label: '第一财经' },
  { value: 6, label: 'CBNData' },
  { value: 7, label: '第一财经商业数据中心' },
  { value: 8, label: '第一财经商业数据中心(CBNData)' },
  { value: 9, label: '母基金研究中心' },
  { value: 10, label: '母基金周刊' },
  { value: 11, label: '清科集团、投资界' },
  { value: 12, label: '投资界' },
  { value: 13, label: '清科研究中心' },
  { value: 14, label: '全球PE论坛' },
  { value: 15, label: '财新数据' },
  { value: 16, label: '全球PE论坛组委会' },
  { value: 17, label: '投中网' },
  { value: 18, label: '投中信息' },
  { value: 19, label: '证券时报' },
];

export const IsHistoryPatentConstant = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];

export const IsInstitutionalInvestorConstant = [
  { label: '是', value: 1 },
  { label: '不是', value: 2 },
];

export const IsStateOwnedConstant = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
];

export const IndustrialChainCoreCompanyContant = [
  { label: '是', value: 1 },
  { label: '不是', value: 2 },
];

export const PatentStatusConstant = [
  { label: '有效-授权', value: 'ZT002001' },
  { label: '有效-部分无效', value: 'ZT002003' },
  { label: '审中-公布', value: 'ZT001001' },
  { label: '审中-实质审查', value: 'ZT001003' },
  { label: '审中-实质审查', value: 'ZT001003' },
  { label: '无效-公布驳回', value: 'ZT003001' },
  { label: '无效-公布撤回', value: 'ZT003002' },
  { label: '无效-公布视为撤回', value: 'ZT003003' },
  { label: '无效-公布视为放弃', value: 'ZT003010' },
];

export const PatentTypeConstant = [
  { label: '发明公布', value: '1' },
  { label: '发明授权', value: '2' },
  { label: '外观设计', value: '4' },
];

export const PatentRoleConstant = [
  { label: '董、监、高、法及自然人股东', value: 1 },
  { label: '其它', value: 2 },
];

export const PatentStableConstant = [
  { value: 3, label: '任意3个年度有专利申请' },
  { value: 2, label: '任意2个年度有专利申请' },
  { value: 1, label: '任意1个年度有专利申请' },
  { value: 0, label: '无专利申请' },
];

export const InternationPatentStatusConstant = [
  { label: 'PCT指定期内-PCT未进入指定国（指定期内）', value: 'ZT005001' },
  { label: 'PCT指定期内-PCT进入指定国（指定期内）', value: 'ZT005002' },
  { label: 'PCT指定期满-PCT未进入指定国（指定期满）', value: 'ZT006001' },
  { label: 'PCT指定期满-PCT进入指定国（指定期满）', value: 'ZT006002' },
];

/**
 * 股东角色
 */
export const ShareholdRoleConstant = [
  { label: '大股东', value: 'majorShareholder' },
  { label: '实际控制人', value: 'actualController' },
  { label: '最终受益人', value: 'beneficialOwner' },
];

/** 企业有无自然人股东 */
export const HasCompanyNaturalPersonShareholder = [
  { value: 1, label: '企业有自然人股东' },
  { value: 0, label: '企业无自然人股东' },
];

/** 企业有无关联企业循环持股 */
export const HasCompanyCircularShareholder = [
  { value: 1, label: '企业有关联企业循环持股' },
  { value: 0, label: '企业无关联企业循环持股' },
];

/** 是否有员工持股平台 */
export const hasEmployeeStockPlatform = [
  { value: 1, label: '企业股东有员工持股平台' },
  { value: 0, label: '企业股东无员工持股平台' },
];

/** 是否有员工持股平台 */
export const hasCertificationRevoked = [
  { value: 1, label: '有省级以上资质被取消' },
  { value: 0, label: '无省级以上资质被取消' },
];

export const RecruitmentStatisticsConstant = [{ value: 1, label: '本科以上招聘占比' }];

export const PatentStatisticsConstant = [
  { value: 1, label: '发明专利占比' },
  { value: 2, label: '发明专利平均占比' },
  { value: 3, label: '发明专利授权率' },
  { value: 4, label: '发明专利平均授权率' },
  { value: 5, label: '发明专利发明人集中度' },
  { value: 6, label: '以转让方式获取的发明专利占比' },
  { value: 7, label: '发明专利申请稳定性' },
];
